{"version": 3, "sources": ["src/assets/stylesheets/main/layout/_source.scss", "src/assets/stylesheets/main.scss", "src/assets/stylesheets/main/_reset.scss", "src/assets/stylesheets/main/_colors.scss", "src/assets/stylesheets/main/_icons.scss", "src/assets/stylesheets/main/_typeset.scss", "src/assets/stylesheets/utilities/_break.scss", "node_modules/material-shadows/material-shadows.scss", "src/assets/stylesheets/main/layout/_base.scss", "src/assets/stylesheets/main/layout/_announce.scss", "src/assets/stylesheets/main/layout/_clipboard.scss", "src/assets/stylesheets/main/layout/_content.scss", "src/assets/stylesheets/main/layout/_dialog.scss", "src/assets/stylesheets/main/layout/_form.scss", "src/assets/stylesheets/main/layout/_header.scss", "src/assets/stylesheets/main/layout/_footer.scss", "src/assets/stylesheets/main/layout/_nav.scss", "src/assets/stylesheets/main/layout/_search.scss", "src/assets/stylesheets/main/layout/_select.scss", "src/assets/stylesheets/main/layout/_sidebar.scss", "src/assets/stylesheets/main/layout/_tabs.scss", "src/assets/stylesheets/main/layout/_version.scss", "src/assets/stylesheets/main/extensions/markdown/_admonition.scss", "node_modules/material-design-color/material-color.scss", "src/assets/stylesheets/main/extensions/markdown/_footnotes.scss", "src/assets/stylesheets/main/extensions/markdown/_toc.scss", "src/assets/stylesheets/main/extensions/pymdownx/_arithmatex.scss", "src/assets/stylesheets/main/extensions/pymdownx/_critic.scss", "src/assets/stylesheets/main/extensions/pymdownx/_details.scss", "src/assets/stylesheets/main/extensions/pymdownx/_emoji.scss", "src/assets/stylesheets/main/extensions/pymdownx/_highlight.scss", "src/assets/stylesheets/main/extensions/pymdownx/_keys.scss", "src/assets/stylesheets/main/extensions/pymdownx/_tabbed.scss", "src/assets/stylesheets/main/extensions/pymdownx/_tasklist.scss", "src/assets/stylesheets/main/_modifiers.scss"], "names": [], "mappings": "AAsJI,gBCgrEJ,CC1yEA,KACE,qBAAA,CACA,6BAAA,CAAA,0BAAA,CAAA,yBAAA,CAAA,qBD1BF,CC8BA,iBAGE,kBD3BF,CC+BA,KACE,QD5BF,CCgCA,qBAIE,uCD7BF,CCiCA,EACE,aAAA,CACA,oBD9BF,CCkCA,GACE,aAAA,CACA,kBAAA,CACA,aAAA,CACA,SAAA,CACA,gBAAA,CACA,QD/BF,CCmCA,MACE,aDhCF,CCoCA,QAEE,eDjCF,CCqCA,IACE,iBDlCF,CCsCA,MACE,uBAAA,CACA,gBDnCF,CCuCA,MAEE,eAAA,CACA,kBDpCF,CCwCA,OACE,QAAA,CACA,SAAA,CACA,iBAAA,CACA,sBAAA,CACA,QDrCF,CCyCA,MACE,QAAA,CACA,YDtCF,CE7CA,MAGE,sCAAA,CACA,6CAAA,CACA,+CAAA,CACA,gDAAA,CACA,0BAAA,CACA,gDAAA,CACA,kDAAA,CACA,oDAAA,CAGA,6BAAA,CACA,oCAAA,CACA,mCAAA,CACA,0BAAA,CACA,gDAAA,CAGA,4BAAA,CACA,sDAAA,CACA,yBAAA,CACA,+CF0CF,CEvCE,QAGE,0BAAA,CACA,0BAAA,CAGA,sCAAA,CACA,iCAAA,CACA,kCAAA,CACA,mCAAA,CACA,mCAAA,CACA,kCAAA,CACA,iCAAA,CACA,+CAAA,CACA,6DAAA,CACA,gEAAA,CACA,4DAAA,CACA,4DAAA,CACA,6DAAA,CAGA,6CAAA,CACA,+CAAA,CAGA,2CAAA,CAGA,2CAAA,CACA,4CAAA,CAGA,8BAAA,CACA,kCAAA,CACA,qCAAA,CAGA,mDAAA,CACA,mDAAA,CAGA,yBAAA,CACA,+CAAA,CACA,iDAAA,CACA,qCAAA,CACA,2CFyBJ,CG9FE,aACE,aAAA,CACA,YAAA,CACA,aAAA,CACA,iBHiGJ,CIxGA,KACE,kCAAA,CACA,iCJ2GF,CIvGA,WAGE,mCAAA,CACA,oGJ0GF,CIpGA,wBARE,6BJoHF,CI5GA,aAIE,4BAAA,CACA,gFJuGF,CI7FA,MACE,sNAAA,CACA,wNJgGF,CIzFA,YACE,eAAA,CACA,eAAA,CACA,gCAAA,CAAA,kBJ4FF,CIxFE,aAPF,YAQI,gBJ2FF,CACF,CIxFE,uGAME,iBAAA,CACA,YJ0FJ,CItFE,eACE,iBAAA,CACA,uCAAA,CAEA,aAAA,CACA,eJyFJ,CIpFE,8BAPE,eAAA,CAGA,qBJ+FJ,CI3FE,eACE,oBAAA,CAEA,kBAAA,CACA,eJuFJ,CIlFE,eACE,mBAAA,CACA,eAAA,CACA,gBAAA,CACA,eAAA,CACA,qBJoFJ,CIhFE,kBACE,eJkFJ,CI9EE,eACE,YAAA,CACA,eAAA,CACA,qBJgFJ,CI5EE,8BAEE,eAAA,CACA,uCAAA,CACA,eAAA,CACA,cAAA,CACA,qBJ8EJ,CI1EE,eACE,wBJ4EJ,CIxEE,eACE,iBAAA,CACA,cAAA,CACA,+DJ0EJ,CItEE,cACE,+BAAA,CACA,qBJwEJ,CIrEI,mCAEE,sBJsEN,CIlEI,wCAEE,+BJmEN,CI9DE,iDAGE,6BAAA,CACA,aJgEJ,CI7DI,aAPF,iDAQI,oBJkEJ,CACF,CI9DE,iBACE,uBAAA,CACA,eAAA,CACA,qBAAA,CACA,wCAAA,CACA,mBAAA,CACA,kCAAA,CAAA,0BJgEJ,CI7DI,qCACE,YAAA,CACA,uCJ+DN,CI1DE,wHAME,cAAA,CACA,eAAA,CACA,wBAAA,CACA,eJ4DJ,CIxDE,mBACE,kBJ0DJ,CItDE,gBACE,iBAAA,CACA,eJwDJ,CIrDI,qBACE,aAAA,CACA,QAAA,CACA,oCAAA,CACA,aAAA,CACA,iBAAA,CACA,eAAA,CACA,kCAAA,CAAA,0BAAA,CACA,iBAAA,CACA,oBAAA,CACA,+DJuDN,CIpDM,2BACE,qDJsDR,CIlDM,wCACE,WAAA,CACA,YJoDR,CIhDM,8CACE,oDJkDR,CI/CQ,oDACE,0CJiDV,CK5FI,wCDqDA,gBACE,iBJ0CJ,CIvCI,qBACE,eJyCN,CACF,CIpCE,gBACE,oBAAA,CACA,uBAAA,CACA,gCAAA,CACA,eAAA,CACA,uBAAA,CACA,qBAAA,CACA,4CAAA,CACA,mBAAA,CACA,mKJsCJ,CI/BE,iBACE,aAAA,CACA,qBAAA,CACA,6CAAA,CACA,kCAAA,CAAA,0BJiCJ,CI7BE,iBACE,oBAAA,CACA,6DAAA,CACA,WJ+BJ,CI5BI,oBANF,iBAOI,iBJ+BJ,CI5BI,wEEzRJ,gGAAA,CF6RM,iBAAA,CACA,MAAA,CACA,oBAAA,CACA,UAAA,CACA,6BAAA,CAAA,0BAAA,CAAA,qBAAA,CACA,aAAA,CACA,cAAA,CACA,mBAAA,CACA,gCAAA,CACA,eAAA,CACA,2CAAA,CACA,mBAAA,CACA,mBJ4BN,CACF,CIvBE,kBACE,WJyBJ,CIrBE,gCAEE,qBJuBJ,CIpBI,oDACE,sBAAA,CACA,aJuBN,CIlBE,uBACE,kBAAA,CACA,uCAAA,CACA,2DJoBJ,CIjBI,iCACE,mBAAA,CACA,cAAA,CACA,4DAAA,CACA,mBJmBN,CIdE,eACE,oBJgBJ,CIZE,8BAEE,kBAAA,CACA,SJcJ,CIXI,kDACE,mBAAA,CACA,aJcN,CIVI,oCACE,2BJaN,CIVM,0CACE,2BJaR,CIRI,oCACE,kBAAA,CACA,kBJWN,CIRM,wDACE,mBAAA,CACA,aJWR,CIPM,kGAEE,aJWR,CIPM,0DACE,eJUR,CINM,oFAEE,yBJUR,CIPQ,4HACE,mBAAA,CACA,aJYV,CILE,eACE,0BJOJ,CIJI,yBACE,oBAAA,CACA,aJMN,CIDE,gCAEE,cAAA,CACA,WJGJ,CIAI,wDAEE,oBJGN,CICI,0DAEE,oBJEN,CIEI,oEACE,YJCN,CIIE,mBACE,yBAAA,CAAA,sBAAA,CAAA,iBAAA,CACA,cAAA,CACA,aAAA,CACA,iBJFJ,CIKI,uBACE,aJHN,CIQE,uBACE,eAAA,CACA,mBAAA,CACA,iBJNJ,CIUE,mBACE,cJRJ,CIYE,+BACE,oBAAA,CACA,cAAA,CACA,aAAA,CACA,gBAAA,CACA,2CAAA,CACA,mBAAA,CACA,kEACE,CAEF,iBJZJ,CIeI,aAbF,+BAcI,aJZJ,CACF,CIiBI,iCACE,gBJfN,CIuBM,8FACE,YJpBR,CIwBM,4FACE,eJrBR,CI0BI,8FAEE,eJxBN,CI2BM,kHACE,gBJxBR,CI6BI,kCACE,cAAA,CACA,sBAAA,CACA,gCAAA,CACA,kBAAA,CACA,kDJ3BN,CI8BM,oCACE,aJ5BR,CIiCI,kCACE,sBAAA,CACA,kBAAA,CACA,4DJ/BN,CImCI,kCACE,iCJjCN,CIoCM,wCACE,iCAAA,CACA,sDJlCR,CIsCM,iDACE,YJpCR,CIyCI,iCACE,iBJvCN,CI4CE,wCACE,cJ1CJ,CI6CI,8CACE,oBAAA,CACA,WAAA,CACA,YAAA,CACA,gBAAA,CACA,kBAAA,CACA,6BAAA,CAAA,qBAAA,CACA,yBAAA,CAAA,iBAAA,CACA,UJ3CN,CI+CI,mEACE,6BAAA,CACA,qDAAA,CAAA,6CJ7CN,CIiDI,oEACE,6BAAA,CACA,sDAAA,CAAA,8CJ/CN,CIoDE,wBACE,iBAAA,CACA,eAAA,CACA,iBJlDJ,CIsDE,mBACE,oBAAA,CACA,kBAAA,CACA,eJpDJ,CIuDI,aANF,mBAOI,aJpDJ,CACF,CIuDI,8BACE,aAAA,CACA,UAAA,CACA,QAAA,CACA,eJrDN,COpiBA,KACE,WAAA,CACA,iBAAA,CAOA,cPiiBF,CKxYI,oCElKJ,KAaI,gBPiiBF,CACF,CK7YI,oCElKJ,KAkBI,cPiiBF,CACF,CO5hBA,KACE,iBAAA,CACA,YAAA,CACA,qBAAA,CACA,UAAA,CACA,eAAA,CAGA,eAAA,CACA,2CP6hBF,CO1hBE,aAZF,KAaI,aP6hBF,CACF,CK9YI,wCE5IF,yBAII,cP0hBJ,CACF,COjhBA,SACE,eAAA,CACA,iBAAA,CACA,gBPohBF,COhhBA,cACE,YAAA,CACA,qBAAA,CACA,WPmhBF,COhhBE,aANF,cAOI,aPmhBF,CACF,CO/gBA,SACE,WPkhBF,CO/gBE,gBACE,YAAA,CACA,WAAA,CACA,iBPihBJ,CO5gBA,aACE,eAAA,CACA,kBAAA,CACA,sBP+gBF,COtgBA,WACE,YPygBF,COrgBA,SACE,cAAA,CAGA,UAAA,CACA,YAAA,CACA,mBAAA,CACA,gCAAA,CACA,gBAAA,CACA,2CAAA,CACA,mBAAA,CACA,2BAAA,CACA,SPsgBF,COngBE,eACE,UAAA,CACA,uBAAA,CACA,SAAA,CACA,oEPqgBJ,CO1fA,MACE,WP6fF,CQnoBA,aACE,aAAA,CACA,0CRqoBF,CQloBE,aALF,aAMI,YRqoBF,CACF,CQloBE,oBACE,iBAAA,CACA,eAAA,CACA,+BAAA,CACA,eRooBJ,CSlpBA,MACE,+PTqpBF,CS/oBA,cACE,iBAAA,CACA,QAAA,CACA,UAAA,CACA,SAAA,CACA,WAAA,CACA,YAAA,CACA,0CAAA,CACA,mBAAA,CACA,cAAA,CACA,qBTkpBF,CS/oBE,aAbF,cAcI,YTkpBF,CACF,CS/oBE,kCACE,YAAA,CACA,uCTipBJ,CS7oBE,qBACE,uCT+oBJ,CS3oBE,wCAEE,+BT4oBJ,CSvoBE,oBACE,aAAA,CACA,aAAA,CACA,cAAA,CACA,aAAA,CACA,6BAAA,CACA,2CAAA,CAAA,mCAAA,CACA,6BAAA,CAAA,qBAAA,CACA,yBAAA,CAAA,iBAAA,CACA,UTyoBJ,CSroBE,sBACE,cTuoBJ,CSpoBI,2BACE,2CTsoBN,CShoBI,kEAEE,+BAAA,CACA,uDTioBN,CUvsBA,YACE,WAAA,CAMA,eAAA,CACA,0BVqsBF,CUlsBE,mBACE,qBAAA,CACA,iBVosBJ,CK/iBI,sCK/IE,kEACE,kBVisBN,CU9rBM,4EACE,mBAAA,CACA,iBVgsBR,CU3rBI,oEACE,mBV6rBN,CU1rBM,8EACE,kBAAA,CACA,kBV4rBR,CACF,CUtrBI,0BACE,aAAA,CACA,YAAA,CACA,UVwrBN,CUprBI,+BACE,eVsrBN,CUhrBE,oBACE,WAAA,CAEA,0BAAA,CACA,SVkrBJ,CU/qBI,aAPF,oBAQI,YVkrBJ,CACF,CU/qBI,8BACE,UAAA,CACA,kBAAA,CACA,aVirBN,CU9qBM,kCACE,oBVgrBR,CU3qBI,gCACE,yCV6qBN,CUzqBI,wBACE,cAAA,CACA,kBV2qBN,CWnwBA,WLFE,gGAAA,CKKA,cAAA,CACA,WAAA,CACA,YAAA,CACA,SAAA,CACA,SAAA,CACA,iBAAA,CACA,mBAAA,CACA,2CAAA,CACA,mBAAA,CACA,0BAAA,CACA,SAAA,CACA,wCACE,CAEF,mBXmwBF,CWhwBE,aApBF,WAqBI,YXmwBF,CACF,CWhwBE,qBACE,UAAA,CACA,UXkwBJ,CW9vBE,+BACE,uBAAA,CACA,SAAA,CACA,kEACE,CAEF,mBX8vBJ,CW1vBE,kBACE,gCAAA,CACA,eX4vBJ,CYpyBE,uBACE,oBAAA,CACA,kBAAA,CACA,gCAAA,CACA,eAAA,CACA,kBAAA,CACA,mBAAA,CACA,gEZuyBJ,CYjyBI,gCACE,gCAAA,CACA,2CAAA,CACA,uCZmyBN,CY/xBI,0DAEE,+BAAA,CACA,0CAAA,CACA,sCZgyBN,CY3xBE,sBACE,aAAA,CACA,eAAA,CACA,eAAA,CACA,mBAAA,CACA,uEACE,CAEF,0BZ2xBJ,CYxxBI,wDAEE,wEZyxBN,CYnxBI,+BACE,UZqxBN,Cax0BA,WACE,uBAAA,CAAA,eAAA,CACA,KAAA,CACA,OAAA,CACA,MAAA,CACA,SAAA,CACA,gCAAA,CACA,2CAAA,CAGA,0DACE,CAEF,2Cbu0BF,Cal0BE,aAlBF,WAmBI,Ybq0BF,CACF,Cal0BE,iCACE,gEACE,CAEF,mGbk0BJ,Ca1zBE,iCACE,2BAAA,CACA,kGb4zBJ,CapzBE,kBACE,YAAA,CACA,kBAAA,CACA,ebszBJ,CalzBE,mBACE,iBAAA,CACA,SAAA,CACA,oBAAA,CACA,YAAA,CACA,aAAA,CACA,kBAAA,CACA,qBAAA,CACA,cAAA,CACA,uBbozBJ,CajzBI,kDAEE,UbkzBN,Ca9yBI,uCACE,YbgzBN,Ca5yBI,2BACE,YAAA,CACA,ab8yBN,CKvsBI,wCQzGA,2BAMI,Yb8yBN,CACF,Ca3yBM,8DAEE,aAAA,CACA,YAAA,CACA,aAAA,CACA,iBb6yBR,CKtuBI,mCQlEA,iCAII,YbwyBN,CACF,CaryBM,wCACE,YbuyBR,CahyBQ,+CACE,oBbkyBV,CKjvBI,sCQ3CA,iCAII,Yb4xBN,CACF,CavxBE,kBACE,iBAAA,CACA,YAAA,CACA,cAAA,CACA,8DbyxBJ,CapxBI,oCACE,UAAA,CACA,6BAAA,CACA,SAAA,CACA,8DACE,CAEF,mBboxBN,CajxBM,8CACE,8BbmxBR,Ca7wBE,kBACE,WAAA,CACA,aAAA,CACA,kBAAA,CACA,gBAAA,CACA,eAAA,CACA,kBb+wBJ,Ca5wBI,0DACE,UAAA,CACA,8BAAA,CACA,SAAA,CACA,8DACE,CAEF,mBb4wBN,CazwBM,oEACE,6Bb2wBR,CavwBM,4EACE,SAAA,CACA,uBAAA,CACA,SAAA,CACA,8DACE,CAEF,mBbuwBR,CalwBI,uCACE,iBAAA,CACA,UAAA,CACA,WbowBN,Ca/vBE,oBACE,YAAA,CACA,aAAA,CACA,cAAA,CACA,kBAAA,CACA,+CbiwBJ,Ca5vBI,2CACE,Yb8vBN,Ca1vBI,+DACE,WAAA,CACA,SAAA,CACA,oCb4vBN,CarvBE,mBACE,YbuvBJ,CKtzBI,mCQ8DF,mBAKI,aAAA,CACA,aAAA,CACA,iBAAA,CACA,gBbuvBJ,CapvBI,6BACE,iBAAA,CACA,absvBN,CACF,CKl0BI,sCQ8DF,mBAmBI,kBbqvBJ,CalvBI,6BACE,mBbovBN,CACF,Cc5+BA,WACE,+BAAA,CACA,0Cd++BF,Cc5+BE,aALF,WAMI,Yd++BF,CACF,Cc5+BE,kBACE,aAAA,CACA,ad8+BJ,Cc1+BE,iBACE,YAAA,CACA,kBAAA,CACA,oBAAA,CACA,uBd4+BJ,CK91BI,mCSlJF,iBAQI,Sd4+BJ,CACF,Ccz+BI,8CAEE,Ud0+BN,Cct+BI,uBACE,Udw+BN,CKt1BI,wCSnJA,uBAKI,Sdw+BN,Ccr+BM,yCACE,Ydu+BR,CACF,Ccn+BM,iCACE,Wdq+BR,Ccl+BQ,qCACE,oBdo+BV,Cc99BI,uBACE,WAAA,CACA,gBdg+BN,CKx2BI,wCS1HA,uBAMI,Sdg+BN,CACF,Cc79BM,iCACE,UAAA,CACA,ed+9BR,Cc59BQ,qCACE,oBd89BV,Ccv9BE,kBACE,iBAAA,CACA,WAAA,CACA,6BAAA,CACA,cAAA,CACA,eAAA,CACA,kBdy9BJ,Ccr9BE,mBACE,YAAA,CACA,adu9BJ,Ccn9BE,sBACE,iBAAA,CACA,OAAA,CACA,MAAA,CACA,gBAAA,CACA,cAAA,CACA,gBAAA,CACA,Udq9BJ,Cch9BA,gBACE,gDdm9BF,Cch9BE,uBACE,YAAA,CACA,cAAA,CACA,6BAAA,CACA,adk9BJ,Cc98BE,kCACE,sCdg9BJ,Cc78BI,gFAEE,+Bd88BN,Ccx8BA,qBACE,UAAA,CACA,iBAAA,CACA,eAAA,CACA,wCAAA,CACA,gBd28BF,CKp7BI,mCS5BJ,qBASI,Ud28BF,CACF,Ccv8BE,gCACE,sCdy8BJ,Ccp8BA,kBACE,cAAA,CACA,qBdu8BF,CKj8BI,mCSRJ,kBAMI,edu8BF,CACF,Ccp8BE,wBACE,oBAAA,CACA,YAAA,CACA,aAAA,CACA,iBds8BJ,Ccn8BI,+BACE,edq8BN,Ccj8BI,4BACE,gBAAA,CACA,mBAAA,CACA,iBdm8BN,CetnCA,MACE,0MAAA,CACA,gMAAA,CACA,yNfynCF,CennCA,QACE,eAAA,CACA,efsnCF,CennCE,eACE,aAAA,CACA,eAAA,CACA,eAAA,CACA,eAAA,CACA,sBfqnCJ,CelnCI,+BACE,YfonCN,CejnCM,mCACE,UAAA,CACA,WfmnCR,Ce5mCQ,sFAEE,aAAA,CACA,YAAA,CACA,aAAA,CACA,iBf8mCV,CevmCE,cACE,QAAA,CACA,SAAA,CACA,efymCJ,CermCE,cACE,efumCJ,CepmCI,4BACE,efsmCN,CenmCM,sCACE,mBAAA,CACA,cfqmCR,Ce/lCE,cACE,aAAA,CACA,iBAAA,CACA,eAAA,CACA,sBAAA,CACA,cAAA,CACA,sBAAA,CACA,uBfimCJ,Ce9lCI,kCACE,uCfgmCN,Ce5lCI,oCACE,+Bf8lCN,Ce1lCI,oCACE,af4lCN,CexlCI,wCAEE,+BfylCN,CerlCI,0CACE,YfulCN,CeplCM,yDACE,aAAA,CACA,UAAA,CACA,WAAA,CACA,qCAAA,CAAA,6BAAA,CACA,6BfslCR,Ce3kCE,kEACE,YfglCJ,CKrhCI,wCUpDA,0CAEE,iBAAA,CACA,KAAA,CACA,OAAA,CACA,MAAA,CACA,SAAA,CACA,YAAA,CACA,qBAAA,CACA,WAAA,CACA,2Cf2kCJ,CepkCI,+DAEE,eAAA,CACA,efskCN,CelkCI,gCACE,iBAAA,CACA,aAAA,CACA,wBAAA,CACA,uCAAA,CACA,eAAA,CACA,kBAAA,CACA,kBAAA,CACA,qDAAA,CACA,cfokCN,CejkCM,8CACE,iBAAA,CACA,SAAA,CACA,UAAA,CACA,aAAA,CACA,YAAA,CACA,aAAA,CACA,YfmkCR,CehkCQ,wDACE,WAAA,CACA,SfkkCV,Ce9jCQ,oDACE,aAAA,CACA,UAAA,CACA,WAAA,CACA,6BAAA,CACA,2CAAA,CAAA,mCAAA,CACA,6BAAA,CAAA,qBAAA,CACA,yBAAA,CAAA,iBAAA,CACA,UfgkCV,Ce3jCM,8CACE,eAAA,CACA,2CAAA,CACA,gEACE,CACF,oCAAA,CAAA,gCAAA,CAAA,4BAAA,CACA,kBf4jCR,CezjCQ,2DACE,Yf2jCV,CetjCM,8CACE,gCAAA,CACA,2CfwjCR,CepjCM,yCACE,iBAAA,CACA,SAAA,CACA,UAAA,CACA,aAAA,CACA,YAAA,CACA,afsjCR,CenjCQ,mDACE,WAAA,CACA,SfqjCV,Ce/iCI,+BACE,MfijCN,Ce7iCI,+BACE,SAAA,CACA,4Df+iCN,Ce5iCM,qDACE,oBf8iCR,Ce3iCQ,+DACE,mBAAA,CACA,mBf6iCV,CexiCM,qDACE,+Bf0iCR,CeviCQ,sHAEE,+BfwiCV,CeliCI,+BACE,iBAAA,CACA,YAAA,CACA,mBfoiCN,CejiCM,6CACE,iBAAA,CACA,OAAA,CACA,WAAA,CACA,YAAA,CACA,aAAA,CACA,iBAAA,CACA,aAAA,CACA,gBfmiCR,CehiCQ,uDACE,UAAA,CACA,UfkiCV,Ce9hCQ,mDACE,aAAA,CACA,UAAA,CACA,WAAA,CACA,6BAAA,CACA,2CAAA,CAAA,mCAAA,CACA,6BAAA,CAAA,qBAAA,CACA,yBAAA,CAAA,iBAAA,CACA,UfgiCV,CevhCM,+CACE,mBfyhCR,CejhCM,kDACE,efmhCR,Ce/gCM,4CACE,eAAA,CACA,wBfihCR,Ce9gCQ,0DACE,mBfghCV,Ce7gCU,oEACE,oBAAA,CACA,cf+gCZ,Ce1gCQ,kEACE,iBf4gCV,CezgCU,4EACE,kBAAA,CACA,cf2gCZ,CetgCQ,0EACE,mBfwgCV,CergCU,oFACE,oBAAA,CACA,cfugCZ,CelgCQ,kFACE,mBfogCV,CejgCU,4FACE,oBAAA,CACA,cfmgCZ,Ce3/BE,mBACE,wBf6/BJ,Cez/BE,wBACE,YAAA,CACA,0BAAA,CACA,SAAA,CACA,oEf2/BJ,Cet/BI,kCACE,2Bfw/BN,Cen/BE,gCACE,uBAAA,CACA,SAAA,CACA,qEfq/BJ,Ceh/BI,8CAEE,kCAAA,CAAA,0Bfi/BN,CACF,CK9sCI,wCUqOA,0CACE,aAAA,CACA,oBf4+BJ,Cez+BI,oDACE,mBAAA,CACA,mBf2+BN,Cev+BI,yDACE,Ufy+BN,Cer+BI,wDACE,Yfu+BN,Cen+BI,kDACE,Yfq+BN,Ceh+BE,gBACE,aAAA,CACA,eAAA,CACA,gCAAA,CACA,iDfk+BJ,CACF,CKhxCM,6DUqTF,6CACE,aAAA,CACA,oBAAA,CACA,sBf89BJ,Ce39BI,uDACE,mBAAA,CACA,mBf69BN,Cez9BI,4DACE,Uf29BN,Cev9BI,2DACE,Yfy9BN,Cer9BI,qDACE,Yfu9BN,CACF,CK9wCI,mCUkUE,6CACE,uBf+8BN,Ce38BI,gDACE,Yf68BN,CACF,CKtxCI,sCUzJJ,QAweI,oDf28BF,Cer8BI,8CACE,uBfu8BN,Ce77BE,sEACE,Yfk8BJ,Ce97BE,sEAEE,af+7BJ,Ce37BE,6CACE,Yf67BJ,Cez7BE,uBACE,aAAA,CACA,ef27BJ,Cex7BI,kCACE,ef07BN,Cet7BI,qCACE,Yfw7BN,Cep7BI,+BACE,afs7BN,Cen7BM,8CACE,aAAA,CACA,SAAA,CACA,mBAAA,CACA,uBfq7BR,Cej7BM,2DACE,Sfm7BR,Ce76BE,cACE,WAAA,CACA,WAAA,CACA,YAAA,CACA,yBf+6BJ,Ce56BI,wBACE,UAAA,CACA,wBf86BN,Ce16BI,oBACE,oBAAA,CACA,UAAA,CACA,WAAA,CACA,qBAAA,CACA,6BAAA,CACA,2CAAA,CAAA,mCAAA,CACA,6BAAA,CAAA,qBAAA,CACA,yBAAA,CAAA,iBAAA,CACA,Uf46BN,Cex6BI,0JAEE,uBfy6BN,Ce35BI,+HACE,Yfi6BN,Ce95BM,oDACE,aAAA,CACA,Sfg6BR,Ce75BQ,kEACE,Yf+5BV,Ce35BQ,2EACE,aAAA,CACA,eAAA,CACA,mBAAA,CACA,uBf65BV,Cex5BM,0DACE,mBf05BR,Cep5BI,2CACE,afs5BN,Cej5BE,qDACE,aAAA,CACA,oBAAA,CACA,mDfm5BJ,Ceh5BI,oEACE,Yfk5BN,CACF,CgB5hDA,MACE,igBhB+hDF,CgBzhDA,WACE,iBhB4hDF,CKl4CI,mCW3JJ,WAKI,ehB4hDF,CACF,CgBzhDE,kBACE,YhB2hDJ,CgBvhDE,oBACE,SAAA,CACA,ShByhDJ,CK33CI,wCWhKF,oBAMI,iBAAA,CACA,SAAA,CACA,YAAA,CACA,UAAA,CACA,WAAA,CACA,eAAA,CACA,2CAAA,CACA,kBAAA,CACA,uBAAA,CACA,4CACE,CAEF,mBhBuhDJ,CgBphDI,8BACE,aAAA,CACA,ShBshDN,CgBlhDI,+DACE,SAAA,CACA,oChBohDN,CACF,CKr6CI,mCW7IF,oBAqCI,cAAA,CACA,KAAA,CACA,MAAA,CACA,OAAA,CACA,QAAA,CACA,gCAAA,CACA,cAAA,CACA,sDhBihDJ,CgB3gDI,8BACE,OAAA,CACA,ShB6gDN,CgBzgDI,+DACE,UAAA,CAKA,YAAA,CACA,SAAA,CACA,4ChBugDN,CACF,CKx6CI,wCWxFA,+DAII,mBhBggDN,CACF,CKt9CM,6DW/CF,+DASI,mBhBggDN,CACF,CK39CM,6DW/CF,+DAcI,mBhBggDN,CACF,CgB3/CE,kBAEE,kCAAA,CAAA,0BhB4/CJ,CK17CI,wCWpEF,kBAMI,cAAA,CACA,KAAA,CACA,SAAA,CACA,SAAA,CACA,UAAA,CACA,WAAA,CACA,wBAAA,CACA,SAAA,CACA,mGhB4/CJ,CgBr/CI,6DACE,MAAA,CACA,uBAAA,CACA,SAAA,CACA,oGhBu/CN,CgBh/CM,uEACE,OAAA,CACA,ShBk/CR,CgB7+CI,iCACE,UAAA,CACA,SAAA,CACA,yBhB++CN,CACF,CKz+CI,mCWjDF,kBAgDI,iBAAA,CACA,WAAA,CACA,aAAA,CACA,eAAA,CACA,8ChB8+CJ,CgB3+CI,4BACE,UhB6+CN,CACF,CK3gDM,6DWkCF,6DAII,ahBy+CN,CACF,CK1/CI,sCWYA,6DASI,ahBy+CN,CACF,CgBp+CE,iBACE,iBhBs+CJ,CKlgDI,mCW2BF,iBAKI,mBhBs+CJ,CACF,CgBl+CE,kBACE,iBAAA,CACA,SAAA,CACA,yBAAA,CACA,sBAAA,CACA,2CAAA,CACA,gCAAA,CACA,2DhBo+CJ,CgB99CI,4BACE,yBhBg+CN,CgB59CI,6CACE,6BAAA,CAAA,qBhB89CN,CgB/9CI,oCACE,0BAAA,CAAA,qBhB89CN,CgB/9CI,yCACE,yBAAA,CAAA,qBhB89CN,CgB/9CI,+BACE,qBhB89CN,CgB19CI,6CAEE,uChB29CN,CgB79CI,oCAEE,uChB29CN,CgB79CI,yCAEE,uChB29CN,CgB79CI,kEAEE,uChB29CN,CgBv9CI,6BACE,YhBy9CN,CgBr9CI,6DACE,oChBu9CN,CK5gDI,wCWkBF,kBAwCI,UAAA,CACA,aAAA,CACA,ehBs9CJ,CACF,CKtiDI,mCWqCF,kBA+CI,UAAA,CACA,aAAA,CACA,mBAAA,CACA,aAAA,CACA,eAAA,CACA,gCAAA,CACA,mBhBs9CJ,CgBn9CI,4BACE,oBhBq9CN,CgBj9CI,mCACE,gChBm9CN,CgB/8CI,6CACE,uChBi9CN,CgBl9CI,oCACE,uChBi9CN,CgBl9CI,yCACE,uChBi9CN,CgBl9CI,+BACE,uChBi9CN,CgB78CI,wBACE,oChB+8CN,CgB38CI,6DACE,gCAAA,CACA,kBAAA,CACA,2CAAA,CACA,6BhB68CN,CgB18CM,wFAEE,uChB28CR,CgB78CM,+EAEE,uChB28CR,CgB78CM,oFAEE,uChB28CR,CgB78CM,wJAEE,uChB28CR,CACF,CgBr8CE,iBACE,iBAAA,CACA,SAAA,CACA,YAAA,CACA,aAAA,CACA,cAAA,CACA,kChBu8CJ,CgBl8CI,uBACE,UhBo8CN,CgBh8CI,+BACE,SAAA,CACA,UhBk8CN,CgB/7CM,yCACE,WAAA,CACA,ShBi8CR,CgB97CQ,6CACE,oBhBg8CV,CKzkDI,wCW8HA,+BAiBI,SAAA,CACA,UhB87CN,CgB37CM,yCACE,WAAA,CACA,ShB67CR,CgBz7CM,+CACE,YhB27CR,CACF,CKzmDI,mCWiJA,+BAkCI,mBhB07CN,CgBv7CM,8CACE,YhBy7CR,CACF,CgBp7CI,6BACE,SAAA,CACA,WAAA,CACA,oBAAA,CACA,SAAA,CACA,+DACE,CAEF,mBhBo7CN,CgBj7CM,uCACE,UAAA,CACA,UhBm7CR,CK1mDI,wCW0KA,6BAkBI,SAAA,CACA,WhBk7CN,CgB/6CM,uCACE,UAAA,CACA,UhBi7CR,CACF,CgB76CM,gGAEE,kBAAA,CACA,SAAA,CACA,mBhB86CR,CgB36CQ,sGACE,UhB66CV,CgBt6CE,mBACE,iBAAA,CACA,SAAA,CACA,UAAA,CACA,eAAA,CACA,6BhBw6CJ,CKnoDI,wCWsNF,mBASI,UAAA,CACA,QhBw6CJ,CACF,CK5pDI,mCWyOF,mBAeI,UAAA,CACA,SAAA,CACA,sBhBw6CJ,CgBr6CI,8DV/YJ,kGAAA,CUkZM,ShBs6CN,CACF,CgBj6CE,uBACE,WAAA,CACA,eAAA,CACA,2CAAA,CAEA,kCAAA,CAAA,0BAAA,CAIA,kBhB+5CJ,CgB55CI,kEAZF,uBAaI,uBhB+5CJ,CACF,CKzsDM,6DW4RJ,uBAkBI,ahB+5CJ,CACF,CKxrDI,sCWsQF,uBAuBI,ahB+5CJ,CACF,CK7rDI,mCWsQF,uBA4BI,YAAA,CACA,oBAAA,CACA,+DhB+5CJ,CgB55CI,kEACE,ehB85CN,CgB15CI,6BACE,qDhB45CN,CgBx5CI,0CACE,WAAA,CACA,YhB05CN,CgBt5CI,gDACE,oDhBw5CN,CgBr5CM,sDACE,0ChBu5CR,CACF,CgBh5CA,kBACE,gCAAA,CACA,qBhBm5CF,CgBh5CE,wBACE,eAAA,CACA,uCAAA,CACA,gBAAA,CACA,kBAAA,CACA,qDAAA,CACA,uBhBk5CJ,CKjuDI,mCWyUF,wBAUI,mBhBk5CJ,CgB/4CI,kCACE,oBAAA,CACA,chBi5CN,CACF,CgB54CE,wBACE,QAAA,CACA,SAAA,CACA,ehB84CJ,CgB14CE,wBACE,2DhB44CJ,CgBz4CI,oCACE,ehB24CN,CgBt4CE,wBACE,aAAA,CACA,YAAA,CACA,gCAAA,CACA,uBhBw4CJ,CgBr4CI,4DAEE,uDhBs4CN,CgBl4CI,gDACE,mBhBo4CN,CgB/3CE,gCACE,aAAA,CACA,mBAAA,CACA,+BAAA,CACA,gBAAA,CACA,SAAA,CACA,cAAA,CACA,2CACE,CAEF,uBhB+3CJ,CK3wDI,mCWkYF,gCAcI,mBhB+3CJ,CgB53CI,0CACE,oBAAA,CACA,kBhB83CN,CACF,CgB13CI,4EAEE,+BAAA,CACA,uDhB23CN,CgBv3CI,gGAEE,YhBw3CN,CgBp3CI,oCACE,WhBs3CN,CgBj3CE,2BACE,iBAAA,CACA,eAAA,CACA,ehBm3CJ,CKnyDI,mCW6aF,2BAOI,mBhBm3CJ,CgBh3CI,qCACE,oBAAA,CACA,kBhBk3CN,CACF,CgB32CM,8DACE,eAAA,CACA,eAAA,CACA,eAAA,CACA,ehB62CR,CgBv2CE,wBACE,iBAAA,CACA,MAAA,CACA,YAAA,CACA,aAAA,CACA,YAAA,CACA,uChBy2CJ,CKvyDI,wCWwbF,wBAUI,YhBy2CJ,CACF,CgBt2CI,8BACE,oBAAA,CACA,UAAA,CACA,WAAA,CACA,6BAAA,CACA,+CAAA,CAAA,uCAAA,CACA,6BAAA,CAAA,qBAAA,CACA,yBAAA,CAAA,iBAAA,CACA,UhBw2CN,CgBp2CI,kCACE,OAAA,CACA,ShBs2CN,CgBn2CM,wCACE,oBhBq2CR,CgB/1CE,yBACE,aAAA,CACA,eAAA,CACA,gBAAA,CACA,ehBi2CJ,CgB71CE,0BACE,mBAAA,CACA,eAAA,CACA,aAAA,CACA,eAAA,CACA,uCAAA,CACA,gBAAA,CACA,eAAA,CACA,sBAAA,CACA,2BAAA,CACA,oBhB+1CJ,CK/0DI,wCWseF,0BAcI,eAAA,CACA,oBhB+1CJ,CACF,CK93DM,6DW+gBJ,0BAoBI,eAAA,CACA,oBhB+1CJ,CACF,CgB51CI,+BACE,yBAAA,CACA,wBhB81CN,CgBz1CE,yBACE,aAAA,CACA,gBAAA,CACA,iBhB21CJ,CgBv1CE,uBACE,+BAAA,CACA,wBhBy1CJ,CiB7hEA,WACE,iBAAA,CACA,SjBgiEF,CiB7hEE,kBACE,iBAAA,CACA,sBAAA,CACA,QAAA,CACA,YAAA,CACA,gBAAA,CACA,gCAAA,CACA,2CAAA,CACA,mBAAA,CACA,kEACE,CAEF,mCAAA,CACA,SAAA,CACA,oEjB6hEJ,CiBvhEI,6EAEE,gBAAA,CACA,+BAAA,CACA,SAAA,CACA,+EjBwhEN,CiBjhEI,wBACE,iBAAA,CACA,KAAA,CACA,QAAA,CACA,OAAA,CACA,QAAA,CACA,iBAAA,CACA,kBAAA,CACA,mCAAA,CAAA,oCAAA,CACA,YAAA,CACA,qCAAA,CAAA,8CAAA,CACA,UjBmhEN,CiB9gEE,iBACE,kBAAA,CACA,QAAA,CACA,SAAA,CACA,aAAA,CACA,eAAA,CACA,oBAAA,CACA,mBjBghEJ,CiB5gEE,iBACE,kBjB8gEJ,CiB1gEE,iBACE,aAAA,CACA,UAAA,CACA,oBAAA,CACA,kBAAA,CACA,cAAA,CACA,2CACE,CAEF,uBjB0gEJ,CiBvgEI,2BACE,mBAAA,CACA,mBjBygEN,CiBrgEI,8CAEE,qDjBsgEN,CkB/lEA,YACE,uBAAA,CAAA,eAAA,CACA,UAAA,CACA,aAAA,CACA,qBAAA,CACA,aAAA,CACA,gBlBkmEF,CkB/lEE,aATF,YAUI,YlBkmEF,CACF,CKx7DI,wCapKA,qBACE,cAAA,CACA,KAAA,CACA,aAAA,CACA,SAAA,CACA,aAAA,CACA,aAAA,CACA,WAAA,CACA,2CAAA,CACA,uBAAA,CACA,iElB+lEJ,CkB1lEI,+BACE,cAAA,CACA,SlB4lEN,CkBxlEI,mEZhBJ,sGAAA,CYmBM,6BlBylEN,CkBtlEM,6EACE,8BlBwlER,CkBnlEI,6CACE,iBAAA,CACA,KAAA,CACA,OAAA,CACA,QAAA,CACA,MAAA,CACA,QAAA,CACA,6BAAA,CAAA,yBAAA,CAAA,qBAAA,CACA,elBqlEN,CACF,CK9+DI,sCalKJ,YAiEI,QlBmlEF,CkBhlEE,mBACE,WlBklEJ,CACF,CkB9kEE,uBACE,YAAA,CACA,OlBglEJ,CK1/DI,mCaxFF,uBAMI,QlBglEJ,CkB7kEI,8BACE,WlB+kEN,CkB3kEI,qCACE,alB6kEN,CkBzkEI,+CACE,kBlB2kEN,CACF,CkBtkEE,wBACE,cAAA,CACA,eAAA,CAEA,kCAAA,CAAA,0BAAA,CAKA,oBAAA,CACA,+DlBmkEJ,CkBhkEI,8BACE,qDlBkkEN,CkB9jEI,2CACE,WAAA,CACA,YlBgkEN,CkB5jEI,iDACE,oDlB8jEN,CkB3jEM,uDACE,0ClB6jER,CKzgEI,wCa1CF,YACE,cAAA,CACA,KAAA,CACA,SAAA,CACA,OAAA,CACA,QAAA,CACA,gCAAA,CACA,SAAA,CACA,sDlBujEF,CkBjjEE,4CACE,UAAA,CACA,WAAA,CACA,SAAA,CACA,4ClBmjEJ,CACF,CDjtEA,0CACE,GACE,QCmtEF,CDhtEA,GACE,aCktEF,CACF,CDztEA,kCACE,GACE,QCmtEF,CDhtEA,GACE,aCktEF,CACF,CD9sEA,yCACE,GACE,0BAAA,CACA,SCgtEF,CD7sEA,IACE,SC+sEF,CD5sEA,GACE,uBAAA,CACA,SC8sEF,CACF,CD3tEA,iCACE,GACE,0BAAA,CACA,SCgtEF,CD7sEA,IACE,SC+sEF,CD5sEA,GACE,uBAAA,CACA,SC8sEF,CACF,CDtsEA,WACE,aAAA,CACA,gBAAA,CACA,eAAA,CACA,kBAAA,CAEA,kCAAA,CAAA,0BAAA,CACA,uBCusEF,CDpsEE,kCAEE,UCqsEJ,CDjsEE,iBACE,oBAAA,CACA,YAAA,CACA,aAAA,CACA,qBCmsEJ,CDhsEI,qBACE,gBAAA,CACA,iBCksEN,CD/rEM,+BACE,kBAAA,CACA,aCisER,CD5rEI,wCACE,iBAAA,CACA,iBC8rEN,CD3rEM,kDACE,kBAAA,CACA,aAAA,CACA,kBAAA,CACA,cC6rER,CDvrEE,uBACE,oBAAA,CACA,6BAAA,CACA,iBAAA,CACA,eAAA,CACA,eAAA,CACA,sBAAA,CACA,qBCyrEJ,CDrrEE,kBACE,QAAA,CACA,SAAA,CACA,eAAA,CACA,eAAA,CACA,gBAAA,CACA,oBAAA,CACA,WCurEJ,CDprEI,uCACE,qDAAA,CAAA,6CCsrEN,CDjrEE,iBACE,UCmrEJ,CDhrEI,2BACE,WCkrEN,CD9qEI,sCACE,oDAAA,CAAA,4CCgrEN,CD5qEI,wBACE,cAAA,CACA,WC8qEN,CD1qEI,oCACE,YC4qEN,CmB9yEA,SACE,UAAA,CACA,aAAA,CACA,gCAAA,CACA,2CAAA,CACA,gCnBizEF,CmB9yEE,aARF,SASI,YnBizEF,CACF,CKtoEI,wCcrLJ,SAcI,YnBizEF,CACF,CmB9yEE,+BACE,mBnBgzEJ,CmB5yEE,eAEE,kBAAA,CACA,SAAA,CACA,kBAAA,CACA,eAAA,CACA,enB8yEJ,CmB3yEI,yBACE,kBAAA,CACA,anB6yEN,CmBxyEE,eACE,oBAAA,CACA,aAAA,CACA,mBAAA,CACA,kBnB0yEJ,CmBryEE,eACE,aAAA,CACA,gBAAA,CACA,eAAA,CAEA,kCAAA,CAAA,0BAAA,CACA,UAAA,CACA,8DnBsyEJ,CmBjyEI,iEAGE,aAAA,CACA,SnBiyEN,CmB5xEM,2CACE,qBnB8xER,CmB/xEM,2CACE,qBnBiyER,CmBlyEM,2CACE,qBnBoyER,CmBryEM,2CACE,qBnBuyER,CmBxyEM,2CACE,oBnB0yER,CmB3yEM,2CACE,qBnB6yER,CmB9yEM,2CACE,qBnBgzER,CmBjzEM,2CACE,qBnBmzER,CmBpzEM,4CACE,qBnBszER,CmBvzEM,4CACE,oBnByzER,CmB1zEM,4CACE,qBnB4zER,CmB7zEM,4CACE,qBnB+zER,CmBh0EM,4CACE,qBnBk0ER,CmBn0EM,4CACE,qBnBq0ER,CmBt0EM,4CACE,oBnBw0ER,CmBl0EI,8CACE,yBAAA,CACA,SAAA,CACA,wCnBo0EN,CoBn5EA,MACE,iQpBs5EF,CoBh5EA,YACE,aAAA,CACA,aAAA,CACA,epBm5EF,CoBh5EE,qBACE,iBAAA,CAKA,UAAA,CACA,kBAAA,CACA,kBpB84EJ,CoB34EI,+BACE,mBAAA,CACA,iBpB64EN,CoBz4EI,2BACE,oBAAA,CACA,WAAA,CACA,YAAA,CACA,iBAAA,CACA,6BAAA,CACA,yCAAA,CAAA,iCAAA,CACA,6BAAA,CAAA,qBAAA,CACA,UpB24EN,CoBx4EM,qCACE,kBAAA,CACA,apB04ER,CoBp4EE,kBACE,iBAAA,CACA,UAAA,CACA,SAAA,CACA,iBAAA,CACA,kBAAA,CACA,SAAA,CACA,aAAA,CACA,gCAAA,CACA,oBAAA,CACA,2CAAA,CACA,mBAAA,CACA,kEACE,CAEF,SAAA,CACA,+CACE,CAEF,oCAAA,CAAA,gCAAA,CAAA,4BpBk4EJ,CoB/3EI,uDAEE,gBAAA,CACA,SAAA,CACA,uCpBg4EN,CoBz3EE,kBACE,kBpB23EJ,CoBv3EE,kBACE,aAAA,CACA,UAAA,CACA,oBAAA,CACA,kBAAA,CACA,kBAAA,CACA,cAAA,CACA,2CACE,CAEF,uBpBu3EJ,CoBp3EI,4BACE,mBAAA,CACA,mBpBs3EN,CoBl3EI,gDAEE,qDpBm3EN,CqB38EA,MAEI,2RAAA,CAAA,4MAAA,CAAA,sPAAA,CAAA,8xBAAA,CAAA,mQAAA,CAAA,ibAAA,CAAA,gMAAA,CAAA,kUAAA,CAAA,0VAAA,CAAA,0eAAA,CAAA,kUAAA,CAAA,gMrBo+EJ,CqBz9EE,4CACE,iBAAA,CACA,eAAA,CACA,eAAA,CACA,mCAAA,CACA,gBAAA,CACA,uBAAA,CACA,8CAAA,CACA,+BAAA,CACA,mBAAA,CACA,yErB49EJ,CqBv9EI,aAfF,4CAgBI,erB09EJ,CACF,CqBv9EI,gEACE,gCAAA,CACA,gBrBy9EN,CqBr9EI,gIACE,cAAA,CACA,iBrBu9EN,CqBn9EI,4FACE,iBrBq9EN,CqBj9EI,kFACE,erBm9EN,CqB/8EI,0FACE,YrBi9EN,CqB78EI,8EACE,mBrB+8EN,CqB18EE,kDACE,iBAAA,CACA,wBAAA,CACA,8BAAA,CACA,eAAA,CACA,oCAAA,CACA,+BrB48EJ,CqBz8EI,sEACE,wBAAA,CACA,8BAAA,CACA,gCAAA,CACA,gBrB28EN,CqBv8EI,kFACE,erBy8EN,CqBr8EI,gEACE,iBAAA,CACA,UAAA,CACA,UAAA,CACA,WAAA,CACA,wBCwIU,CDvIV,kDAAA,CAAA,0CAAA,CACA,6BAAA,CAAA,qBAAA,CACA,yBAAA,CAAA,iBAAA,CACA,UrBu8EN,CqBp8EM,oFACE,WAAA,CACA,SrBs8ER,CqBh8EI,4DACE,cAAA,CACA,eAAA,CACA,kBAAA,CACA,wBAAA,CACA,qBAAA,CACA,erBk8EN,CqB77EI,gGACE,YrB+7EN,CqBj7EE,sDACE,oBrBo7EJ,CqBh7EE,8DACE,oCAAA,CACA,oBrBm7EJ,CqBh7EI,4EACE,wBAdG,CAeH,kDAAA,CAAA,0CAAA,CACA,6BAAA,CAAA,qBAAA,CACA,yBAAA,CAAA,iBrBk7EN,CqBh8EE,gLACE,oBrBm8EJ,CqB/7EE,wMACE,mCAAA,CACA,oBrBk8EJ,CqB/7EI,kPACE,wBAdG,CAeH,sDAAA,CAAA,8CAAA,CACA,6BAAA,CAAA,qBAAA,CACA,yBAAA,CAAA,iBrBi8EN,CqB/8EE,4GACE,oBrBk9EJ,CqB98EE,4HACE,mCAAA,CACA,oBrBi9EJ,CqB98EI,wJACE,wBAdG,CAeH,kDAAA,CAAA,0CAAA,CACA,6BAAA,CAAA,qBAAA,CACA,yBAAA,CAAA,iBrBg9EN,CqB99EE,0KACE,oBrBi+EJ,CqB79EE,kMACE,mCAAA,CACA,oBrBg+EJ,CqB79EI,4OACE,wBAdG,CAeH,iDAAA,CAAA,yCAAA,CACA,6BAAA,CAAA,qBAAA,CACA,yBAAA,CAAA,iBrB+9EN,CqB7+EE,0KACE,oBrBg/EJ,CqB5+EE,kMACE,kCAAA,CACA,oBrB++EJ,CqB5+EI,4OACE,wBAdG,CAeH,qDAAA,CAAA,6CAAA,CACA,6BAAA,CAAA,qBAAA,CACA,yBAAA,CAAA,iBrB8+EN,CqB5/EE,wKACE,oBrB+/EJ,CqB3/EE,gMACE,oCAAA,CACA,oBrB8/EJ,CqB3/EI,0OACE,wBAdG,CAeH,sDAAA,CAAA,8CAAA,CACA,6BAAA,CAAA,qBAAA,CACA,yBAAA,CAAA,iBrB6/EN,CqB3gFE,wLACE,oBrB8gFJ,CqB1gFE,gNACE,mCAAA,CACA,oBrB6gFJ,CqB1gFI,0PACE,wBAdG,CAeH,qDAAA,CAAA,6CAAA,CACA,6BAAA,CAAA,qBAAA,CACA,yBAAA,CAAA,iBrB4gFN,CqB1hFE,8KACE,oBrB6hFJ,CqBzhFE,sMACE,mCAAA,CACA,oBrB4hFJ,CqBzhFI,gPACE,wBAdG,CAeH,qDAAA,CAAA,6CAAA,CACA,6BAAA,CAAA,qBAAA,CACA,yBAAA,CAAA,iBrB2hFN,CqBziFE,kHACE,oBrB4iFJ,CqBxiFE,kIACE,mCAAA,CACA,oBrB2iFJ,CqBxiFI,8JACE,wBAdG,CAeH,oDAAA,CAAA,4CAAA,CACA,6BAAA,CAAA,qBAAA,CACA,yBAAA,CAAA,iBrB0iFN,CqBxjFE,oDACE,oBrB2jFJ,CqBvjFE,4DACE,kCAAA,CACA,oBrB0jFJ,CqBvjFI,0EACE,wBAdG,CAeH,iDAAA,CAAA,yCAAA,CACA,6BAAA,CAAA,qBAAA,CACA,yBAAA,CAAA,iBrByjFN,CqBvkFE,4DACE,oBrB0kFJ,CqBtkFE,oEACE,oCAAA,CACA,oBrBykFJ,CqBtkFI,kFACE,wBAdG,CAeH,qDAAA,CAAA,6CAAA,CACA,6BAAA,CAAA,qBAAA,CACA,yBAAA,CAAA,iBrBwkFN,CqBtlFE,8GACE,oBrBylFJ,CqBrlFE,8HACE,kCAAA,CACA,oBrBwlFJ,CqBrlFI,0JACE,wBAdG,CAeH,mDAAA,CAAA,2CAAA,CACA,6BAAA,CAAA,qBAAA,CACA,yBAAA,CAAA,iBrBulFN,CuB/vFA,MACE,wMvBkwFF,CuBzvFE,kCACE,mBAAA,CACA,kBAAA,CACA,kBvB4vFJ,CuBxvFE,+BACE,mBAAA,CACA,mBAAA,CACA,mBvB0vFJ,CuBtvFE,sBACE,uCAAA,CACA,gBvBwvFJ,CuBrvFI,yBACE,avBuvFN,CuBnvFI,yBACE,sBvBqvFN,CuBlvFM,gCACE,gCvBovFR,CuBhvFM,mGAEE,uBAAA,CACA,SvBivFR,CuB7uFM,sCACE,YvB+uFR,CuBzuFE,8BACE,oBAAA,CACA,+BAAA,CAEA,WAAA,CACA,0BAAA,CACA,4BAAA,CACA,SAAA,CACA,4DvB0uFJ,CuBpuFI,aAdF,8BAeI,+BAAA,CACA,uBAAA,CACA,SvBuuFJ,CACF,CuBpuFI,wCACE,6BvBsuFN,CuBluFI,oCACE,+BvBouFN,CuBhuFI,qCACE,oBAAA,CACA,WAAA,CACA,YAAA,CACA,6BAAA,CACA,2CAAA,CAAA,mCAAA,CACA,6BAAA,CAAA,qBAAA,CACA,yBAAA,CAAA,iBAAA,CACA,UvBkuFN,CuB5tFQ,mDACE,oBvB8tFV,CwBl0FE,wBACE,oBAAA,CACA,iBAAA,CACA,yCAAA,CACA,SAAA,CACA,mCxBq0FJ,CwBh0FI,aAVF,wBAWI,YxBm0FJ,CACF,CwBh0FI,kCACE,kBAAA,CACA,axBk0FN,CwB7zFE,6FAGE,SAAA,CACA,mCxB+zFJ,CwBzzFE,4FAGE,+BxB2zFJ,CwBpzFE,oBACE,wBxBszFJ,CwBlzFE,kEAGE,mBxBozFJ,CwBjzFI,uFACE,aAAA,CACA,kBAAA,CACA,kBAAA,CACA,UxBqzFN,CwBhzFE,sBACE,mBxBkzFJ,CwB/yFI,6BACE,aAAA,CACA,mBAAA,CACA,mBAAA,CACA,UxBizFN,CwB5yFE,4CAEE,mBxB8yFJ,CwB3yFI,0DACE,aAAA,CACA,kBAAA,CACA,kBAAA,CACA,UxB8yFN,CyBl4FE,2BACE,azBq4FJ,CKptFI,wCoBlLF,2BAKI,ezBq4FJ,CACF,CyBl4FI,6BACE,yBAAA,CAAA,sBAAA,CAAA,iBAAA,CAEA,yBAAA,CACA,eAAA,CACA,iBzBm4FN,C0Bj5FE,0EAGE,kCAAA,CAAA,0B1Bo5FJ,C0Bh5FE,uBACE,4C1Bk5FJ,C0B94FE,uBACE,4C1Bg5FJ,C0B54FE,4BACE,qC1B84FJ,C0B34FI,mCACE,a1B64FN,C0Bz4FI,kCACE,a1B24FN,C0Bt4FE,0BACE,aAAA,CACA,YAAA,CACA,mBAAA,CACA,kBAAA,CACA,aAAA,CACA,e1Bw4FJ,C0Br4FI,uCACE,e1Bu4FN,C0Bn4FI,sCACE,kB1Bq4FN,C2Bv7FA,MACE,8L3B07FF,C2Bj7FE,oBAGE,iBAAA,CACA,aAAA,CACA,gB3Bk7FJ,C2B/6FI,wCACE,uB3Bi7FN,C2B76FI,gCACE,gBAAA,CACA,e3B+6FN,C2Bz6FM,wCACE,mB3B26FR,C2Bt6FI,0BACE,aAAA,CACA,U3Bw6FN,C2Bn6FE,oBAGE,aAAA,CACA,eAAA,CACA,+BAAA,CACA,4BAAA,CACA,6BAAA,CACA,c3Bm6FJ,C2Bh6FI,8BACE,iC3Bk6FN,C2B95FI,wCACE,YAAA,CACA,uC3Bg6FN,C2B55FI,0BACE,iBAAA,CACA,SAAA,CACA,WAAA,CACA,UAAA,CACA,WAAA,CACA,6BAAA,CACA,yCAAA,CAAA,iCAAA,CACA,6BAAA,CAAA,qBAAA,CACA,yBAAA,CAAA,iBAAA,CACA,sBAAA,CACA,yBAAA,CACA,U3B85FN,C2B35FM,oCACE,UAAA,CACA,UAAA,CACA,wB3B65FR,C2Bx5FI,wEAEE,Y3By5FN,C4Bj/FE,+DAGE,mBAAA,CACA,cAAA,CACA,uB5Bo/FJ,C4Bj/FI,2EACE,aAAA,CACA,eAAA,CACA,iB5Bq/FN,C6BlgGE,6BAEE,sC7BqgGJ,C6BlgGE,cACE,yC7BogGJ,C6BjgGE,sIASE,oC7BmgGJ,C6BhgGE,2EAKE,qC7BkgGJ,C6B//FE,wGAOE,oC7BigGJ,C6B9/FE,yFAME,qC7BggGJ,C6B7/FE,6BAEE,kC7B+/FJ,C6B5/FE,6CAGE,sC7B8/FJ,C6B3/FE,4DAIE,sC7B6/FJ,C6B1/FE,4DAIE,qC7B4/FJ,C6Bz/FE,yFAME,qC7B2/FJ,C6Bx/FE,2EAKE,sC7B0/FJ,C6Bv/FE,wHAQE,qC7By/FJ,C6Bt/FE,8BAEE,gBAAA,CACA,gBAAA,CACA,mB7Bw/FJ,C6Br/FE,eACE,4C7Bu/FJ,C6Bp/FE,eACE,4C7Bs/FJ,C6Bl/FE,gBACE,aAAA,CACA,wBAAA,CACA,wBAAA,CACA,wC7Bo/FJ,C6Bh/FE,iCACE,uBAAA,CAAA,eAAA,CACA,oBAAA,CACA,UAAA,CACA,2BAAA,CACA,2BAAA,CACA,2BAAA,CACA,uCAAA,CACA,wCAAA,CACA,+DAAA,CACA,0BAAA,CACA,wBAAA,CAAA,qBAAA,CAAA,oBAAA,CAAA,gB7Bk/FJ,C6Bz+FA,gBACE,iBAAA,CACA,e7B4+FF,C6Bx+FE,yCAEE,aAAA,CACA,S7B0+FJ,C6Br+FE,mBACE,Y7Bu+FJ,C6Bl+FE,oBACE,Q7Bo+FJ,C6B/9FE,yBAEE,oDAAA,CACA,eAAA,CACA,wCAAA,CACA,wBAAA,CAAA,qBAAA,CAAA,oBAAA,CAAA,gB7Bi+FJ,C6B79FE,2BACE,2BAAA,CACA,+D7B+9FJ,C6B59FI,+BACE,uCAAA,CACA,gB7B89FN,C6Bz9FE,sBACE,MAAA,CACA,e7B29FJ,C6Bj9FE,4BACE,YAAA,CACA,aAAA,CACA,mB7Bo9FJ,C6Bj9FI,iCACE,e7Bm9FN,CKl/FI,wCwBuCA,uBACE,iB7B88FJ,C6B38FI,4BACE,eAAA,CACA,e7B68FN,C6Bz8FI,4BACE,e7B28FN,C6Bt8FE,4BACE,iBAAA,CACA,e7Bw8FJ,C6Br8FI,iCACE,eAAA,CACA,e7Bu8FN,CACF,C8BrrGI,yDAEE,iBAAA,CACA,QAAA,CACA,aAAA,CACA,+BAAA,CACA,8B9BwrGN,C8BprGI,uBACE,cAAA,CACA,uC9BsrGN,C8BjoGQ,iHACE,kBAAA,CACA,W9B2oGV,C8B7oGQ,6HACE,kBAAA,CACA,W9BupGV,C8BzpGQ,6HACE,kBAAA,CACA,W9BmqGV,C8BrqGQ,oHACE,kBAAA,CACA,W9B+qGV,C8BjrGQ,0HACE,kBAAA,CACA,W9B2rGV,C8B7rGQ,uHACE,kBAAA,CACA,W9BusGV,C8BzsGQ,uHACE,kBAAA,CACA,W9BmtGV,C8BrtGQ,6HACE,kBAAA,CACA,W9B+tGV,C8BjuGQ,yCACE,kBAAA,CACA,W9BmuGV,C8BruGQ,yCACE,kBAAA,CACA,W9BuuGV,C8BzuGQ,0CACE,kBAAA,CACA,W9B2uGV,C8B7uGQ,uCACE,kBAAA,CACA,W9B+uGV,C8BjvGQ,wCACE,kBAAA,CACA,W9BmvGV,C8BrvGQ,sCACE,kBAAA,CACA,W9BuvGV,C8BzvGQ,wCACE,kBAAA,CACA,W9B2vGV,C8B7vGQ,oCACE,kBAAA,CACA,W9B+vGV,C8BjwGQ,2CACE,kBAAA,CACA,W9BmwGV,C8BrwGQ,qCACE,kBAAA,CACA,W9BuwGV,C8BzwGQ,oCACE,kBAAA,CACA,W9B2wGV,C8B7wGQ,kCACE,kBAAA,CACA,W9B+wGV,C8BjxGQ,qCACE,kBAAA,CACA,W9BmxGV,C8BrxGQ,mCACE,kBAAA,CACA,W9BuxGV,C8BzxGQ,qCACE,kBAAA,CACA,W9B2xGV,C8B7xGQ,wCACE,kBAAA,CACA,W9B+xGV,C8BjyGQ,sCACE,kBAAA,CACA,W9BmyGV,C8BryGQ,2CACE,kBAAA,CACA,W9BuyGV,C8B3xGQ,iCACE,iBAAA,CACA,W9B6xGV,C8B/xGQ,uCACE,iBAAA,CACA,W9BiyGV,C8BnyGQ,mCACE,iBAAA,CACA,W9BqyGV,C+Bz3GE,4BACE,YAAA,CACA,QAAA,CACA,UAAA,CACA,yD/B43GJ,C+Bz3GI,aAPF,4BAQI,aAAA,CACA,O/B43GJ,CACF,C+Bx3GI,wJAGE,Q/B03GN,C+Bv3GM,uKACE,wBAAA,CACA,yB/B23GR,C+Bt3GI,wCACE,Q/Bw3GN,C+Bn3GE,wBACE,iBAAA,CACA,YAAA,CACA,cAAA,CACA,YAAA,CACA,mB/Bq3GJ,C+B/2GI,8BACE,iBAAA,CACA,OAAA,CACA,QAAA,CACA,S/Bi3GN,C+B92GM,4CACE,+BAAA,CACA,sC/Bg3GR,C+B72GQ,4DACE,a/B+2GV,C+B12GM,0CACE,kB/B42GR,C+Bx2GM,wDACE,YAAA,CACA,uC/B02GR,C+Br2GI,8BACE,SAAA,CACA,UAAA,CACA,+BAAA,CACA,uCAAA,CACA,eAAA,CACA,gBAAA,CACA,qCAAA,CACA,cAAA,CACA,qB/Bu2GN,C+Bp2GM,oCACE,+B/Bs2GR,CgCh8GA,MACE,mVAAA,CAEA,4VhCo8GF,CgC17GE,4BACE,iBAAA,CACA,oBhC67GJ,CgCz7GI,4CACE,iBAAA,CACA,SAAA,CACA,ShC27GN,CgCx7GM,sDACE,UAAA,CACA,ShC07GR,CgCp7GE,+CACE,UAAA,CACA,ShCs7GJ,CgCl7GE,wCACE,iBAAA,CACA,SAAA,CACA,WAAA,CACA,YAAA,CACA,aAAA,CACA,qDAAA,CACA,0CAAA,CAAA,kCAAA,CACA,6BAAA,CAAA,qBAAA,CACA,yBAAA,CAAA,iBAAA,CACA,UhCo7GJ,CgCj7GI,kDACE,YAAA,CACA,ShCm7GN,CgC96GE,gEACE,wBV8Va,CU7Vb,mDAAA,CAAA,2ChCg7GJ,CK30GI,mC4B5JA,oBACE,UAAA,CACA,aAAA,CACA,YAAA,CACA,kBAAA,CACA,mBjC2+GJ,CiCj+GI,sDACE,WAAA,CACA,cAAA,CACA,iBjCw+GN,CiCr+GM,kCACE,UAAA,CACA,kBAAA,CACA,ajCu+GR,CACF", "file": "src/assets/stylesheets/main.scss", "sourcesContent": ["////\n/// Copyright (c) 2016-2021 <PERSON> <<EMAIL>>\n///\n/// Permission is hereby granted, free of charge, to any person obtaining a\n/// copy of this software and associated documentation files (the \"Software\"),\n/// to deal in the Software without restriction, including without limitation\n/// the rights to use, copy, modify, merge, publish, distribute, sublicense,\n/// and/or sell copies of the Software, and to permit persons to whom the\n/// Software is furnished to do so, subject to the following conditions:\n///\n/// The above copyright notice and this permission notice shall be included in\n/// all copies or substantial portions of the Software.\n///\n/// THE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR\n/// IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,\n/// FITNESS FOR A PARTICULAR PURPOSE AND NON-INFRINGEMENT. IN NO EVENT SHALL\n/// THE AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER\n/// LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING\n/// FROM, OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER\n/// DEALINGS\n////\n\n// ----------------------------------------------------------------------------\n// Keyframes\n// ----------------------------------------------------------------------------\n\n// Show repository facts\n@keyframes md-source__facts--done {\n  0% {\n    height: 0;\n  }\n\n  100% {\n    height: px2rem(13px);\n  }\n}\n\n// Show repository fact\n@keyframes md-source__fact--done {\n  0% {\n    transform: translateY(100%);\n    opacity: 0;\n  }\n\n  50% {\n    opacity: 0;\n  }\n\n  100% {\n    transform: translateY(0%);\n    opacity: 1;\n  }\n}\n\n// ----------------------------------------------------------------------------\n// Rules\n// ----------------------------------------------------------------------------\n\n// Repository information\n.md-source {\n  display: block;\n  font-size: px2rem(13px);\n  line-height: 1.2;\n  white-space: nowrap;\n  // Hack: promote to own layer to reduce jitter\n  backface-visibility: hidden;\n  transition: opacity 250ms;\n\n  // Repository information on focus/hover\n  &:focus,\n  &:hover {\n    opacity: 0.7;\n  }\n\n  // Repository icon\n  &__icon {\n    display: inline-block;\n    width: px2rem(48px);\n    height: px2rem(48px);\n    vertical-align: middle;\n\n    // Align with margin only (as opposed to normal button alignment)\n    svg {\n      margin-top: px2rem(12px);\n      margin-left: px2rem(12px);\n\n      // Adjust for right-to-left languages\n      [dir=\"rtl\"] & {\n        margin-right: px2rem(12px);\n        margin-left: initial;\n      }\n    }\n\n    // Adjust spacing if icon is present\n    + .md-source__repository {\n      margin-left: px2rem(-40px);\n      padding-left: px2rem(40px);\n\n      // Adjust for right-to-left languages\n      [dir=\"rtl\"] & {\n        margin-right: px2rem(-40px);\n        margin-left: initial;\n        padding-right: px2rem(40px);\n        padding-left: initial;\n      }\n    }\n  }\n\n  // Repository name\n  &__repository {\n    display: inline-block;\n    max-width: calc(100% - #{px2rem(24px)});\n    margin-left: px2rem(12px);\n    overflow: hidden;\n    font-weight: 700;\n    text-overflow: ellipsis;\n    vertical-align: middle;\n  }\n\n  // Repository facts\n  &__facts {\n    margin: 0;\n    padding: 0;\n    overflow: hidden;\n    font-weight: 700;\n    font-size: px2rem(11px);\n    list-style-type: none;\n    opacity: 0.75;\n\n    // Show after the data was loaded\n    [data-md-state=\"done\"] & {\n      animation: md-source__facts--done 250ms ease-in;\n    }\n  }\n\n  // Repository fact\n  &__fact {\n    float: left;\n\n    // Adjust for right-to-left languages\n    [dir=\"rtl\"] & {\n      float: right;\n    }\n\n    // Show after the data was loaded\n    [data-md-state=\"done\"] & {\n      animation: md-source__fact--done 400ms ease-out;\n    }\n\n    // Middle dot before fact\n    &::before {\n      margin: 0 px2rem(2px);\n      content: \"\\00B7\";\n    }\n\n    // Remove middle dot on first fact\n    &:first-child::before {\n      display: none;\n    }\n  }\n}\n", "@charset \"UTF-8\";\nhtml {\n  box-sizing: border-box;\n  text-size-adjust: none;\n}\n\n*,\n*::before,\n*::after {\n  box-sizing: inherit;\n}\n\nbody {\n  margin: 0;\n}\n\na,\nbutton,\nlabel,\ninput {\n  -webkit-tap-highlight-color: transparent;\n}\n\na {\n  color: inherit;\n  text-decoration: none;\n}\n\nhr {\n  display: block;\n  box-sizing: content-box;\n  height: 0.05rem;\n  padding: 0;\n  overflow: visible;\n  border: 0;\n}\n\nsmall {\n  font-size: 80%;\n}\n\nsub,\nsup {\n  line-height: 1em;\n}\n\nimg {\n  border-style: none;\n}\n\ntable {\n  border-collapse: separate;\n  border-spacing: 0;\n}\n\ntd,\nth {\n  font-weight: 400;\n  vertical-align: top;\n}\n\nbutton {\n  margin: 0;\n  padding: 0;\n  font-size: inherit;\n  background: transparent;\n  border: 0;\n}\n\ninput {\n  border: 0;\n  outline: none;\n}\n\n:root {\n  --md-default-fg-color: hsla(0, 0%, 0%, 0.87);\n  --md-default-fg-color--light: hsla(0, 0%, 0%, 0.54);\n  --md-default-fg-color--lighter: hsla(0, 0%, 0%, 0.32);\n  --md-default-fg-color--lightest: hsla(0, 0%, 0%, 0.07);\n  --md-default-bg-color: hsla(0, 0%, 100%, 1);\n  --md-default-bg-color--light: hsla(0, 0%, 100%, 0.7);\n  --md-default-bg-color--lighter: hsla(0, 0%, 100%, 0.3);\n  --md-default-bg-color--lightest: hsla(0, 0%, 100%, 0.12);\n  --md-primary-fg-color: hsla(231, 48%, 48%, 1);\n  --md-primary-fg-color--light: hsla(231, 44%, 56%, 1);\n  --md-primary-fg-color--dark: hsla(232, 54%, 41%, 1);\n  --md-primary-bg-color: hsla(0, 0%, 100%, 1);\n  --md-primary-bg-color--light: hsla(0, 0%, 100%, 0.7);\n  --md-accent-fg-color: hsla(231, 99%, 66%, 1);\n  --md-accent-fg-color--transparent: hsla(231, 99%, 66%, 0.1);\n  --md-accent-bg-color: hsla(0, 0%, 100%, 1);\n  --md-accent-bg-color--light: hsla(0, 0%, 100%, 0.7);\n}\n:root > * {\n  --md-code-fg-color: hsla(200, 18%, 26%, 1);\n  --md-code-bg-color: hsla(0, 0%, 96%, 1);\n  --md-code-hl-color: hsla(60, 100%, 50%, 0.5);\n  --md-code-hl-number-color: hsla(0, 67%, 50%, 1);\n  --md-code-hl-special-color: hsla(340, 83%, 47%, 1);\n  --md-code-hl-function-color: hsla(291, 45%, 50%, 1);\n  --md-code-hl-constant-color: hsla(250, 63%, 60%, 1);\n  --md-code-hl-keyword-color: hsla(219, 54%, 51%, 1);\n  --md-code-hl-string-color: hsla(150, 63%, 30%, 1);\n  --md-code-hl-name-color: var(--md-code-fg-color);\n  --md-code-hl-operator-color: var(--md-default-fg-color--light);\n  --md-code-hl-punctuation-color: var(--md-default-fg-color--light);\n  --md-code-hl-comment-color: var(--md-default-fg-color--light);\n  --md-code-hl-generic-color: var(--md-default-fg-color--light);\n  --md-code-hl-variable-color: var(--md-default-fg-color--light);\n  --md-typeset-color: var(--md-default-fg-color);\n  --md-typeset-a-color: var(--md-primary-fg-color);\n  --md-typeset-mark-color: hsla(60, 100%, 50%, 0.5);\n  --md-typeset-del-color: hsla(6, 90%, 60%, 0.15);\n  --md-typeset-ins-color: hsla(150, 90%, 44%, 0.15);\n  --md-typeset-kbd-color: hsla(0, 0%, 98%, 1);\n  --md-typeset-kbd-accent-color: hsla(0, 100%, 100%, 1);\n  --md-typeset-kbd-border-color: hsla(0, 0%, 72%, 1);\n  --md-admonition-fg-color: var(--md-default-fg-color);\n  --md-admonition-bg-color: var(--md-default-bg-color);\n  --md-footer-fg-color: hsla(0, 0%, 100%, 1);\n  --md-footer-fg-color--light: hsla(0, 0%, 100%, 0.7);\n  --md-footer-fg-color--lighter: hsla(0, 0%, 100%, 0.3);\n  --md-footer-bg-color: hsla(0, 0%, 0%, 0.87);\n  --md-footer-bg-color--dark: hsla(0, 0%, 0%, 0.32);\n}\n\n.md-icon svg {\n  display: block;\n  width: 1.2rem;\n  height: 1.2rem;\n  fill: currentColor;\n}\n\nbody {\n  -webkit-font-smoothing: antialiased;\n  -moz-osx-font-smoothing: grayscale;\n}\n\nbody,\ninput {\n  color: var(--md-typeset-color);\n  font-feature-settings: \"kern\", \"liga\";\n  font-family: var(--md-text-font-family, _), -apple-system, BlinkMacSystemFont, Helvetica, Arial, sans-serif;\n}\n\ncode,\npre,\nkbd {\n  color: var(--md-typeset-color);\n  font-feature-settings: \"kern\";\n  font-family: var(--md-code-font-family, _), SFMono-Regular, Consolas, Menlo, monospace;\n}\n\n:root {\n  --md-typeset-table--ascending: svg-load(\"material/arrow-down.svg\");\n  --md-typeset-table--descending: svg-load(\"material/arrow-up.svg\");\n}\n\n.md-typeset {\n  font-size: 0.8rem;\n  line-height: 1.6;\n  color-adjust: exact;\n}\n@media print {\n  .md-typeset {\n    font-size: 0.68rem;\n  }\n}\n.md-typeset ul,\n.md-typeset ol,\n.md-typeset dl,\n.md-typeset figure,\n.md-typeset blockquote,\n.md-typeset pre {\n  display: flow-root;\n  margin: 1em 0;\n}\n.md-typeset h1 {\n  margin: 0 0 1.25em;\n  color: var(--md-default-fg-color--light);\n  font-weight: 300;\n  font-size: 2em;\n  line-height: 1.3;\n  letter-spacing: -0.01em;\n}\n.md-typeset h2 {\n  margin: 1.6em 0 0.64em;\n  font-weight: 300;\n  font-size: 1.5625em;\n  line-height: 1.4;\n  letter-spacing: -0.01em;\n}\n.md-typeset h3 {\n  margin: 1.6em 0 0.8em;\n  font-weight: 400;\n  font-size: 1.25em;\n  line-height: 1.5;\n  letter-spacing: -0.01em;\n}\n.md-typeset h2 + h3 {\n  margin-top: 0.8em;\n}\n.md-typeset h4 {\n  margin: 1em 0;\n  font-weight: 700;\n  letter-spacing: -0.01em;\n}\n.md-typeset h5,\n.md-typeset h6 {\n  margin: 1.25em 0;\n  color: var(--md-default-fg-color--light);\n  font-weight: 700;\n  font-size: 0.8em;\n  letter-spacing: -0.01em;\n}\n.md-typeset h5 {\n  text-transform: uppercase;\n}\n.md-typeset hr {\n  display: flow-root;\n  margin: 1.5em 0;\n  border-bottom: 0.05rem solid var(--md-default-fg-color--lightest);\n}\n.md-typeset a {\n  color: var(--md-typeset-a-color);\n  word-break: break-word;\n}\n.md-typeset a, .md-typeset a::before {\n  transition: color 125ms;\n}\n.md-typeset a:focus, .md-typeset a:hover {\n  color: var(--md-accent-fg-color);\n}\n.md-typeset code,\n.md-typeset pre,\n.md-typeset kbd {\n  color: var(--md-code-fg-color);\n  direction: ltr;\n}\n@media print {\n  .md-typeset code,\n.md-typeset pre,\n.md-typeset kbd {\n    white-space: pre-wrap;\n  }\n}\n.md-typeset code {\n  padding: 0 0.2941176471em;\n  font-size: 0.85em;\n  word-break: break-word;\n  background-color: var(--md-code-bg-color);\n  border-radius: 0.1rem;\n  box-decoration-break: clone;\n}\n.md-typeset code:not(.focus-visible) {\n  outline: none;\n  -webkit-tap-highlight-color: transparent;\n}\n.md-typeset h1 code,\n.md-typeset h2 code,\n.md-typeset h3 code,\n.md-typeset h4 code,\n.md-typeset h5 code,\n.md-typeset h6 code {\n  margin: initial;\n  padding: initial;\n  background-color: transparent;\n  box-shadow: none;\n}\n.md-typeset a code {\n  color: currentColor;\n}\n.md-typeset pre {\n  position: relative;\n  line-height: 1.4;\n}\n.md-typeset pre > code {\n  display: block;\n  margin: 0;\n  padding: 0.7720588235em 1.1764705882em;\n  overflow: auto;\n  word-break: normal;\n  box-shadow: none;\n  box-decoration-break: slice;\n  touch-action: auto;\n  scrollbar-width: thin;\n  scrollbar-color: var(--md-default-fg-color--lighter) transparent;\n}\n.md-typeset pre > code:hover {\n  scrollbar-color: var(--md-accent-fg-color) transparent;\n}\n.md-typeset pre > code::-webkit-scrollbar {\n  width: 0.2rem;\n  height: 0.2rem;\n}\n.md-typeset pre > code::-webkit-scrollbar-thumb {\n  background-color: var(--md-default-fg-color--lighter);\n}\n.md-typeset pre > code::-webkit-scrollbar-thumb:hover {\n  background-color: var(--md-accent-fg-color);\n}\n@media screen and (max-width: 44.9375em) {\n  .md-typeset > pre {\n    margin: 1em -0.8rem;\n  }\n  .md-typeset > pre code {\n    border-radius: 0;\n  }\n}\n.md-typeset kbd {\n  display: inline-block;\n  padding: 0 0.6666666667em;\n  color: var(--md-default-fg-color);\n  font-size: 0.75em;\n  vertical-align: text-top;\n  word-break: break-word;\n  background-color: var(--md-typeset-kbd-color);\n  border-radius: 0.1rem;\n  box-shadow: 0 0.1rem 0 0.05rem var(--md-typeset-kbd-border-color), 0 0.1rem 0 var(--md-typeset-kbd-border-color), 0 -0.1rem 0.2rem var(--md-typeset-kbd-accent-color) inset;\n}\n.md-typeset mark {\n  color: inherit;\n  word-break: break-word;\n  background-color: var(--md-typeset-mark-color);\n  box-decoration-break: clone;\n}\n.md-typeset abbr {\n  text-decoration: none;\n  border-bottom: 0.05rem dotted var(--md-default-fg-color--light);\n  cursor: help;\n}\n@media (hover: none) {\n  .md-typeset abbr {\n    position: relative;\n  }\n  .md-typeset abbr[title]:focus::after, .md-typeset abbr[title]:hover::after {\n    box-shadow: 0 2px 2px 0 rgba(0, 0, 0, 0.14), 0 1px 5px 0 rgba(0, 0, 0, 0.12), 0 3px 1px -2px rgba(0, 0, 0, 0.2);\n    position: absolute;\n    left: 0;\n    display: inline-block;\n    width: auto;\n    min-width: max-content;\n    max-width: 80%;\n    margin-top: 2em;\n    padding: 0.2rem 0.3rem;\n    color: var(--md-default-bg-color);\n    font-size: 0.7rem;\n    background-color: var(--md-default-fg-color);\n    border-radius: 0.1rem;\n    content: attr(title);\n  }\n}\n.md-typeset small {\n  opacity: 0.75;\n}\n.md-typeset sup,\n.md-typeset sub {\n  margin-left: 0.078125em;\n}\n[dir=rtl] .md-typeset sup,\n[dir=rtl] .md-typeset sub {\n  margin-right: 0.078125em;\n  margin-left: initial;\n}\n.md-typeset blockquote {\n  padding-left: 0.6rem;\n  color: var(--md-default-fg-color--light);\n  border-left: 0.2rem solid var(--md-default-fg-color--lighter);\n}\n[dir=rtl] .md-typeset blockquote {\n  padding-right: 0.6rem;\n  padding-left: initial;\n  border-right: 0.2rem solid var(--md-default-fg-color--lighter);\n  border-left: initial;\n}\n.md-typeset ul {\n  list-style-type: disc;\n}\n.md-typeset ul,\n.md-typeset ol {\n  margin-left: 0.625em;\n  padding: 0;\n}\n[dir=rtl] .md-typeset ul,\n[dir=rtl] .md-typeset ol {\n  margin-right: 0.625em;\n  margin-left: initial;\n}\n.md-typeset ul ol,\n.md-typeset ol ol {\n  list-style-type: lower-alpha;\n}\n.md-typeset ul ol ol,\n.md-typeset ol ol ol {\n  list-style-type: lower-roman;\n}\n.md-typeset ul li,\n.md-typeset ol li {\n  margin-bottom: 0.5em;\n  margin-left: 1.25em;\n}\n[dir=rtl] .md-typeset ul li,\n[dir=rtl] .md-typeset ol li {\n  margin-right: 1.25em;\n  margin-left: initial;\n}\n.md-typeset ul li p,\n.md-typeset ul li blockquote,\n.md-typeset ol li p,\n.md-typeset ol li blockquote {\n  margin: 0.5em 0;\n}\n.md-typeset ul li:last-child,\n.md-typeset ol li:last-child {\n  margin-bottom: 0;\n}\n.md-typeset ul li ul,\n.md-typeset ul li ol,\n.md-typeset ol li ul,\n.md-typeset ol li ol {\n  margin: 0.5em 0 0.5em 0.625em;\n}\n[dir=rtl] .md-typeset ul li ul,\n[dir=rtl] .md-typeset ul li ol,\n[dir=rtl] .md-typeset ol li ul,\n[dir=rtl] .md-typeset ol li ol {\n  margin-right: 0.625em;\n  margin-left: initial;\n}\n.md-typeset dd {\n  margin: 1em 0 1.5em 1.875em;\n}\n[dir=rtl] .md-typeset dd {\n  margin-right: 1.875em;\n  margin-left: initial;\n}\n.md-typeset img,\n.md-typeset svg {\n  max-width: 100%;\n  height: auto;\n}\n.md-typeset img[align=left],\n.md-typeset svg[align=left] {\n  margin: 1em;\n  margin-left: 0;\n}\n.md-typeset img[align=right],\n.md-typeset svg[align=right] {\n  margin: 1em;\n  margin-right: 0;\n}\n.md-typeset img[align]:only-child,\n.md-typeset svg[align]:only-child {\n  margin-top: 0;\n}\n.md-typeset figure {\n  width: fit-content;\n  max-width: 100%;\n  margin: 0 auto;\n  text-align: center;\n}\n.md-typeset figure img {\n  display: block;\n}\n.md-typeset figcaption {\n  max-width: 24rem;\n  margin: 1em auto 2em;\n  font-style: italic;\n}\n.md-typeset iframe {\n  max-width: 100%;\n}\n.md-typeset table:not([class]) {\n  display: inline-block;\n  max-width: 100%;\n  overflow: auto;\n  font-size: 0.64rem;\n  background-color: var(--md-default-bg-color);\n  border-radius: 0.1rem;\n  box-shadow: 0 0.2rem 0.5rem rgba(0, 0, 0, 0.05), 0 0 0.05rem rgba(0, 0, 0, 0.1);\n  touch-action: auto;\n}\n@media print {\n  .md-typeset table:not([class]) {\n    display: table;\n  }\n}\n.md-typeset table:not([class]) + * {\n  margin-top: 1.5em;\n}\n.md-typeset table:not([class]) th > *:first-child,\n.md-typeset table:not([class]) td > *:first-child {\n  margin-top: 0;\n}\n.md-typeset table:not([class]) th > *:last-child,\n.md-typeset table:not([class]) td > *:last-child {\n  margin-bottom: 0;\n}\n.md-typeset table:not([class]) th:not([align]),\n.md-typeset table:not([class]) td:not([align]) {\n  text-align: left;\n}\n[dir=rtl] .md-typeset table:not([class]) th:not([align]),\n[dir=rtl] .md-typeset table:not([class]) td:not([align]) {\n  text-align: right;\n}\n.md-typeset table:not([class]) th {\n  min-width: 5rem;\n  padding: 0.9375em 1.25em;\n  color: var(--md-default-bg-color);\n  vertical-align: top;\n  background-color: var(--md-default-fg-color--light);\n}\n.md-typeset table:not([class]) th a {\n  color: inherit;\n}\n.md-typeset table:not([class]) td {\n  padding: 0.9375em 1.25em;\n  vertical-align: top;\n  border-top: 0.05rem solid var(--md-default-fg-color--lightest);\n}\n.md-typeset table:not([class]) tr {\n  transition: background-color 125ms;\n}\n.md-typeset table:not([class]) tr:hover {\n  background-color: rgba(0, 0, 0, 0.035);\n  box-shadow: 0 0.05rem 0 var(--md-default-bg-color) inset;\n}\n.md-typeset table:not([class]) tr:first-child td {\n  border-top: 0;\n}\n.md-typeset table:not([class]) a {\n  word-break: normal;\n}\n.md-typeset table th[role=columnheader] {\n  cursor: pointer;\n}\n.md-typeset table th[role=columnheader]::after {\n  display: inline-block;\n  width: 1.2em;\n  height: 1.2em;\n  margin-left: 0.5em;\n  vertical-align: sub;\n  mask-repeat: no-repeat;\n  mask-size: contain;\n  content: \"\";\n}\n.md-typeset table th[role=columnheader][aria-sort=ascending]::after {\n  background-color: currentColor;\n  mask-image: var(--md-typeset-table--ascending);\n}\n.md-typeset table th[role=columnheader][aria-sort=descending]::after {\n  background-color: currentColor;\n  mask-image: var(--md-typeset-table--descending);\n}\n.md-typeset__scrollwrap {\n  margin: 1em -0.8rem;\n  overflow-x: auto;\n  touch-action: auto;\n}\n.md-typeset__table {\n  display: inline-block;\n  margin-bottom: 0.5em;\n  padding: 0 0.8rem;\n}\n@media print {\n  .md-typeset__table {\n    display: block;\n  }\n}\nhtml .md-typeset__table table {\n  display: table;\n  width: 100%;\n  margin: 0;\n  overflow: hidden;\n}\n\nhtml {\n  height: 100%;\n  overflow-x: hidden;\n  font-size: 125%;\n}\n@media screen and (min-width: 100em) {\n  html {\n    font-size: 137.5%;\n  }\n}\n@media screen and (min-width: 125em) {\n  html {\n    font-size: 150%;\n  }\n}\n\nbody {\n  position: relative;\n  display: flex;\n  flex-direction: column;\n  width: 100%;\n  min-height: 100%;\n  font-size: 0.5rem;\n  background-color: var(--md-default-bg-color);\n}\n@media print {\n  body {\n    display: block;\n  }\n}\n@media screen and (max-width: 59.9375em) {\n  body[data-md-state=lock] {\n    position: fixed;\n  }\n}\n\n.md-grid {\n  max-width: 61rem;\n  margin-right: auto;\n  margin-left: auto;\n}\n\n.md-container {\n  display: flex;\n  flex-direction: column;\n  flex-grow: 1;\n}\n@media print {\n  .md-container {\n    display: block;\n  }\n}\n\n.md-main {\n  flex-grow: 1;\n}\n.md-main__inner {\n  display: flex;\n  height: 100%;\n  margin-top: 1.5rem;\n}\n\n.md-ellipsis {\n  overflow: hidden;\n  white-space: nowrap;\n  text-overflow: ellipsis;\n}\n\n.md-toggle {\n  display: none;\n}\n\n.md-skip {\n  position: fixed;\n  z-index: -1;\n  margin: 0.5rem;\n  padding: 0.3rem 0.5rem;\n  color: var(--md-default-bg-color);\n  font-size: 0.64rem;\n  background-color: var(--md-default-fg-color);\n  border-radius: 0.1rem;\n  transform: translateY(0.4rem);\n  opacity: 0;\n}\n.md-skip:focus {\n  z-index: 10;\n  transform: translateY(0);\n  opacity: 1;\n  transition: transform 250ms cubic-bezier(0.4, 0, 0.2, 1), opacity 175ms 75ms;\n}\n\n@page {\n  margin: 25mm;\n}\n.md-announce {\n  overflow: auto;\n  background-color: var(--md-footer-bg-color);\n}\n@media print {\n  .md-announce {\n    display: none;\n  }\n}\n.md-announce__inner {\n  margin: 0.6rem auto;\n  padding: 0 0.8rem;\n  color: var(--md-footer-fg-color);\n  font-size: 0.7rem;\n}\n\n:root {\n  --md-clipboard-icon: svg-load(\"material/content-copy.svg\");\n}\n\n.md-clipboard {\n  position: absolute;\n  top: 0.5em;\n  right: 0.5em;\n  z-index: 1;\n  width: 1.5em;\n  height: 1.5em;\n  color: var(--md-default-fg-color--lightest);\n  border-radius: 0.1rem;\n  cursor: pointer;\n  transition: color 250ms;\n}\n@media print {\n  .md-clipboard {\n    display: none;\n  }\n}\n.md-clipboard:not(.focus-visible) {\n  outline: none;\n  -webkit-tap-highlight-color: transparent;\n}\n:hover > .md-clipboard {\n  color: var(--md-default-fg-color--light);\n}\n.md-clipboard:focus, .md-clipboard:hover {\n  color: var(--md-accent-fg-color);\n}\n.md-clipboard::after {\n  display: block;\n  width: 1.125em;\n  height: 1.125em;\n  margin: 0 auto;\n  background-color: currentColor;\n  mask-image: var(--md-clipboard-icon);\n  mask-repeat: no-repeat;\n  mask-size: contain;\n  content: \"\";\n}\n.md-clipboard--inline {\n  cursor: pointer;\n}\n.md-clipboard--inline code {\n  transition: color 250ms, background-color 250ms;\n}\n.md-clipboard--inline:focus code, .md-clipboard--inline:hover code {\n  color: var(--md-accent-fg-color);\n  background-color: var(--md-accent-fg-color--transparent);\n}\n\n.md-content {\n  flex-grow: 1;\n  overflow: hidden;\n  scroll-padding-top: 51.2rem;\n}\n.md-content__inner {\n  margin: 0 0.8rem 1.2rem;\n  padding-top: 0.6rem;\n}\n@media screen and (min-width: 76.25em) {\n  .md-sidebar--primary:not([hidden]) ~ .md-content > .md-content__inner {\n    margin-left: 1.2rem;\n  }\n  [dir=rtl] .md-sidebar--primary:not([hidden]) ~ .md-content > .md-content__inner {\n    margin-right: 1.2rem;\n    margin-left: 0.8rem;\n  }\n  .md-sidebar--secondary:not([hidden]) ~ .md-content > .md-content__inner {\n    margin-right: 1.2rem;\n  }\n  [dir=rtl] .md-sidebar--secondary:not([hidden]) ~ .md-content > .md-content__inner {\n    margin-right: 0.8rem;\n    margin-left: 1.2rem;\n  }\n}\n.md-content__inner::before {\n  display: block;\n  height: 0.4rem;\n  content: \"\";\n}\n.md-content__inner > :last-child {\n  margin-bottom: 0;\n}\n.md-content__button {\n  float: right;\n  margin: 0.4rem 0;\n  margin-left: 0.4rem;\n  padding: 0;\n}\n@media print {\n  .md-content__button {\n    display: none;\n  }\n}\n[dir=rtl] .md-content__button {\n  float: left;\n  margin-right: 0.4rem;\n  margin-left: initial;\n}\n[dir=rtl] .md-content__button svg {\n  transform: scaleX(-1);\n}\n.md-typeset .md-content__button {\n  color: var(--md-default-fg-color--lighter);\n}\n.md-content__button svg {\n  display: inline;\n  vertical-align: top;\n}\n\n.md-dialog {\n  box-shadow: 0 2px 2px 0 rgba(0, 0, 0, 0.14), 0 1px 5px 0 rgba(0, 0, 0, 0.12), 0 3px 1px -2px rgba(0, 0, 0, 0.2);\n  position: fixed;\n  right: 0.8rem;\n  bottom: 0.8rem;\n  left: initial;\n  z-index: 2;\n  min-width: 11.1rem;\n  padding: 0.4rem 0.6rem;\n  background-color: var(--md-default-fg-color);\n  border-radius: 0.1rem;\n  transform: translateY(100%);\n  opacity: 0;\n  transition: transform 0ms 400ms, opacity 400ms;\n  pointer-events: none;\n}\n@media print {\n  .md-dialog {\n    display: none;\n  }\n}\n[dir=rtl] .md-dialog {\n  right: initial;\n  left: 0.8rem;\n}\n.md-dialog[data-md-state=open] {\n  transform: translateY(0);\n  opacity: 1;\n  transition: transform 400ms cubic-bezier(0.075, 0.85, 0.175, 1), opacity 400ms;\n  pointer-events: initial;\n}\n.md-dialog__inner {\n  color: var(--md-default-bg-color);\n  font-size: 0.7rem;\n}\n\n.md-typeset .md-button {\n  display: inline-block;\n  padding: 0.625em 2em;\n  color: var(--md-primary-fg-color);\n  font-weight: 700;\n  border: 0.1rem solid currentColor;\n  border-radius: 0.1rem;\n  transition: color 125ms, background-color 125ms, border-color 125ms;\n}\n.md-typeset .md-button--primary {\n  color: var(--md-primary-bg-color);\n  background-color: var(--md-primary-fg-color);\n  border-color: var(--md-primary-fg-color);\n}\n.md-typeset .md-button:focus, .md-typeset .md-button:hover {\n  color: var(--md-accent-bg-color);\n  background-color: var(--md-accent-fg-color);\n  border-color: var(--md-accent-fg-color);\n}\n.md-typeset .md-input {\n  height: 1.8rem;\n  padding: 0 0.6rem;\n  font-size: 0.8rem;\n  border-radius: 0.1rem;\n  box-shadow: 0 0.2rem 0.5rem rgba(0, 0, 0, 0.1), 0 0.025rem 0.05rem rgba(0, 0, 0, 0.1);\n  transition: box-shadow 250ms;\n}\n.md-typeset .md-input:focus, .md-typeset .md-input:hover {\n  box-shadow: 0 0.4rem 1rem rgba(0, 0, 0, 0.15), 0 0.025rem 0.05rem rgba(0, 0, 0, 0.15);\n}\n.md-typeset .md-input--stretch {\n  width: 100%;\n}\n\n.md-header {\n  position: sticky;\n  top: 0;\n  right: 0;\n  left: 0;\n  z-index: 2;\n  color: var(--md-primary-bg-color);\n  background-color: var(--md-primary-fg-color);\n  box-shadow: 0 0 0.2rem rgba(0, 0, 0, 0), 0 0.2rem 0.4rem rgba(0, 0, 0, 0);\n  transition: color 250ms, background-color 250ms;\n}\n@media print {\n  .md-header {\n    display: none;\n  }\n}\n.md-header[data-md-state=shadow] {\n  box-shadow: 0 0 0.2rem rgba(0, 0, 0, 0.1), 0 0.2rem 0.4rem rgba(0, 0, 0, 0.2);\n  transition: transform 250ms cubic-bezier(0.1, 0.7, 0.1, 1), color 250ms, background-color 250ms, box-shadow 250ms;\n}\n.md-header[data-md-state=hidden] {\n  transform: translateY(-100%);\n  transition: transform 250ms cubic-bezier(0.8, 0, 0.6, 1), color 250ms, background-color 250ms, box-shadow 250ms;\n}\n.md-header__inner {\n  display: flex;\n  align-items: center;\n  padding: 0 0.2rem;\n}\n.md-header__button {\n  position: relative;\n  z-index: 1;\n  display: inline-block;\n  margin: 0.2rem;\n  padding: 0.4rem;\n  color: currentColor;\n  vertical-align: middle;\n  cursor: pointer;\n  transition: opacity 250ms;\n}\n.md-header__button:focus, .md-header__button:hover {\n  opacity: 0.7;\n}\n.md-header__button:not(.focus-visible) {\n  outline: none;\n}\n.md-header__button.md-logo {\n  margin: 0.2rem;\n  padding: 0.4rem;\n}\n@media screen and (max-width: 76.1875em) {\n  .md-header__button.md-logo {\n    display: none;\n  }\n}\n.md-header__button.md-logo img,\n.md-header__button.md-logo svg {\n  display: block;\n  width: 1.2rem;\n  height: 1.2rem;\n  fill: currentColor;\n}\n@media screen and (min-width: 60em) {\n  .md-header__button[for=__search] {\n    display: none;\n  }\n}\n.no-js .md-header__button[for=__search] {\n  display: none;\n}\n[dir=rtl] .md-header__button[for=__search] svg {\n  transform: scaleX(-1);\n}\n@media screen and (min-width: 76.25em) {\n  .md-header__button[for=__drawer] {\n    display: none;\n  }\n}\n.md-header__topic {\n  position: absolute;\n  display: flex;\n  max-width: 100%;\n  transition: transform 400ms cubic-bezier(0.1, 0.7, 0.1, 1), opacity 150ms;\n}\n.md-header__topic + .md-header__topic {\n  z-index: -1;\n  transform: translateX(1.25rem);\n  opacity: 0;\n  transition: transform 400ms cubic-bezier(1, 0.7, 0.1, 0.1), opacity 150ms;\n  pointer-events: none;\n}\n[dir=rtl] .md-header__topic + .md-header__topic {\n  transform: translateX(-1.25rem);\n}\n.md-header__title {\n  flex-grow: 1;\n  height: 2.4rem;\n  margin-right: 0.4rem;\n  margin-left: 1rem;\n  font-size: 0.9rem;\n  line-height: 2.4rem;\n}\n.md-header__title[data-md-state=active] .md-header__topic {\n  z-index: -1;\n  transform: translateX(-1.25rem);\n  opacity: 0;\n  transition: transform 400ms cubic-bezier(1, 0.7, 0.1, 0.1), opacity 150ms;\n  pointer-events: none;\n}\n[dir=rtl] .md-header__title[data-md-state=active] .md-header__topic {\n  transform: translateX(1.25rem);\n}\n.md-header__title[data-md-state=active] .md-header__topic + .md-header__topic {\n  z-index: 0;\n  transform: translateX(0);\n  opacity: 1;\n  transition: transform 400ms cubic-bezier(0.1, 0.7, 0.1, 1), opacity 150ms;\n  pointer-events: initial;\n}\n.md-header__title > .md-header__ellipsis {\n  position: relative;\n  width: 100%;\n  height: 100%;\n}\n.md-header__options {\n  display: flex;\n  flex-shrink: 0;\n  max-width: 100%;\n  white-space: nowrap;\n  transition: max-width 0ms 250ms, opacity 250ms 250ms;\n}\n.md-header__options > [data-md-state=hidden] {\n  display: none;\n}\n[data-md-toggle=search]:checked ~ .md-header .md-header__options {\n  max-width: 0;\n  opacity: 0;\n  transition: max-width 0ms, opacity 0ms;\n}\n.md-header__source {\n  display: none;\n}\n@media screen and (min-width: 60em) {\n  .md-header__source {\n    display: block;\n    width: 11.7rem;\n    max-width: 11.7rem;\n    margin-left: 1rem;\n  }\n  [dir=rtl] .md-header__source {\n    margin-right: 1rem;\n    margin-left: initial;\n  }\n}\n@media screen and (min-width: 76.25em) {\n  .md-header__source {\n    margin-left: 1.4rem;\n  }\n  [dir=rtl] .md-header__source {\n    margin-right: 1.4rem;\n  }\n}\n\n.md-footer {\n  color: var(--md-footer-fg-color);\n  background-color: var(--md-footer-bg-color);\n}\n@media print {\n  .md-footer {\n    display: none;\n  }\n}\n.md-footer__inner {\n  padding: 0.2rem;\n  overflow: auto;\n}\n.md-footer__link {\n  display: flex;\n  padding-top: 1.4rem;\n  padding-bottom: 0.4rem;\n  transition: opacity 250ms;\n}\n@media screen and (min-width: 45em) {\n  .md-footer__link {\n    width: 50%;\n  }\n}\n.md-footer__link:focus, .md-footer__link:hover {\n  opacity: 0.7;\n}\n.md-footer__link--prev {\n  float: left;\n}\n@media screen and (max-width: 44.9375em) {\n  .md-footer__link--prev {\n    width: 25%;\n  }\n  .md-footer__link--prev .md-footer__title {\n    display: none;\n  }\n}\n[dir=rtl] .md-footer__link--prev {\n  float: right;\n}\n[dir=rtl] .md-footer__link--prev svg {\n  transform: scaleX(-1);\n}\n.md-footer__link--next {\n  float: right;\n  text-align: right;\n}\n@media screen and (max-width: 44.9375em) {\n  .md-footer__link--next {\n    width: 75%;\n  }\n}\n[dir=rtl] .md-footer__link--next {\n  float: left;\n  text-align: left;\n}\n[dir=rtl] .md-footer__link--next svg {\n  transform: scaleX(-1);\n}\n.md-footer__title {\n  position: relative;\n  flex-grow: 1;\n  max-width: calc(100% - 2.4rem);\n  padding: 0 1rem;\n  font-size: 0.9rem;\n  line-height: 2.4rem;\n}\n.md-footer__button {\n  margin: 0.2rem;\n  padding: 0.4rem;\n}\n.md-footer__direction {\n  position: absolute;\n  right: 0;\n  left: 0;\n  margin-top: -1rem;\n  padding: 0 1rem;\n  font-size: 0.64rem;\n  opacity: 0.7;\n}\n\n.md-footer-meta {\n  background-color: var(--md-footer-bg-color--dark);\n}\n.md-footer-meta__inner {\n  display: flex;\n  flex-wrap: wrap;\n  justify-content: space-between;\n  padding: 0.2rem;\n}\nhtml .md-footer-meta.md-typeset a {\n  color: var(--md-footer-fg-color--light);\n}\nhtml .md-footer-meta.md-typeset a:focus, html .md-footer-meta.md-typeset a:hover {\n  color: var(--md-footer-fg-color);\n}\n\n.md-footer-copyright {\n  width: 100%;\n  margin: auto 0.6rem;\n  padding: 0.4rem 0;\n  color: var(--md-footer-fg-color--lighter);\n  font-size: 0.64rem;\n}\n@media screen and (min-width: 45em) {\n  .md-footer-copyright {\n    width: auto;\n  }\n}\n.md-footer-copyright__highlight {\n  color: var(--md-footer-fg-color--light);\n}\n\n.md-footer-social {\n  margin: 0 0.4rem;\n  padding: 0.2rem 0 0.6rem;\n}\n@media screen and (min-width: 45em) {\n  .md-footer-social {\n    padding: 0.6rem 0;\n  }\n}\n.md-footer-social__link {\n  display: inline-block;\n  width: 1.6rem;\n  height: 1.6rem;\n  text-align: center;\n}\n.md-footer-social__link::before {\n  line-height: 1.9;\n}\n.md-footer-social__link svg {\n  max-height: 0.8rem;\n  vertical-align: -25%;\n  fill: currentColor;\n}\n\n:root {\n  --md-nav-icon--prev: svg-load(\"material/arrow-left.svg\");\n  --md-nav-icon--next: svg-load(\"material/chevron-right.svg\");\n  --md-toc-icon: svg-load(\"material/table-of-contents.svg\");\n}\n\n.md-nav {\n  font-size: 0.7rem;\n  line-height: 1.3;\n}\n.md-nav__title {\n  display: block;\n  padding: 0 0.6rem;\n  overflow: hidden;\n  font-weight: 700;\n  text-overflow: ellipsis;\n}\n.md-nav__title .md-nav__button {\n  display: none;\n}\n.md-nav__title .md-nav__button img {\n  width: auto;\n  height: 100%;\n}\n.md-nav__title .md-nav__button.md-logo img,\n.md-nav__title .md-nav__button.md-logo svg {\n  display: block;\n  width: 2.4rem;\n  height: 2.4rem;\n  fill: currentColor;\n}\n.md-nav__list {\n  margin: 0;\n  padding: 0;\n  list-style: none;\n}\n.md-nav__item {\n  padding: 0 0.6rem;\n}\n.md-nav__item .md-nav__item {\n  padding-right: 0;\n}\n[dir=rtl] .md-nav__item .md-nav__item {\n  padding-right: 0.6rem;\n  padding-left: 0;\n}\n.md-nav__link {\n  display: block;\n  margin-top: 0.625em;\n  overflow: hidden;\n  text-overflow: ellipsis;\n  cursor: pointer;\n  transition: color 125ms;\n  scroll-snap-align: start;\n}\n.md-nav__link[data-md-state=blur] {\n  color: var(--md-default-fg-color--light);\n}\n.md-nav__item .md-nav__link--active {\n  color: var(--md-typeset-a-color);\n}\n.md-nav__item--nested > .md-nav__link {\n  color: inherit;\n}\n.md-nav__link:focus, .md-nav__link:hover {\n  color: var(--md-accent-fg-color);\n}\n.md-nav--primary .md-nav__link[for=__toc] {\n  display: none;\n}\n.md-nav--primary .md-nav__link[for=__toc] .md-icon::after {\n  display: block;\n  width: 100%;\n  height: 100%;\n  mask-image: var(--md-toc-icon);\n  background-color: currentColor;\n}\n.md-nav--primary .md-nav__link[for=__toc] ~ .md-nav {\n  display: none;\n}\n.md-nav__source {\n  display: none;\n}\n@media screen and (max-width: 76.1875em) {\n  .md-nav--primary, .md-nav--primary .md-nav {\n    position: absolute;\n    top: 0;\n    right: 0;\n    left: 0;\n    z-index: 1;\n    display: flex;\n    flex-direction: column;\n    height: 100%;\n    background-color: var(--md-default-bg-color);\n  }\n  .md-nav--primary .md-nav__title,\n.md-nav--primary .md-nav__item {\n    font-size: 0.8rem;\n    line-height: 1.5;\n  }\n  .md-nav--primary .md-nav__title {\n    position: relative;\n    height: 5.6rem;\n    padding: 3rem 0.8rem 0.2rem;\n    color: var(--md-default-fg-color--light);\n    font-weight: 400;\n    line-height: 2.4rem;\n    white-space: nowrap;\n    background-color: var(--md-default-fg-color--lightest);\n    cursor: pointer;\n  }\n  .md-nav--primary .md-nav__title .md-nav__icon {\n    position: absolute;\n    top: 0.4rem;\n    left: 0.4rem;\n    display: block;\n    width: 1.2rem;\n    height: 1.2rem;\n    margin: 0.2rem;\n  }\n  [dir=rtl] .md-nav--primary .md-nav__title .md-nav__icon {\n    right: 0.4rem;\n    left: initial;\n  }\n  .md-nav--primary .md-nav__title .md-nav__icon::after {\n    display: block;\n    width: 100%;\n    height: 100%;\n    background-color: currentColor;\n    mask-image: var(--md-nav-icon--prev);\n    mask-repeat: no-repeat;\n    mask-size: contain;\n    content: \"\";\n  }\n  .md-nav--primary .md-nav__title ~ .md-nav__list {\n    overflow-y: auto;\n    background-color: var(--md-default-bg-color);\n    box-shadow: 0 0.05rem 0 var(--md-default-fg-color--lightest) inset;\n    scroll-snap-type: y mandatory;\n    touch-action: pan-y;\n  }\n  .md-nav--primary .md-nav__title ~ .md-nav__list > :first-child {\n    border-top: 0;\n  }\n  .md-nav--primary .md-nav__title[for=__drawer] {\n    color: var(--md-primary-bg-color);\n    background-color: var(--md-primary-fg-color);\n  }\n  .md-nav--primary .md-nav__title .md-logo {\n    position: absolute;\n    top: 0.2rem;\n    left: 0.2rem;\n    display: block;\n    margin: 0.2rem;\n    padding: 0.4rem;\n  }\n  [dir=rtl] .md-nav--primary .md-nav__title .md-logo {\n    right: 0.2rem;\n    left: initial;\n  }\n  .md-nav--primary .md-nav__list {\n    flex: 1;\n  }\n  .md-nav--primary .md-nav__item {\n    padding: 0;\n    border-top: 0.05rem solid var(--md-default-fg-color--lightest);\n  }\n  .md-nav--primary .md-nav__item--nested > .md-nav__link {\n    padding-right: 2.4rem;\n  }\n  [dir=rtl] .md-nav--primary .md-nav__item--nested > .md-nav__link {\n    padding-right: 0.8rem;\n    padding-left: 2.4rem;\n  }\n  .md-nav--primary .md-nav__item--active > .md-nav__link {\n    color: var(--md-typeset-a-color);\n  }\n  .md-nav--primary .md-nav__item--active > .md-nav__link:focus, .md-nav--primary .md-nav__item--active > .md-nav__link:hover {\n    color: var(--md-accent-fg-color);\n  }\n  .md-nav--primary .md-nav__link {\n    position: relative;\n    margin-top: 0;\n    padding: 0.6rem 0.8rem;\n  }\n  .md-nav--primary .md-nav__link .md-nav__icon {\n    position: absolute;\n    top: 50%;\n    right: 0.6rem;\n    width: 1.2rem;\n    height: 1.2rem;\n    margin-top: -0.6rem;\n    color: inherit;\n    font-size: 1.2rem;\n  }\n  [dir=rtl] .md-nav--primary .md-nav__link .md-nav__icon {\n    right: initial;\n    left: 0.6rem;\n  }\n  .md-nav--primary .md-nav__link .md-nav__icon::after {\n    display: block;\n    width: 100%;\n    height: 100%;\n    background-color: currentColor;\n    mask-image: var(--md-nav-icon--next);\n    mask-repeat: no-repeat;\n    mask-size: contain;\n    content: \"\";\n  }\n  [dir=rtl] .md-nav--primary .md-nav__icon::after {\n    transform: scale(-1);\n  }\n  .md-nav--primary .md-nav--secondary .md-nav__link {\n    position: static;\n  }\n  .md-nav--primary .md-nav--secondary .md-nav {\n    position: static;\n    background-color: transparent;\n  }\n  .md-nav--primary .md-nav--secondary .md-nav .md-nav__link {\n    padding-left: 1.4rem;\n  }\n  [dir=rtl] .md-nav--primary .md-nav--secondary .md-nav .md-nav__link {\n    padding-right: 1.4rem;\n    padding-left: initial;\n  }\n  .md-nav--primary .md-nav--secondary .md-nav .md-nav .md-nav__link {\n    padding-left: 2rem;\n  }\n  [dir=rtl] .md-nav--primary .md-nav--secondary .md-nav .md-nav .md-nav__link {\n    padding-right: 2rem;\n    padding-left: initial;\n  }\n  .md-nav--primary .md-nav--secondary .md-nav .md-nav .md-nav .md-nav__link {\n    padding-left: 2.6rem;\n  }\n  [dir=rtl] .md-nav--primary .md-nav--secondary .md-nav .md-nav .md-nav .md-nav__link {\n    padding-right: 2.6rem;\n    padding-left: initial;\n  }\n  .md-nav--primary .md-nav--secondary .md-nav .md-nav .md-nav .md-nav .md-nav__link {\n    padding-left: 3.2rem;\n  }\n  [dir=rtl] .md-nav--primary .md-nav--secondary .md-nav .md-nav .md-nav .md-nav .md-nav__link {\n    padding-right: 3.2rem;\n    padding-left: initial;\n  }\n  .md-nav--secondary {\n    background-color: transparent;\n  }\n  .md-nav__toggle ~ .md-nav {\n    display: flex;\n    transform: translateX(100%);\n    opacity: 0;\n    transition: transform 250ms cubic-bezier(0.8, 0, 0.6, 1), opacity 125ms 50ms;\n  }\n  [dir=rtl] .md-nav__toggle ~ .md-nav {\n    transform: translateX(-100%);\n  }\n  .md-nav__toggle:checked ~ .md-nav {\n    transform: translateX(0);\n    opacity: 1;\n    transition: transform 250ms cubic-bezier(0.4, 0, 0.2, 1), opacity 125ms 125ms;\n  }\n  .md-nav__toggle:checked ~ .md-nav > .md-nav__list {\n    backface-visibility: hidden;\n  }\n}\n@media screen and (max-width: 59.9375em) {\n  .md-nav--primary .md-nav__link[for=__toc] {\n    display: block;\n    padding-right: 2.4rem;\n  }\n  [dir=rtl] .md-nav--primary .md-nav__link[for=__toc] {\n    padding-right: 0.8rem;\n    padding-left: 2.4rem;\n  }\n  .md-nav--primary .md-nav__link[for=__toc] .md-icon::after {\n    content: \"\";\n  }\n  .md-nav--primary .md-nav__link[for=__toc] + .md-nav__link {\n    display: none;\n  }\n  .md-nav--primary .md-nav__link[for=__toc] ~ .md-nav {\n    display: flex;\n  }\n  .md-nav__source {\n    display: block;\n    padding: 0 0.2rem;\n    color: var(--md-primary-bg-color);\n    background-color: var(--md-primary-fg-color--dark);\n  }\n}\n@media screen and (min-width: 60em) and (max-width: 76.1875em) {\n  .md-nav--integrated .md-nav__link[for=__toc] {\n    display: block;\n    padding-right: 2.4rem;\n    scroll-snap-align: initial;\n  }\n  [dir=rtl] .md-nav--integrated .md-nav__link[for=__toc] {\n    padding-right: 0.8rem;\n    padding-left: 2.4rem;\n  }\n  .md-nav--integrated .md-nav__link[for=__toc] .md-icon::after {\n    content: \"\";\n  }\n  .md-nav--integrated .md-nav__link[for=__toc] + .md-nav__link {\n    display: none;\n  }\n  .md-nav--integrated .md-nav__link[for=__toc] ~ .md-nav {\n    display: flex;\n  }\n}\n@media screen and (min-width: 60em) {\n  .md-nav--secondary .md-nav__title[for=__toc] {\n    scroll-snap-align: start;\n  }\n  .md-nav--secondary .md-nav__title .md-nav__icon {\n    display: none;\n  }\n}\n@media screen and (min-width: 76.25em) {\n  .md-nav {\n    transition: max-height 250ms cubic-bezier(0.86, 0, 0.07, 1);\n  }\n  .md-nav--primary .md-nav__title[for=__drawer] {\n    scroll-snap-align: start;\n  }\n  .md-nav--primary .md-nav__title .md-nav__icon {\n    display: none;\n  }\n  .md-nav__toggle ~ .md-nav {\n    display: none;\n  }\n  .md-nav__toggle:checked ~ .md-nav, .md-nav__toggle:indeterminate ~ .md-nav {\n    display: block;\n  }\n  .md-nav__item--nested > .md-nav > .md-nav__title {\n    display: none;\n  }\n  .md-nav__item--section {\n    display: block;\n    margin: 1.25em 0;\n  }\n  .md-nav__item--section:last-child {\n    margin-bottom: 0;\n  }\n  .md-nav__item--section > .md-nav__link {\n    display: none;\n  }\n  .md-nav__item--section > .md-nav {\n    display: block;\n  }\n  .md-nav__item--section > .md-nav > .md-nav__title {\n    display: block;\n    padding: 0;\n    pointer-events: none;\n    scroll-snap-align: start;\n  }\n  .md-nav__item--section > .md-nav > .md-nav__list > .md-nav__item {\n    padding: 0;\n  }\n  .md-nav__icon {\n    float: right;\n    width: 0.9rem;\n    height: 0.9rem;\n    transition: transform 250ms;\n  }\n  [dir=rtl] .md-nav__icon {\n    float: left;\n    transform: rotate(180deg);\n  }\n  .md-nav__icon::after {\n    display: inline-block;\n    width: 100%;\n    height: 100%;\n    vertical-align: -0.1rem;\n    background-color: currentColor;\n    mask-image: var(--md-nav-icon--next);\n    mask-repeat: no-repeat;\n    mask-size: contain;\n    content: \"\";\n  }\n  .md-nav__item--nested .md-nav__toggle:checked ~ .md-nav__link .md-nav__icon, .md-nav__item--nested .md-nav__toggle:indeterminate ~ .md-nav__link .md-nav__icon {\n    transform: rotate(90deg);\n  }\n  .md-nav--lifted > .md-nav__list > .md-nav__item--nested,\n.md-nav--lifted > .md-nav__title {\n    display: none;\n  }\n  .md-nav--lifted > .md-nav__list > .md-nav__item {\n    display: none;\n  }\n  .md-nav--lifted > .md-nav__list > .md-nav__item--active {\n    display: block;\n    padding: 0;\n  }\n  .md-nav--lifted > .md-nav__list > .md-nav__item--active > .md-nav__link {\n    display: none;\n  }\n  .md-nav--lifted > .md-nav__list > .md-nav__item--active > .md-nav > .md-nav__title {\n    display: block;\n    padding: 0 0.6rem;\n    pointer-events: none;\n    scroll-snap-align: start;\n  }\n  .md-nav--lifted > .md-nav__list > .md-nav__item > .md-nav__item {\n    padding-right: 0.6rem;\n  }\n  .md-nav--lifted .md-nav[data-md-level=\"1\"] {\n    display: block;\n  }\n  .md-nav--integrated .md-nav__link[for=__toc] ~ .md-nav {\n    display: block;\n    margin-bottom: 1.25em;\n    border-left: 0.05rem solid var(--md-primary-fg-color);\n  }\n  .md-nav--integrated .md-nav__link[for=__toc] ~ .md-nav > .md-nav__title {\n    display: none;\n  }\n}\n\n:root {\n  --md-search-result-icon: svg-load(\"material/file-search-outline.svg\");\n}\n\n.md-search {\n  position: relative;\n}\n@media screen and (min-width: 60em) {\n  .md-search {\n    padding: 0.2rem 0;\n  }\n}\n.no-js .md-search {\n  display: none;\n}\n.md-search__overlay {\n  z-index: 1;\n  opacity: 0;\n}\n@media screen and (max-width: 59.9375em) {\n  .md-search__overlay {\n    position: absolute;\n    top: 0.2rem;\n    left: -2.2rem;\n    width: 2rem;\n    height: 2rem;\n    overflow: hidden;\n    background-color: var(--md-default-bg-color);\n    border-radius: 1rem;\n    transform-origin: center;\n    transition: transform 300ms 100ms, opacity 200ms 200ms;\n    pointer-events: none;\n  }\n  [dir=rtl] .md-search__overlay {\n    right: -2.2rem;\n    left: initial;\n  }\n  [data-md-toggle=search]:checked ~ .md-header .md-search__overlay {\n    opacity: 1;\n    transition: transform 400ms, opacity 100ms;\n  }\n}\n@media screen and (min-width: 60em) {\n  .md-search__overlay {\n    position: fixed;\n    top: 0;\n    left: 0;\n    width: 0;\n    height: 0;\n    background-color: rgba(0, 0, 0, 0.54);\n    cursor: pointer;\n    transition: width 0ms 250ms, height 0ms 250ms, opacity 250ms;\n  }\n  [dir=rtl] .md-search__overlay {\n    right: 0;\n    left: initial;\n  }\n  [data-md-toggle=search]:checked ~ .md-header .md-search__overlay {\n    width: 100%;\n    height: 200vh;\n    opacity: 1;\n    transition: width 0ms, height 0ms, opacity 250ms;\n  }\n}\n@media screen and (max-width: 29.9375em) {\n  [data-md-toggle=search]:checked ~ .md-header .md-search__overlay {\n    transform: scale(45);\n  }\n}\n@media screen and (min-width: 30em) and (max-width: 44.9375em) {\n  [data-md-toggle=search]:checked ~ .md-header .md-search__overlay {\n    transform: scale(60);\n  }\n}\n@media screen and (min-width: 45em) and (max-width: 59.9375em) {\n  [data-md-toggle=search]:checked ~ .md-header .md-search__overlay {\n    transform: scale(75);\n  }\n}\n.md-search__inner {\n  backface-visibility: hidden;\n}\n@media screen and (max-width: 59.9375em) {\n  .md-search__inner {\n    position: fixed;\n    top: 0;\n    left: 100%;\n    z-index: 2;\n    width: 100%;\n    height: 100%;\n    transform: translateX(5%);\n    opacity: 0;\n    transition: right 0ms 300ms, left 0ms 300ms, transform 150ms 150ms cubic-bezier(0.4, 0, 0.2, 1), opacity 150ms 150ms;\n  }\n  [data-md-toggle=search]:checked ~ .md-header .md-search__inner {\n    left: 0;\n    transform: translateX(0);\n    opacity: 1;\n    transition: right 0ms 0ms, left 0ms 0ms, transform 150ms 150ms cubic-bezier(0.1, 0.7, 0.1, 1), opacity 150ms 150ms;\n  }\n  [dir=rtl] [data-md-toggle=search]:checked ~ .md-header .md-search__inner {\n    right: 0;\n    left: initial;\n  }\n  html [dir=rtl] .md-search__inner {\n    right: 100%;\n    left: initial;\n    transform: translateX(-5%);\n  }\n}\n@media screen and (min-width: 60em) {\n  .md-search__inner {\n    position: relative;\n    float: right;\n    width: 11.7rem;\n    padding: 0.1rem 0;\n    transition: width 250ms cubic-bezier(0.1, 0.7, 0.1, 1);\n  }\n  [dir=rtl] .md-search__inner {\n    float: left;\n  }\n}\n@media screen and (min-width: 60em) and (max-width: 76.1875em) {\n  [data-md-toggle=search]:checked ~ .md-header .md-search__inner {\n    width: 23.4rem;\n  }\n}\n@media screen and (min-width: 76.25em) {\n  [data-md-toggle=search]:checked ~ .md-header .md-search__inner {\n    width: 34.4rem;\n  }\n}\n.md-search__form {\n  position: relative;\n}\n@media screen and (min-width: 60em) {\n  .md-search__form {\n    border-radius: 0.1rem;\n  }\n}\n.md-search__input {\n  position: relative;\n  z-index: 2;\n  padding: 0 2.2rem 0 3.6rem;\n  text-overflow: ellipsis;\n  background-color: var(--md-default-bg-color);\n  box-shadow: 0 0 0.6rem transparent;\n  transition: color 250ms, background-color 250ms, box-shadow 250ms;\n}\n[dir=rtl] .md-search__input {\n  padding: 0 3.6rem 0 2.2rem;\n}\n.md-search__input::placeholder {\n  transition: color 250ms;\n}\n.md-search__input ~ .md-search__icon, .md-search__input::placeholder {\n  color: var(--md-default-fg-color--light);\n}\n.md-search__input::-ms-clear {\n  display: none;\n}\n[data-md-toggle=search]:checked ~ .md-header .md-search__input {\n  box-shadow: 0 0 0.6rem rgba(0, 0, 0, 0.07);\n}\n@media screen and (max-width: 59.9375em) {\n  .md-search__input {\n    width: 100%;\n    height: 2.4rem;\n    font-size: 0.9rem;\n  }\n}\n@media screen and (min-width: 60em) {\n  .md-search__input {\n    width: 100%;\n    height: 1.8rem;\n    padding-left: 2.2rem;\n    color: inherit;\n    font-size: 0.8rem;\n    background-color: rgba(0, 0, 0, 0.26);\n    border-radius: 0.1rem;\n  }\n  [dir=rtl] .md-search__input {\n    padding-right: 2.2rem;\n  }\n  .md-search__input + .md-search__icon {\n    color: var(--md-primary-bg-color);\n  }\n  .md-search__input::placeholder {\n    color: var(--md-primary-bg-color--light);\n  }\n  .md-search__input:hover {\n    background-color: rgba(255, 255, 255, 0.12);\n  }\n  [data-md-toggle=search]:checked ~ .md-header .md-search__input {\n    color: var(--md-default-fg-color);\n    text-overflow: clip;\n    background-color: var(--md-default-bg-color);\n    border-radius: 0.1rem 0.1rem 0 0;\n  }\n  [data-md-toggle=search]:checked ~ .md-header .md-search__input + .md-search__icon, [data-md-toggle=search]:checked ~ .md-header .md-search__input::placeholder {\n    color: var(--md-default-fg-color--light);\n  }\n}\n.md-search__icon {\n  position: absolute;\n  z-index: 2;\n  width: 1.2rem;\n  height: 1.2rem;\n  cursor: pointer;\n  transition: color 250ms, opacity 250ms;\n}\n.md-search__icon:hover {\n  opacity: 0.7;\n}\n.md-search__icon[for=__search] {\n  top: 0.3rem;\n  left: 0.5rem;\n}\n[dir=rtl] .md-search__icon[for=__search] {\n  right: 0.5rem;\n  left: initial;\n}\n[dir=rtl] .md-search__icon[for=__search] svg {\n  transform: scaleX(-1);\n}\n@media screen and (max-width: 59.9375em) {\n  .md-search__icon[for=__search] {\n    top: 0.6rem;\n    left: 0.8rem;\n  }\n  [dir=rtl] .md-search__icon[for=__search] {\n    right: 0.8rem;\n    left: initial;\n  }\n  .md-search__icon[for=__search] svg:first-child {\n    display: none;\n  }\n}\n@media screen and (min-width: 60em) {\n  .md-search__icon[for=__search] {\n    pointer-events: none;\n  }\n  .md-search__icon[for=__search] svg:last-child {\n    display: none;\n  }\n}\n.md-search__icon[type=reset] {\n  top: 0.3rem;\n  right: 0.5rem;\n  transform: scale(0.75);\n  opacity: 0;\n  transition: transform 150ms cubic-bezier(0.1, 0.7, 0.1, 1), opacity 150ms;\n  pointer-events: none;\n}\n[dir=rtl] .md-search__icon[type=reset] {\n  right: initial;\n  left: 0.5rem;\n}\n@media screen and (max-width: 59.9375em) {\n  .md-search__icon[type=reset] {\n    top: 0.6rem;\n    right: 0.8rem;\n  }\n  [dir=rtl] .md-search__icon[type=reset] {\n    right: initial;\n    left: 0.8rem;\n  }\n}\n[data-md-toggle=search]:checked ~ .md-header .md-search__input:valid ~ .md-search__icon[type=reset] {\n  transform: scale(1);\n  opacity: 1;\n  pointer-events: initial;\n}\n[data-md-toggle=search]:checked ~ .md-header .md-search__input:valid ~ .md-search__icon[type=reset]:hover {\n  opacity: 0.7;\n}\n.md-search__output {\n  position: absolute;\n  z-index: 1;\n  width: 100%;\n  overflow: hidden;\n  border-radius: 0 0 0.1rem 0.1rem;\n}\n@media screen and (max-width: 59.9375em) {\n  .md-search__output {\n    top: 2.4rem;\n    bottom: 0;\n  }\n}\n@media screen and (min-width: 60em) {\n  .md-search__output {\n    top: 1.9rem;\n    opacity: 0;\n    transition: opacity 400ms;\n  }\n  [data-md-toggle=search]:checked ~ .md-header .md-search__output {\n    box-shadow: 0 6px 10px 0 rgba(0, 0, 0, 0.14), 0 1px 18px 0 rgba(0, 0, 0, 0.12), 0 3px 5px -1px rgba(0, 0, 0, 0.4);\n    opacity: 1;\n  }\n}\n.md-search__scrollwrap {\n  height: 100%;\n  overflow-y: auto;\n  background-color: var(--md-default-bg-color);\n  backface-visibility: hidden;\n  touch-action: pan-y;\n}\n@media (max-resolution: 1dppx) {\n  .md-search__scrollwrap {\n    transform: translateZ(0);\n  }\n}\n@media screen and (min-width: 60em) and (max-width: 76.1875em) {\n  .md-search__scrollwrap {\n    width: 23.4rem;\n  }\n}\n@media screen and (min-width: 76.25em) {\n  .md-search__scrollwrap {\n    width: 34.4rem;\n  }\n}\n@media screen and (min-width: 60em) {\n  .md-search__scrollwrap {\n    max-height: 0;\n    scrollbar-width: thin;\n    scrollbar-color: var(--md-default-fg-color--lighter) transparent;\n  }\n  [data-md-toggle=search]:checked ~ .md-header .md-search__scrollwrap {\n    max-height: 75vh;\n  }\n  .md-search__scrollwrap:hover {\n    scrollbar-color: var(--md-accent-fg-color) transparent;\n  }\n  .md-search__scrollwrap::-webkit-scrollbar {\n    width: 0.2rem;\n    height: 0.2rem;\n  }\n  .md-search__scrollwrap::-webkit-scrollbar-thumb {\n    background-color: var(--md-default-fg-color--lighter);\n  }\n  .md-search__scrollwrap::-webkit-scrollbar-thumb:hover {\n    background-color: var(--md-accent-fg-color);\n  }\n}\n\n.md-search-result {\n  color: var(--md-default-fg-color);\n  word-break: break-word;\n}\n.md-search-result__meta {\n  padding: 0 0.8rem;\n  color: var(--md-default-fg-color--light);\n  font-size: 0.64rem;\n  line-height: 1.8rem;\n  background-color: var(--md-default-fg-color--lightest);\n  scroll-snap-align: start;\n}\n@media screen and (min-width: 60em) {\n  .md-search-result__meta {\n    padding-left: 2.2rem;\n  }\n  [dir=rtl] .md-search-result__meta {\n    padding-right: 2.2rem;\n    padding-left: initial;\n  }\n}\n.md-search-result__list {\n  margin: 0;\n  padding: 0;\n  list-style: none;\n}\n.md-search-result__item {\n  box-shadow: 0 -0.05rem 0 var(--md-default-fg-color--lightest);\n}\n.md-search-result__item:first-child {\n  box-shadow: none;\n}\n.md-search-result__link {\n  display: block;\n  outline: none;\n  transition: background-color 250ms;\n  scroll-snap-align: start;\n}\n.md-search-result__link:focus, .md-search-result__link:hover {\n  background-color: var(--md-accent-fg-color--transparent);\n}\n.md-search-result__link:last-child p:last-child {\n  margin-bottom: 0.6rem;\n}\n.md-search-result__more summary {\n  display: block;\n  padding: 0.75em 0.8rem;\n  color: var(--md-typeset-a-color);\n  font-size: 0.64rem;\n  outline: 0;\n  cursor: pointer;\n  transition: color 250ms, background-color 250ms;\n  scroll-snap-align: start;\n}\n@media screen and (min-width: 60em) {\n  .md-search-result__more summary {\n    padding-left: 2.2rem;\n  }\n  [dir=rtl] .md-search-result__more summary {\n    padding-right: 2.2rem;\n    padding-left: 0.8rem;\n  }\n}\n.md-search-result__more summary:focus, .md-search-result__more summary:hover {\n  color: var(--md-accent-fg-color);\n  background-color: var(--md-accent-fg-color--transparent);\n}\n.md-search-result__more summary::marker, .md-search-result__more summary::-webkit-details-marker {\n  display: none;\n}\n.md-search-result__more summary ~ * > * {\n  opacity: 0.65;\n}\n.md-search-result__article {\n  position: relative;\n  padding: 0 0.8rem;\n  overflow: hidden;\n}\n@media screen and (min-width: 60em) {\n  .md-search-result__article {\n    padding-left: 2.2rem;\n  }\n  [dir=rtl] .md-search-result__article {\n    padding-right: 2.2rem;\n    padding-left: 0.8rem;\n  }\n}\n.md-search-result__article--document .md-search-result__title {\n  margin: 0.55rem 0;\n  font-weight: 400;\n  font-size: 0.8rem;\n  line-height: 1.4;\n}\n.md-search-result__icon {\n  position: absolute;\n  left: 0;\n  width: 1.2rem;\n  height: 1.2rem;\n  margin: 0.5rem;\n  color: var(--md-default-fg-color--light);\n}\n@media screen and (max-width: 59.9375em) {\n  .md-search-result__icon {\n    display: none;\n  }\n}\n.md-search-result__icon::after {\n  display: inline-block;\n  width: 100%;\n  height: 100%;\n  background-color: currentColor;\n  mask-image: var(--md-search-result-icon);\n  mask-repeat: no-repeat;\n  mask-size: contain;\n  content: \"\";\n}\n[dir=rtl] .md-search-result__icon {\n  right: 0;\n  left: initial;\n}\n[dir=rtl] .md-search-result__icon::after {\n  transform: scaleX(-1);\n}\n.md-search-result__title {\n  margin: 0.5em 0;\n  font-weight: 700;\n  font-size: 0.64rem;\n  line-height: 1.6;\n}\n.md-search-result__teaser {\n  display: -webkit-box;\n  max-height: 2rem;\n  margin: 0.5em 0;\n  overflow: hidden;\n  color: var(--md-default-fg-color--light);\n  font-size: 0.64rem;\n  line-height: 1.6;\n  text-overflow: ellipsis;\n  -webkit-box-orient: vertical;\n  -webkit-line-clamp: 2;\n}\n@media screen and (max-width: 44.9375em) {\n  .md-search-result__teaser {\n    max-height: 3rem;\n    -webkit-line-clamp: 3;\n  }\n}\n@media screen and (min-width: 60em) and (max-width: 76.1875em) {\n  .md-search-result__teaser {\n    max-height: 3rem;\n    -webkit-line-clamp: 3;\n  }\n}\n.md-search-result__teaser mark {\n  text-decoration: underline;\n  background-color: transparent;\n}\n.md-search-result__terms {\n  margin: 0.5em 0;\n  font-size: 0.64rem;\n  font-style: italic;\n}\n.md-search-result mark {\n  color: var(--md-accent-fg-color);\n  background-color: transparent;\n}\n\n.md-select {\n  position: relative;\n  z-index: 1;\n}\n.md-select__inner {\n  position: absolute;\n  top: calc(100% - 0.2rem);\n  left: 50%;\n  max-height: 0;\n  margin-top: 0.2rem;\n  color: var(--md-default-fg-color);\n  background-color: var(--md-default-bg-color);\n  border-radius: 0.1rem;\n  box-shadow: 0 0.2rem 0.5rem rgba(0, 0, 0, 0.1), 0 0 0.05rem rgba(0, 0, 0, 0.25);\n  transform: translate3d(-50%, 0.3rem, 0);\n  opacity: 0;\n  transition: transform 250ms 375ms, opacity 250ms 250ms, max-height 0ms 500ms;\n}\n.md-select:focus-within .md-select__inner, .md-select:hover .md-select__inner {\n  max-height: 10rem;\n  transform: translate3d(-50%, 0, 0);\n  opacity: 1;\n  transition: transform 250ms cubic-bezier(0.1, 0.7, 0.1, 1), opacity 250ms, max-height 250ms;\n}\n.md-select__inner::after {\n  position: absolute;\n  top: 0;\n  left: 50%;\n  width: 0;\n  height: 0;\n  margin-top: -0.2rem;\n  margin-left: -0.2rem;\n  border: 0.2rem solid transparent;\n  border-top: 0;\n  border-bottom-color: var(--md-default-bg-color);\n  content: \"\";\n}\n.md-select__list {\n  max-height: inherit;\n  margin: 0;\n  padding: 0;\n  overflow: auto;\n  font-size: 0.8rem;\n  list-style-type: none;\n  border-radius: 0.1rem;\n}\n.md-select__item {\n  line-height: 1.8rem;\n}\n.md-select__link {\n  display: block;\n  width: 100%;\n  padding-right: 1.2rem;\n  padding-left: 0.6rem;\n  cursor: pointer;\n  transition: background-color 250ms, color 250ms;\n  scroll-snap-align: start;\n}\n[dir=rtl] .md-select__link {\n  padding-right: 0.6rem;\n  padding-left: 1.2rem;\n}\n.md-select__link:focus, .md-select__link:hover {\n  background-color: var(--md-default-fg-color--lightest);\n}\n\n.md-sidebar {\n  position: sticky;\n  top: 2.4rem;\n  flex-shrink: 0;\n  align-self: flex-start;\n  width: 12.1rem;\n  padding: 1.2rem 0;\n}\n@media print {\n  .md-sidebar {\n    display: none;\n  }\n}\n@media screen and (max-width: 76.1875em) {\n  .md-sidebar--primary {\n    position: fixed;\n    top: 0;\n    left: -12.1rem;\n    z-index: 3;\n    display: block;\n    width: 12.1rem;\n    height: 100%;\n    background-color: var(--md-default-bg-color);\n    transform: translateX(0);\n    transition: transform 250ms cubic-bezier(0.4, 0, 0.2, 1), box-shadow 250ms;\n  }\n  [dir=rtl] .md-sidebar--primary {\n    right: -12.1rem;\n    left: initial;\n  }\n  [data-md-toggle=drawer]:checked ~ .md-container .md-sidebar--primary {\n    box-shadow: 0 8px 10px 1px rgba(0, 0, 0, 0.14), 0 3px 14px 2px rgba(0, 0, 0, 0.12), 0 5px 5px -3px rgba(0, 0, 0, 0.4);\n    transform: translateX(12.1rem);\n  }\n  [dir=rtl] [data-md-toggle=drawer]:checked ~ .md-container .md-sidebar--primary {\n    transform: translateX(-12.1rem);\n  }\n  .md-sidebar--primary .md-sidebar__scrollwrap {\n    position: absolute;\n    top: 0;\n    right: 0;\n    bottom: 0;\n    left: 0;\n    margin: 0;\n    scroll-snap-type: none;\n    overflow: hidden;\n  }\n}\n@media screen and (min-width: 76.25em) {\n  .md-sidebar {\n    height: 0;\n  }\n  .no-js .md-sidebar {\n    height: auto;\n  }\n}\n.md-sidebar--secondary {\n  display: none;\n  order: 2;\n}\n@media screen and (min-width: 60em) {\n  .md-sidebar--secondary {\n    height: 0;\n  }\n  .no-js .md-sidebar--secondary {\n    height: auto;\n  }\n  .md-sidebar--secondary:not([hidden]) {\n    display: block;\n  }\n  .md-sidebar--secondary .md-sidebar__scrollwrap {\n    touch-action: pan-y;\n  }\n}\n.md-sidebar__scrollwrap {\n  margin: 0 0.2rem;\n  overflow-y: auto;\n  backface-visibility: hidden;\n  scrollbar-width: thin;\n  scrollbar-color: var(--md-default-fg-color--lighter) transparent;\n}\n.md-sidebar__scrollwrap:hover {\n  scrollbar-color: var(--md-accent-fg-color) transparent;\n}\n.md-sidebar__scrollwrap::-webkit-scrollbar {\n  width: 0.2rem;\n  height: 0.2rem;\n}\n.md-sidebar__scrollwrap::-webkit-scrollbar-thumb {\n  background-color: var(--md-default-fg-color--lighter);\n}\n.md-sidebar__scrollwrap::-webkit-scrollbar-thumb:hover {\n  background-color: var(--md-accent-fg-color);\n}\n\n@media screen and (max-width: 76.1875em) {\n  .md-overlay {\n    position: fixed;\n    top: 0;\n    z-index: 3;\n    width: 0;\n    height: 0;\n    background-color: rgba(0, 0, 0, 0.54);\n    opacity: 0;\n    transition: width 0ms 250ms, height 0ms 250ms, opacity 250ms;\n  }\n  [data-md-toggle=drawer]:checked ~ .md-overlay {\n    width: 100%;\n    height: 100%;\n    opacity: 1;\n    transition: width 0ms, height 0ms, opacity 250ms;\n  }\n}\n@keyframes md-source__facts--done {\n  0% {\n    height: 0;\n  }\n  100% {\n    height: 0.65rem;\n  }\n}\n@keyframes md-source__fact--done {\n  0% {\n    transform: translateY(100%);\n    opacity: 0;\n  }\n  50% {\n    opacity: 0;\n  }\n  100% {\n    transform: translateY(0%);\n    opacity: 1;\n  }\n}\n.md-source {\n  display: block;\n  font-size: 0.65rem;\n  line-height: 1.2;\n  white-space: nowrap;\n  backface-visibility: hidden;\n  transition: opacity 250ms;\n}\n.md-source:focus, .md-source:hover {\n  opacity: 0.7;\n}\n.md-source__icon {\n  display: inline-block;\n  width: 2.4rem;\n  height: 2.4rem;\n  vertical-align: middle;\n}\n.md-source__icon svg {\n  margin-top: 0.6rem;\n  margin-left: 0.6rem;\n}\n[dir=rtl] .md-source__icon svg {\n  margin-right: 0.6rem;\n  margin-left: initial;\n}\n.md-source__icon + .md-source__repository {\n  margin-left: -2rem;\n  padding-left: 2rem;\n}\n[dir=rtl] .md-source__icon + .md-source__repository {\n  margin-right: -2rem;\n  margin-left: initial;\n  padding-right: 2rem;\n  padding-left: initial;\n}\n.md-source__repository {\n  display: inline-block;\n  max-width: calc(100% - 1.2rem);\n  margin-left: 0.6rem;\n  overflow: hidden;\n  font-weight: 700;\n  text-overflow: ellipsis;\n  vertical-align: middle;\n}\n.md-source__facts {\n  margin: 0;\n  padding: 0;\n  overflow: hidden;\n  font-weight: 700;\n  font-size: 0.55rem;\n  list-style-type: none;\n  opacity: 0.75;\n}\n[data-md-state=done] .md-source__facts {\n  animation: md-source__facts--done 250ms ease-in;\n}\n.md-source__fact {\n  float: left;\n}\n[dir=rtl] .md-source__fact {\n  float: right;\n}\n[data-md-state=done] .md-source__fact {\n  animation: md-source__fact--done 400ms ease-out;\n}\n.md-source__fact::before {\n  margin: 0 0.1rem;\n  content: \"·\";\n}\n.md-source__fact:first-child::before {\n  display: none;\n}\n\n.md-tabs {\n  width: 100%;\n  overflow: auto;\n  color: var(--md-primary-bg-color);\n  background-color: var(--md-primary-fg-color);\n  transition: background-color 250ms;\n}\n@media print {\n  .md-tabs {\n    display: none;\n  }\n}\n@media screen and (max-width: 76.1875em) {\n  .md-tabs {\n    display: none;\n  }\n}\n.md-tabs[data-md-state=hidden] {\n  pointer-events: none;\n}\n.md-tabs__list {\n  margin: 0;\n  margin-left: 0.2rem;\n  padding: 0;\n  white-space: nowrap;\n  list-style: none;\n  contain: content;\n}\n[dir=rtl] .md-tabs__list {\n  margin-right: 0.2rem;\n  margin-left: initial;\n}\n.md-tabs__item {\n  display: inline-block;\n  height: 2.4rem;\n  padding-right: 0.6rem;\n  padding-left: 0.6rem;\n}\n.md-tabs__link {\n  display: block;\n  margin-top: 0.8rem;\n  font-size: 0.7rem;\n  backface-visibility: hidden;\n  opacity: 0.7;\n  transition: transform 400ms cubic-bezier(0.1, 0.7, 0.1, 1), opacity 250ms;\n}\n.md-tabs__link--active, .md-tabs__link:focus, .md-tabs__link:hover {\n  color: inherit;\n  opacity: 1;\n}\n.md-tabs__item:nth-child(2) .md-tabs__link {\n  transition-delay: 20ms;\n}\n.md-tabs__item:nth-child(3) .md-tabs__link {\n  transition-delay: 40ms;\n}\n.md-tabs__item:nth-child(4) .md-tabs__link {\n  transition-delay: 60ms;\n}\n.md-tabs__item:nth-child(5) .md-tabs__link {\n  transition-delay: 80ms;\n}\n.md-tabs__item:nth-child(6) .md-tabs__link {\n  transition-delay: 100ms;\n}\n.md-tabs__item:nth-child(7) .md-tabs__link {\n  transition-delay: 120ms;\n}\n.md-tabs__item:nth-child(8) .md-tabs__link {\n  transition-delay: 140ms;\n}\n.md-tabs__item:nth-child(9) .md-tabs__link {\n  transition-delay: 160ms;\n}\n.md-tabs__item:nth-child(10) .md-tabs__link {\n  transition-delay: 180ms;\n}\n.md-tabs__item:nth-child(11) .md-tabs__link {\n  transition-delay: 200ms;\n}\n.md-tabs__item:nth-child(12) .md-tabs__link {\n  transition-delay: 220ms;\n}\n.md-tabs__item:nth-child(13) .md-tabs__link {\n  transition-delay: 240ms;\n}\n.md-tabs__item:nth-child(14) .md-tabs__link {\n  transition-delay: 260ms;\n}\n.md-tabs__item:nth-child(15) .md-tabs__link {\n  transition-delay: 280ms;\n}\n.md-tabs__item:nth-child(16) .md-tabs__link {\n  transition-delay: 300ms;\n}\n.md-tabs[data-md-state=hidden] .md-tabs__link {\n  transform: translateY(50%);\n  opacity: 0;\n  transition: transform 0ms 100ms, opacity 100ms;\n}\n\n:root {\n  --md-version-icon: svg-load(\"fontawesome/solid/caret-down.svg\");\n}\n\n.md-version {\n  flex-shrink: 0;\n  height: 2.4rem;\n  font-size: 0.8rem;\n}\n.md-version__current {\n  position: relative;\n  top: 0.05rem;\n  margin-right: 0.4rem;\n  margin-left: 1.4rem;\n}\n[dir=rtl] .md-version__current {\n  margin-right: 1.4rem;\n  margin-left: 0.4rem;\n}\n.md-version__current::after {\n  display: inline-block;\n  width: 0.4rem;\n  height: 0.6rem;\n  margin-left: 0.4rem;\n  background-color: currentColor;\n  mask-image: var(--md-version-icon);\n  mask-repeat: no-repeat;\n  content: \"\";\n}\n[dir=rtl] .md-version__current::after {\n  margin-right: 0.4rem;\n  margin-left: initial;\n}\n.md-version__list {\n  position: absolute;\n  top: 0.15rem;\n  z-index: 1;\n  max-height: 1.8rem;\n  margin: 0.2rem 0.8rem;\n  padding: 0;\n  overflow: auto;\n  color: var(--md-default-fg-color);\n  list-style-type: none;\n  background-color: var(--md-default-bg-color);\n  border-radius: 0.1rem;\n  box-shadow: 0 0.2rem 0.5rem rgba(0, 0, 0, 0.1), 0 0 0.05rem rgba(0, 0, 0, 0.25);\n  opacity: 0;\n  transition: max-height 0ms 500ms, opacity 250ms 250ms;\n  scroll-snap-type: y mandatory;\n}\n.md-version__list:focus-within, .md-version__list:hover {\n  max-height: 10rem;\n  opacity: 1;\n  transition: max-height 250ms, opacity 250ms;\n}\n.md-version__item {\n  line-height: 1.8rem;\n}\n.md-version__link {\n  display: block;\n  width: 100%;\n  padding-right: 1.2rem;\n  padding-left: 0.6rem;\n  white-space: nowrap;\n  cursor: pointer;\n  transition: color 250ms, background-color 250ms;\n  scroll-snap-align: start;\n}\n[dir=rtl] .md-version__link {\n  padding-right: 0.6rem;\n  padding-left: 1.2rem;\n}\n.md-version__link:focus, .md-version__link:hover {\n  background-color: var(--md-default-fg-color--lightest);\n}\n\n:root {\n  --md-admonition-icon--note:\n    svg-load(\"material/pencil.svg\");\n  --md-admonition-icon--abstract:\n    svg-load(\"material/text-subject.svg\");\n  --md-admonition-icon--info:\n    svg-load(\"material/information.svg\");\n  --md-admonition-icon--tip:\n    svg-load(\"material/fire.svg\");\n  --md-admonition-icon--success:\n    svg-load(\"material/check-circle.svg\");\n  --md-admonition-icon--question:\n    svg-load(\"material/help-circle.svg\");\n  --md-admonition-icon--warning:\n    svg-load(\"material/alert.svg\");\n  --md-admonition-icon--failure:\n    svg-load(\"material/close-circle.svg\");\n  --md-admonition-icon--danger:\n    svg-load(\"material/flash-circle.svg\");\n  --md-admonition-icon--bug:\n    svg-load(\"material/bug.svg\");\n  --md-admonition-icon--example:\n    svg-load(\"material/format-list-numbered.svg\");\n  --md-admonition-icon--quote:\n    svg-load(\"material/format-quote-close.svg\");\n}\n\n.md-typeset .admonition, .md-typeset details {\n  margin: 1.5625em 0;\n  padding: 0 0.6rem;\n  overflow: hidden;\n  color: var(--md-admonition-fg-color);\n  font-size: 0.64rem;\n  page-break-inside: avoid;\n  background-color: var(--md-admonition-bg-color);\n  border-left: 0.2rem solid #448aff;\n  border-radius: 0.1rem;\n  box-shadow: 0 0.2rem 0.5rem rgba(0, 0, 0, 0.05), 0 0.025rem 0.05rem rgba(0, 0, 0, 0.05);\n}\n@media print {\n  .md-typeset .admonition, .md-typeset details {\n    box-shadow: none;\n  }\n}\n[dir=rtl] .md-typeset .admonition, [dir=rtl] .md-typeset details {\n  border-right: 0.2rem solid #448aff;\n  border-left: none;\n}\n.md-typeset .admonition .admonition, .md-typeset details .admonition, .md-typeset .admonition details, .md-typeset details details {\n  margin-top: 1em;\n  margin-bottom: 1em;\n}\n.md-typeset .admonition .md-typeset__scrollwrap, .md-typeset details .md-typeset__scrollwrap {\n  margin: 1em -0.6rem;\n}\n.md-typeset .admonition .md-typeset__table, .md-typeset details .md-typeset__table {\n  padding: 0 0.6rem;\n}\n.md-typeset .admonition > .tabbed-set:only-child, .md-typeset details > .tabbed-set:only-child {\n  margin-top: 0;\n}\nhtml .md-typeset .admonition > :last-child, html .md-typeset details > :last-child {\n  margin-bottom: 0.6rem;\n}\n.md-typeset .admonition-title, .md-typeset summary {\n  position: relative;\n  margin: 0 -0.6rem 0 -0.8rem;\n  padding: 0.4rem 0.6rem 0.4rem 2rem;\n  font-weight: 700;\n  background-color: rgba(68, 138, 255, 0.1);\n  border-left: 0.2rem solid #448aff;\n}\n[dir=rtl] .md-typeset .admonition-title, [dir=rtl] .md-typeset summary {\n  margin: 0 -0.8rem 0 -0.6rem;\n  padding: 0.4rem 2rem 0.4rem 0.6rem;\n  border-right: 0.2rem solid #448aff;\n  border-left: none;\n}\nhtml .md-typeset .admonition-title:last-child, html .md-typeset summary:last-child {\n  margin-bottom: 0;\n}\n.md-typeset .admonition-title::before, .md-typeset summary::before {\n  position: absolute;\n  left: 0.6rem;\n  width: 1rem;\n  height: 1rem;\n  background-color: #448aff;\n  mask-image: var(--md-admonition-icon--note);\n  mask-repeat: no-repeat;\n  mask-size: contain;\n  content: \"\";\n}\n[dir=rtl] .md-typeset .admonition-title::before, [dir=rtl] .md-typeset summary::before {\n  right: 0.6rem;\n  left: initial;\n}\n.md-typeset .admonition-title code, .md-typeset summary code {\n  margin: initial;\n  padding: initial;\n  color: currentColor;\n  background-color: transparent;\n  border-radius: initial;\n  box-shadow: none;\n}\n.md-typeset .admonition-title + .tabbed-set:last-child, .md-typeset summary + .tabbed-set:last-child {\n  margin-top: 0;\n}\n\n.md-typeset .admonition.note, .md-typeset details.note {\n  border-color: #448aff;\n}\n\n.md-typeset .note > .admonition-title, .md-typeset .note > summary {\n  background-color: rgba(68, 138, 255, 0.1);\n  border-color: #448aff;\n}\n.md-typeset .note > .admonition-title::before, .md-typeset .note > summary::before {\n  background-color: #448aff;\n  mask-image: var(--md-admonition-icon--note);\n  mask-repeat: no-repeat;\n  mask-size: contain;\n}\n\n.md-typeset .admonition.abstract, .md-typeset details.abstract, .md-typeset .admonition.tldr, .md-typeset details.tldr, .md-typeset .admonition.summary, .md-typeset details.summary {\n  border-color: #00b0ff;\n}\n\n.md-typeset .abstract > .admonition-title, .md-typeset .abstract > summary, .md-typeset .tldr > .admonition-title, .md-typeset .tldr > summary, .md-typeset .summary > .admonition-title, .md-typeset .summary > summary {\n  background-color: rgba(0, 176, 255, 0.1);\n  border-color: #00b0ff;\n}\n.md-typeset .abstract > .admonition-title::before, .md-typeset .abstract > summary::before, .md-typeset .tldr > .admonition-title::before, .md-typeset .tldr > summary::before, .md-typeset .summary > .admonition-title::before, .md-typeset .summary > summary::before {\n  background-color: #00b0ff;\n  mask-image: var(--md-admonition-icon--abstract);\n  mask-repeat: no-repeat;\n  mask-size: contain;\n}\n\n.md-typeset .admonition.info, .md-typeset details.info, .md-typeset .admonition.todo, .md-typeset details.todo {\n  border-color: #00b8d4;\n}\n\n.md-typeset .info > .admonition-title, .md-typeset .info > summary, .md-typeset .todo > .admonition-title, .md-typeset .todo > summary {\n  background-color: rgba(0, 184, 212, 0.1);\n  border-color: #00b8d4;\n}\n.md-typeset .info > .admonition-title::before, .md-typeset .info > summary::before, .md-typeset .todo > .admonition-title::before, .md-typeset .todo > summary::before {\n  background-color: #00b8d4;\n  mask-image: var(--md-admonition-icon--info);\n  mask-repeat: no-repeat;\n  mask-size: contain;\n}\n\n.md-typeset .admonition.tip, .md-typeset details.tip, .md-typeset .admonition.important, .md-typeset details.important, .md-typeset .admonition.hint, .md-typeset details.hint {\n  border-color: #00bfa5;\n}\n\n.md-typeset .tip > .admonition-title, .md-typeset .tip > summary, .md-typeset .important > .admonition-title, .md-typeset .important > summary, .md-typeset .hint > .admonition-title, .md-typeset .hint > summary {\n  background-color: rgba(0, 191, 165, 0.1);\n  border-color: #00bfa5;\n}\n.md-typeset .tip > .admonition-title::before, .md-typeset .tip > summary::before, .md-typeset .important > .admonition-title::before, .md-typeset .important > summary::before, .md-typeset .hint > .admonition-title::before, .md-typeset .hint > summary::before {\n  background-color: #00bfa5;\n  mask-image: var(--md-admonition-icon--tip);\n  mask-repeat: no-repeat;\n  mask-size: contain;\n}\n\n.md-typeset .admonition.success, .md-typeset details.success, .md-typeset .admonition.done, .md-typeset details.done, .md-typeset .admonition.check, .md-typeset details.check {\n  border-color: #00c853;\n}\n\n.md-typeset .success > .admonition-title, .md-typeset .success > summary, .md-typeset .done > .admonition-title, .md-typeset .done > summary, .md-typeset .check > .admonition-title, .md-typeset .check > summary {\n  background-color: rgba(0, 200, 83, 0.1);\n  border-color: #00c853;\n}\n.md-typeset .success > .admonition-title::before, .md-typeset .success > summary::before, .md-typeset .done > .admonition-title::before, .md-typeset .done > summary::before, .md-typeset .check > .admonition-title::before, .md-typeset .check > summary::before {\n  background-color: #00c853;\n  mask-image: var(--md-admonition-icon--success);\n  mask-repeat: no-repeat;\n  mask-size: contain;\n}\n\n.md-typeset .admonition.question, .md-typeset details.question, .md-typeset .admonition.faq, .md-typeset details.faq, .md-typeset .admonition.help, .md-typeset details.help {\n  border-color: #64dd17;\n}\n\n.md-typeset .question > .admonition-title, .md-typeset .question > summary, .md-typeset .faq > .admonition-title, .md-typeset .faq > summary, .md-typeset .help > .admonition-title, .md-typeset .help > summary {\n  background-color: rgba(100, 221, 23, 0.1);\n  border-color: #64dd17;\n}\n.md-typeset .question > .admonition-title::before, .md-typeset .question > summary::before, .md-typeset .faq > .admonition-title::before, .md-typeset .faq > summary::before, .md-typeset .help > .admonition-title::before, .md-typeset .help > summary::before {\n  background-color: #64dd17;\n  mask-image: var(--md-admonition-icon--question);\n  mask-repeat: no-repeat;\n  mask-size: contain;\n}\n\n.md-typeset .admonition.warning, .md-typeset details.warning, .md-typeset .admonition.attention, .md-typeset details.attention, .md-typeset .admonition.caution, .md-typeset details.caution {\n  border-color: #ff9100;\n}\n\n.md-typeset .warning > .admonition-title, .md-typeset .warning > summary, .md-typeset .attention > .admonition-title, .md-typeset .attention > summary, .md-typeset .caution > .admonition-title, .md-typeset .caution > summary {\n  background-color: rgba(255, 145, 0, 0.1);\n  border-color: #ff9100;\n}\n.md-typeset .warning > .admonition-title::before, .md-typeset .warning > summary::before, .md-typeset .attention > .admonition-title::before, .md-typeset .attention > summary::before, .md-typeset .caution > .admonition-title::before, .md-typeset .caution > summary::before {\n  background-color: #ff9100;\n  mask-image: var(--md-admonition-icon--warning);\n  mask-repeat: no-repeat;\n  mask-size: contain;\n}\n\n.md-typeset .admonition.failure, .md-typeset details.failure, .md-typeset .admonition.missing, .md-typeset details.missing, .md-typeset .admonition.fail, .md-typeset details.fail {\n  border-color: #ff5252;\n}\n\n.md-typeset .failure > .admonition-title, .md-typeset .failure > summary, .md-typeset .missing > .admonition-title, .md-typeset .missing > summary, .md-typeset .fail > .admonition-title, .md-typeset .fail > summary {\n  background-color: rgba(255, 82, 82, 0.1);\n  border-color: #ff5252;\n}\n.md-typeset .failure > .admonition-title::before, .md-typeset .failure > summary::before, .md-typeset .missing > .admonition-title::before, .md-typeset .missing > summary::before, .md-typeset .fail > .admonition-title::before, .md-typeset .fail > summary::before {\n  background-color: #ff5252;\n  mask-image: var(--md-admonition-icon--failure);\n  mask-repeat: no-repeat;\n  mask-size: contain;\n}\n\n.md-typeset .admonition.danger, .md-typeset details.danger, .md-typeset .admonition.error, .md-typeset details.error {\n  border-color: #ff1744;\n}\n\n.md-typeset .danger > .admonition-title, .md-typeset .danger > summary, .md-typeset .error > .admonition-title, .md-typeset .error > summary {\n  background-color: rgba(255, 23, 68, 0.1);\n  border-color: #ff1744;\n}\n.md-typeset .danger > .admonition-title::before, .md-typeset .danger > summary::before, .md-typeset .error > .admonition-title::before, .md-typeset .error > summary::before {\n  background-color: #ff1744;\n  mask-image: var(--md-admonition-icon--danger);\n  mask-repeat: no-repeat;\n  mask-size: contain;\n}\n\n.md-typeset .admonition.bug, .md-typeset details.bug {\n  border-color: #f50057;\n}\n\n.md-typeset .bug > .admonition-title, .md-typeset .bug > summary {\n  background-color: rgba(245, 0, 87, 0.1);\n  border-color: #f50057;\n}\n.md-typeset .bug > .admonition-title::before, .md-typeset .bug > summary::before {\n  background-color: #f50057;\n  mask-image: var(--md-admonition-icon--bug);\n  mask-repeat: no-repeat;\n  mask-size: contain;\n}\n\n.md-typeset .admonition.example, .md-typeset details.example {\n  border-color: #7c4dff;\n}\n\n.md-typeset .example > .admonition-title, .md-typeset .example > summary {\n  background-color: rgba(124, 77, 255, 0.1);\n  border-color: #7c4dff;\n}\n.md-typeset .example > .admonition-title::before, .md-typeset .example > summary::before {\n  background-color: #7c4dff;\n  mask-image: var(--md-admonition-icon--example);\n  mask-repeat: no-repeat;\n  mask-size: contain;\n}\n\n.md-typeset .admonition.quote, .md-typeset details.quote, .md-typeset .admonition.cite, .md-typeset details.cite {\n  border-color: #9e9e9e;\n}\n\n.md-typeset .quote > .admonition-title, .md-typeset .quote > summary, .md-typeset .cite > .admonition-title, .md-typeset .cite > summary {\n  background-color: rgba(158, 158, 158, 0.1);\n  border-color: #9e9e9e;\n}\n.md-typeset .quote > .admonition-title::before, .md-typeset .quote > summary::before, .md-typeset .cite > .admonition-title::before, .md-typeset .cite > summary::before {\n  background-color: #9e9e9e;\n  mask-image: var(--md-admonition-icon--quote);\n  mask-repeat: no-repeat;\n  mask-size: contain;\n}\n\n:root {\n  --md-footnotes-icon: svg-load(\"material/keyboard-return.svg\");\n}\n\n.md-typeset [id^=\"fnref:\"]:target {\n  scroll-margin-top: initial;\n  margin-top: -3.4rem;\n  padding-top: 3.4rem;\n}\n.md-typeset [id^=\"fn:\"]:target {\n  scroll-margin-top: initial;\n  margin-top: -3.45rem;\n  padding-top: 3.45rem;\n}\n.md-typeset .footnote {\n  color: var(--md-default-fg-color--light);\n  font-size: 0.64rem;\n}\n.md-typeset .footnote ol {\n  margin-left: 0;\n}\n.md-typeset .footnote li {\n  transition: color 125ms;\n}\n.md-typeset .footnote li:target {\n  color: var(--md-default-fg-color);\n}\n.md-typeset .footnote li:hover .footnote-backref, .md-typeset .footnote li:target .footnote-backref {\n  transform: translateX(0);\n  opacity: 1;\n}\n.md-typeset .footnote li > :first-child {\n  margin-top: 0;\n}\n.md-typeset .footnote-backref {\n  display: inline-block;\n  color: var(--md-typeset-a-color);\n  font-size: 0;\n  vertical-align: text-bottom;\n  transform: translateX(0.25rem);\n  opacity: 0;\n  transition: color 250ms, transform 250ms 250ms, opacity 125ms 250ms;\n}\n@media print {\n  .md-typeset .footnote-backref {\n    color: var(--md-typeset-a-color);\n    transform: translateX(0);\n    opacity: 1;\n  }\n}\n[dir=rtl] .md-typeset .footnote-backref {\n  transform: translateX(-0.25rem);\n}\n.md-typeset .footnote-backref:hover {\n  color: var(--md-accent-fg-color);\n}\n.md-typeset .footnote-backref::before {\n  display: inline-block;\n  width: 0.8rem;\n  height: 0.8rem;\n  background-color: currentColor;\n  mask-image: var(--md-footnotes-icon);\n  mask-repeat: no-repeat;\n  mask-size: contain;\n  content: \"\";\n}\n[dir=rtl] .md-typeset .footnote-backref::before svg {\n  transform: scaleX(-1);\n}\n\n.md-typeset .headerlink {\n  display: inline-block;\n  margin-left: 0.5rem;\n  color: var(--md-default-fg-color--lighter);\n  opacity: 0;\n  transition: color 250ms, opacity 125ms;\n}\n@media print {\n  .md-typeset .headerlink {\n    display: none;\n  }\n}\n[dir=rtl] .md-typeset .headerlink {\n  margin-right: 0.5rem;\n  margin-left: initial;\n}\n.md-typeset :hover > .headerlink,\n.md-typeset :target > .headerlink,\n.md-typeset .headerlink:focus {\n  opacity: 1;\n  transition: color 250ms, opacity 125ms;\n}\n.md-typeset :target > .headerlink,\n.md-typeset .headerlink:focus,\n.md-typeset .headerlink:hover {\n  color: var(--md-accent-fg-color);\n}\n.md-typeset :target {\n  scroll-margin-top: 3.6rem;\n}\n.md-typeset h1:target,\n.md-typeset h2:target,\n.md-typeset h3:target {\n  scroll-margin-top: initial;\n}\n.md-typeset h1:target::before,\n.md-typeset h2:target::before,\n.md-typeset h3:target::before {\n  display: block;\n  margin-top: -3.4rem;\n  padding-top: 3.4rem;\n  content: \"\";\n}\n.md-typeset h4:target {\n  scroll-margin-top: initial;\n}\n.md-typeset h4:target::before {\n  display: block;\n  margin-top: -3.45rem;\n  padding-top: 3.45rem;\n  content: \"\";\n}\n.md-typeset h5:target,\n.md-typeset h6:target {\n  scroll-margin-top: initial;\n}\n.md-typeset h5:target::before,\n.md-typeset h6:target::before {\n  display: block;\n  margin-top: -3.6rem;\n  padding-top: 3.6rem;\n  content: \"\";\n}\n\n.md-typeset div.arithmatex {\n  overflow: auto;\n}\n@media screen and (max-width: 44.9375em) {\n  .md-typeset div.arithmatex {\n    margin: 0 -0.8rem;\n  }\n}\n.md-typeset div.arithmatex > * {\n  width: min-content;\n  margin: 1em auto !important;\n  padding: 0 0.8rem;\n  touch-action: auto;\n}\n\n.md-typeset del.critic,\n.md-typeset ins.critic,\n.md-typeset .critic.comment {\n  box-decoration-break: clone;\n}\n.md-typeset del.critic {\n  background-color: var(--md-typeset-del-color);\n}\n.md-typeset ins.critic {\n  background-color: var(--md-typeset-ins-color);\n}\n.md-typeset .critic.comment {\n  color: var(--md-code-hl-comment-color);\n}\n.md-typeset .critic.comment::before {\n  content: \"/* \";\n}\n.md-typeset .critic.comment::after {\n  content: \" */\";\n}\n.md-typeset .critic.block {\n  display: block;\n  margin: 1em 0;\n  padding-right: 0.8rem;\n  padding-left: 0.8rem;\n  overflow: auto;\n  box-shadow: none;\n}\n.md-typeset .critic.block > :first-child {\n  margin-top: 0.5em;\n}\n.md-typeset .critic.block > :last-child {\n  margin-bottom: 0.5em;\n}\n\n:root {\n  --md-details-icon: svg-load(\"material/chevron-right.svg\");\n}\n\n.md-typeset details {\n  display: flow-root;\n  padding-top: 0;\n  overflow: visible;\n}\n.md-typeset details[open] > summary::after {\n  transform: rotate(90deg);\n}\n.md-typeset details:not([open]) {\n  padding-bottom: 0;\n  box-shadow: none;\n}\n.md-typeset details:not([open]) > summary {\n  border-radius: 0.1rem;\n}\n.md-typeset details::after {\n  display: table;\n  content: \"\";\n}\n.md-typeset summary {\n  display: block;\n  min-height: 1rem;\n  padding: 0.4rem 1.8rem 0.4rem 2rem;\n  border-top-left-radius: 0.1rem;\n  border-top-right-radius: 0.1rem;\n  cursor: pointer;\n}\n[dir=rtl] .md-typeset summary {\n  padding: 0.4rem 2.2rem 0.4rem 1.8rem;\n}\n.md-typeset summary:not(.focus-visible) {\n  outline: none;\n  -webkit-tap-highlight-color: transparent;\n}\n.md-typeset summary::after {\n  position: absolute;\n  top: 0.4rem;\n  right: 0.4rem;\n  width: 1rem;\n  height: 1rem;\n  background-color: currentColor;\n  mask-image: var(--md-details-icon);\n  mask-repeat: no-repeat;\n  mask-size: contain;\n  transform: rotate(0deg);\n  transition: transform 250ms;\n  content: \"\";\n}\n[dir=rtl] .md-typeset summary::after {\n  right: initial;\n  left: 0.4rem;\n  transform: rotate(180deg);\n}\n.md-typeset summary::marker, .md-typeset summary::-webkit-details-marker {\n  display: none;\n}\n\n.md-typeset .emojione,\n.md-typeset .twemoji,\n.md-typeset .gemoji {\n  display: inline-flex;\n  height: 1.125em;\n  vertical-align: text-top;\n}\n.md-typeset .emojione svg,\n.md-typeset .twemoji svg,\n.md-typeset .gemoji svg {\n  width: 1.125em;\n  max-height: 100%;\n  fill: currentColor;\n}\n\n.highlight .o,\n.highlight .ow {\n  color: var(--md-code-hl-operator-color);\n}\n.highlight .p {\n  color: var(--md-code-hl-punctuation-color);\n}\n.highlight .cpf,\n.highlight .l,\n.highlight .s,\n.highlight .sb,\n.highlight .sc,\n.highlight .s2,\n.highlight .si,\n.highlight .s1,\n.highlight .ss {\n  color: var(--md-code-hl-string-color);\n}\n.highlight .cp,\n.highlight .se,\n.highlight .sh,\n.highlight .sr,\n.highlight .sx {\n  color: var(--md-code-hl-special-color);\n}\n.highlight .m,\n.highlight .mb,\n.highlight .mf,\n.highlight .mh,\n.highlight .mi,\n.highlight .il,\n.highlight .mo {\n  color: var(--md-code-hl-number-color);\n}\n.highlight .k,\n.highlight .kd,\n.highlight .kn,\n.highlight .kp,\n.highlight .kr,\n.highlight .kt {\n  color: var(--md-code-hl-keyword-color);\n}\n.highlight .kc,\n.highlight .n {\n  color: var(--md-code-hl-name-color);\n}\n.highlight .no,\n.highlight .nb,\n.highlight .bp {\n  color: var(--md-code-hl-constant-color);\n}\n.highlight .nc,\n.highlight .ne,\n.highlight .nf,\n.highlight .nn {\n  color: var(--md-code-hl-function-color);\n}\n.highlight .nd,\n.highlight .ni,\n.highlight .nl,\n.highlight .nt {\n  color: var(--md-code-hl-keyword-color);\n}\n.highlight .c,\n.highlight .cm,\n.highlight .c1,\n.highlight .ch,\n.highlight .cs,\n.highlight .sd {\n  color: var(--md-code-hl-comment-color);\n}\n.highlight .na,\n.highlight .nv,\n.highlight .vc,\n.highlight .vg,\n.highlight .vi {\n  color: var(--md-code-hl-variable-color);\n}\n.highlight .ge,\n.highlight .gr,\n.highlight .gh,\n.highlight .go,\n.highlight .gp,\n.highlight .gs,\n.highlight .gu,\n.highlight .gt {\n  color: var(--md-code-hl-generic-color);\n}\n.highlight .gd,\n.highlight .gi {\n  margin: 0 -0.125em;\n  padding: 0 0.125em;\n  border-radius: 0.1rem;\n}\n.highlight .gd {\n  background-color: var(--md-typeset-del-color);\n}\n.highlight .gi {\n  background-color: var(--md-typeset-ins-color);\n}\n.highlight .hll {\n  display: block;\n  margin: 0 -1.1764705882em;\n  padding: 0 1.1764705882em;\n  background-color: var(--md-code-hl-color);\n}\n.highlight [data-linenos]::before {\n  position: sticky;\n  left: -1.1764705882em;\n  float: left;\n  margin-right: 1.1764705882em;\n  margin-left: -1.1764705882em;\n  padding-left: 1.1764705882em;\n  color: var(--md-default-fg-color--light);\n  background-color: var(--md-code-bg-color);\n  box-shadow: -0.05rem 0 var(--md-default-fg-color--lightest) inset;\n  content: attr(data-linenos);\n  user-select: none;\n}\n\n.highlighttable {\n  display: flow-root;\n  overflow: hidden;\n}\n.highlighttable tbody,\n.highlighttable td {\n  display: block;\n  padding: 0;\n}\n.highlighttable tr {\n  display: flex;\n}\n.highlighttable pre {\n  margin: 0;\n}\n.highlighttable .linenos {\n  padding: 0.7720588235em 1.1764705882em;\n  padding-right: 0;\n  font-size: 0.85em;\n  background-color: var(--md-code-bg-color);\n  user-select: none;\n}\n.highlighttable .linenodiv {\n  padding-right: 0.5882352941em;\n  box-shadow: -0.05rem 0 var(--md-default-fg-color--lightest) inset;\n}\n.highlighttable .linenodiv pre {\n  color: var(--md-default-fg-color--light);\n  text-align: right;\n}\n.highlighttable .code {\n  flex: 1;\n  overflow: hidden;\n}\n\n.md-typeset .highlighttable {\n  margin: 1em 0;\n  direction: ltr;\n  border-radius: 0.1rem;\n}\n.md-typeset .highlighttable code {\n  border-radius: 0;\n}\n@media screen and (max-width: 44.9375em) {\n  .md-typeset > .highlight {\n    margin: 1em -0.8rem;\n  }\n  .md-typeset > .highlight .hll {\n    margin: 0 -0.8rem;\n    padding: 0 0.8rem;\n  }\n  .md-typeset > .highlight code {\n    border-radius: 0;\n  }\n  .md-typeset > .highlighttable {\n    margin: 1em -0.8rem;\n    border-radius: 0;\n  }\n  .md-typeset > .highlighttable .hll {\n    margin: 0 -0.8rem;\n    padding: 0 0.8rem;\n  }\n}\n\n.md-typeset .keys kbd::before,\n.md-typeset .keys kbd::after {\n  position: relative;\n  margin: 0;\n  color: inherit;\n  -moz-osx-font-smoothing: initial;\n  -webkit-font-smoothing: initial;\n}\n.md-typeset .keys span {\n  padding: 0 0.2em;\n  color: var(--md-default-fg-color--light);\n}\n.md-typeset .keys .key-alt::before {\n  padding-right: 0.4em;\n  content: \"⎇\";\n}\n.md-typeset .keys .key-left-alt::before {\n  padding-right: 0.4em;\n  content: \"⎇\";\n}\n.md-typeset .keys .key-right-alt::before {\n  padding-right: 0.4em;\n  content: \"⎇\";\n}\n.md-typeset .keys .key-command::before {\n  padding-right: 0.4em;\n  content: \"⌘\";\n}\n.md-typeset .keys .key-left-command::before {\n  padding-right: 0.4em;\n  content: \"⌘\";\n}\n.md-typeset .keys .key-right-command::before {\n  padding-right: 0.4em;\n  content: \"⌘\";\n}\n.md-typeset .keys .key-control::before {\n  padding-right: 0.4em;\n  content: \"⌃\";\n}\n.md-typeset .keys .key-left-control::before {\n  padding-right: 0.4em;\n  content: \"⌃\";\n}\n.md-typeset .keys .key-right-control::before {\n  padding-right: 0.4em;\n  content: \"⌃\";\n}\n.md-typeset .keys .key-meta::before {\n  padding-right: 0.4em;\n  content: \"◆\";\n}\n.md-typeset .keys .key-left-meta::before {\n  padding-right: 0.4em;\n  content: \"◆\";\n}\n.md-typeset .keys .key-right-meta::before {\n  padding-right: 0.4em;\n  content: \"◆\";\n}\n.md-typeset .keys .key-option::before {\n  padding-right: 0.4em;\n  content: \"⌥\";\n}\n.md-typeset .keys .key-left-option::before {\n  padding-right: 0.4em;\n  content: \"⌥\";\n}\n.md-typeset .keys .key-right-option::before {\n  padding-right: 0.4em;\n  content: \"⌥\";\n}\n.md-typeset .keys .key-shift::before {\n  padding-right: 0.4em;\n  content: \"⇧\";\n}\n.md-typeset .keys .key-left-shift::before {\n  padding-right: 0.4em;\n  content: \"⇧\";\n}\n.md-typeset .keys .key-right-shift::before {\n  padding-right: 0.4em;\n  content: \"⇧\";\n}\n.md-typeset .keys .key-super::before {\n  padding-right: 0.4em;\n  content: \"❖\";\n}\n.md-typeset .keys .key-left-super::before {\n  padding-right: 0.4em;\n  content: \"❖\";\n}\n.md-typeset .keys .key-right-super::before {\n  padding-right: 0.4em;\n  content: \"❖\";\n}\n.md-typeset .keys .key-windows::before {\n  padding-right: 0.4em;\n  content: \"⊞\";\n}\n.md-typeset .keys .key-left-windows::before {\n  padding-right: 0.4em;\n  content: \"⊞\";\n}\n.md-typeset .keys .key-right-windows::before {\n  padding-right: 0.4em;\n  content: \"⊞\";\n}\n.md-typeset .keys .key-arrow-down::before {\n  padding-right: 0.4em;\n  content: \"↓\";\n}\n.md-typeset .keys .key-arrow-left::before {\n  padding-right: 0.4em;\n  content: \"←\";\n}\n.md-typeset .keys .key-arrow-right::before {\n  padding-right: 0.4em;\n  content: \"→\";\n}\n.md-typeset .keys .key-arrow-up::before {\n  padding-right: 0.4em;\n  content: \"↑\";\n}\n.md-typeset .keys .key-backspace::before {\n  padding-right: 0.4em;\n  content: \"⌫\";\n}\n.md-typeset .keys .key-backtab::before {\n  padding-right: 0.4em;\n  content: \"⇤\";\n}\n.md-typeset .keys .key-caps-lock::before {\n  padding-right: 0.4em;\n  content: \"⇪\";\n}\n.md-typeset .keys .key-clear::before {\n  padding-right: 0.4em;\n  content: \"⌧\";\n}\n.md-typeset .keys .key-context-menu::before {\n  padding-right: 0.4em;\n  content: \"☰\";\n}\n.md-typeset .keys .key-delete::before {\n  padding-right: 0.4em;\n  content: \"⌦\";\n}\n.md-typeset .keys .key-eject::before {\n  padding-right: 0.4em;\n  content: \"⏏\";\n}\n.md-typeset .keys .key-end::before {\n  padding-right: 0.4em;\n  content: \"⤓\";\n}\n.md-typeset .keys .key-escape::before {\n  padding-right: 0.4em;\n  content: \"⎋\";\n}\n.md-typeset .keys .key-home::before {\n  padding-right: 0.4em;\n  content: \"⤒\";\n}\n.md-typeset .keys .key-insert::before {\n  padding-right: 0.4em;\n  content: \"⎀\";\n}\n.md-typeset .keys .key-page-down::before {\n  padding-right: 0.4em;\n  content: \"⇟\";\n}\n.md-typeset .keys .key-page-up::before {\n  padding-right: 0.4em;\n  content: \"⇞\";\n}\n.md-typeset .keys .key-print-screen::before {\n  padding-right: 0.4em;\n  content: \"⎙\";\n}\n.md-typeset .keys .key-tab::after {\n  padding-left: 0.4em;\n  content: \"⇥\";\n}\n.md-typeset .keys .key-num-enter::after {\n  padding-left: 0.4em;\n  content: \"⌤\";\n}\n.md-typeset .keys .key-enter::after {\n  padding-left: 0.4em;\n  content: \"⏎\";\n}\n\n.md-typeset .tabbed-content {\n  display: none;\n  order: 99;\n  width: 100%;\n  box-shadow: 0 -0.05rem var(--md-default-fg-color--lightest);\n}\n@media print {\n  .md-typeset .tabbed-content {\n    display: block;\n    order: initial;\n  }\n}\n.md-typeset .tabbed-content > pre:only-child,\n.md-typeset .tabbed-content > .highlight:only-child pre,\n.md-typeset .tabbed-content > .highlighttable:only-child {\n  margin: 0;\n}\n.md-typeset .tabbed-content > pre:only-child > code,\n.md-typeset .tabbed-content > .highlight:only-child pre > code,\n.md-typeset .tabbed-content > .highlighttable:only-child > code {\n  border-top-left-radius: 0;\n  border-top-right-radius: 0;\n}\n.md-typeset .tabbed-content > .tabbed-set {\n  margin: 0;\n}\n.md-typeset .tabbed-set {\n  position: relative;\n  display: flex;\n  flex-wrap: wrap;\n  margin: 1em 0;\n  border-radius: 0.1rem;\n}\n.md-typeset .tabbed-set > input {\n  position: absolute;\n  width: 0;\n  height: 0;\n  opacity: 0;\n}\n.md-typeset .tabbed-set > input:checked + label {\n  color: var(--md-accent-fg-color);\n  border-color: var(--md-accent-fg-color);\n}\n.md-typeset .tabbed-set > input:checked + label + .tabbed-content {\n  display: block;\n}\n.md-typeset .tabbed-set > input:focus + label {\n  outline-style: auto;\n}\n.md-typeset .tabbed-set > input:not(.focus-visible) + label {\n  outline: none;\n  -webkit-tap-highlight-color: transparent;\n}\n.md-typeset .tabbed-set > label {\n  z-index: 1;\n  width: auto;\n  padding: 0.9375em 1.25em 0.78125em;\n  color: var(--md-default-fg-color--light);\n  font-weight: 700;\n  font-size: 0.64rem;\n  border-bottom: 0.1rem solid transparent;\n  cursor: pointer;\n  transition: color 250ms;\n}\n.md-typeset .tabbed-set > label:hover {\n  color: var(--md-accent-fg-color);\n}\n\n:root {\n  --md-tasklist-icon:\n    svg-load(\"octicons/check-circle-fill-24.svg\");\n  --md-tasklist-icon--checked:\n    svg-load(\"octicons/check-circle-fill-24.svg\");\n}\n\n.md-typeset .task-list-item {\n  position: relative;\n  list-style-type: none;\n}\n.md-typeset .task-list-item [type=checkbox] {\n  position: absolute;\n  top: 0.45em;\n  left: -2em;\n}\n[dir=rtl] .md-typeset .task-list-item [type=checkbox] {\n  right: -2em;\n  left: initial;\n}\n.md-typeset .task-list-control [type=checkbox] {\n  z-index: -1;\n  opacity: 0;\n}\n.md-typeset .task-list-indicator::before {\n  position: absolute;\n  top: 0.15em;\n  left: -1.5em;\n  width: 1.25em;\n  height: 1.25em;\n  background-color: var(--md-default-fg-color--lightest);\n  mask-image: var(--md-tasklist-icon);\n  mask-repeat: no-repeat;\n  mask-size: contain;\n  content: \"\";\n}\n[dir=rtl] .md-typeset .task-list-indicator::before {\n  right: -1.5em;\n  left: initial;\n}\n.md-typeset [type=checkbox]:checked + .task-list-indicator::before {\n  background-color: #00e676;\n  mask-image: var(--md-tasklist-icon--checked);\n}\n\n@media screen and (min-width: 45em) {\n  .md-typeset .inline {\n    float: left;\n    width: 11.7rem;\n    margin-top: 0;\n    margin-right: 0.8rem;\n    margin-bottom: 0.8rem;\n  }\n  [dir=rtl] .md-typeset .inline {\n    float: right;\n    margin-right: 0;\n    margin-left: 0.8rem;\n  }\n  .md-typeset .inline.end {\n    float: right;\n    margin-right: 0;\n    margin-left: 0.8rem;\n  }\n  [dir=rtl] .md-typeset .inline.end {\n    float: left;\n    margin-right: 0.8rem;\n    margin-left: 0;\n  }\n}\n\n/*# sourceMappingURL=main.css.map */", "////\n/// Copyright (c) 2016-2021 <PERSON> <<EMAIL>>\n///\n/// Permission is hereby granted, free of charge, to any person obtaining a\n/// copy of this software and associated documentation files (the \"Software\"),\n/// to deal in the Software without restriction, including without limitation\n/// the rights to use, copy, modify, merge, publish, distribute, sublicense,\n/// and/or sell copies of the Software, and to permit persons to whom the\n/// Software is furnished to do so, subject to the following conditions:\n///\n/// The above copyright notice and this permission notice shall be included in\n/// all copies or substantial portions of the Software.\n///\n/// THE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR\n/// IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,\n/// FITNESS FOR A PARTICULAR PURPOSE AND NON-INFRINGEMENT. IN NO EVENT SHALL\n/// THE AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER\n/// LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING\n/// FROM, OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER\n/// DEALINGS\n////\n\n// ----------------------------------------------------------------------------\n// Rules\n// ----------------------------------------------------------------------------\n\n// Enforce correct box model and prevent adjustments of font size after\n// orientation changes in IE and iOS\nhtml {\n  box-sizing: border-box;\n  text-size-adjust: none;\n}\n\n// All elements shall inherit the document default\n*,\n*::before,\n*::after {\n  box-sizing: inherit;\n}\n\n// Remove margin in all browsers\nbody {\n  margin: 0;\n}\n\n// Reset tap outlines on iOS and Android\na,\nbutton,\nlabel,\ninput {\n  -webkit-tap-highlight-color: transparent;\n}\n\n// Reset link styles\na {\n  color: inherit;\n  text-decoration: none;\n}\n\n// Normalize horizontal separator styles\nhr {\n  display: block;\n  box-sizing: content-box;\n  height: px2rem(1px);\n  padding: 0;\n  overflow: visible;\n  border: 0;\n}\n\n// Normalize font-size in all browsers\nsmall {\n  font-size: 80%;\n}\n\n// Prevent subscript and superscript from affecting line-height\nsub,\nsup {\n  line-height: 1em;\n}\n\n// Remove border on image\nimg {\n  border-style: none;\n}\n\n// Reset table styles\ntable {\n  border-collapse: separate;\n  border-spacing: 0;\n}\n\n// Reset table cell styles\ntd,\nth {\n  font-weight: 400;\n  vertical-align: top;\n}\n\n// Reset button styles\nbutton {\n  margin: 0;\n  padding: 0;\n  font-size: inherit;\n  background: transparent;\n  border: 0;\n}\n\n// Reset input styles\ninput {\n  border: 0;\n  outline: none;\n}\n", "////\n/// Copyright (c) 2016-2021 <PERSON> <<EMAIL>>\n///\n/// Permission is hereby granted, free of charge, to any person obtaining a\n/// copy of this software and associated documentation files (the \"Software\"),\n/// to deal in the Software without restriction, including without limitation\n/// the rights to use, copy, modify, merge, publish, distribute, sublicense,\n/// and/or sell copies of the Software, and to permit persons to whom the\n/// Software is furnished to do so, subject to the following conditions:\n///\n/// The above copyright notice and this permission notice shall be included in\n/// all copies or substantial portions of the Software.\n///\n/// THE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR\n/// IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,\n/// FITNESS FOR A PARTICULAR PURPOSE AND NON-INFRINGEMENT. IN NO EVENT SHALL\n/// THE AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER\n/// LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING\n/// FROM, OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER\n/// DEALINGS\n////\n\n// ----------------------------------------------------------------------------\n// Rules\n// ----------------------------------------------------------------------------\n\n// Color definitions\n:root {\n\n  // Default color shades\n  --md-default-fg-color:               hsla(0, 0%, 0%, 0.87);\n  --md-default-fg-color--light:        hsla(0, 0%, 0%, 0.54);\n  --md-default-fg-color--lighter:      hsla(0, 0%, 0%, 0.32);\n  --md-default-fg-color--lightest:     hsla(0, 0%, 0%, 0.07);\n  --md-default-bg-color:               hsla(0, 0%, 100%, 1);\n  --md-default-bg-color--light:        hsla(0, 0%, 100%, 0.7);\n  --md-default-bg-color--lighter:      hsla(0, 0%, 100%, 0.3);\n  --md-default-bg-color--lightest:     hsla(0, 0%, 100%, 0.12);\n\n  // Primary color shades\n  --md-primary-fg-color:               hsla(#{hex2hsl($clr-indigo-500)}, 1);\n  --md-primary-fg-color--light:        hsla(#{hex2hsl($clr-indigo-400)}, 1);\n  --md-primary-fg-color--dark:         hsla(#{hex2hsl($clr-indigo-700)}, 1);\n  --md-primary-bg-color:               hsla(0, 0%, 100%, 1);\n  --md-primary-bg-color--light:        hsla(0, 0%, 100%, 0.7);\n\n  // Accent color shades\n  --md-accent-fg-color:                hsla(#{hex2hsl($clr-indigo-a200)}, 1);\n  --md-accent-fg-color--transparent:   hsla(#{hex2hsl($clr-indigo-a200)}, 0.1);\n  --md-accent-bg-color:                hsla(0, 0%, 100%, 1);\n  --md-accent-bg-color--light:         hsla(0, 0%, 100%, 0.7);\n\n  // Light theme (default)\n  > * {\n\n    // Code color shades\n    --md-code-fg-color:                hsla(200, 18%, 26%, 1);\n    --md-code-bg-color:                hsla(0, 0%, 96%, 1);\n\n    // Code highlighting color shades\n    --md-code-hl-color:                hsla(#{hex2hsl($clr-yellow-a200)}, 0.5);\n    --md-code-hl-number-color:         hsla(0, 67%, 50%, 1);\n    --md-code-hl-special-color:        hsla(340, 83%, 47%, 1);\n    --md-code-hl-function-color:       hsla(291, 45%, 50%, 1);\n    --md-code-hl-constant-color:       hsla(250, 63%, 60%, 1);\n    --md-code-hl-keyword-color:        hsla(219, 54%, 51%, 1);\n    --md-code-hl-string-color:         hsla(150, 63%, 30%, 1);\n    --md-code-hl-name-color:           var(--md-code-fg-color);\n    --md-code-hl-operator-color:       var(--md-default-fg-color--light);\n    --md-code-hl-punctuation-color:    var(--md-default-fg-color--light);\n    --md-code-hl-comment-color:        var(--md-default-fg-color--light);\n    --md-code-hl-generic-color:        var(--md-default-fg-color--light);\n    --md-code-hl-variable-color:       var(--md-default-fg-color--light);\n\n    // Typeset color shades\n    --md-typeset-color:                var(--md-default-fg-color);\n    --md-typeset-a-color:              var(--md-primary-fg-color);\n\n    // Typeset `mark` color shades\n    --md-typeset-mark-color:           hsla(#{hex2hsl($clr-yellow-a200)}, 0.5);\n\n    // Typeset `del` and `ins` color shades\n    --md-typeset-del-color:            hsla(6, 90%, 60%, 0.15);\n    --md-typeset-ins-color:            hsla(150, 90%, 44%, 0.15);\n\n    // Typeset `kbd` color shades\n    --md-typeset-kbd-color:            hsla(0, 0%, 98%, 1);\n    --md-typeset-kbd-accent-color:     hsla(0, 100%, 100%, 1);\n    --md-typeset-kbd-border-color:     hsla(0, 0%, 72%, 1);\n\n    // Admonition color shades\n    --md-admonition-fg-color:          var(--md-default-fg-color);\n    --md-admonition-bg-color:          var(--md-default-bg-color);\n\n    // Footer color shades\n    --md-footer-fg-color:              hsla(0, 0%, 100%, 1);\n    --md-footer-fg-color--light:       hsla(0, 0%, 100%, 0.7);\n    --md-footer-fg-color--lighter:     hsla(0, 0%, 100%, 0.3);\n    --md-footer-bg-color:              hsla(0, 0%, 0%, 0.87);\n    --md-footer-bg-color--dark:        hsla(0, 0%, 0%, 0.32);\n  }\n}\n", "////\n/// Copyright (c) 2016-2021 <PERSON> <<EMAIL>>\n///\n/// Permission is hereby granted, free of charge, to any person obtaining a\n/// copy of this software and associated documentation files (the \"Software\"),\n/// to deal in the Software without restriction, including without limitation\n/// the rights to use, copy, modify, merge, publish, distribute, sublicense,\n/// and/or sell copies of the Software, and to permit persons to whom the\n/// Software is furnished to do so, subject to the following conditions:\n///\n/// The above copyright notice and this permission notice shall be included in\n/// all copies or substantial portions of the Software.\n///\n/// THE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR\n/// IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,\n/// FITNESS FOR A PARTICULAR PURPOSE AND NON-INFRINGEMENT. IN NO EVENT SHALL\n/// THE AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER\n/// LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING\n/// FROM, OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER\n/// DEALINGS\n////\n\n// ----------------------------------------------------------------------------\n// Rules\n// ----------------------------------------------------------------------------\n\n// Icon\n.md-icon {\n\n  // SVG defaults\n  svg {\n    display: block;\n    width: px2rem(24px);\n    height: px2rem(24px);\n    fill: currentColor;\n  }\n}\n", "////\n/// Copyright (c) 2016-2021 <PERSON> <<EMAIL>>\n///\n/// Permission is hereby granted, free of charge, to any person obtaining a\n/// copy of this software and associated documentation files (the \"Software\"),\n/// to deal in the Software without restriction, including without limitation\n/// the rights to use, copy, modify, merge, publish, distribute, sublicense,\n/// and/or sell copies of the Software, and to permit persons to whom the\n/// Software is furnished to do so, subject to the following conditions:\n///\n/// The above copyright notice and this permission notice shall be included in\n/// all copies or substantial portions of the Software.\n///\n/// THE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR\n/// IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,\n/// FITNESS FOR A PARTICULAR PURPOSE AND NON-INFRINGEMENT. IN NO EVENT SHALL\n/// THE AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER\n/// LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING\n/// FROM, OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER\n/// DEALINGS\n////\n\n// ----------------------------------------------------------------------------\n// Rules: font definitions\n// ----------------------------------------------------------------------------\n\n// Enable font-smoothing in Webkit and FF\nbody {\n  -webkit-font-smoothing: antialiased;\n  -moz-osx-font-smoothing: grayscale;\n}\n\n// Define default fonts\nbody,\ninput {\n  color: var(--md-typeset-color);\n  font-feature-settings: \"kern\", \"liga\";\n  font-family:\n    var(--md-text-font-family, _),\n    -apple-system, BlinkMacSystemFont, Helvetica, Arial, sans-serif;\n}\n\n// Define monospaced fonts\ncode,\npre,\nkbd {\n  color: var(--md-typeset-color);\n  font-feature-settings: \"kern\";\n  font-family:\n    var(--md-code-font-family, _),\n    SFMono-Regular, Consolas, Menlo, monospace;\n}\n\n// ----------------------------------------------------------------------------\n// Rules: typesetted content\n// ----------------------------------------------------------------------------\n\n// Icon definitions\n:root {\n  --md-typeset-table--ascending: svg-load(\"material/arrow-down.svg\");\n  --md-typeset-table--descending: svg-load(\"material/arrow-up.svg\");\n}\n\n// ----------------------------------------------------------------------------\n\n// Content that is typeset - if possible, all margins, paddings and font sizes\n// should be set in ems, so nested blocks (e.g. admonitions) render correctly.\n.md-typeset {\n  font-size: px2rem(16px);\n  line-height: 1.6;\n  color-adjust: exact;\n\n  // [print]: We'll use a smaller `font-size` for printing, so code examples\n  // don't break too early, and `16px` looks too big anyway.\n  @media print {\n    font-size: px2rem(13.6px);\n  }\n\n  // Default spacing\n  ul,\n  ol,\n  dl,\n  figure,\n  blockquote,\n  pre {\n    display: flow-root;\n    margin: 1em 0;\n  }\n\n  // Headline on level 1\n  h1 {\n    margin: 0 0 px2em(40px, 32px);\n    color: var(--md-default-fg-color--light);\n    font-weight: 300;\n    font-size: px2em(32px);\n    line-height: 1.3;\n    letter-spacing: -0.01em;\n  }\n\n  // Headline on level 2\n  h2 {\n    margin: px2em(40px, 25px) 0 px2em(16px, 25px);\n    font-weight: 300;\n    font-size: px2em(25px);\n    line-height: 1.4;\n    letter-spacing: -0.01em;\n  }\n\n  // Headline on level 3\n  h3 {\n    margin: px2em(32px, 20px) 0 px2em(16px, 20px);\n    font-weight: 400;\n    font-size: px2em(20px);\n    line-height: 1.5;\n    letter-spacing: -0.01em;\n  }\n\n  // Headline on level 3 following level 2\n  h2 + h3 {\n    margin-top: px2em(16px, 20px);\n  }\n\n  // Headline on level 4\n  h4 {\n    margin: px2em(16px) 0;\n    font-weight: 700;\n    letter-spacing: -0.01em;\n  }\n\n  // Headline on level 5-6\n  h5,\n  h6 {\n    margin: px2em(16px, 12.8px) 0;\n    color: var(--md-default-fg-color--light);\n    font-weight: 700;\n    font-size: px2em(12.8px);\n    letter-spacing: -0.01em;\n  }\n\n  // Headline on level 5\n  h5 {\n    text-transform: uppercase;\n  }\n\n  // Horizontal separator\n  hr {\n    display: flow-root;\n    margin: 1.5em 0;\n    border-bottom: px2rem(1px) solid var(--md-default-fg-color--lightest);\n  }\n\n  // Text link\n  a {\n    color: var(--md-typeset-a-color);\n    word-break: break-word;\n\n    // Also enable color transition on pseudo elements\n    &,\n    &::before {\n      transition: color 125ms;\n    }\n\n    // Text link on focus/hover\n    &:focus,\n    &:hover {\n      color: var(--md-accent-fg-color);\n    }\n  }\n\n  // Code block\n  code,\n  pre,\n  kbd {\n    color: var(--md-code-fg-color);\n    direction: ltr;\n\n    // [print]: Wrap text and hide scollbars\n    @media print {\n      white-space: pre-wrap;\n    }\n  }\n\n  // Inline code block\n  code {\n    padding: 0 px2em(4px, 13.6px);\n    font-size: px2em(13.6px);\n    word-break: break-word;\n    background-color: var(--md-code-bg-color);\n    border-radius: px2rem(2px);\n    box-decoration-break: clone;\n\n    // Hide outline for pointer devices\n    &:not(.focus-visible) {\n      outline: none;\n      -webkit-tap-highlight-color: transparent;\n    }\n  }\n\n  // Code block in headline\n  h1 code,\n  h2 code,\n  h3 code,\n  h4 code,\n  h5 code,\n  h6 code {\n    margin: initial;\n    padding: initial;\n    background-color: transparent;\n    box-shadow: none;\n  }\n\n  // Ensure link color in code blocks\n  a code {\n    color: currentColor;\n  }\n\n  // Unformatted content\n  pre {\n    position: relative;\n    line-height: 1.4;\n\n    // Code block\n    > code {\n      display: block;\n      margin: 0;\n      padding: px2em(10.5px, 13.6px) px2em(16px, 13.6px);\n      overflow: auto;\n      word-break: normal;\n      box-shadow: none;\n      box-decoration-break: slice;\n      touch-action: auto;\n      scrollbar-width: thin;\n      scrollbar-color: var(--md-default-fg-color--lighter) transparent;\n\n      // Code block on hover\n      &:hover {\n        scrollbar-color: var(--md-accent-fg-color) transparent;\n      }\n\n      // Webkit scrollbar\n      &::-webkit-scrollbar {\n        width: px2rem(4px);\n        height: px2rem(4px);\n      }\n\n      // Webkit scrollbar thumb\n      &::-webkit-scrollbar-thumb {\n        background-color: var(--md-default-fg-color--lighter);\n\n        // Webkit scrollbar thumb on hover\n        &:hover {\n          background-color: var(--md-accent-fg-color);\n        }\n      }\n    }\n  }\n\n  // [mobile -]: Align with body copy\n  @include break-to-device(mobile) {\n\n    // Unformatted text\n    > pre {\n      margin: 1em px2rem(-16px);\n\n      // Code block\n      code {\n        border-radius: 0;\n      }\n    }\n  }\n\n  // Keyboard key\n  kbd {\n    display: inline-block;\n    padding: 0 px2em(8px, 12px);\n    color: var(--md-default-fg-color);\n    font-size: px2em(12px);\n    vertical-align: text-top;\n    word-break: break-word;\n    background-color: var(--md-typeset-kbd-color);\n    border-radius: px2rem(2px);\n    box-shadow:\n      0 px2rem(2px)  0 px2rem(1px) var(--md-typeset-kbd-border-color),\n      0 px2rem(2px)  0             var(--md-typeset-kbd-border-color),\n      0 px2rem(-2px) px2rem(4px)   var(--md-typeset-kbd-accent-color) inset;\n  }\n\n  // Text highlighting marker\n  mark {\n    color: inherit;\n    word-break: break-word;\n    background-color: var(--md-typeset-mark-color);\n    box-decoration-break: clone;\n  }\n\n  // Abbreviation\n  abbr {\n    text-decoration: none;\n    border-bottom: px2rem(1px) dotted var(--md-default-fg-color--light);\n    cursor: help;\n\n    // Show tooltip for touch devices\n    @media (hover: none) {\n      position: relative;\n\n      // Tooltip\n      &[title]:focus::after,\n      &[title]:hover::after {\n        @include z-depth(2);\n\n        position: absolute;\n        left: 0;\n        display: inline-block;\n        width: auto;\n        min-width: max-content;\n        max-width: 80%;\n        margin-top: 2em;\n        padding: px2rem(4px) px2rem(6px);\n        color: var(--md-default-bg-color);\n        font-size: px2rem(14px);\n        background-color: var(--md-default-fg-color);\n        border-radius: px2rem(2px);\n        content: attr(title);\n      }\n    }\n  }\n\n  // Small text\n  small {\n    opacity: 0.75;\n  }\n\n  // Superscript and subscript\n  sup,\n  sub {\n    margin-left: px2em(1px, 12.8px);\n\n    // Adjust for right-to-left languages\n    [dir=\"rtl\"] & {\n      margin-right: px2em(1px, 12.8px);\n      margin-left: initial;\n    }\n  }\n\n  // Blockquotes, possibly nested\n  blockquote {\n    padding-left: px2rem(12px);\n    color: var(--md-default-fg-color--light);\n    border-left: px2rem(4px) solid var(--md-default-fg-color--lighter);\n\n    // Adjust for right-to-left languages\n    [dir=\"rtl\"] & {\n      padding-right: px2rem(12px);\n      padding-left: initial;\n      border-right: px2rem(4px) solid var(--md-default-fg-color--lighter);\n      border-left: initial;\n    }\n  }\n\n  // Unordered list\n  ul {\n    list-style-type: disc;\n  }\n\n  // Unordered and ordered list\n  ul,\n  ol {\n    margin-left: px2em(10px);\n    padding: 0;\n\n    // Adjust for right-to-left languages\n    [dir=\"rtl\"] & {\n      margin-right: px2em(10px);\n      margin-left: initial;\n    }\n\n    // Nested ordered list\n    ol {\n      list-style-type: lower-alpha;\n\n      // Triply nested ordered list\n      ol {\n        list-style-type: lower-roman;\n      }\n    }\n\n    // List element\n    li {\n      margin-bottom: 0.5em;\n      margin-left: px2em(20px);\n\n      // Adjust for right-to-left languages\n      [dir=\"rtl\"] & {\n        margin-right: px2em(20px);\n        margin-left: initial;\n      }\n\n      // Adjust spacing\n      p,\n      blockquote {\n        margin: 0.5em 0;\n      }\n\n      // Adjust spacing on last child\n      &:last-child {\n        margin-bottom: 0;\n      }\n\n      // Nested list\n      ul,\n      ol {\n        margin: 0.5em 0 0.5em px2em(10px);\n\n        // Adjust for right-to-left languages\n        [dir=\"rtl\"] & {\n          margin-right: px2em(10px);\n          margin-left: initial;\n        }\n      }\n    }\n  }\n\n  // Definition list\n  dd {\n    margin: 1em 0 1.5em px2em(30px);\n\n    // Adjust for right-to-left languages\n    [dir=\"rtl\"] & {\n      margin-right: px2em(30px);\n      margin-left: initial;\n    }\n  }\n\n  // Image or icon\n  img,\n  svg {\n    max-width: 100%;\n    height: auto;\n\n    // Adjust spacing when left-aligned\n    &[align=\"left\"] {\n      margin: 1em;\n      margin-left: 0;\n    }\n\n    // Adjust spacing when right-aligned\n    &[align=\"right\"] {\n      margin: 1em;\n      margin-right: 0;\n    }\n\n    // Adjust spacing when sole children\n    &[align]:only-child {\n      margin-top: 0;\n    }\n  }\n\n  // Figure\n  figure {\n    width: fit-content;\n    max-width: 100%;\n    margin: 0 auto;\n    text-align: center;\n\n    // Figure images\n    img {\n      display: block;\n    }\n  }\n\n  // Figure caption\n  figcaption {\n    max-width: px2rem(480px);\n    margin: 1em auto 2em;\n    font-style: italic;\n  }\n\n  // Limit width to container\n  iframe {\n    max-width: 100%;\n  }\n\n  // Data table\n  table:not([class]) {\n    display: inline-block;\n    max-width: 100%;\n    overflow: auto;\n    font-size: px2rem(12.8px);\n    background-color: var(--md-default-bg-color);\n    border-radius: px2rem(2px);\n    box-shadow:\n      0 px2rem(4px) px2rem(10px) hsla(0, 0%, 0%, 0.05),\n      0 0           px2rem(1px)  hsla(0, 0%, 0%, 0.1);\n    touch-action: auto;\n\n    // [print]: Reset display mode so table header wraps when printing\n    @media print {\n      display: table;\n    }\n\n    // Due to margin collapse because of the necessary inline-block hack, we\n    // cannot increase the bottom margin on the table, so we just increase the\n    // top margin on the following element\n    + * {\n      margin-top: 1.5em;\n    }\n\n    // Elements in table heading and cell\n    th > *,\n    td > * {\n\n      // Adjust spacing on first child\n      &:first-child {\n        margin-top: 0;\n      }\n\n      // Adjust spacing on last child\n      &:last-child {\n        margin-bottom: 0;\n      }\n    }\n\n    // Table heading and cell\n    th:not([align]),\n    td:not([align]) {\n      text-align: left;\n\n      // Adjust for right-to-left languages\n      [dir=\"rtl\"] & {\n        text-align: right;\n      }\n    }\n\n    // Table heading\n    th {\n      min-width: px2rem(100px);\n      padding: px2em(12px, 12.8px) px2em(16px, 12.8px);\n      color: var(--md-default-bg-color);\n      vertical-align: top;\n      background-color: var(--md-default-fg-color--light);\n\n      // Links in table headings\n      a {\n        color: inherit;\n      }\n    }\n\n    // Table cell\n    td {\n      padding: px2em(12px, 12.8px) px2em(16px, 12.8px);\n      vertical-align: top;\n      border-top: px2rem(1px) solid var(--md-default-fg-color--lightest);\n    }\n\n    // Table row\n    tr {\n      transition: background-color 125ms;\n\n      // Table row on hover\n      &:hover {\n        background-color: rgba(0, 0, 0, 0.035);\n        box-shadow: 0 px2rem(1px) 0 var(--md-default-bg-color) inset;\n      }\n\n      // Hide border on first table row\n      &:first-child td {\n        border-top: 0;\n      }\n    }\n\n    // Text link in table\n    a {\n      word-break: normal;\n    }\n  }\n\n  // Sortable table\n  table th[role=\"columnheader\"] {\n    cursor: pointer;\n\n    // Sort icon\n    &::after {\n      display: inline-block;\n      width: 1.2em;\n      height: 1.2em;\n      margin-left: 0.5em;\n      vertical-align: sub;\n      mask-repeat: no-repeat;\n      mask-size: contain;\n      content: \"\";\n    }\n\n    // Sort ascending\n    &[aria-sort=\"ascending\"]::after {\n      background-color: currentColor;\n      mask-image: var(--md-typeset-table--ascending);\n    }\n\n    // Sort descending\n    &[aria-sort=\"descending\"]::after {\n      background-color: currentColor;\n      mask-image: var(--md-typeset-table--descending);\n    }\n  }\n\n  // Data table scroll wrapper\n  &__scrollwrap {\n    margin: 1em px2rem(-16px);\n    overflow-x: auto;\n    touch-action: auto;\n  }\n\n  // Data table wrapper\n  &__table {\n    display: inline-block;\n    margin-bottom: 0.5em;\n    padding: 0 px2rem(16px);\n\n    // [print]: Reset display mode so table header wraps when printing\n    @media print {\n      display: block;\n    }\n\n    // Data table\n    html & table {\n      display: table;\n      width: 100%;\n      margin: 0;\n      overflow: hidden;\n    }\n  }\n}\n", "////\n/// Copyright (c) 2016-2021 <PERSON> <<EMAIL>>\n///\n/// Permission is hereby granted, free of charge, to any person obtaining a\n/// copy of this software and associated documentation files (the \"Software\"),\n/// to deal in the Software without restriction, including without limitation\n/// the rights to use, copy, modify, merge, publish, distribute, sublicense,\n/// and/or sell copies of the Software, and to permit persons to whom the\n/// Software is furnished to do so, subject to the following conditions:\n///\n/// The above copyright notice and this permission notice shall be included in\n/// all copies or substantial portions of the Software.\n///\n/// THE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR\n/// IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,\n/// FITNESS FOR A PARTICULAR PURPOSE AND NON-INFRINGEMENT. IN NO EVENT SHALL\n/// THE AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER\n/// LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING\n/// FROM, OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER\n/// DEALINGS\n////\n\n// ----------------------------------------------------------------------------\n// Variables\n// ----------------------------------------------------------------------------\n\n///\n/// Device-specific breakpoints\n///\n/// @example\n///   $break-devices: (\n///     mobile: (\n///       portrait:  220px  479px,\n///       landscape: 480px  719px\n///     ),\n///     tablet: (\n///       portrait:  720px  959px,\n///       landscape: 960px  1219px\n///     ),\n///     screen: (\n///       small:     1220px 1599px,\n///       medium:    1600px 1999px,\n///       large:     2000px\n///     )\n///   );\n///\n$break-devices: () !default;\n\n// ----------------------------------------------------------------------------\n// Helpers\n// ----------------------------------------------------------------------------\n\n///\n/// Choose minimum and maximum device widths\n///\n@function break-select-min-max($devices) {\n  $min: 1000000;\n  $max: 0;\n  @each $key, $value in $devices {\n    @while type-of($value) == map {\n      $value: break-select-min-max($value);\n    }\n    @if type-of($value) == list {\n      @each $number in $value {\n        @if type-of($number) == number {\n          $min: min($number, $min);\n          @if $max {\n            $max: max($number, $max);\n          }\n        } @else {\n          @error \"Invalid number: #{$number}\";\n        }\n      }\n    } @else if type-of($value) == number {\n      $min: min($value, $min);\n      $max: null;\n    } @else {\n      @error \"Invalid value: #{$value}\";\n    }\n  }\n  @return $min, $max;\n}\n\n///\n/// Select minimum and maximum widths for a device breakpoint\n///\n@function break-select-device($device) {\n  $current: $break-devices;\n  @for $n from 1 through length($device) {\n    @if type-of($current) == map {\n      $current: map-get($current, nth($device, $n));\n    } @else {\n      @error \"Invalid device map: #{$devices}\";\n    }\n  }\n  @if type-of($current) == list or type-of($current) == number {\n    $current: (default: $current);\n  }\n  @return break-select-min-max($current);\n}\n\n// ----------------------------------------------------------------------------\n// Mixins\n// ----------------------------------------------------------------------------\n\n///\n/// A minimum-maximum media query breakpoint\n///\n@mixin break-at($breakpoint) {\n  @if type-of($breakpoint) == number {\n    @media screen and (min-width: $breakpoint) {\n      @content;\n    }\n  } @else if type-of($breakpoint) == list {\n    $min: nth($breakpoint, 1);\n    $max: nth($breakpoint, 2);\n    @if type-of($min) == number and type-of($max) == number {\n      @media screen and (min-width: $min) and (max-width: $max) {\n        @content;\n      }\n    } @else {\n      @error \"Invalid breakpoint: #{$breakpoint}\";\n    }\n  } @else {\n    @error \"Invalid breakpoint: #{$breakpoint}\";\n  }\n}\n\n///\n/// An orientation media query breakpoint\n///\n@mixin break-at-orientation($breakpoint) {\n  @if type-of($breakpoint) == string {\n    @media screen and (orientation: $breakpoint) {\n      @content;\n    }\n  } @else {\n    @error \"Invalid breakpoint: #{$breakpoint}\";\n  }\n}\n\n///\n/// A maximum-aspect-ratio media query breakpoint\n///\n@mixin break-at-ratio($breakpoint) {\n  @if type-of($breakpoint) == number {\n    @media screen and (max-aspect-ratio: $breakpoint) {\n      @content;\n    }\n  } @else {\n    @error \"Invalid breakpoint: #{$breakpoint}\";\n  }\n}\n\n///\n/// A minimum-maximum media query device breakpoint\n///\n@mixin break-at-device($device) {\n  @if type-of($device) == string {\n    $device: $device,;\n  }\n  @if type-of($device) == list {\n    $breakpoint: break-select-device($device);\n    @if nth($breakpoint, 2) {\n      $min: nth($breakpoint, 1);\n      $max: nth($breakpoint, 2);\n\n      @media screen and (min-width: $min) and (max-width: $max) {\n        @content;\n      }\n    } @else {\n      @error \"Invalid device: #{$device}\";\n    }\n  } @else {\n    @error \"Invalid device: #{$device}\";\n  }\n}\n\n///\n/// A minimum media query device breakpoint\n///\n@mixin break-from-device($device) {\n  @if type-of($device) == string {\n    $device: $device,;\n  }\n  @if type-of($device) == list {\n    $breakpoint: break-select-device($device);\n    $min: nth($breakpoint, 1);\n\n    @media screen and (min-width: $min) {\n      @content;\n    }\n  } @else {\n    @error \"Invalid device: #{$device}\";\n  }\n}\n\n///\n/// A maximum media query device breakpoint\n///\n@mixin break-to-device($device) {\n  @if type-of($device) == string {\n    $device: $device,;\n  }\n  @if type-of($device) == list {\n    $breakpoint: break-select-device($device);\n    $max: nth($breakpoint, 2);\n\n    @media screen and (max-width: $max) {\n      @content;\n    }\n  } @else {\n    @error \"Invalid device: #{$device}\";\n  }\n}\n", "//\n// Name:           Material Shadows\n// Description:    Mixins for Material Design Shadows.\n// Version:        3.0.1\n//\n// Author:         <PERSON>\n// Git:            https://github.com/mrmlnc/material-shadows\n//\n// twitter:        @mrmlnc\n//\n// ------------------------------------\n\n\n// Mixins\n// ------------------------------------\n\n@mixin z-depth-transition() {\n  transition: box-shadow .28s cubic-bezier(.4, 0, .2, 1);\n}\n\n@mixin z-depth-focus() {\n  box-shadow: 0 0 8px rgba(0, 0, 0, .18), 0 8px 16px rgba(0, 0, 0, .36);\n}\n\n@mixin z-depth-2dp() {\n  box-shadow: 0 2px 2px 0 rgba(0, 0, 0, .14),\n              0 1px 5px 0 rgba(0, 0, 0, .12),\n              0 3px 1px -2px rgba(0, 0, 0, .2);\n}\n\n@mixin z-depth-3dp() {\n  box-shadow: 0 3px 4px 0 rgba(0, 0, 0, .14),\n              0 1px 8px 0 rgba(0, 0, 0, .12),\n              0 3px 3px -2px rgba(0, 0, 0, .4);\n}\n\n@mixin z-depth-4dp() {\n  box-shadow: 0 4px 5px 0 rgba(0, 0, 0, .14),\n              0 1px 10px 0 rgba(0, 0, 0, .12),\n              0 2px 4px -1px rgba(0, 0, 0, .4);\n}\n\n@mixin z-depth-6dp() {\n  box-shadow: 0 6px 10px 0 rgba(0, 0, 0, .14),\n              0 1px 18px 0 rgba(0, 0, 0, .12),\n              0 3px 5px -1px rgba(0, 0, 0, .4);\n}\n\n@mixin z-depth-8dp() {\n  box-shadow: 0 8px 10px 1px rgba(0, 0, 0, .14),\n              0 3px 14px 2px rgba(0, 0, 0, .12),\n              0 5px 5px -3px rgba(0, 0, 0, .4);\n}\n\n@mixin z-depth-16dp() {\n  box-shadow: 0 16px 24px 2px rgba(0, 0, 0, .14),\n              0  6px 30px 5px rgba(0, 0, 0, .12),\n              0  8px 10px -5px rgba(0, 0, 0, .4);\n}\n\n@mixin z-depth-24dp() {\n  box-shadow: 0  9px 46px  8px rgba(0, 0, 0, .14),\n              0 24px 38px  3px rgba(0, 0, 0, .12),\n              0 11px 15px -7px rgba(0, 0, 0, .4);\n}\n\n@mixin z-depth($dp: 2) {\n  @if $dp == 2 {\n    @include z-depth-2dp();\n  } @else if $dp == 3 {\n    @include z-depth-3dp();\n  } @else if $dp == 4 {\n    @include z-depth-4dp();\n  } @else if $dp == 6 {\n    @include z-depth-6dp();\n  } @else if $dp == 8 {\n    @include z-depth-8dp();\n  } @else if $dp == 16 {\n    @include z-depth-16dp();\n  } @else if $dp == 24 {\n    @include z-depth-24dp();\n  }\n}\n\n\n// Class generator\n// ------------------------------------\n\n@mixin z-depth-classes($transition: false, $focus: false) {\n  @if $transition == true {\n    &-transition {\n      @include z-depth-transition();\n    }\n  }\n\n  @if $focus == true {\n    &-focus {\n      @include z-depth-focus();\n    }\n  }\n\n  // The available values for the shadow depth\n  @each $depth in 2, 3, 4, 6, 8, 16, 24 {\n    &-#{$depth}dp {\n      @include z-depth($depth);\n    }\n  }\n}\n", "////\n/// Copyright (c) 2016-2021 <PERSON> <<EMAIL>>\n///\n/// Permission is hereby granted, free of charge, to any person obtaining a\n/// copy of this software and associated documentation files (the \"Software\"),\n/// to deal in the Software without restriction, including without limitation\n/// the rights to use, copy, modify, merge, publish, distribute, sublicense,\n/// and/or sell copies of the Software, and to permit persons to whom the\n/// Software is furnished to do so, subject to the following conditions:\n///\n/// The above copyright notice and this permission notice shall be included in\n/// all copies or substantial portions of the Software.\n///\n/// THE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR\n/// IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,\n/// FITNESS FOR A PARTICULAR PURPOSE AND NON-INFRINGEMENT. IN NO EVENT SHALL\n/// THE AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER\n/// LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING\n/// FROM, OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER\n/// DEALINGS\n////\n\n// ----------------------------------------------------------------------------\n// Rules: base grid and containers\n// ----------------------------------------------------------------------------\n\n// Stretch container to viewport and set base `font-size`\nhtml {\n  height: 100%;\n  overflow-x: hidden;\n  // Hack: normally, we would set the base `font-size` to `62.5%`, so we can\n  // base all calculations on `10px`, but Chromium and Chrome define a minimal\n  // `font-size` of `12px` if the system language is set to Chinese. For this\n  // reason we just double the `font-size` and set it to `20px`.\n  //\n  // See https://github.com/squidfunk/mkdocs-material/issues/911\n  font-size: 125%;\n\n  // [screen medium +]: Set base `font-size` to `11px`\n  @include break-from-device(screen medium) {\n    font-size: 137.5%;\n  }\n\n  // [screen large +]: Set base `font-size` to `12px`\n  @include break-from-device(screen large) {\n    font-size: 150%;\n  }\n}\n\n// Stretch body to container - flexbox is used, so the footer will always be\n// aligned to the bottom of the viewport\nbody {\n  position: relative;\n  display: flex;\n  flex-direction: column;\n  width: 100%;\n  min-height: 100%;\n  // Hack: reset `font-size` to `10px`, so the spacing for all inline elements\n  // is correct again. Otherwise the spacing would be based on `20px`.\n  font-size: px2rem(10px);\n  background-color: var(--md-default-bg-color);\n\n  // [print]: Omit flexbox layout due to a Firefox bug (https://mzl.la/39DgR3m)\n  @media print {\n    display: block;\n  }\n\n  // Body in locked state\n  &[data-md-state=\"lock\"] {\n\n    // [tablet portrait -]: Omit scroll bubbling\n    @include break-to-device(tablet portrait) {\n      position: fixed;\n    }\n  }\n}\n\n// ----------------------------------------------------------------------------\n\n// Grid container - this class is applied to wrapper elements within the\n// header, content area and footer, and makes sure that their width is limited\n// to `1220px`, and they are rendered centered if the screen is larger.\n.md-grid {\n  max-width: px2rem(1220px);\n  margin-right: auto;\n  margin-left: auto;\n}\n\n// Main container\n.md-container {\n  display: flex;\n  flex-direction: column;\n  flex-grow: 1;\n\n  // [print]: Omit flexbox layout due to a Firefox bug (https://mzl.la/39DgR3m)\n  @media print {\n    display: block;\n  }\n}\n\n// Main area - stretch to remaining space of container\n.md-main {\n  flex-grow: 1;\n\n  // Main area wrapper\n  &__inner {\n    display: flex;\n    height: 100%;\n    margin-top: px2rem(24px + 6px);\n  }\n}\n\n// Add ellipsis in case of overflowing text\n.md-ellipsis {\n  overflow: hidden;\n  white-space: nowrap;\n  text-overflow: ellipsis;\n}\n\n// ----------------------------------------------------------------------------\n// Rules: navigational elements\n// ----------------------------------------------------------------------------\n\n// Toggle - this class is applied to the checkbox elements, which are used to\n// implement the CSS-only drawer and navigation, as well as the search\n.md-toggle {\n  display: none;\n}\n\n// Skip link\n.md-skip {\n  position: fixed;\n  // Hack: if we don't set the negative `z-index`, the skip link will force the\n  // creation of new layers when code blocks are near the header on scrolling\n  z-index: -1;\n  margin: px2rem(10px);\n  padding: px2rem(6px) px2rem(10px);\n  color: var(--md-default-bg-color);\n  font-size: px2rem(12.8px);\n  background-color: var(--md-default-fg-color);\n  border-radius: px2rem(2px);\n  transform: translateY(px2rem(8px));\n  opacity: 0;\n\n  // Show skip link on focus\n  &:focus {\n    z-index: 10;\n    transform: translateY(0);\n    opacity: 1;\n    transition:\n      transform 250ms cubic-bezier(0.4, 0, 0.2, 1),\n      opacity   175ms 75ms;\n  }\n}\n\n// ----------------------------------------------------------------------------\n// Rules: print styles\n// ----------------------------------------------------------------------------\n\n// Add margins to page\n@page {\n  margin: 25mm;\n}\n", "////\n/// Copyright (c) 2016-2021 <PERSON> <<EMAIL>>\n///\n/// Permission is hereby granted, free of charge, to any person obtaining a\n/// copy of this software and associated documentation files (the \"Software\"),\n/// to deal in the Software without restriction, including without limitation\n/// the rights to use, copy, modify, merge, publish, distribute, sublicense,\n/// and/or sell copies of the Software, and to permit persons to whom the\n/// Software is furnished to do so, subject to the following conditions:\n///\n/// The above copyright notice and this permission notice shall be included in\n/// all copies or substantial portions of the Software.\n///\n/// THE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR\n/// IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,\n/// FITNESS FOR A PARTICULAR PURPOSE AND NON-INFRINGEMENT. IN NO EVENT SHALL\n/// THE AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER\n/// LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING\n/// FROM, OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER\n/// DEALINGS\n////\n\n// ----------------------------------------------------------------------------\n// Rules\n// ----------------------------------------------------------------------------\n\n// Announcement bar\n.md-announce {\n  overflow: auto;\n  background-color: var(--md-footer-bg-color);\n\n  // [print]: Hide announcement bar\n  @media print {\n    display: none;\n  }\n\n  // Announcement wrapper\n  &__inner {\n    margin: px2rem(12px) auto;\n    padding: 0 px2rem(16px);\n    color: var(--md-footer-fg-color);\n    font-size: px2rem(14px);\n  }\n}\n", "////\n/// Copyright (c) 2016-2021 <PERSON> <<EMAIL>>\n///\n/// Permission is hereby granted, free of charge, to any person obtaining a\n/// copy of this software and associated documentation files (the \"Software\"),\n/// to deal in the Software without restriction, including without limitation\n/// the rights to use, copy, modify, merge, publish, distribute, sublicense,\n/// and/or sell copies of the Software, and to permit persons to whom the\n/// Software is furnished to do so, subject to the following conditions:\n///\n/// The above copyright notice and this permission notice shall be included in\n/// all copies or substantial portions of the Software.\n///\n/// THE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR\n/// IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,\n/// FITNESS FOR A PARTICULAR PURPOSE AND NON-INFRINGEMENT. IN NO EVENT SHALL\n/// THE AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER\n/// LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING\n/// FROM, OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER\n/// DEALINGS\n////\n\n// ----------------------------------------------------------------------------\n// Rules\n// ----------------------------------------------------------------------------\n\n// Icon definitions\n:root {\n  --md-clipboard-icon: svg-load(\"material/content-copy.svg\");\n}\n\n// ----------------------------------------------------------------------------\n\n// Button to copy to clipboard\n.md-clipboard {\n  position: absolute;\n  top: px2em(8px);\n  right: px2em(8px);\n  z-index: 1;\n  width: px2em(24px);\n  height: px2em(24px);\n  color: var(--md-default-fg-color--lightest);\n  border-radius: px2rem(2px);\n  cursor: pointer;\n  transition: color 250ms;\n\n  // [print]: Hide button\n  @media print {\n    display: none;\n  }\n\n  // Hide outline for pointer devices\n  &:not(.focus-visible) {\n    outline: none;\n    -webkit-tap-highlight-color: transparent;\n  }\n\n  // Darken color on code block hover\n  :hover > & {\n    color: var(--md-default-fg-color--light);\n  }\n\n  // Button on focus/hover\n  &:focus,\n  &:hover {\n    color: var(--md-accent-fg-color);\n  }\n\n  // Button icon - the width and height are defined in `em`, so the size is\n  // automatically adjusted for nested code blocks (e.g. in admonitions)\n  &::after {\n    display: block;\n    width: px2em(18px);\n    height: px2em(18px);\n    margin: 0 auto;\n    background-color: currentColor;\n    mask-image: var(--md-clipboard-icon);\n    mask-repeat: no-repeat;\n    mask-size: contain;\n    content: \"\";\n  }\n\n  // Inline button\n  &--inline {\n    cursor: pointer;\n\n    // Code block\n    code {\n      transition:\n        color            250ms,\n        background-color 250ms;\n    }\n\n    // Code block on focus/hover\n    &:focus code,\n    &:hover code {\n      color: var(--md-accent-fg-color);\n      background-color: var(--md-accent-fg-color--transparent);\n    }\n  }\n}\n", "////\n/// Copyright (c) 2016-2021 <PERSON> <<EMAIL>>\n///\n/// Permission is hereby granted, free of charge, to any person obtaining a\n/// copy of this software and associated documentation files (the \"Software\"),\n/// to deal in the Software without restriction, including without limitation\n/// the rights to use, copy, modify, merge, publish, distribute, sublicense,\n/// and/or sell copies of the Software, and to permit persons to whom the\n/// Software is furnished to do so, subject to the following conditions:\n///\n/// The above copyright notice and this permission notice shall be included in\n/// all copies or substantial portions of the Software.\n///\n/// THE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR\n/// IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,\n/// FITNESS FOR A PARTICULAR PURPOSE AND NON-INFRINGEMENT. IN NO EVENT SHALL\n/// THE AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER\n/// LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING\n/// FROM, OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER\n/// DEALINGS\n////\n\n// ----------------------------------------------------------------------------\n// Rules\n// ----------------------------------------------------------------------------\n\n// Content area\n.md-content {\n  flex-grow: 1;\n  // Hack: we must use `overflow: hidden`, so the content area is capped by\n  // the dimensions of its parent. Otherwise, long code blocks might lead to\n  // a wider content area which will break everything. This, however, induces\n  // margin collapse, which will break scroll margins. Adding a large enough\n  // scroll padding seems to do the trick, at least in Chrome and Firefox.\n  overflow: hidden;\n  scroll-padding-top: px2rem(1024px);\n\n  // Content wrapper\n  &__inner {\n    margin: 0 px2rem(16px) px2rem(24px);\n    padding-top: px2rem(12px);\n\n    // [screen +]: Adjust spacing between content area and sidebars\n    @include break-from-device(screen) {\n\n      // Sidebar with navigation is visible\n      .md-sidebar--primary:not([hidden]) ~ .md-content > & {\n        margin-left: px2rem(24px);\n\n        // Adjust for right-to-left languages\n        [dir=\"rtl\"] & {\n          margin-right: px2rem(24px);\n          margin-left: px2rem(16px);\n        }\n      }\n\n      // Sidebar with table of contents is visible\n      .md-sidebar--secondary:not([hidden]) ~ .md-content > & {\n        margin-right: px2rem(24px);\n\n        // Adjust for right-to-left languages\n        [dir=\"rtl\"] & {\n          margin-right: px2rem(16px);\n          margin-left: px2rem(24px);\n        }\n      }\n    }\n\n    // Hack: add pseudo element for spacing, as the overflow of the content\n    // container may not be hidden due to an imminent offset error on targets\n    &::before {\n      display: block;\n      height: px2rem(8px);\n      content: \"\";\n    }\n\n    // Adjust spacing on last child\n    > :last-child {\n      margin-bottom: 0;\n    }\n  }\n\n  // Button inside of the content area - these buttons are meant for actions on\n  // a document-level, i.e. linking to related source code files, printing etc.\n  &__button {\n    float: right;\n    margin: px2rem(8px) 0;\n    margin-left: px2rem(8px);\n    padding: 0;\n\n    // [print]: Hide buttons\n    @media print {\n      display: none;\n    }\n\n    // Adjust for right-to-left languages\n    [dir=\"rtl\"] & {\n      float: left;\n      margin-right: px2rem(8px);\n      margin-left: initial;\n\n      // Flip icon vertically\n      svg {\n        transform: scaleX(-1);\n      }\n    }\n\n    // Adjust default link color for icons\n    .md-typeset & {\n      color: var(--md-default-fg-color--lighter);\n    }\n\n    // Align with body copy located next to icon\n    svg {\n      display: inline;\n      vertical-align: top;\n    }\n  }\n}\n", "////\n/// Copyright (c) 2016-2021 <PERSON> <<EMAIL>>\n///\n/// Permission is hereby granted, free of charge, to any person obtaining a\n/// copy of this software and associated documentation files (the \"Software\"),\n/// to deal in the Software without restriction, including without limitation\n/// the rights to use, copy, modify, merge, publish, distribute, sublicense,\n/// and/or sell copies of the Software, and to permit persons to whom the\n/// Software is furnished to do so, subject to the following conditions:\n///\n/// The above copyright notice and this permission notice shall be included in\n/// all copies or substantial portions of the Software.\n///\n/// THE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR\n/// IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,\n/// FITNESS FOR A PARTICULAR PURPOSE AND NON-INFRINGEMENT. IN NO EVENT SHALL\n/// THE AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER\n/// LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING\n/// FROM, OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER\n/// DEALINGS\n////\n\n// ----------------------------------------------------------------------------\n// Rules\n// ----------------------------------------------------------------------------\n\n// Dialog\n.md-dialog {\n  @include z-depth(2);\n\n  position: fixed;\n  right: px2rem(16px);\n  bottom: px2rem(16px);\n  left: initial;\n  z-index: 2;\n  min-width: px2rem(222px);\n  padding: px2rem(8px) px2rem(12px);\n  background-color: var(--md-default-fg-color);\n  border-radius: px2rem(2px);\n  transform: translateY(100%);\n  opacity: 0;\n  transition:\n    transform 0ms   400ms,\n    opacity   400ms;\n  pointer-events: none;\n\n  // [print]: Hide dialog\n  @media print {\n    display: none;\n  }\n\n  // Adjust for right-to-left languages\n  [dir=\"rtl\"] & {\n    right: initial;\n    left: px2rem(16px);\n  }\n\n  // Dialog in open state\n  &[data-md-state=\"open\"] {\n    transform: translateY(0);\n    opacity: 1;\n    transition:\n      transform 400ms cubic-bezier(0.075, 0.85, 0.175, 1),\n      opacity   400ms;\n    pointer-events: initial;\n  }\n\n  // Dialog wrapper\n  &__inner {\n    color: var(--md-default-bg-color);\n    font-size: px2rem(14px);\n  }\n}\n", "////\n/// Copyright (c) 2016-2021 <PERSON> <<EMAIL>>\n///\n/// Permission is hereby granted, free of charge, to any person obtaining a\n/// copy of this software and associated documentation files (the \"Software\"),\n/// to deal in the Software without restriction, including without limitation\n/// the rights to use, copy, modify, merge, publish, distribute, sublicense,\n/// and/or sell copies of the Software, and to permit persons to whom the\n/// Software is furnished to do so, subject to the following conditions:\n///\n/// The above copyright notice and this permission notice shall be included in\n/// all copies or substantial portions of the Software.\n///\n/// THE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR\n/// IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,\n/// FITNESS FOR A PARTICULAR PURPOSE AND NON-INFRINGEMENT. IN NO EVENT SHALL\n/// THE AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER\n/// LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING\n/// FROM, OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER\n/// DEALINGS\n////\n\n// ----------------------------------------------------------------------------\n// Rules\n// ----------------------------------------------------------------------------\n\n// Scoped in typesetted content to match specificity of regular content\n.md-typeset {\n\n  // Form button\n  .md-button {\n    display: inline-block;\n    padding: px2em(10px) px2em(32px);\n    color: var(--md-primary-fg-color);\n    font-weight: 700;\n    border: px2rem(2px) solid currentColor;\n    border-radius: px2rem(2px);\n    transition:\n      color            125ms,\n      background-color 125ms,\n      border-color     125ms;\n\n    // Primary button\n    &--primary {\n      color: var(--md-primary-bg-color);\n      background-color: var(--md-primary-fg-color);\n      border-color: var(--md-primary-fg-color);\n    }\n\n    // Button on focus/hover\n    &:focus,\n    &:hover {\n      color: var(--md-accent-bg-color);\n      background-color: var(--md-accent-fg-color);\n      border-color: var(--md-accent-fg-color);\n    }\n  }\n\n  // Form input\n  .md-input {\n    height: px2rem(36px);\n    padding: 0 px2rem(12px);\n    font-size: px2rem(16px);\n    border-radius: px2rem(2px);\n    box-shadow:\n      0 px2rem(4px)   px2rem(10px) hsla(0, 0%, 0%, 0.1),\n      0 px2rem(0.5px) px2rem(1px)  hsla(0, 0%, 0%, 0.1);\n    transition: box-shadow 250ms;\n\n    // Input on focus/hover\n    &:focus,\n    &:hover {\n      box-shadow:\n        0 px2rem(8px)   px2rem(20px) hsla(0, 0%, 0%, 0.15),\n        0 px2rem(0.5px) px2rem(1px)  hsla(0, 0%, 0%, 0.15);\n    }\n\n    // Stretch to full width\n    &--stretch {\n      width: 100%;\n    }\n  }\n}\n", "////\n/// Copyright (c) 2016-2021 <PERSON> <<EMAIL>>\n///\n/// Permission is hereby granted, free of charge, to any person obtaining a\n/// copy of this software and associated documentation files (the \"Software\"),\n/// to deal in the Software without restriction, including without limitation\n/// the rights to use, copy, modify, merge, publish, distribute, sublicense,\n/// and/or sell copies of the Software, and to permit persons to whom the\n/// Software is furnished to do so, subject to the following conditions:\n///\n/// The above copyright notice and this permission notice shall be included in\n/// all copies or substantial portions of the Software.\n///\n/// THE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR\n/// IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,\n/// FITNESS FOR A PARTICULAR PURPOSE AND NON-INFRINGEMENT. IN NO EVENT SHALL\n/// THE AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER\n/// LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING\n/// FROM, OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER\n/// DEALINGS\n////\n\n// ----------------------------------------------------------------------------\n// Rules\n// ----------------------------------------------------------------------------\n\n// Header - by default, the header will be sticky and stay always on top of the\n// viewport. If this behavior is not desired, just set `position: static`.\n.md-header {\n  position: sticky;\n  top: 0;\n  right: 0;\n  left: 0;\n  z-index: 2;\n  color: var(--md-primary-bg-color);\n  background-color: var(--md-primary-fg-color);\n  // Hack: reduce jitter by adding a transparent box shadow of the same size\n  // so the size of the layer doesn't change during animation\n  box-shadow:\n    0 0           px2rem(4px) rgba(0, 0, 0, 0),\n    0 px2rem(4px) px2rem(8px) rgba(0, 0, 0, 0);\n  transition:\n    color            250ms,\n    background-color 250ms;\n\n  // [print]: Hide header\n  @media print {\n    display: none;\n  }\n\n  // Header in shadow state, i.e. shadow is visible\n  &[data-md-state=\"shadow\"] {\n    box-shadow:\n      0 0           px2rem(4px) rgba(0, 0, 0, 0.1),\n      0 px2rem(4px) px2rem(8px) rgba(0, 0, 0, 0.2);\n    transition:\n      transform        250ms cubic-bezier(0.1, 0.7, 0.1, 1),\n      color            250ms,\n      background-color 250ms,\n      box-shadow       250ms;\n  }\n\n  // Header in hidden state, i.e. moved out of sight\n  &[data-md-state=\"hidden\"] {\n    transform: translateY(-100%);\n    transition:\n      transform        250ms cubic-bezier(0.8, 0, 0.6, 1),\n      color            250ms,\n      background-color 250ms,\n      box-shadow       250ms;\n  }\n\n  // Header wrapper\n  &__inner {\n    display: flex;\n    align-items: center;\n    padding: 0 px2rem(4px);\n  }\n\n  // Header button\n  &__button {\n    position: relative;\n    z-index: 1;\n    display: inline-block;\n    margin: px2rem(4px);\n    padding: px2rem(8px);\n    color: currentColor;\n    vertical-align: middle;\n    cursor: pointer;\n    transition: opacity 250ms;\n\n    // Button on focus/hover\n    &:focus,\n    &:hover {\n      opacity: 0.7;\n    }\n\n    // Hide outline for pointer devices\n    &:not(.focus-visible) {\n      outline: none;\n    }\n\n    // Button with logo, pointing to `config.site_url`\n    &.md-logo {\n      margin: px2rem(4px);\n      padding: px2rem(8px);\n\n      // [tablet -]: Hide button\n      @include break-to-device(tablet) {\n        display: none;\n      }\n\n      // Image or icon\n      img,\n      svg {\n        display: block;\n        width: px2rem(24px);\n        height: px2rem(24px);\n        fill: currentColor;\n      }\n    }\n\n    // Button for search\n    &[for=\"__search\"] {\n\n      // [tablet landscape +]: Hide button\n      @include break-from-device(tablet landscape) {\n        display: none;\n      }\n\n      // [no-js]: Hide button\n      .no-js & {\n        display: none;\n      }\n\n      // Adjust for right-to-left languages\n      [dir=\"rtl\"] & {\n\n        // Flip icon vertically\n        svg {\n          transform: scaleX(-1);\n        }\n      }\n    }\n\n    // Button for drawer\n    &[for=\"__drawer\"] {\n\n      // [screen +]: Hide button\n      @include break-from-device(screen) {\n        display: none;\n      }\n    }\n  }\n\n  // Header topic\n  &__topic {\n    position: absolute;\n    display: flex;\n    max-width: 100%;\n    transition:\n      transform 400ms cubic-bezier(0.1, 0.7, 0.1, 1),\n      opacity   150ms;\n\n    // Second header topic - title of the current page\n    & + & {\n      z-index: -1;\n      transform: translateX(px2rem(25px));\n      opacity: 0;\n      transition:\n        transform 400ms cubic-bezier(1, 0.7, 0.1, 0.1),\n        opacity   150ms;\n      pointer-events: none;\n\n      // Adjust for right-to-left languages\n      [dir=\"rtl\"] & {\n        transform: translateX(px2rem(-25px));\n      }\n    }\n  }\n\n  // Header title\n  &__title {\n    flex-grow: 1;\n    height: px2rem(48px);\n    margin-right: px2rem(8px);\n    margin-left: px2rem(20px);\n    font-size: px2rem(18px);\n    line-height: px2rem(48px);\n\n    // Header title in active state, i.e. page title is visible\n    &[data-md-state=\"active\"] .md-header__topic {\n      z-index: -1;\n      transform: translateX(px2rem(-25px));\n      opacity: 0;\n      transition:\n        transform 400ms cubic-bezier(1, 0.7, 0.1, 0.1),\n        opacity   150ms;\n      pointer-events: none;\n\n      // Adjust for right-to-left languages\n      [dir=\"rtl\"] & {\n        transform: translateX(px2rem(25px));\n      }\n\n      // Second header topic - title of the current page\n      + .md-header__topic {\n        z-index: 0;\n        transform: translateX(0);\n        opacity: 1;\n        transition:\n          transform 400ms cubic-bezier(0.1, 0.7, 0.1, 1),\n          opacity   150ms;\n        pointer-events: initial;\n      }\n    }\n\n    // Add ellipsis in case of overflowing text\n    > .md-header__ellipsis {\n      position: relative;\n      width: 100%;\n      height: 100%;\n    }\n  }\n\n  // Header options\n  &__options {\n    display: flex;\n    flex-shrink: 0;\n    max-width: 100%;\n    white-space: nowrap;\n    transition:\n      max-width  0ms 250ms,\n      opacity  250ms 250ms;\n\n    // Hide inactive buttons\n    > [data-md-state=\"hidden\"] {\n      display: none;\n    }\n\n    // Hide toggle when search is active\n    [data-md-toggle=\"search\"]:checked ~ .md-header & {\n      max-width: 0;\n      opacity: 0;\n      transition:\n        max-width 0ms,\n        opacity   0ms;\n    }\n  }\n\n  // Repository information container\n  &__source {\n    display: none;\n\n    // [tablet landscape +]: Show repository information\n    @include break-from-device(tablet landscape) {\n      display: block;\n      width: px2rem(234px);\n      max-width: px2rem(234px);\n      margin-left: px2rem(20px);\n\n      // Adjust for right-to-left languages\n      [dir=\"rtl\"] & {\n        margin-right: px2rem(20px);\n        margin-left: initial;\n      }\n    }\n\n    // [screen +]: Adjust spacing of search bar\n    @include break-from-device(screen) {\n      margin-left: px2rem(28px);\n\n      // Adjust for right-to-left languages\n      [dir=\"rtl\"] & {\n        margin-right: px2rem(28px);\n      }\n    }\n  }\n}\n", "////\n/// Copyright (c) 2016-2021 <PERSON> <<EMAIL>>\n///\n/// Permission is hereby granted, free of charge, to any person obtaining a\n/// copy of this software and associated documentation files (the \"Software\"),\n/// to deal in the Software without restriction, including without limitation\n/// the rights to use, copy, modify, merge, publish, distribute, sublicense,\n/// and/or sell copies of the Software, and to permit persons to whom the\n/// Software is furnished to do so, subject to the following conditions:\n///\n/// The above copyright notice and this permission notice shall be included in\n/// all copies or substantial portions of the Software.\n///\n/// THE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR\n/// IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,\n/// FITNESS FOR A PARTICULAR PURPOSE AND NON-INFRINGEMENT. IN NO EVENT SHALL\n/// THE AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER\n/// LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING\n/// FROM, OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER\n/// DEALINGS\n////\n\n// ----------------------------------------------------------------------------\n// Rules\n// ----------------------------------------------------------------------------\n\n// Footer\n.md-footer {\n  color: var(--md-footer-fg-color);\n  background-color: var(--md-footer-bg-color);\n\n  // [print]: Hide footer\n  @media print {\n    display: none;\n  }\n\n  // Footer wrapper\n  &__inner {\n    padding: px2rem(4px);\n    overflow: auto;\n  }\n\n  // Footer link to previous and next page\n  &__link {\n    display: flex;\n    padding-top: px2rem(28px);\n    padding-bottom: px2rem(8px);\n    transition: opacity 250ms;\n\n    // [tablet +]: Adjust width to 50/50\n    @include break-from-device(tablet) {\n      width: 50%;\n    }\n\n    // Footer link on focus/hover\n    &:focus,\n    &:hover {\n      opacity: 0.7;\n    }\n\n    // Footer link to previous page\n    &--prev {\n      float: left;\n\n      // [mobile -]: Adjust width to 25/75 and hide title\n      @include break-to-device(mobile) {\n        width: 25%;\n\n        // Hide footer title\n        .md-footer__title {\n          display: none;\n        }\n      }\n\n      // Adjust for right-to-left languages\n      [dir=\"rtl\"] & {\n        float: right;\n\n        // Flip icon vertically\n        svg {\n          transform: scaleX(-1);\n        }\n      }\n    }\n\n    // Footer link to next page\n    &--next {\n      float: right;\n      text-align: right;\n\n      // [mobile -]: Adjust width to 25/75\n      @include break-to-device(mobile) {\n        width: 75%;\n      }\n\n      // Adjust for right-to-left languages\n      [dir=\"rtl\"] & {\n        float: left;\n        text-align: left;\n\n        // Flip icon vertically\n        svg {\n          transform: scaleX(-1);\n        }\n      }\n    }\n  }\n\n  // Footer title\n  &__title {\n    position: relative;\n    flex-grow: 1;\n    max-width: calc(100% - #{px2rem(48px)});\n    padding: 0 px2rem(20px);\n    font-size: px2rem(18px);\n    line-height: px2rem(48px);\n  }\n\n  // Footer link button\n  &__button {\n    margin: px2rem(4px);\n    padding: px2rem(8px);\n  }\n\n  // Footer link direction (i.e. prev and next)\n  &__direction {\n    position: absolute;\n    right: 0;\n    left: 0;\n    margin-top: px2rem(-20px);\n    padding: 0 px2rem(20px);\n    font-size: px2rem(12.8px);\n    opacity: 0.7;\n  }\n}\n\n// Footer metadata\n.md-footer-meta {\n  background-color: var(--md-footer-bg-color--dark);\n\n  // Footer metadata wrapper\n  &__inner {\n    display: flex;\n    flex-wrap: wrap;\n    justify-content: space-between;\n    padding: px2rem(4px);\n  }\n\n  // Lighten color for non-hovered text links\n  html &.md-typeset a {\n    color: var(--md-footer-fg-color--light);\n\n    // Text link on focus/hover\n    &:focus,\n    &:hover {\n      color: var(--md-footer-fg-color);\n    }\n  }\n}\n\n// Footer copyright and theme information\n.md-footer-copyright {\n  width: 100%;\n  margin: auto px2rem(12px);\n  padding: px2rem(8px) 0;\n  color: var(--md-footer-fg-color--lighter);\n  font-size: px2rem(12.8px);\n\n  // [tablet portrait +]: Show copyright and social links in one line\n  @include break-from-device(tablet portrait) {\n    width: auto;\n  }\n\n  // Footer copyright highlight - this is the upper part of the copyright and\n  // theme information, which will include a darker color than the theme link\n  &__highlight {\n    color: var(--md-footer-fg-color--light);\n  }\n}\n\n// Footer social links\n.md-footer-social {\n  margin: 0 px2rem(8px);\n  padding: px2rem(4px) 0 px2rem(12px);\n\n  // [tablet portrait +]: Show copyright and social links in one line\n  @include break-from-device(tablet portrait) {\n    padding: px2rem(12px) 0;\n  }\n\n  // Footer social link\n  &__link {\n    display: inline-block;\n    width: px2rem(32px);\n    height: px2rem(32px);\n    text-align: center;\n\n    // Adjust line-height to match height for correct alignment\n    &::before {\n      line-height: 1.9;\n    }\n\n    // Fill icon with current color\n    svg {\n      max-height: px2rem(16px);\n      vertical-align: -25%;\n      fill: currentColor;\n    }\n  }\n}\n", "////\n/// Copyright (c) 2016-2021 <PERSON> <<EMAIL>>\n///\n/// Permission is hereby granted, free of charge, to any person obtaining a\n/// copy of this software and associated documentation files (the \"Software\"),\n/// to deal in the Software without restriction, including without limitation\n/// the rights to use, copy, modify, merge, publish, distribute, sublicense,\n/// and/or sell copies of the Software, and to permit persons to whom the\n/// Software is furnished to do so, subject to the following conditions:\n///\n/// The above copyright notice and this permission notice shall be included in\n/// all copies or substantial portions of the Software.\n///\n/// THE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR\n/// IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,\n/// FITNESS FOR A PARTICULAR PURPOSE AND NON-INFRINGEMENT. IN NO EVENT SHALL\n/// THE AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER\n/// LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING\n/// FROM, OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER\n/// DEALINGS\n////\n\n// ----------------------------------------------------------------------------\n// Rules\n// ----------------------------------------------------------------------------\n\n// Icon definitions\n:root {\n  --md-nav-icon--prev: svg-load(\"material/arrow-left.svg\");\n  --md-nav-icon--next: svg-load(\"material/chevron-right.svg\");\n  --md-toc-icon: svg-load(\"material/table-of-contents.svg\");\n}\n\n// ----------------------------------------------------------------------------\n\n// Navigation\n.md-nav {\n  font-size: px2rem(14px);\n  line-height: 1.3;\n\n  // Navigation title\n  &__title {\n    display: block;\n    padding: 0 px2rem(12px);\n    overflow: hidden;\n    font-weight: 700;\n    text-overflow: ellipsis;\n\n    // Navigaton button\n    .md-nav__button {\n      display: none;\n\n      // Stretch images based on height, as it's the smaller dimension\n      img {\n        width: auto;\n        height: 100%;\n      }\n\n      // Button with logo, pointing to `config.site_url`\n      &.md-logo {\n\n        // Image or icon\n        img,\n        svg {\n          display: block;\n          width: px2rem(48px);\n          height: px2rem(48px);\n          fill: currentColor;\n        }\n      }\n    }\n  }\n\n  // Navigation list\n  &__list {\n    margin: 0;\n    padding: 0;\n    list-style: none;\n  }\n\n  // Navigation item\n  &__item {\n    padding: 0 px2rem(12px);\n\n    // Navigation item on level 2\n    & & {\n      padding-right: 0;\n\n      // Adjust for right-to-left languages\n      [dir=\"rtl\"] & {\n        padding-right: px2rem(12px);\n        padding-left: 0;\n      }\n    }\n  }\n\n  // Navigation link\n  &__link {\n    display: block;\n    margin-top: 0.625em;\n    overflow: hidden;\n    text-overflow: ellipsis;\n    cursor: pointer;\n    transition: color 125ms;\n    scroll-snap-align: start;\n\n    // Link in blurred state\n    &[data-md-state=\"blur\"] {\n      color: var(--md-default-fg-color--light);\n    }\n\n    // Active link\n    .md-nav__item &--active {\n      color: var(--md-typeset-a-color);\n    }\n\n    // Navigation link in nested list\n    .md-nav__item--nested > & {\n      color: inherit;\n    }\n\n    // Navigation link on focus/hover\n    &:focus,\n    &:hover {\n      color: var(--md-accent-fg-color);\n    }\n\n    // Navigation link to table of contents\n    .md-nav--primary &[for=\"__toc\"] {\n      display: none;\n\n      // Table of contents icon\n      .md-icon::after {\n        display: block;\n        width: 100%;\n        height: 100%;\n        mask-image: var(--md-toc-icon);\n        background-color: currentColor;\n      }\n\n      // Hide table of contents\n      ~ .md-nav {\n        display: none;\n      }\n    }\n  }\n\n  // Repository information container\n  &__source {\n    display: none;\n  }\n\n  // [tablet -]: Layered navigation\n  @include break-to-device(tablet) {\n\n    // Primary and nested navigation\n    &--primary,\n    &--primary & {\n      position: absolute;\n      top: 0;\n      right: 0;\n      left: 0;\n      z-index: 1;\n      display: flex;\n      flex-direction: column;\n      height: 100%;\n      background-color: var(--md-default-bg-color);\n    }\n\n    // Primary navigation\n    &--primary {\n\n      // Navigation title and item\n      .md-nav__title,\n      .md-nav__item {\n        font-size: px2rem(16px);\n        line-height: 1.5;\n      }\n\n      // Navigation title\n      .md-nav__title {\n        position: relative;\n        height: px2rem(112px);\n        padding: px2rem(60px) px2rem(16px) px2rem(4px);\n        color: var(--md-default-fg-color--light);\n        font-weight: 400;\n        line-height: px2rem(48px);\n        white-space: nowrap;\n        background-color: var(--md-default-fg-color--lightest);\n        cursor: pointer;\n\n        // Navigation icon\n        .md-nav__icon {\n          position: absolute;\n          top: px2rem(8px);\n          left: px2rem(8px);\n          display: block;\n          width: px2rem(24px);\n          height: px2rem(24px);\n          margin: px2rem(4px);\n\n          // Adjust for right-to-left languages\n          [dir=\"rtl\"] & {\n            right: px2rem(8px);\n            left: initial;\n          }\n\n          // Navigation icon in link to previous level\n          &::after {\n            display: block;\n            width: 100%;\n            height: 100%;\n            background-color: currentColor;\n            mask-image: var(--md-nav-icon--prev);\n            mask-repeat: no-repeat;\n            mask-size: contain;\n            content: \"\";\n          }\n        }\n\n        // Navigation list\n        ~ .md-nav__list {\n          overflow-y: auto;\n          background-color: var(--md-default-bg-color);\n          box-shadow:\n            0 px2rem(1px) 0 var(--md-default-fg-color--lightest) inset;\n          scroll-snap-type: y mandatory;\n          touch-action: pan-y;\n\n          // Omit border on first child\n          > :first-child {\n            border-top: 0;\n          }\n        }\n\n        // Top-level navigation title\n        &[for=\"__drawer\"] {\n          color: var(--md-primary-bg-color);\n          background-color: var(--md-primary-fg-color);\n        }\n\n        // Button with logo, pointing to `config.site_url`\n        .md-logo {\n          position: absolute;\n          top: px2rem(4px);\n          left: px2rem(4px);\n          display: block;\n          margin: px2rem(4px);\n          padding: px2rem(8px);\n\n          // Adjust for right-to-left languages\n          [dir=\"rtl\"] & {\n            right: px2rem(4px);\n            left: initial;\n          }\n        }\n      }\n\n      // Navigation list\n      .md-nav__list {\n        flex: 1;\n      }\n\n      // Navigation item\n      .md-nav__item {\n        padding: 0;\n        border-top: px2rem(1px) solid var(--md-default-fg-color--lightest);\n\n        // Navigation link in nested navigation\n        &--nested > .md-nav__link {\n          padding-right: px2rem(48px);\n\n          // Adjust for right-to-left languages\n          [dir=\"rtl\"] & {\n            padding-right: px2rem(16px);\n            padding-left: px2rem(48px);\n          }\n        }\n\n        // Navigation link in active navigation\n        &--active > .md-nav__link {\n          color: var(--md-typeset-a-color);\n\n          // Navigation link on focus/hover\n          &:focus,\n          &:hover {\n            color: var(--md-accent-fg-color);\n          }\n        }\n      }\n\n      // Navigation link\n      .md-nav__link {\n        position: relative;\n        margin-top: 0;\n        padding: px2rem(12px) px2rem(16px);\n\n        // Navigation icon\n        .md-nav__icon {\n          position: absolute;\n          top: 50%;\n          right: px2rem(12px);\n          width: px2rem(24px);\n          height: px2rem(24px);\n          margin-top: px2rem(-12px);\n          color: inherit;\n          font-size: px2rem(24px);\n\n          // Adjust for right-to-left languages\n          [dir=\"rtl\"] & {\n            right: initial;\n            left: px2rem(12px);\n          }\n\n          // Navigation icon in link to next level\n          &::after {\n            display: block;\n            width: 100%;\n            height: 100%;\n            background-color: currentColor;\n            mask-image: var(--md-nav-icon--next);\n            mask-repeat: no-repeat;\n            mask-size: contain;\n            content: \"\";\n          }\n        }\n      }\n\n      // Flip icon vertically\n      .md-nav__icon {\n\n        // Adjust for right-to-left languages\n        [dir=\"rtl\"] &::after {\n          transform: scale(-1);\n        }\n      }\n\n      // Table of contents contained in primary navigation\n      .md-nav--secondary {\n\n        // Navigation link - omit unnecessary layering\n        .md-nav__link {\n          position: static;\n        }\n\n        // Navigation on level 2-6\n        .md-nav {\n          position: static;\n          background-color: transparent;\n\n          // Navigation link on level 3\n          .md-nav__link {\n            padding-left: px2rem(28px);\n\n            // Adjust for right-to-left languages\n            [dir=\"rtl\"] & {\n              padding-right: px2rem(28px);\n              padding-left: initial;\n            }\n          }\n\n          // Navigation link on level 4\n          .md-nav .md-nav__link {\n            padding-left: px2rem(40px);\n\n            // Adjust for right-to-left languages\n            [dir=\"rtl\"] & {\n              padding-right: px2rem(40px);\n              padding-left: initial;\n            }\n          }\n\n          // Navigation link on level 5\n          .md-nav .md-nav .md-nav__link {\n            padding-left: px2rem(52px);\n\n            // Adjust for right-to-left languages\n            [dir=\"rtl\"] & {\n              padding-right: px2rem(52px);\n              padding-left: initial;\n            }\n          }\n\n          // Navigation link on level 6\n          .md-nav .md-nav .md-nav .md-nav__link {\n            padding-left: px2rem(64px);\n\n            // Adjust for right-to-left languages\n            [dir=\"rtl\"] & {\n              padding-right: px2rem(64px);\n              padding-left: initial;\n            }\n          }\n        }\n      }\n    }\n\n    // Table of contents\n    &--secondary {\n      background-color: transparent;\n    }\n\n    // Toggle for nested navigation\n    &__toggle ~ & {\n      display: flex;\n      transform: translateX(100%);\n      opacity: 0;\n      transition:\n        transform 250ms cubic-bezier(0.8, 0, 0.6, 1),\n        opacity   125ms 50ms;\n\n      // Adjust for right-to-left languages\n      [dir=\"rtl\"] & {\n        transform: translateX(-100%);\n      }\n    }\n\n    // Show nested navigation when toggle is active\n    &__toggle:checked ~ & {\n      transform: translateX(0);\n      opacity: 1;\n      transition:\n        transform 250ms cubic-bezier(0.4, 0, 0.2, 1),\n        opacity   125ms 125ms;\n\n      // Navigation list\n      > .md-nav__list {\n        // Hack: promote to own layer to reduce jitter\n        backface-visibility: hidden;\n      }\n    }\n  }\n\n  // [tablet portrait -]: Layered navigation with table of contents\n  @include break-to-device(tablet portrait) {\n\n    // Show link to table of contents\n    &--primary &__link[for=\"__toc\"] {\n      display: block;\n      padding-right: px2rem(48px);\n\n      // Adjust for right-to-left languages\n      [dir=\"rtl\"] & {\n        padding-right: px2rem(16px);\n        padding-left: px2rem(48px);\n      }\n\n      // Show table of contents icon\n      .md-icon::after {\n        content: \"\";\n      }\n\n      // Hide navigation link to current page\n      + .md-nav__link {\n        display: none;\n      }\n\n      // Show table of contents\n      ~ .md-nav {\n        display: flex;\n      }\n    }\n\n    // Repository information container\n    &__source {\n      display: block;\n      padding: 0 px2rem(4px);\n      color: var(--md-primary-bg-color);\n      background-color: var(--md-primary-fg-color--dark);\n    }\n  }\n\n  // [tablet landscape]: Layered navigation with table of contents\n  @include break-at-device(tablet landscape) {\n\n    // Show link to integrated table of contents\n    &--integrated &__link[for=\"__toc\"] {\n      display: block;\n      padding-right: px2rem(48px);\n      scroll-snap-align: initial;\n\n      // Adjust for right-to-left languages\n      [dir=\"rtl\"] & {\n        padding-right: px2rem(16px);\n        padding-left: px2rem(48px);\n      }\n\n      // Show table of contents icon\n      .md-icon::after {\n        content: \"\";\n      }\n\n      // Hide navigation link to current page\n      + .md-nav__link {\n        display: none;\n      }\n\n      // Show table of contents\n      ~ .md-nav {\n        display: flex;\n      }\n    }\n  }\n\n  // [tablet landscape +]: Tree-like table of contents\n  @include break-from-device(tablet landscape) {\n\n    // Navigation title\n    &--secondary &__title {\n\n      // Adjust snapping behavior\n      &[for=\"__toc\"] {\n        scroll-snap-align: start;\n      }\n\n      // Hide navigation icon\n      .md-nav__icon {\n        display: none;\n      }\n    }\n  }\n\n  // [screen +]: Tree-like navigation\n  @include break-from-device(screen) {\n    transition: max-height 250ms cubic-bezier(0.86, 0, 0.07, 1);\n\n    // Navigation title\n    &--primary &__title {\n\n      // Adjust snapping behavior\n      &[for=\"__drawer\"] {\n        scroll-snap-align: start;\n      }\n\n      // Hide navigation icon\n      .md-nav__icon {\n        display: none;\n      }\n    }\n\n    // Hide toggle for nested navigation\n    &__toggle ~ & {\n      display: none;\n    }\n\n    // Show nested navigation when toggle is active or indeterminate\n    &__toggle:checked ~ &,\n    &__toggle:indeterminate ~ & {\n      display: block;\n    }\n\n    // Hide navigation title in nested navigation\n    &__item--nested > & > &__title {\n      display: none;\n    }\n\n    // Navigation section\n    &__item--section {\n      display: block;\n      margin: 1.25em 0;\n\n      // Adjust spacing on last child\n      &:last-child {\n        margin-bottom: 0;\n      }\n\n      // Hide navigation link, as sections are always expanded\n      > .md-nav__link {\n        display: none;\n      }\n\n      // Navigation\n      > .md-nav {\n        display: block;\n\n        // Navigation title\n        > .md-nav__title {\n          display: block;\n          padding: 0;\n          pointer-events: none;\n          scroll-snap-align: start;\n        }\n\n        // Adjust spacing on next level item\n        > .md-nav__list > .md-nav__item {\n          padding: 0;\n        }\n      }\n    }\n\n    // Navigation icon\n    &__icon {\n      float: right;\n      width: px2rem(18px);\n      height: px2rem(18px);\n      transition: transform 250ms;\n\n      // Adjust for right-to-left languages\n      [dir=\"rtl\"] & {\n        float: left;\n        transform: rotate(180deg);\n      }\n\n      // Navigation icon content\n      &::after {\n        display: inline-block;\n        width: 100%;\n        height: 100%;\n        vertical-align: px2rem(-2px);\n        background-color: currentColor;\n        mask-image: var(--md-nav-icon--next);\n        mask-repeat: no-repeat;\n        mask-size: contain;\n        content: \"\";\n      }\n\n      // Navigation icon - rotate icon when toggle is active or indeterminate\n      .md-nav__item--nested .md-nav__toggle:checked ~ .md-nav__link &,\n      .md-nav__item--nested .md-nav__toggle:indeterminate ~ .md-nav__link & {\n        transform: rotate(90deg);\n      }\n    }\n\n    // Modifier for when navigation tabs are rendered\n    &--lifted {\n\n      // Hide nested items on level 1 and site title\n      > .md-nav__list > .md-nav__item--nested,\n      > .md-nav__title {\n        display: none;\n      }\n\n      // Hide level 1 items\n      > .md-nav__list > .md-nav__item {\n        display: none;\n\n        // Active parent navigation item\n        &--active {\n          display: block;\n          padding: 0;\n\n          // Hide nested links\n          > .md-nav__link {\n            display: none;\n          }\n\n          // Show title and adjust spacing\n          > .md-nav > .md-nav__title {\n            display: block;\n            padding: 0 px2rem(12px);\n            pointer-events: none;\n            scroll-snap-align: start;\n          }\n        }\n\n        // Adjust spacing for navigation item on level 2\n        > .md-nav__item {\n          padding-right: px2rem(12px);\n        }\n      }\n\n      // Hack: Always show active navigation tab on breakpoint screen, despite\n      // of checkbox being checked or not. Fixes #1655.\n      .md-nav[data-md-level=\"1\"] {\n        display: block;\n      }\n    }\n\n    // Modifier for when table of contents is rendered in primary navigation\n    &--integrated &__link[for=\"__toc\"] ~ .md-nav {\n      display: block;\n      margin-bottom: 1.25em;\n      border-left: px2rem(1px) solid var(--md-primary-fg-color);\n\n      // Hide navigation title\n      > .md-nav__title {\n        display: none;\n      }\n    }\n  }\n}\n", "////\n/// Copyright (c) 2016-2021 <PERSON> <<EMAIL>>\n///\n/// Permission is hereby granted, free of charge, to any person obtaining a\n/// copy of this software and associated documentation files (the \"Software\"),\n/// to deal in the Software without restriction, including without limitation\n/// the rights to use, copy, modify, merge, publish, distribute, sublicense,\n/// and/or sell copies of the Software, and to permit persons to whom the\n/// Software is furnished to do so, subject to the following conditions:\n///\n/// The above copyright notice and this permission notice shall be included in\n/// all copies or substantial portions of the Software.\n///\n/// THE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR\n/// IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,\n/// FITNESS FOR A PARTICULAR PURPOSE AND NON-INFRINGEMENT. IN NO EVENT SHALL\n/// THE AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER\n/// LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING\n/// FROM, OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER\n/// DEALINGS\n////\n\n// ----------------------------------------------------------------------------\n// Rules\n// ----------------------------------------------------------------------------\n\n// Icon definitions\n:root {\n  --md-search-result-icon: svg-load(\"material/file-search-outline.svg\");\n}\n\n// ----------------------------------------------------------------------------\n\n// Search\n.md-search {\n  position: relative;\n\n  // [tablet landscape +]: Header-embedded search\n  @include break-from-device(tablet landscape) {\n    padding: px2rem(4px) 0;\n  }\n\n  // [no-js]: Hide search\n  .no-js & {\n    display: none;\n  }\n\n  // Search overlay\n  &__overlay {\n    z-index: 1;\n    opacity: 0;\n\n    // [tablet portrait -]: Search modal\n    @include break-to-device(tablet portrait) {\n      position: absolute;\n      top: px2rem(4px);\n      left: px2rem(-44px);\n      width: px2rem(40px);\n      height: px2rem(40px);\n      overflow: hidden;\n      background-color: var(--md-default-bg-color);\n      border-radius: px2rem(20px);\n      transform-origin: center;\n      transition:\n        transform 300ms 100ms,\n        opacity   200ms 200ms;\n      pointer-events: none;\n\n      // Adjust for right-to-left languages\n      [dir=\"rtl\"] & {\n        right: px2rem(-44px);\n        left: initial;\n      }\n\n      // Show overlay when search is active\n      [data-md-toggle=\"search\"]:checked ~ .md-header & {\n        opacity: 1;\n        transition:\n          transform 400ms,\n          opacity   100ms;\n      }\n    }\n\n    // [tablet landscape +]: Header-embedded search\n    @include break-from-device(tablet landscape) {\n      position: fixed;\n      top: 0;\n      left: 0;\n      width: 0;\n      height: 0;\n      background-color: hsla(0, 0%, 0%, 0.54);\n      cursor: pointer;\n      transition:\n        width     0ms 250ms,\n        height    0ms 250ms,\n        opacity 250ms;\n\n      // Adjust for right-to-left languages\n      [dir=\"rtl\"] & {\n        right: 0;\n        left: initial;\n      }\n\n      // Show overlay when search is active\n      [data-md-toggle=\"search\"]:checked ~ .md-header & {\n        width: 100%;\n        // Hack: when the header is translated upon scrolling, a new layer is\n        // induced, which means that the height will now refer to the height of\n        // the header, albeit positioning is fixed. This should be mitigated\n        // in all cases when setting the height to 2x the viewport.\n        height: 200vh;\n        opacity: 1;\n        transition:\n          width     0ms,\n          height    0ms,\n          opacity 250ms;\n      }\n    }\n\n    // Adjust appearance when search is active\n    [data-md-toggle=\"search\"]:checked ~ .md-header & {\n\n      // [mobile portrait -]: Scale up 45 times\n      @include break-to-device(mobile portrait) {\n        transform: scale(45);\n      }\n\n      // [mobile landscape]: Scale up 60 times\n      @include break-at-device(mobile landscape) {\n        transform: scale(60);\n      }\n\n      // [tablet portrait]: Scale up 75 times\n      @include break-at-device(tablet portrait) {\n        transform: scale(75);\n      }\n    }\n  }\n\n  // Search wrapper\n  &__inner {\n    // Hack: promote to own layer to reduce jitter\n    backface-visibility: hidden;\n\n    // [tablet portrait -]: Search modal\n    @include break-to-device(tablet portrait) {\n      position: fixed;\n      top: 0;\n      left: 100%;\n      z-index: 2;\n      width: 100%;\n      height: 100%;\n      transform: translateX(5%);\n      opacity: 0;\n      transition:\n        right       0ms 300ms,\n        left        0ms 300ms,\n        transform 150ms 150ms cubic-bezier(0.4, 0, 0.2, 1),\n        opacity   150ms 150ms;\n\n      // Adjust appearance when search is active\n      [data-md-toggle=\"search\"]:checked ~ .md-header & {\n        left: 0;\n        transform: translateX(0);\n        opacity: 1;\n        transition:\n          right       0ms   0ms,\n          left        0ms   0ms,\n          transform 150ms 150ms cubic-bezier(0.1, 0.7, 0.1, 1),\n          opacity   150ms 150ms;\n\n        // Adjust for right-to-left languages\n        [dir=\"rtl\"] & {\n          right: 0;\n          left: initial;\n        }\n      }\n\n      // Adjust for right-to-left languages\n      html [dir=\"rtl\"] & {\n        right: 100%;\n        left: initial;\n        transform: translateX(-5%);\n      }\n    }\n\n    // [tablet landscape +]: Header-embedded search\n    @include break-from-device(tablet landscape) {\n      position: relative;\n      float: right;\n      width: px2rem(234px);\n      padding: px2rem(2px) 0;\n      transition: width 250ms cubic-bezier(0.1, 0.7, 0.1, 1);\n\n      // Adjust for right-to-left languages\n      [dir=\"rtl\"] & {\n        float: left;\n      }\n    }\n\n    // Adjust appearance when search is active\n    [data-md-toggle=\"search\"]:checked ~ .md-header & {\n\n      // [tablet landscape]: Omit overlaying header title\n      @include break-at-device(tablet landscape) {\n        width: px2rem(468px);\n      }\n\n      // [screen +]: Match width of content area\n      @include break-from-device(screen) {\n        width: px2rem(688px);\n      }\n    }\n  }\n\n  // Search form\n  &__form {\n    position: relative;\n\n    // [tablet landscape +]: Header-embedded search\n    @include break-from-device(tablet landscape) {\n      border-radius: px2rem(2px);\n    }\n  }\n\n  // Search input\n  &__input {\n    position: relative;\n    z-index: 2;\n    padding: 0 px2rem(44px) 0 px2rem(72px);\n    text-overflow: ellipsis;\n    background-color: var(--md-default-bg-color);\n    box-shadow: 0 0 px2rem(12px) transparent;\n    transition:\n      color            250ms,\n      background-color 250ms,\n      box-shadow       250ms;\n\n    // Adjust for right-to-left languages\n    [dir=\"rtl\"] & {\n      padding: 0 px2rem(72px) 0 px2rem(44px);\n    }\n\n    // Search placeholder\n    &::placeholder {\n      transition: color 250ms;\n    }\n\n    // Search icon and placeholder\n    ~ .md-search__icon,\n    &::placeholder {\n      color: var(--md-default-fg-color--light);\n    }\n\n    // Remove the \"x\" rendered by Internet Explorer\n    &::-ms-clear {\n      display: none;\n    }\n\n    // Adjust appearance when search is active\n    [data-md-toggle=\"search\"]:checked ~ .md-header & {\n      box-shadow: 0 0 px2rem(12px) hsla(0, 0%, 0%, 0.07);\n    }\n\n    // [tablet portrait -]: Search modal\n    @include break-to-device(tablet portrait) {\n      width: 100%;\n      height: px2rem(48px);\n      font-size: px2rem(18px);\n    }\n\n    // [tablet landscape +]: Header-embedded search\n    @include break-from-device(tablet landscape) {\n      width: 100%;\n      height: px2rem(36px);\n      padding-left: px2rem(44px);\n      color: inherit;\n      font-size: px2rem(16px);\n      background-color: hsla(0, 0%, 0%, 0.26);\n      border-radius: px2rem(2px);\n\n      // Adjust for right-to-left languages\n      [dir=\"rtl\"] & {\n        padding-right: px2rem(44px);\n      }\n\n      // Search icon\n      + .md-search__icon {\n        color: var(--md-primary-bg-color);\n      }\n\n      // Search placeholder\n      &::placeholder {\n        color: var(--md-primary-bg-color--light);\n      }\n\n      // Search input on hover\n      &:hover {\n        background-color: hsla(0, 0%, 100%, 0.12);\n      }\n\n      // Adjust appearance when search is active\n      [data-md-toggle=\"search\"]:checked ~ .md-header & {\n        color: var(--md-default-fg-color);\n        text-overflow: clip;\n        background-color: var(--md-default-bg-color);\n        border-radius: px2rem(2px) px2rem(2px) 0 0;\n\n        // Search icon and placeholder\n        + .md-search__icon,\n        &::placeholder {\n          color: var(--md-default-fg-color--light);\n        }\n      }\n    }\n  }\n\n  // Search icon\n  &__icon {\n    position: absolute;\n    z-index: 2;\n    width: px2rem(24px);\n    height: px2rem(24px);\n    cursor: pointer;\n    transition:\n      color   250ms,\n      opacity 250ms;\n\n    // Search icon on hover\n    &:hover {\n      opacity: 0.7;\n    }\n\n    // Search focus button\n    &[for=\"__search\"] {\n      top: px2rem(6px);\n      left: px2rem(10px);\n\n      // Adjust for right-to-left languages\n      [dir=\"rtl\"] & {\n        right: px2rem(10px);\n        left: initial;\n\n        // Flip icon vertically\n        svg {\n          transform: scaleX(-1);\n        }\n      }\n\n      // [tablet portrait -]: Search modal\n      @include break-to-device(tablet portrait) {\n        top: px2rem(12px);\n        left: px2rem(16px);\n\n        // Adjust for right-to-left languages\n        [dir=\"rtl\"] & {\n          right: px2rem(16px);\n          left: initial;\n        }\n\n        // Hide the magnifying glass\n        svg:first-child {\n          display: none;\n        }\n      }\n\n      // [tablet landscape +]: Header-embedded search\n      @include break-from-device(tablet landscape) {\n        pointer-events: none;\n\n        // Hide the back arrow\n        svg:last-child {\n          display: none;\n        }\n      }\n    }\n\n    // Search reset button\n    &[type=\"reset\"] {\n      top: px2rem(6px);\n      right: px2rem(10px);\n      transform: scale(0.75);\n      opacity: 0;\n      transition:\n        transform 150ms cubic-bezier(0.1, 0.7, 0.1, 1),\n        opacity   150ms;\n      pointer-events: none;\n\n      // Adjust for right-to-left languages\n      [dir=\"rtl\"] & {\n        right: initial;\n        left: px2rem(10px);\n      }\n\n      // [tablet portrait -]: Search modal\n      @include break-to-device(tablet portrait) {\n        top: px2rem(12px);\n        right: px2rem(16px);\n\n        // Adjust for right-to-left languages\n        [dir=\"rtl\"] & {\n          right: initial;\n          left: px2rem(16px);\n        }\n      }\n\n      // Show reset button when search is active and input non-empty\n      [data-md-toggle=\"search\"]:checked ~ .md-header\n      .md-search__input:valid ~ & {\n        transform: scale(1);\n        opacity: 1;\n        pointer-events: initial;\n\n        // Search focus icon\n        &:hover {\n          opacity: 0.7;\n        }\n      }\n    }\n  }\n\n  // Search output\n  &__output {\n    position: absolute;\n    z-index: 1;\n    width: 100%;\n    overflow: hidden;\n    border-radius: 0 0 px2rem(2px) px2rem(2px);\n\n    // [tablet portrait -]: Search modal\n    @include break-to-device(tablet portrait) {\n      top: px2rem(48px);\n      bottom: 0;\n    }\n\n    // [tablet landscape +]: Header-embedded search\n    @include break-from-device(tablet landscape) {\n      top: px2rem(38px);\n      opacity: 0;\n      transition: opacity 400ms;\n\n      // Show output when search is active\n      [data-md-toggle=\"search\"]:checked ~ .md-header & {\n        @include z-depth(6);\n\n        opacity: 1;\n      }\n    }\n  }\n\n  // Search scroll wrapper\n  &__scrollwrap {\n    height: 100%;\n    overflow-y: auto;\n    background-color: var(--md-default-bg-color);\n    // Hack: promote to own layer to reduce jitter\n    backface-visibility: hidden;\n    // Hack: Chrome 88+ has weird overscroll behavior. Overall, scroll snapping\n    // seems to be something that is not ready for prime time on some browsers.\n    // scroll-snap-type: y mandatory;\n    touch-action: pan-y;\n\n    // Mitigiate excessive repaints on non-retina devices\n    @media (max-resolution: 1dppx) {\n      transform: translateZ(0);\n    }\n\n    // [tablet landscape]: Set fixed width to omit unnecessary reflow\n    @include break-at-device(tablet landscape) {\n      width: px2rem(468px);\n    }\n\n    // [screen +]: Set fixed width to omit unnecessary reflow\n    @include break-from-device(screen) {\n      width: px2rem(688px);\n    }\n\n    // [tablet landscape +]: Limit height to viewport\n    @include break-from-device(tablet landscape) {\n      max-height: 0;\n      scrollbar-width: thin;\n      scrollbar-color: var(--md-default-fg-color--lighter) transparent;\n\n      // Show scroll wrapper when search is active\n      [data-md-toggle=\"search\"]:checked ~ .md-header & {\n        max-height: 75vh;\n      }\n\n      // Search scroll wrapper on hover\n      &:hover {\n        scrollbar-color: var(--md-accent-fg-color) transparent;\n      }\n\n      // Webkit scrollbar\n      &::-webkit-scrollbar {\n        width: px2rem(4px);\n        height: px2rem(4px);\n      }\n\n      // Webkit scrollbar thumb\n      &::-webkit-scrollbar-thumb {\n        background-color: var(--md-default-fg-color--lighter);\n\n        // Webkit scrollbar thumb on hover\n        &:hover {\n          background-color: var(--md-accent-fg-color);\n        }\n      }\n    }\n  }\n}\n\n// Search result\n.md-search-result {\n  color: var(--md-default-fg-color);\n  word-break: break-word;\n\n  // Search result metadata\n  &__meta {\n    padding: 0 px2rem(16px);\n    color: var(--md-default-fg-color--light);\n    font-size: px2rem(12.8px);\n    line-height: px2rem(36px);\n    background-color: var(--md-default-fg-color--lightest);\n    scroll-snap-align: start;\n\n    // [tablet landscape +]: Adjust spacing\n    @include break-from-device(tablet landscape) {\n      padding-left: px2rem(44px);\n\n      // Adjust for right-to-left languages\n      [dir=\"rtl\"] & {\n        padding-right: px2rem(44px);\n        padding-left: initial;\n      }\n    }\n  }\n\n  // Search result list\n  &__list {\n    margin: 0;\n    padding: 0;\n    list-style: none;\n  }\n\n  // Search result item\n  &__item {\n    box-shadow: 0 px2rem(-1px) 0 var(--md-default-fg-color--lightest);\n\n    // Omit border on first child\n    &:first-child {\n      box-shadow: none;\n    }\n  }\n\n  // Search result link\n  &__link {\n    display: block;\n    outline: none;\n    transition: background-color 250ms;\n    scroll-snap-align: start;\n\n    // Search result link on focus/hover\n    &:focus,\n    &:hover {\n      background-color: var(--md-accent-fg-color--transparent);\n    }\n\n    // Adjust spacing on last child of last link\n    &:last-child p:last-child {\n      margin-bottom: px2rem(12px);\n    }\n  }\n\n  // Search result more link\n  &__more summary {\n    display: block;\n    padding: px2em(12px) px2rem(16px);\n    color: var(--md-typeset-a-color);\n    font-size: px2rem(12.8px);\n    outline: 0;\n    cursor: pointer;\n    transition:\n      color            250ms,\n      background-color 250ms;\n    scroll-snap-align: start;\n\n    // [tablet landscape +]: Adjust spacing\n    @include break-from-device(tablet landscape) {\n      padding-left: px2rem(44px);\n\n      // Adjust for right-to-left languages\n      [dir=\"rtl\"] & {\n        padding-right: px2rem(44px);\n        padding-left: px2rem(16px);\n      }\n    }\n\n    // Search result more link on focus/hover\n    &:focus,\n    &:hover {\n      color: var(--md-accent-fg-color);\n      background-color: var(--md-accent-fg-color--transparent);\n    }\n\n    // Hide native details marker\n    &::marker,\n    &::-webkit-details-marker {\n      display: none;\n    }\n\n    // Adjust transparency of less relevant results\n    ~ * > * {\n      opacity: 0.65;\n    }\n  }\n\n  // Search result article\n  &__article {\n    position: relative;\n    padding: 0 px2rem(16px);\n    overflow: hidden;\n\n    // [tablet landscape +]: Adjust spacing\n    @include break-from-device(tablet landscape) {\n      padding-left: px2rem(44px);\n\n      // Adjust for right-to-left languages\n      [dir=\"rtl\"] & {\n        padding-right: px2rem(44px);\n        padding-left: px2rem(16px);\n      }\n    }\n\n    // Search result article document\n    &--document {\n\n      // Search result title\n      .md-search-result__title {\n        margin: px2rem(11px) 0;\n        font-weight: 400;\n        font-size: px2rem(16px);\n        line-height: 1.4;\n      }\n    }\n  }\n\n  // Search result icon\n  &__icon {\n    position: absolute;\n    left: 0;\n    width: px2rem(24px);\n    height: px2rem(24px);\n    margin: px2rem(10px);\n    color: var(--md-default-fg-color--light);\n\n    // [tablet portrait -]: Hide icon\n    @include break-to-device(tablet portrait) {\n      display: none;\n    }\n\n    // Search result icon content\n    &::after {\n      display: inline-block;\n      width: 100%;\n      height: 100%;\n      background-color: currentColor;\n      mask-image: var(--md-search-result-icon);\n      mask-repeat: no-repeat;\n      mask-size: contain;\n      content: \"\";\n    }\n\n    // Adjust for right-to-left languages\n    [dir=\"rtl\"] & {\n      right: 0;\n      left: initial;\n\n      // Flip icon vertically\n      &::after {\n        transform: scaleX(-1);\n      }\n    }\n  }\n\n  // Search result title\n  &__title {\n    margin: 0.5em 0;\n    font-weight: 700;\n    font-size: px2rem(12.8px);\n    line-height: 1.6;\n  }\n\n  // Search result teaser\n  &__teaser {\n    display: -webkit-box;\n    max-height: px2rem(40px);\n    margin: 0.5em 0;\n    overflow: hidden;\n    color: var(--md-default-fg-color--light);\n    font-size: px2rem(12.8px);\n    line-height: 1.6;\n    text-overflow: ellipsis;\n    -webkit-box-orient: vertical;\n    -webkit-line-clamp: 2;\n\n    // [mobile -]: Adjust number of lines\n    @include break-to-device(mobile) {\n      max-height: px2rem(60px);\n      -webkit-line-clamp: 3;\n    }\n\n    // [tablet landscape]: Adjust number of lines\n    @include break-at-device(tablet landscape) {\n      max-height: px2rem(60px);\n      -webkit-line-clamp: 3;\n    }\n\n    // Search term highlighting\n    mark {\n      text-decoration: underline;\n      background-color: transparent;\n    }\n  }\n\n  // Search result terms\n  &__terms {\n    margin: 0.5em 0;\n    font-size: px2rem(12.8px);\n    font-style: italic;\n  }\n\n  // Search term highlighting\n  mark {\n    color: var(--md-accent-fg-color);\n    background-color: transparent;\n  }\n}\n", "////\n/// Copyright (c) 2016-2021 <PERSON> <<EMAIL>>\n///\n/// Permission is hereby granted, free of charge, to any person obtaining a\n/// copy of this software and associated documentation files (the \"Software\"),\n/// to deal in the Software without restriction, including without limitation\n/// the rights to use, copy, modify, merge, publish, distribute, sublicense,\n/// and/or sell copies of the Software, and to permit persons to whom the\n/// Software is furnished to do so, subject to the following conditions:\n///\n/// The above copyright notice and this permission notice shall be included in\n/// all copies or substantial portions of the Software.\n///\n/// THE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR\n/// IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,\n/// FITNESS FOR A PARTICULAR PURPOSE AND NON-INFRINGEMENT. IN NO EVENT SHALL\n/// THE AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER\n/// LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING\n/// FROM, OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER\n/// DEALINGS\n////\n\n// ----------------------------------------------------------------------------\n// Rules\n// ----------------------------------------------------------------------------\n\n// Selection\n.md-select {\n  position: relative;\n  z-index: 1;\n\n  // Selection bubble\n  &__inner {\n    position: absolute;\n    top: calc(100% - #{px2rem(4px)});\n    left: 50%;\n    max-height: 0;\n    margin-top: px2rem(4px);\n    color: var(--md-default-fg-color);\n    background-color: var(--md-default-bg-color);\n    border-radius: px2rem(2px);\n    box-shadow:\n      0 px2rem(4px) px2rem(10px) hsla(0, 0%, 0%, 0.1),\n      0 0           px2rem(1px)  hsla(0, 0%, 0%, 0.25);\n    transform: translate3d(-50%, px2rem(6px), 0);\n    opacity: 0;\n    transition:\n      transform  250ms 375ms,\n      opacity    250ms 250ms,\n      max-height   0ms 500ms;\n\n    // Selection bubble on parent focus/hover\n    .md-select:focus-within &,\n    .md-select:hover & {\n      max-height: px2rem(200px);\n      transform: translate3d(-50%, 0, 0);\n      opacity: 1;\n      transition:\n        transform  250ms cubic-bezier(0.1, 0.7, 0.1, 1),\n        opacity    250ms,\n        max-height 250ms;\n    }\n\n    // Selection bubble handle\n    &::after {\n      position: absolute;\n      top: 0;\n      left: 50%;\n      width: 0;\n      height: 0;\n      margin-top: px2rem(-4px);\n      margin-left: px2rem(-4px);\n      border: px2rem(4px) solid transparent;\n      border-top: 0;\n      border-bottom-color: var(--md-default-bg-color);\n      content: \"\";\n    }\n  }\n\n  // Selection list\n  &__list {\n    max-height: inherit;\n    margin: 0;\n    padding: 0;\n    overflow: auto;\n    font-size: px2rem(16px);\n    list-style-type: none;\n    border-radius: px2rem(2px);\n  }\n\n  // Selection item\n  &__item {\n    line-height: px2rem(36px);\n  }\n\n  // Selection link\n  &__link {\n    display: block;\n    width: 100%;\n    padding-right: px2rem(24px);\n    padding-left: px2rem(12px);\n    cursor: pointer;\n    transition:\n      background-color 250ms,\n      color            250ms;\n    scroll-snap-align: start;\n\n    // Adjust for right-to-left languages\n    [dir=\"rtl\"] & {\n      padding-right: px2rem(12px);\n      padding-left: px2rem(24px);\n    }\n\n    // Link on focus/hover\n    &:focus,\n    &:hover {\n      background-color: var(--md-default-fg-color--lightest);\n    }\n  }\n}\n", "////\n/// Copyright (c) 2016-2021 <PERSON> <<EMAIL>>\n///\n/// Permission is hereby granted, free of charge, to any person obtaining a\n/// copy of this software and associated documentation files (the \"Software\"),\n/// to deal in the Software without restriction, including without limitation\n/// the rights to use, copy, modify, merge, publish, distribute, sublicense,\n/// and/or sell copies of the Software, and to permit persons to whom the\n/// Software is furnished to do so, subject to the following conditions:\n///\n/// The above copyright notice and this permission notice shall be included in\n/// all copies or substantial portions of the Software.\n///\n/// THE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR\n/// IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,\n/// FITNESS FOR A PARTICULAR PURPOSE AND NON-INFRINGEMENT. IN NO EVENT SHALL\n/// THE AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER\n/// LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING\n/// FROM, OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER\n/// DEALINGS\n////\n\n// ----------------------------------------------------------------------------\n// Rules\n// ----------------------------------------------------------------------------\n\n// Sidebar\n.md-sidebar {\n  position: sticky;\n  top: px2rem(48px);\n  flex-shrink: 0;\n  align-self: flex-start;\n  width: px2rem(242px);\n  padding: px2rem(24px) 0;\n\n  // [print]: Hide sidebar\n  @media print {\n    display: none;\n  }\n\n  // [tablet -]: Show navigation as drawer\n  @include break-to-device(tablet) {\n\n    // Primary sidebar with navigation\n    &--primary {\n      position: fixed;\n      top: 0;\n      left: px2rem(-242px);\n      z-index: 3;\n      display: block;\n      width: px2rem(242px);\n      height: 100%;\n      background-color: var(--md-default-bg-color);\n      transform: translateX(0);\n      transition:\n        transform  250ms cubic-bezier(0.4, 0, 0.2, 1),\n        box-shadow 250ms;\n\n      // Adjust for right-to-left languages\n      [dir=\"rtl\"] & {\n        right: px2rem(-242px);\n        left: initial;\n      }\n\n      // Show sidebar when drawer is active\n      [data-md-toggle=\"drawer\"]:checked ~ .md-container & {\n        @include z-depth(8);\n\n        transform: translateX(px2rem(242px));\n\n        // Adjust for right-to-left languages\n        [dir=\"rtl\"] & {\n          transform: translateX(px2rem(-242px));\n        }\n      }\n\n      // Stretch scroll wrapper for primary sidebar\n      .md-sidebar__scrollwrap {\n        position: absolute;\n        top: 0;\n        right: 0;\n        bottom: 0;\n        left: 0;\n        margin: 0;\n        scroll-snap-type: none;\n        overflow: hidden;\n      }\n    }\n  }\n\n  // [screen +]: Show navigation as sidebar\n  @include break-from-device(screen) {\n    height: 0;\n\n    // [no-js]: Switch to native sticky behavior\n    .no-js & {\n      height: auto;\n    }\n  }\n\n  // Secondary sidebar with table of contents\n  &--secondary {\n    display: none;\n    order: 2;\n\n    // [tablet landscape +]: Show table of contents as sidebar\n    @include break-from-device(tablet landscape) {\n      height: 0;\n\n      // [no-js]: Switch to native sticky behavior\n      .no-js & {\n        height: auto;\n      }\n\n      // Sidebar is visible\n      &:not([hidden]) {\n        display: block;\n      }\n\n      // Ensure smooth scrolling on iOS\n      .md-sidebar__scrollwrap {\n        touch-action: pan-y;\n      }\n    }\n  }\n\n  // Sidebar scroll wrapper\n  &__scrollwrap {\n    margin: 0 px2rem(4px);\n    overflow-y: auto;\n    // Hack: promote to own layer to reduce jitter\n    backface-visibility: hidden;\n    // Hack: Chrome 81+ exhibits a strange bug, where it scrolls the container\n    // to the bottom if `scroll-snap-type` is set on the initial render. For\n    // this reason, we disable scroll snapping until this is resolved (#1667).\n    // scroll-snap-type: y mandatory;\n    scrollbar-width: thin;\n    scrollbar-color: var(--md-default-fg-color--lighter) transparent;\n\n    // Sidebar scroll wrapper on hover\n    &:hover {\n      scrollbar-color: var(--md-accent-fg-color) transparent;\n    }\n\n    // Webkit scrollbar\n    &::-webkit-scrollbar {\n      width: px2rem(4px);\n      height: px2rem(4px);\n    }\n\n    // Webkit scrollbar thumb\n    &::-webkit-scrollbar-thumb {\n      background-color: var(--md-default-fg-color--lighter);\n\n      // Webkit scrollbar thumb on hover\n      &:hover {\n        background-color: var(--md-accent-fg-color);\n      }\n    }\n  }\n}\n\n// [tablet -]: Show overlay on active drawer\n@include break-to-device(tablet) {\n\n  // Sidebar overlay\n  .md-overlay {\n    position: fixed;\n    top: 0;\n    z-index: 3;\n    width: 0;\n    height: 0;\n    background-color: hsla(0, 0%, 0%, 0.54);\n    opacity: 0;\n    transition:\n      width     0ms 250ms,\n      height    0ms 250ms,\n      opacity 250ms;\n\n    // Show overlay when drawer is active\n    [data-md-toggle=\"drawer\"]:checked ~ & {\n      width: 100%;\n      height: 100%;\n      opacity: 1;\n      transition:\n        width     0ms,\n        height    0ms,\n        opacity 250ms;\n    }\n  }\n}\n", "////\n/// Copyright (c) 2016-2021 <PERSON> <<EMAIL>>\n///\n/// Permission is hereby granted, free of charge, to any person obtaining a\n/// copy of this software and associated documentation files (the \"Software\"),\n/// to deal in the Software without restriction, including without limitation\n/// the rights to use, copy, modify, merge, publish, distribute, sublicense,\n/// and/or sell copies of the Software, and to permit persons to whom the\n/// Software is furnished to do so, subject to the following conditions:\n///\n/// The above copyright notice and this permission notice shall be included in\n/// all copies or substantial portions of the Software.\n///\n/// THE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR\n/// IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,\n/// FITNESS FOR A PARTICULAR PURPOSE AND NON-INFRINGEMENT. IN NO EVENT SHALL\n/// THE AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER\n/// LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING\n/// FROM, OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER\n/// DEALINGS\n////\n\n// ----------------------------------------------------------------------------\n// Rules\n// ----------------------------------------------------------------------------\n\n// Navigation tabs\n.md-tabs {\n  width: 100%;\n  overflow: auto;\n  color: var(--md-primary-bg-color);\n  background-color: var(--md-primary-fg-color);\n  transition: background-color 250ms;\n\n  // [print]: Hide tabs\n  @media print {\n    display: none;\n  }\n\n  // [tablet -]: Hide tabs\n  @include break-to-device(tablet) {\n    display: none;\n  }\n\n  // Tabs in hidden state, i.e. when scrolling down\n  &[data-md-state=\"hidden\"] {\n    pointer-events: none;\n  }\n\n  // Navigation tabs list\n  &__list {\n    margin: 0;\n    margin-left: px2rem(4px);\n    padding: 0;\n    white-space: nowrap;\n    list-style: none;\n    contain: content;\n\n    // Adjust for right-to-left languages\n    [dir=\"rtl\"] & {\n      margin-right: px2rem(4px);\n      margin-left: initial;\n    }\n  }\n\n  // Navigation tabs item\n  &__item {\n    display: inline-block;\n    height: px2rem(48px);\n    padding-right: px2rem(12px);\n    padding-left: px2rem(12px);\n  }\n\n  // Navigation tabs link - could be defined as block elements and aligned via\n  // line height, but this would imply more repaints when scrolling\n  &__link {\n    display: block;\n    margin-top: px2rem(16px);\n    font-size: px2rem(14px);\n    // Hack: save a repaint when tabs are appearing on scrolling up\n    backface-visibility: hidden;\n    opacity: 0.7;\n    transition:\n      transform 400ms cubic-bezier(0.1, 0.7, 0.1, 1),\n      opacity   250ms;\n\n    // Active link and link on focus/hover\n    &--active,\n    &:focus,\n    &:hover {\n      color: inherit;\n      opacity: 1;\n    }\n\n    // Delay transitions by a small amount\n    @for $i from 2 through 16 {\n      .md-tabs__item:nth-child(#{$i}) & {\n        transition-delay: 20ms * ($i - 1);\n      }\n    }\n\n    // Hide tabs upon scrolling - disable transition to minimizes repaints\n    // while scrolling down, while scrolling up seems to be okay\n    .md-tabs[data-md-state=\"hidden\"] & {\n      transform: translateY(50%);\n      opacity: 0;\n      transition:\n        transform 0ms 100ms,\n        opacity 100ms;\n    }\n  }\n}\n", "////\n/// Copyright (c) 2016-2021 <PERSON> <<EMAIL>>\n///\n/// Permission is hereby granted, free of charge, to any person obtaining a\n/// copy of this software and associated documentation files (the \"Software\"),\n/// to deal in the Software without restriction, including without limitation\n/// the rights to use, copy, modify, merge, publish, distribute, sublicense,\n/// and/or sell copies of the Software, and to permit persons to whom the\n/// Software is furnished to do so, subject to the following conditions:\n///\n/// The above copyright notice and this permission notice shall be included in\n/// all copies or substantial portions of the Software.\n///\n/// THE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR\n/// IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,\n/// FITNESS FOR A PARTICULAR PURPOSE AND NON-INFRINGEMENT. IN NO EVENT SHALL\n/// THE AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER\n/// LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING\n/// FROM, OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER\n/// DEALINGS\n////\n\n// ----------------------------------------------------------------------------\n// Rules\n// ----------------------------------------------------------------------------\n\n// Icon definitions\n:root {\n  --md-version-icon: svg-load(\"fontawesome/solid/caret-down.svg\");\n}\n\n// ----------------------------------------------------------------------------\n\n// Version selection\n.md-version {\n  flex-shrink: 0;\n  height: px2rem(48px);\n  font-size: px2rem(16px);\n\n  // Current selection\n  &__current {\n    position: relative;\n    // Hack: in general, we would use `vertical-align` to align the version at\n    // the bottom with the title, but since the list uses absolute positioning,\n    // this won't work consistently. Furthermore, we would need to use inline\n    // positioning to align the links, which looks jagged.\n    top: px2rem(1px);\n    margin-right: px2rem(8px);\n    margin-left: px2rem(28px);\n\n    // Adjust for right-to-left languages\n    [dir=\"rtl\"] & {\n      margin-right: px2rem(28px);\n      margin-left: px2rem(8px);\n    }\n\n    // Version selection icon\n    &::after {\n      display: inline-block;\n      width: px2rem(8px);\n      height: px2rem(12px);\n      margin-left: px2rem(8px);\n      background-color: currentColor;\n      mask-image: var(--md-version-icon);\n      mask-repeat: no-repeat;\n      content: \"\";\n\n      // Adjust for right-to-left languages\n      [dir=\"rtl\"] & {\n        margin-right: px2rem(8px);\n        margin-left: initial;\n      }\n    }\n  }\n\n  // Version selection list\n  &__list {\n    position: absolute;\n    top: px2rem(3px);\n    z-index: 1;\n    max-height: px2rem(36px);\n    margin: px2rem(4px) px2rem(16px);\n    padding: 0;\n    overflow: auto;\n    color: var(--md-default-fg-color);\n    list-style-type: none;\n    background-color: var(--md-default-bg-color);\n    border-radius: px2rem(2px);\n    box-shadow:\n      0 px2rem(4px) px2rem(10px) hsla(0, 0%, 0%, 0.1),\n      0 0           px2rem(1px)  hsla(0, 0%, 0%, 0.25);\n    opacity: 0;\n    transition:\n      max-height 0ms 500ms,\n      opacity  250ms 250ms;\n    scroll-snap-type: y mandatory;\n\n    // List on focus/hover\n    &:focus-within,\n    &:hover {\n      max-height: px2rem(200px);\n      opacity: 1;\n      transition:\n        max-height 250ms,\n        opacity    250ms;\n    }\n  }\n\n  // Version selection item\n  &__item {\n    line-height: px2rem(36px);\n  }\n\n  // Version selection link\n  &__link {\n    display: block;\n    width: 100%;\n    padding-right: px2rem(24px);\n    padding-left: px2rem(12px);\n    white-space: nowrap;\n    cursor: pointer;\n    transition:\n      color            250ms,\n      background-color 250ms;\n    scroll-snap-align: start;\n\n    // Adjust for right-to-left languages\n    [dir=\"rtl\"] & {\n      padding-right: px2rem(12px);\n      padding-left: px2rem(24px);\n    }\n\n    // Link on focus/hover\n    &:focus,\n    &:hover {\n      background-color: var(--md-default-fg-color--lightest);\n    }\n  }\n}\n", "////\n/// Copyright (c) 2016-2021 <PERSON> <<EMAIL>>\n///\n/// Permission is hereby granted, free of charge, to any person obtaining a\n/// copy of this software and associated documentation files (the \"Software\"),\n/// to deal in the Software without restriction, including without limitation\n/// the rights to use, copy, modify, merge, publish, distribute, sublicense,\n/// and/or sell copies of the Software, and to permit persons to whom the\n/// Software is furnished to do so, subject to the following conditions:\n///\n/// The above copyright notice and this permission notice shall be included in\n/// all copies or substantial portions of the Software.\n///\n/// THE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR\n/// IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,\n/// FITNESS FOR A PARTICULAR PURPOSE AND NON-INFRINGEMENT. IN NO EVENT SHALL\n/// THE AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER\n/// LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING\n/// FROM, OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER\n/// DEALINGS\n////\n\n// ----------------------------------------------------------------------------\n// Variables\n// ----------------------------------------------------------------------------\n\n/// Admonition flavours\n$admonitions: (\n  note:                       pencil $clr-blue-a200,\n  abstract summary tldr:      text-subject $clr-light-blue-a400,\n  info todo:                  information $clr-cyan-a700,\n  tip hint important:         fire $clr-teal-a700,\n  success check done:         check-circle $clr-green-a700,\n  question help faq:          help-circle $clr-light-green-a700,\n  warning caution attention:  alert $clr-orange-a400,\n  failure fail missing:       close-circle $clr-red-a200,\n  danger error:               flash-circle $clr-red-a400,\n  bug:                        bug $clr-pink-a400,\n  example:                    format-list-numbered $clr-deep-purple-a200,\n  quote cite:                 format-quote-close $clr-grey\n) !default;\n\n// ----------------------------------------------------------------------------\n// Rules: layout\n// ----------------------------------------------------------------------------\n\n// Icon definitions\n:root {\n  @each $names, $props in $admonitions {\n    --md-admonition-icon--#{nth($names, 1)}:\n      svg-load(\"material/#{nth($props, 1)}.svg\");\n  }\n}\n\n// ----------------------------------------------------------------------------\n\n// Scoped in typesetted content to match specificity of regular content\n.md-typeset {\n\n  // Admonition\n  .admonition {\n    margin: px2em(20px, 12.8px) 0;\n    padding: 0 px2rem(12px);\n    overflow: hidden;\n    color: var(--md-admonition-fg-color);\n    font-size: px2rem(12.8px);\n    page-break-inside: avoid;\n    background-color: var(--md-admonition-bg-color);\n    border-left: px2rem(4px) solid $clr-blue-a200;\n    border-radius: px2rem(2px);\n    box-shadow:\n      0 px2rem(4px)   px2rem(10px) hsla(0, 0%, 0%, 0.05),\n      0 px2rem(0.5px) px2rem(1px)  hsla(0, 0%, 0%, 0.05);\n\n    // [print]: Omit shadow as it may lead to rendering errors\n    @media print {\n      box-shadow: none;\n    }\n\n    // Adjust for right-to-left languages\n    [dir=\"rtl\"] & {\n      border-right: px2rem(4px) solid $clr-blue-a200;\n      border-left: none;\n    }\n\n    // Adjust vertical spacing for nested admonitions\n    .admonition {\n      margin-top: 1em;\n      margin-bottom: 1em;\n    }\n\n    // Adjust spacing for contained table wrappers\n    .md-typeset__scrollwrap {\n      margin: 1em px2rem(-12px);\n    }\n\n    // Adjust spacing for contained tables\n    .md-typeset__table {\n      padding: 0 px2rem(12px);\n    }\n\n    // Adjust spacing for single-child tabbed block container\n    > .tabbed-set:only-child {\n      margin-top: 0;\n    }\n\n    // Adjust spacing on last child\n    html & > :last-child {\n      margin-bottom: px2rem(12px);\n    }\n  }\n\n  // Admonition title\n  .admonition-title {\n    position: relative;\n    margin: 0 px2rem(-12px) 0 px2rem(-16px);\n    padding: px2rem(8px) px2rem(12px) px2rem(8px) px2rem(40px);\n    font-weight: 700;\n    background-color: transparentize($clr-blue-a200, 0.9);\n    border-left: px2rem(4px) solid $clr-blue-a200;\n\n    // Adjust for right-to-left languages\n    [dir=\"rtl\"] & {\n      margin: 0 px2rem(-16px) 0 px2rem(-12px);\n      padding: px2rem(8px) px2rem(40px) px2rem(8px) px2rem(12px);\n      border-right: px2rem(4px) solid $clr-blue-a200;\n      border-left: none;\n    }\n\n    // Adjust spacing for title-only admonitions\n    html &:last-child {\n      margin-bottom: 0;\n    }\n\n    // Admonition icon\n    &::before {\n      position: absolute;\n      left: px2rem(12px);\n      width: px2rem(20px);\n      height: px2rem(20px);\n      background-color: $clr-blue-a200;\n      mask-image: var(--md-admonition-icon--note);\n      mask-repeat: no-repeat;\n      mask-size: contain;\n      content: \"\";\n\n      // Adjust for right-to-left languages\n      [dir=\"rtl\"] & {\n        right: px2rem(12px);\n        left: initial;\n      }\n    }\n\n    // Omit background on inline code blocks, as they don't go well with the\n    // pastelly tones applied to admonition titles\n    code {\n      margin: initial;\n      padding: initial;\n      color: currentColor;\n      background-color: transparent;\n      border-radius: initial;\n      box-shadow: none;\n    }\n\n    // Adjust spacing on last tabbed block container child - if the tabbed\n    // block container is the sole child, it looks better to omit the margin\n    + .tabbed-set:last-child {\n      margin-top: 0;\n    }\n  }\n}\n\n// ----------------------------------------------------------------------------\n// Rules: flavours\n// ----------------------------------------------------------------------------\n\n@each $names, $props in $admonitions {\n  $name: nth($names, 1);\n  $tint: nth($props, 2);\n\n  // Admonition flavour\n  .md-typeset .admonition.#{$name} {\n    border-color: $tint;\n  }\n\n  // Admonition flavour title\n  .md-typeset .#{$name} > .admonition-title {\n    background-color: transparentize($tint, 0.9);\n    border-color: $tint;\n\n    // Admonition icon\n    &::before {\n      background-color: $tint;\n      mask-image: var(--md-admonition-icon--#{$name});\n      mask-repeat: no-repeat;\n      mask-size: contain;\n    }\n  }\n\n  // Define synonyms for flavours\n  @if length($names) > 1 {\n    @for $n from 2 through length($names) {\n      .#{nth($names, $n)} {\n        @extend .#{$name};\n      }\n    }\n  }\n}\n", "// ==========================================================================\n//\n// Name:        UI Color Palette\n// Description: The color palette of material design.\n// Version:     2.3.1\n//\n// Author:      <PERSON>\n// Git:         https://github.com/mrmlnc/material-color\n//\n// twitter:     @mrmlnc\n//\n// ==========================================================================\n\n\n//\n// List of base colors\n//\n\n// $clr-red\n// $clr-pink\n// $clr-purple\n// $clr-deep-purple\n// $clr-indigo\n// $clr-blue\n// $clr-light-blue\n// $clr-cyan\n// $clr-teal\n// $clr-green\n// $clr-light-green\n// $clr-lime\n// $clr-yellow\n// $clr-amber\n// $clr-orange\n// $clr-deep-orange\n// $clr-brown\n// $clr-grey\n// $clr-blue-grey\n// $clr-black\n// $clr-white\n\n\n//\n// Red\n//\n\n$clr-red-list: (\n  \"base\": #f44336,\n  \"50\":   #ffebee,\n  \"100\":  #ffcdd2,\n  \"200\":  #ef9a9a,\n  \"300\":  #e57373,\n  \"400\":  #ef5350,\n  \"500\":  #f44336,\n  \"600\":  #e53935,\n  \"700\":  #d32f2f,\n  \"800\":  #c62828,\n  \"900\":  #b71c1c,\n  \"a100\": #ff8a80,\n  \"a200\": #ff5252,\n  \"a400\": #ff1744,\n  \"a700\": #d50000\n);\n\n$clr-red:      map-get($clr-red-list, \"base\");\n\n$clr-red-50:   map-get($clr-red-list, \"50\");\n$clr-red-100:  map-get($clr-red-list, \"100\");\n$clr-red-200:  map-get($clr-red-list, \"200\");\n$clr-red-300:  map-get($clr-red-list, \"300\");\n$clr-red-400:  map-get($clr-red-list, \"400\");\n$clr-red-500:  map-get($clr-red-list, \"500\");\n$clr-red-600:  map-get($clr-red-list, \"600\");\n$clr-red-700:  map-get($clr-red-list, \"700\");\n$clr-red-800:  map-get($clr-red-list, \"800\");\n$clr-red-900:  map-get($clr-red-list, \"900\");\n$clr-red-a100: map-get($clr-red-list, \"a100\");\n$clr-red-a200: map-get($clr-red-list, \"a200\");\n$clr-red-a400: map-get($clr-red-list, \"a400\");\n$clr-red-a700: map-get($clr-red-list, \"a700\");\n\n\n//\n// Pink\n//\n\n$clr-pink-list: (\n  \"base\": #e91e63,\n  \"50\":   #fce4ec,\n  \"100\":  #f8bbd0,\n  \"200\":  #f48fb1,\n  \"300\":  #f06292,\n  \"400\":  #ec407a,\n  \"500\":  #e91e63,\n  \"600\":  #d81b60,\n  \"700\":  #c2185b,\n  \"800\":  #ad1457,\n  \"900\":  #880e4f,\n  \"a100\": #ff80ab,\n  \"a200\": #ff4081,\n  \"a400\": #f50057,\n  \"a700\": #c51162\n);\n\n$clr-pink:      map-get($clr-pink-list, \"base\");\n\n$clr-pink-50:   map-get($clr-pink-list, \"50\");\n$clr-pink-100:  map-get($clr-pink-list, \"100\");\n$clr-pink-200:  map-get($clr-pink-list, \"200\");\n$clr-pink-300:  map-get($clr-pink-list, \"300\");\n$clr-pink-400:  map-get($clr-pink-list, \"400\");\n$clr-pink-500:  map-get($clr-pink-list, \"500\");\n$clr-pink-600:  map-get($clr-pink-list, \"600\");\n$clr-pink-700:  map-get($clr-pink-list, \"700\");\n$clr-pink-800:  map-get($clr-pink-list, \"800\");\n$clr-pink-900:  map-get($clr-pink-list, \"900\");\n$clr-pink-a100: map-get($clr-pink-list, \"a100\");\n$clr-pink-a200: map-get($clr-pink-list, \"a200\");\n$clr-pink-a400: map-get($clr-pink-list, \"a400\");\n$clr-pink-a700: map-get($clr-pink-list, \"a700\");\n\n\n//\n// Purple\n//\n\n$clr-purple-list: (\n  \"base\": #9c27b0,\n  \"50\":   #f3e5f5,\n  \"100\":  #e1bee7,\n  \"200\":  #ce93d8,\n  \"300\":  #ba68c8,\n  \"400\":  #ab47bc,\n  \"500\":  #9c27b0,\n  \"600\":  #8e24aa,\n  \"700\":  #7b1fa2,\n  \"800\":  #6a1b9a,\n  \"900\":  #4a148c,\n  \"a100\": #ea80fc,\n  \"a200\": #e040fb,\n  \"a400\": #d500f9,\n  \"a700\": #aa00ff\n);\n\n$clr-purple:      map-get($clr-purple-list, \"base\");\n\n$clr-purple-50:   map-get($clr-purple-list, \"50\");\n$clr-purple-100:  map-get($clr-purple-list, \"100\");\n$clr-purple-200:  map-get($clr-purple-list, \"200\");\n$clr-purple-300:  map-get($clr-purple-list, \"300\");\n$clr-purple-400:  map-get($clr-purple-list, \"400\");\n$clr-purple-500:  map-get($clr-purple-list, \"500\");\n$clr-purple-600:  map-get($clr-purple-list, \"600\");\n$clr-purple-700:  map-get($clr-purple-list, \"700\");\n$clr-purple-800:  map-get($clr-purple-list, \"800\");\n$clr-purple-900:  map-get($clr-purple-list, \"900\");\n$clr-purple-a100: map-get($clr-purple-list, \"a100\");\n$clr-purple-a200: map-get($clr-purple-list, \"a200\");\n$clr-purple-a400: map-get($clr-purple-list, \"a400\");\n$clr-purple-a700: map-get($clr-purple-list, \"a700\");\n\n\n//\n// Deep purple\n//\n\n$clr-deep-purple-list: (\n  \"base\": #673ab7,\n  \"50\":   #ede7f6,\n  \"100\":  #d1c4e9,\n  \"200\":  #b39ddb,\n  \"300\":  #9575cd,\n  \"400\":  #7e57c2,\n  \"500\":  #673ab7,\n  \"600\":  #5e35b1,\n  \"700\":  #512da8,\n  \"800\":  #4527a0,\n  \"900\":  #311b92,\n  \"a100\": #b388ff,\n  \"a200\": #7c4dff,\n  \"a400\": #651fff,\n  \"a700\": #6200ea\n);\n\n$clr-deep-purple:      map-get($clr-deep-purple-list, \"base\");\n\n$clr-deep-purple-50:   map-get($clr-deep-purple-list, \"50\");\n$clr-deep-purple-100:  map-get($clr-deep-purple-list, \"100\");\n$clr-deep-purple-200:  map-get($clr-deep-purple-list, \"200\");\n$clr-deep-purple-300:  map-get($clr-deep-purple-list, \"300\");\n$clr-deep-purple-400:  map-get($clr-deep-purple-list, \"400\");\n$clr-deep-purple-500:  map-get($clr-deep-purple-list, \"500\");\n$clr-deep-purple-600:  map-get($clr-deep-purple-list, \"600\");\n$clr-deep-purple-700:  map-get($clr-deep-purple-list, \"700\");\n$clr-deep-purple-800:  map-get($clr-deep-purple-list, \"800\");\n$clr-deep-purple-900:  map-get($clr-deep-purple-list, \"900\");\n$clr-deep-purple-a100: map-get($clr-deep-purple-list, \"a100\");\n$clr-deep-purple-a200: map-get($clr-deep-purple-list, \"a200\");\n$clr-deep-purple-a400: map-get($clr-deep-purple-list, \"a400\");\n$clr-deep-purple-a700: map-get($clr-deep-purple-list, \"a700\");\n\n\n//\n// Indigo\n//\n\n$clr-indigo-list: (\n  \"base\": #3f51b5,\n  \"50\":   #e8eaf6,\n  \"100\":  #c5cae9,\n  \"200\":  #9fa8da,\n  \"300\":  #7986cb,\n  \"400\":  #5c6bc0,\n  \"500\":  #3f51b5,\n  \"600\":  #3949ab,\n  \"700\":  #303f9f,\n  \"800\":  #283593,\n  \"900\":  #1a237e,\n  \"a100\": #8c9eff,\n  \"a200\": #536dfe,\n  \"a400\": #3d5afe,\n  \"a700\": #304ffe\n);\n\n$clr-indigo:      map-get($clr-indigo-list, \"base\");\n\n$clr-indigo-50:   map-get($clr-indigo-list, \"50\");\n$clr-indigo-100:  map-get($clr-indigo-list, \"100\");\n$clr-indigo-200:  map-get($clr-indigo-list, \"200\");\n$clr-indigo-300:  map-get($clr-indigo-list, \"300\");\n$clr-indigo-400:  map-get($clr-indigo-list, \"400\");\n$clr-indigo-500:  map-get($clr-indigo-list, \"500\");\n$clr-indigo-600:  map-get($clr-indigo-list, \"600\");\n$clr-indigo-700:  map-get($clr-indigo-list, \"700\");\n$clr-indigo-800:  map-get($clr-indigo-list, \"800\");\n$clr-indigo-900:  map-get($clr-indigo-list, \"900\");\n$clr-indigo-a100: map-get($clr-indigo-list, \"a100\");\n$clr-indigo-a200: map-get($clr-indigo-list, \"a200\");\n$clr-indigo-a400: map-get($clr-indigo-list, \"a400\");\n$clr-indigo-a700: map-get($clr-indigo-list, \"a700\");\n\n\n//\n// Blue\n//\n\n$clr-blue-list: (\n  \"base\": #2196f3,\n  \"50\":   #e3f2fd,\n  \"100\":  #bbdefb,\n  \"200\":  #90caf9,\n  \"300\":  #64b5f6,\n  \"400\":  #42a5f5,\n  \"500\":  #2196f3,\n  \"600\":  #1e88e5,\n  \"700\":  #1976d2,\n  \"800\":  #1565c0,\n  \"900\":  #0d47a1,\n  \"a100\": #82b1ff,\n  \"a200\": #448aff,\n  \"a400\": #2979ff,\n  \"a700\": #2962ff\n);\n\n$clr-blue:      map-get($clr-blue-list, \"base\");\n\n$clr-blue-50:   map-get($clr-blue-list, \"50\");\n$clr-blue-100:  map-get($clr-blue-list, \"100\");\n$clr-blue-200:  map-get($clr-blue-list, \"200\");\n$clr-blue-300:  map-get($clr-blue-list, \"300\");\n$clr-blue-400:  map-get($clr-blue-list, \"400\");\n$clr-blue-500:  map-get($clr-blue-list, \"500\");\n$clr-blue-600:  map-get($clr-blue-list, \"600\");\n$clr-blue-700:  map-get($clr-blue-list, \"700\");\n$clr-blue-800:  map-get($clr-blue-list, \"800\");\n$clr-blue-900:  map-get($clr-blue-list, \"900\");\n$clr-blue-a100: map-get($clr-blue-list, \"a100\");\n$clr-blue-a200: map-get($clr-blue-list, \"a200\");\n$clr-blue-a400: map-get($clr-blue-list, \"a400\");\n$clr-blue-a700: map-get($clr-blue-list, \"a700\");\n\n\n//\n// Light Blue\n//\n\n$clr-light-blue-list: (\n  \"base\": #03a9f4,\n  \"50\":   #e1f5fe,\n  \"100\":  #b3e5fc,\n  \"200\":  #81d4fa,\n  \"300\":  #4fc3f7,\n  \"400\":  #29b6f6,\n  \"500\":  #03a9f4,\n  \"600\":  #039be5,\n  \"700\":  #0288d1,\n  \"800\":  #0277bd,\n  \"900\":  #01579b,\n  \"a100\": #80d8ff,\n  \"a200\": #40c4ff,\n  \"a400\": #00b0ff,\n  \"a700\": #0091ea\n);\n\n$clr-light-blue:      map-get($clr-light-blue-list, \"base\");\n\n$clr-light-blue-50:   map-get($clr-light-blue-list, \"50\");\n$clr-light-blue-100:  map-get($clr-light-blue-list, \"100\");\n$clr-light-blue-200:  map-get($clr-light-blue-list, \"200\");\n$clr-light-blue-300:  map-get($clr-light-blue-list, \"300\");\n$clr-light-blue-400:  map-get($clr-light-blue-list, \"400\");\n$clr-light-blue-500:  map-get($clr-light-blue-list, \"500\");\n$clr-light-blue-600:  map-get($clr-light-blue-list, \"600\");\n$clr-light-blue-700:  map-get($clr-light-blue-list, \"700\");\n$clr-light-blue-800:  map-get($clr-light-blue-list, \"800\");\n$clr-light-blue-900:  map-get($clr-light-blue-list, \"900\");\n$clr-light-blue-a100: map-get($clr-light-blue-list, \"a100\");\n$clr-light-blue-a200: map-get($clr-light-blue-list, \"a200\");\n$clr-light-blue-a400: map-get($clr-light-blue-list, \"a400\");\n$clr-light-blue-a700: map-get($clr-light-blue-list, \"a700\");\n\n\n//\n// Cyan\n//\n\n$clr-cyan-list: (\n  \"base\": #00bcd4,\n  \"50\":   #e0f7fa,\n  \"100\":  #b2ebf2,\n  \"200\":  #80deea,\n  \"300\":  #4dd0e1,\n  \"400\":  #26c6da,\n  \"500\":  #00bcd4,\n  \"600\":  #00acc1,\n  \"700\":  #0097a7,\n  \"800\":  #00838f,\n  \"900\":  #006064,\n  \"a100\": #84ffff,\n  \"a200\": #18ffff,\n  \"a400\": #00e5ff,\n  \"a700\": #00b8d4\n);\n\n$clr-cyan:      map-get($clr-cyan-list, \"base\");\n\n$clr-cyan-50:   map-get($clr-cyan-list, \"50\");\n$clr-cyan-100:  map-get($clr-cyan-list, \"100\");\n$clr-cyan-200:  map-get($clr-cyan-list, \"200\");\n$clr-cyan-300:  map-get($clr-cyan-list, \"300\");\n$clr-cyan-400:  map-get($clr-cyan-list, \"400\");\n$clr-cyan-500:  map-get($clr-cyan-list, \"500\");\n$clr-cyan-600:  map-get($clr-cyan-list, \"600\");\n$clr-cyan-700:  map-get($clr-cyan-list, \"700\");\n$clr-cyan-800:  map-get($clr-cyan-list, \"800\");\n$clr-cyan-900:  map-get($clr-cyan-list, \"900\");\n$clr-cyan-a100: map-get($clr-cyan-list, \"a100\");\n$clr-cyan-a200: map-get($clr-cyan-list, \"a200\");\n$clr-cyan-a400: map-get($clr-cyan-list, \"a400\");\n$clr-cyan-a700: map-get($clr-cyan-list, \"a700\");\n\n\n//\n// Teal\n//\n\n$clr-teal-list: (\n  \"base\": #009688,\n  \"50\":   #e0f2f1,\n  \"100\":  #b2dfdb,\n  \"200\":  #80cbc4,\n  \"300\":  #4db6ac,\n  \"400\":  #26a69a,\n  \"500\":  #009688,\n  \"600\":  #00897b,\n  \"700\":  #00796b,\n  \"800\":  #00695c,\n  \"900\":  #004d40,\n  \"a100\": #a7ffeb,\n  \"a200\": #64ffda,\n  \"a400\": #1de9b6,\n  \"a700\": #00bfa5\n);\n\n$clr-teal:      map-get($clr-teal-list, \"base\");\n\n$clr-teal-50:   map-get($clr-teal-list, \"50\");\n$clr-teal-100:  map-get($clr-teal-list, \"100\");\n$clr-teal-200:  map-get($clr-teal-list, \"200\");\n$clr-teal-300:  map-get($clr-teal-list, \"300\");\n$clr-teal-400:  map-get($clr-teal-list, \"400\");\n$clr-teal-500:  map-get($clr-teal-list, \"500\");\n$clr-teal-600:  map-get($clr-teal-list, \"600\");\n$clr-teal-700:  map-get($clr-teal-list, \"700\");\n$clr-teal-800:  map-get($clr-teal-list, \"800\");\n$clr-teal-900:  map-get($clr-teal-list, \"900\");\n$clr-teal-a100: map-get($clr-teal-list, \"a100\");\n$clr-teal-a200: map-get($clr-teal-list, \"a200\");\n$clr-teal-a400: map-get($clr-teal-list, \"a400\");\n$clr-teal-a700: map-get($clr-teal-list, \"a700\");\n\n\n//\n// Green\n//\n\n$clr-green-list: (\n  \"base\": #4caf50,\n  \"50\":   #e8f5e9,\n  \"100\":  #c8e6c9,\n  \"200\":  #a5d6a7,\n  \"300\":  #81c784,\n  \"400\":  #66bb6a,\n  \"500\":  #4caf50,\n  \"600\":  #43a047,\n  \"700\":  #388e3c,\n  \"800\":  #2e7d32,\n  \"900\":  #1b5e20,\n  \"a100\": #b9f6ca,\n  \"a200\": #69f0ae,\n  \"a400\": #00e676,\n  \"a700\": #00c853\n);\n\n$clr-green:      map-get($clr-green-list, \"base\");\n\n$clr-green-50:   map-get($clr-green-list, \"50\");\n$clr-green-100:  map-get($clr-green-list, \"100\");\n$clr-green-200:  map-get($clr-green-list, \"200\");\n$clr-green-300:  map-get($clr-green-list, \"300\");\n$clr-green-400:  map-get($clr-green-list, \"400\");\n$clr-green-500:  map-get($clr-green-list, \"500\");\n$clr-green-600:  map-get($clr-green-list, \"600\");\n$clr-green-700:  map-get($clr-green-list, \"700\");\n$clr-green-800:  map-get($clr-green-list, \"800\");\n$clr-green-900:  map-get($clr-green-list, \"900\");\n$clr-green-a100: map-get($clr-green-list, \"a100\");\n$clr-green-a200: map-get($clr-green-list, \"a200\");\n$clr-green-a400: map-get($clr-green-list, \"a400\");\n$clr-green-a700: map-get($clr-green-list, \"a700\");\n\n\n//\n// Light green\n//\n\n$clr-light-green-list: (\n  \"base\": #8bc34a,\n  \"50\":   #f1f8e9,\n  \"100\":  #dcedc8,\n  \"200\":  #c5e1a5,\n  \"300\":  #aed581,\n  \"400\":  #9ccc65,\n  \"500\":  #8bc34a,\n  \"600\":  #7cb342,\n  \"700\":  #689f38,\n  \"800\":  #558b2f,\n  \"900\":  #33691e,\n  \"a100\": #ccff90,\n  \"a200\": #b2ff59,\n  \"a400\": #76ff03,\n  \"a700\": #64dd17\n);\n\n$clr-light-green:      map-get($clr-light-green-list, \"base\");\n\n$clr-light-green-50:   map-get($clr-light-green-list, \"50\");\n$clr-light-green-100:  map-get($clr-light-green-list, \"100\");\n$clr-light-green-200:  map-get($clr-light-green-list, \"200\");\n$clr-light-green-300:  map-get($clr-light-green-list, \"300\");\n$clr-light-green-400:  map-get($clr-light-green-list, \"400\");\n$clr-light-green-500:  map-get($clr-light-green-list, \"500\");\n$clr-light-green-600:  map-get($clr-light-green-list, \"600\");\n$clr-light-green-700:  map-get($clr-light-green-list, \"700\");\n$clr-light-green-800:  map-get($clr-light-green-list, \"800\");\n$clr-light-green-900:  map-get($clr-light-green-list, \"900\");\n$clr-light-green-a100: map-get($clr-light-green-list, \"a100\");\n$clr-light-green-a200: map-get($clr-light-green-list, \"a200\");\n$clr-light-green-a400: map-get($clr-light-green-list, \"a400\");\n$clr-light-green-a700: map-get($clr-light-green-list, \"a700\");\n\n\n//\n// Lime\n//\n\n$clr-lime-list: (\n  \"base\": #cddc39,\n  \"50\":   #f9fbe7,\n  \"100\":  #f0f4c3,\n  \"200\":  #e6ee9c,\n  \"300\":  #dce775,\n  \"400\":  #d4e157,\n  \"500\":  #cddc39,\n  \"600\":  #c0ca33,\n  \"700\":  #afb42b,\n  \"800\":  #9e9d24,\n  \"900\":  #827717,\n  \"a100\": #f4ff81,\n  \"a200\": #eeff41,\n  \"a400\": #c6ff00,\n  \"a700\": #aeea00\n);\n\n$clr-lime:      map-get($clr-lime-list, \"base\");\n\n$clr-lime-50:   map-get($clr-lime-list, \"50\");\n$clr-lime-100:  map-get($clr-lime-list, \"100\");\n$clr-lime-200:  map-get($clr-lime-list, \"200\");\n$clr-lime-300:  map-get($clr-lime-list, \"300\");\n$clr-lime-400:  map-get($clr-lime-list, \"400\");\n$clr-lime-500:  map-get($clr-lime-list, \"500\");\n$clr-lime-600:  map-get($clr-lime-list, \"600\");\n$clr-lime-700:  map-get($clr-lime-list, \"700\");\n$clr-lime-800:  map-get($clr-lime-list, \"800\");\n$clr-lime-900:  map-get($clr-lime-list, \"900\");\n$clr-lime-a100: map-get($clr-lime-list, \"a100\");\n$clr-lime-a200: map-get($clr-lime-list, \"a200\");\n$clr-lime-a400: map-get($clr-lime-list, \"a400\");\n$clr-lime-a700: map-get($clr-lime-list, \"a700\");\n\n\n//\n// Yellow\n//\n\n$clr-yellow-list: (\n  \"base\": #ffeb3b,\n  \"50\":   #fffde7,\n  \"100\":  #fff9c4,\n  \"200\":  #fff59d,\n  \"300\":  #fff176,\n  \"400\":  #ffee58,\n  \"500\":  #ffeb3b,\n  \"600\":  #fdd835,\n  \"700\":  #fbc02d,\n  \"800\":  #f9a825,\n  \"900\":  #f57f17,\n  \"a100\": #ffff8d,\n  \"a200\": #ffff00,\n  \"a400\": #ffea00,\n  \"a700\": #ffd600\n);\n\n$clr-yellow:      map-get($clr-yellow-list, \"base\");\n\n$clr-yellow-50:   map-get($clr-yellow-list, \"50\");\n$clr-yellow-100:  map-get($clr-yellow-list, \"100\");\n$clr-yellow-200:  map-get($clr-yellow-list, \"200\");\n$clr-yellow-300:  map-get($clr-yellow-list, \"300\");\n$clr-yellow-400:  map-get($clr-yellow-list, \"400\");\n$clr-yellow-500:  map-get($clr-yellow-list, \"500\");\n$clr-yellow-600:  map-get($clr-yellow-list, \"600\");\n$clr-yellow-700:  map-get($clr-yellow-list, \"700\");\n$clr-yellow-800:  map-get($clr-yellow-list, \"800\");\n$clr-yellow-900:  map-get($clr-yellow-list, \"900\");\n$clr-yellow-a100: map-get($clr-yellow-list, \"a100\");\n$clr-yellow-a200: map-get($clr-yellow-list, \"a200\");\n$clr-yellow-a400: map-get($clr-yellow-list, \"a400\");\n$clr-yellow-a700: map-get($clr-yellow-list, \"a700\");\n\n\n//\n// amber\n//\n\n$clr-amber-list: (\n  \"base\": #ffc107,\n  \"50\":   #fff8e1,\n  \"100\":  #ffecb3,\n  \"200\":  #ffe082,\n  \"300\":  #ffd54f,\n  \"400\":  #ffca28,\n  \"500\":  #ffc107,\n  \"600\":  #ffb300,\n  \"700\":  #ffa000,\n  \"800\":  #ff8f00,\n  \"900\":  #ff6f00,\n  \"a100\": #ffe57f,\n  \"a200\": #ffd740,\n  \"a400\": #ffc400,\n  \"a700\": #ffab00\n);\n\n$clr-amber:      map-get($clr-amber-list, \"base\");\n\n$clr-amber-50:   map-get($clr-amber-list, \"50\");\n$clr-amber-100:  map-get($clr-amber-list, \"100\");\n$clr-amber-200:  map-get($clr-amber-list, \"200\");\n$clr-amber-300:  map-get($clr-amber-list, \"300\");\n$clr-amber-400:  map-get($clr-amber-list, \"400\");\n$clr-amber-500:  map-get($clr-amber-list, \"500\");\n$clr-amber-600:  map-get($clr-amber-list, \"600\");\n$clr-amber-700:  map-get($clr-amber-list, \"700\");\n$clr-amber-800:  map-get($clr-amber-list, \"800\");\n$clr-amber-900:  map-get($clr-amber-list, \"900\");\n$clr-amber-a100: map-get($clr-amber-list, \"a100\");\n$clr-amber-a200: map-get($clr-amber-list, \"a200\");\n$clr-amber-a400: map-get($clr-amber-list, \"a400\");\n$clr-amber-a700: map-get($clr-amber-list, \"a700\");\n\n\n//\n// Orange\n//\n\n$clr-orange-list: (\n  \"base\": #ff9800,\n  \"50\":   #fff3e0,\n  \"100\":  #ffe0b2,\n  \"200\":  #ffcc80,\n  \"300\":  #ffb74d,\n  \"400\":  #ffa726,\n  \"500\":  #ff9800,\n  \"600\":  #fb8c00,\n  \"700\":  #f57c00,\n  \"800\":  #ef6c00,\n  \"900\":  #e65100,\n  \"a100\": #ffd180,\n  \"a200\": #ffab40,\n  \"a400\": #ff9100,\n  \"a700\": #ff6d00\n);\n\n$clr-orange:      map-get($clr-orange-list, \"base\");\n\n$clr-orange-50:   map-get($clr-orange-list, \"50\");\n$clr-orange-100:  map-get($clr-orange-list, \"100\");\n$clr-orange-200:  map-get($clr-orange-list, \"200\");\n$clr-orange-300:  map-get($clr-orange-list, \"300\");\n$clr-orange-400:  map-get($clr-orange-list, \"400\");\n$clr-orange-500:  map-get($clr-orange-list, \"500\");\n$clr-orange-600:  map-get($clr-orange-list, \"600\");\n$clr-orange-700:  map-get($clr-orange-list, \"700\");\n$clr-orange-800:  map-get($clr-orange-list, \"800\");\n$clr-orange-900:  map-get($clr-orange-list, \"900\");\n$clr-orange-a100: map-get($clr-orange-list, \"a100\");\n$clr-orange-a200: map-get($clr-orange-list, \"a200\");\n$clr-orange-a400: map-get($clr-orange-list, \"a400\");\n$clr-orange-a700: map-get($clr-orange-list, \"a700\");\n\n\n//\n// Deep orange\n//\n\n$clr-deep-orange-list: (\n  \"base\": #ff5722,\n  \"50\":   #fbe9e7,\n  \"100\":  #ffccbc,\n  \"200\":  #ffab91,\n  \"300\":  #ff8a65,\n  \"400\":  #ff7043,\n  \"500\":  #ff5722,\n  \"600\":  #f4511e,\n  \"700\":  #e64a19,\n  \"800\":  #d84315,\n  \"900\":  #bf360c,\n  \"a100\": #ff9e80,\n  \"a200\": #ff6e40,\n  \"a400\": #ff3d00,\n  \"a700\": #dd2c00\n);\n\n$clr-deep-orange:      map-get($clr-deep-orange-list, \"base\");\n\n$clr-deep-orange-50:   map-get($clr-deep-orange-list, \"50\");\n$clr-deep-orange-100:  map-get($clr-deep-orange-list, \"100\");\n$clr-deep-orange-200:  map-get($clr-deep-orange-list, \"200\");\n$clr-deep-orange-300:  map-get($clr-deep-orange-list, \"300\");\n$clr-deep-orange-400:  map-get($clr-deep-orange-list, \"400\");\n$clr-deep-orange-500:  map-get($clr-deep-orange-list, \"500\");\n$clr-deep-orange-600:  map-get($clr-deep-orange-list, \"600\");\n$clr-deep-orange-700:  map-get($clr-deep-orange-list, \"700\");\n$clr-deep-orange-800:  map-get($clr-deep-orange-list, \"800\");\n$clr-deep-orange-900:  map-get($clr-deep-orange-list, \"900\");\n$clr-deep-orange-a100: map-get($clr-deep-orange-list, \"a100\");\n$clr-deep-orange-a200: map-get($clr-deep-orange-list, \"a200\");\n$clr-deep-orange-a400: map-get($clr-deep-orange-list, \"a400\");\n$clr-deep-orange-a700: map-get($clr-deep-orange-list, \"a700\");\n\n\n//\n// Brown\n//\n\n$clr-brown-list: (\n  \"base\": #795548,\n  \"50\":   #efebe9,\n  \"100\":  #d7ccc8,\n  \"200\":  #bcaaa4,\n  \"300\":  #a1887f,\n  \"400\":  #8d6e63,\n  \"500\":  #795548,\n  \"600\":  #6d4c41,\n  \"700\":  #5d4037,\n  \"800\":  #4e342e,\n  \"900\":  #3e2723,\n);\n\n$clr-brown:     map-get($clr-brown-list, \"base\");\n\n$clr-brown-50:  map-get($clr-brown-list, \"50\");\n$clr-brown-100: map-get($clr-brown-list, \"100\");\n$clr-brown-200: map-get($clr-brown-list, \"200\");\n$clr-brown-300: map-get($clr-brown-list, \"300\");\n$clr-brown-400: map-get($clr-brown-list, \"400\");\n$clr-brown-500: map-get($clr-brown-list, \"500\");\n$clr-brown-600: map-get($clr-brown-list, \"600\");\n$clr-brown-700: map-get($clr-brown-list, \"700\");\n$clr-brown-800: map-get($clr-brown-list, \"800\");\n$clr-brown-900: map-get($clr-brown-list, \"900\");\n\n\n//\n// Grey\n//\n\n$clr-grey-list: (\n  \"base\": #9e9e9e,\n  \"50\":   #fafafa,\n  \"100\":  #f5f5f5,\n  \"200\":  #eeeeee,\n  \"300\":  #e0e0e0,\n  \"400\":  #bdbdbd,\n  \"500\":  #9e9e9e,\n  \"600\":  #757575,\n  \"700\":  #616161,\n  \"800\":  #424242,\n  \"900\":  #212121,\n);\n\n$clr-grey:     map-get($clr-grey-list, \"base\");\n\n$clr-grey-50:  map-get($clr-grey-list, \"50\");\n$clr-grey-100: map-get($clr-grey-list, \"100\");\n$clr-grey-200: map-get($clr-grey-list, \"200\");\n$clr-grey-300: map-get($clr-grey-list, \"300\");\n$clr-grey-400: map-get($clr-grey-list, \"400\");\n$clr-grey-500: map-get($clr-grey-list, \"500\");\n$clr-grey-600: map-get($clr-grey-list, \"600\");\n$clr-grey-700: map-get($clr-grey-list, \"700\");\n$clr-grey-800: map-get($clr-grey-list, \"800\");\n$clr-grey-900: map-get($clr-grey-list, \"900\");\n\n\n//\n// Blue grey\n//\n\n$clr-blue-grey-list: (\n  \"base\": #607d8b,\n  \"50\":   #eceff1,\n  \"100\":  #cfd8dc,\n  \"200\":  #b0bec5,\n  \"300\":  #90a4ae,\n  \"400\":  #78909c,\n  \"500\":  #607d8b,\n  \"600\":  #546e7a,\n  \"700\":  #455a64,\n  \"800\":  #37474f,\n  \"900\":  #263238,\n);\n\n$clr-blue-grey:     map-get($clr-blue-grey-list, \"base\");\n\n$clr-blue-grey-50:  map-get($clr-blue-grey-list, \"50\");\n$clr-blue-grey-100: map-get($clr-blue-grey-list, \"100\");\n$clr-blue-grey-200: map-get($clr-blue-grey-list, \"200\");\n$clr-blue-grey-300: map-get($clr-blue-grey-list, \"300\");\n$clr-blue-grey-400: map-get($clr-blue-grey-list, \"400\");\n$clr-blue-grey-500: map-get($clr-blue-grey-list, \"500\");\n$clr-blue-grey-600: map-get($clr-blue-grey-list, \"600\");\n$clr-blue-grey-700: map-get($clr-blue-grey-list, \"700\");\n$clr-blue-grey-800: map-get($clr-blue-grey-list, \"800\");\n$clr-blue-grey-900: map-get($clr-blue-grey-list, \"900\");\n\n\n//\n// Black\n//\n\n$clr-black-list: (\n  \"base\": #000\n);\n\n$clr-black: map-get($clr-black-list, \"base\");\n\n\n//\n// White\n//\n\n$clr-white-list: (\n  \"base\": #fff\n);\n\n$clr-white: map-get($clr-white-list, \"base\");\n\n\n//\n// List for all Colors for looping\n//\n\n$clr-list-all: (\n  \"red\":         $clr-red-list,\n  \"pink\":        $clr-pink-list,\n  \"purple\":      $clr-purple-list,\n  \"deep-purple\": $clr-deep-purple-list,\n  \"indigo\":      $clr-indigo-list,\n  \"blue\":        $clr-blue-list,\n  \"light-blue\":  $clr-light-blue-list,\n  \"cyan\":        $clr-cyan-list,\n  \"teal\":        $clr-teal-list,\n  \"green\":       $clr-green-list,\n  \"light-green\": $clr-light-green-list,\n  \"lime\":        $clr-lime-list,\n  \"yellow\":      $clr-yellow-list,\n  \"amber\":       $clr-amber-list,\n  \"orange\":      $clr-orange-list,\n  \"deep-orange\": $clr-deep-orange-list,\n  \"brown\":       $clr-brown-list,\n  \"grey\":        $clr-grey-list,\n  \"blue-grey\":   $clr-blue-grey-list,\n  \"black\":       $clr-black-list,\n  \"white\":       $clr-white-list\n);\n\n\n//\n// Typography\n//\n\n$clr-ui-display-4: $clr-grey-600;\n$clr-ui-display-3: $clr-grey-600;\n$clr-ui-display-2: $clr-grey-600;\n$clr-ui-display-1: $clr-grey-600;\n$clr-ui-headline:  $clr-grey-900;\n$clr-ui-title:     $clr-grey-900;\n$clr-ui-subhead-1: $clr-grey-900;\n$clr-ui-body-2:    $clr-grey-900;\n$clr-ui-body-1:    $clr-grey-900;\n$clr-ui-caption:   $clr-grey-600;\n$clr-ui-menu:      $clr-grey-900;\n$clr-ui-button:    $clr-grey-900;\n", "////\n/// Copyright (c) 2016-2021 <PERSON> <<EMAIL>>\n///\n/// Permission is hereby granted, free of charge, to any person obtaining a\n/// copy of this software and associated documentation files (the \"Software\"),\n/// to deal in the Software without restriction, including without limitation\n/// the rights to use, copy, modify, merge, publish, distribute, sublicense,\n/// and/or sell copies of the Software, and to permit persons to whom the\n/// Software is furnished to do so, subject to the following conditions:\n///\n/// The above copyright notice and this permission notice shall be included in\n/// all copies or substantial portions of the Software.\n///\n/// THE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR\n/// IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,\n/// FITNESS FOR A PARTICULAR PURPOSE AND NON-INFRINGEMENT. IN NO EVENT SHALL\n/// THE AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER\n/// LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING\n/// FROM, OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER\n/// DEALINGS\n////\n\n// ----------------------------------------------------------------------------\n// Rules\n// ----------------------------------------------------------------------------\n\n// Icon definitions\n:root {\n  --md-footnotes-icon: svg-load(\"material/keyboard-return.svg\");\n}\n\n// ----------------------------------------------------------------------------\n\n// Scoped in typesetted content to match specificity of regular content\n.md-typeset {\n\n  // Footnote reference\n  [id^=\"fnref:\"]:target {\n    scroll-margin-top: initial;\n    margin-top: -1 * px2rem(48px + 24px - 4px);\n    padding-top: px2rem(48px + 24px - 4px);\n  }\n\n  // Footnote\n  [id^=\"fn:\"]:target {\n    scroll-margin-top: initial;\n    margin-top: -1 * px2rem(48px + 24px - 3px);\n    padding-top: px2rem(48px + 24px - 3px);\n  }\n\n  // Footnote container\n  .footnote {\n    color: var(--md-default-fg-color--light);\n    font-size: px2rem(12.8px);\n\n    // Footnote list - omit left indentation\n    ol {\n      margin-left: 0;\n    }\n\n    // Footnote list item\n    li {\n      transition: color 125ms;\n\n      // Darken color on target\n      &:target {\n        color: var(--md-default-fg-color);\n      }\n\n      // Show backreferences on footnote hover\n      &:hover  .footnote-backref,\n      &:target .footnote-backref {\n        transform: translateX(0);\n        opacity: 1;\n      }\n\n      // Adjust spacing on first child\n      > :first-child {\n        margin-top: 0;\n      }\n    }\n  }\n\n  // Footnote backreference\n  .footnote-backref {\n    display: inline-block;\n    color: var(--md-typeset-a-color);\n    // Hack: omit Unicode arrow for replacement with icon\n    font-size: 0;\n    vertical-align: text-bottom;\n    transform: translateX(px2rem(5px));\n    opacity: 0;\n    transition:\n      color     250ms,\n      transform 250ms 250ms,\n      opacity   125ms 250ms;\n\n    // [print]: Show footnote backreferences\n    @media print {\n      color: var(--md-typeset-a-color);\n      transform: translateX(0);\n      opacity: 1;\n    }\n\n    // Adjust for right-to-left languages\n    [dir=\"rtl\"] & {\n      transform: translateX(px2rem(-5px));\n    }\n\n    // Adjust color on hover\n    &:hover {\n      color: var(--md-accent-fg-color);\n    }\n\n    // Footnote backreference icon\n    &::before {\n      display: inline-block;\n      width: px2rem(16px);\n      height: px2rem(16px);\n      background-color: currentColor;\n      mask-image: var(--md-footnotes-icon);\n      mask-repeat: no-repeat;\n      mask-size: contain;\n      content: \"\";\n\n      // Adjust for right-to-left languages\n      [dir=\"rtl\"] & {\n\n        // Flip icon vertically\n        svg {\n          transform: scaleX(-1);\n        }\n      }\n    }\n  }\n}\n", "////\n/// Copyright (c) 2016-2021 <PERSON> <<EMAIL>>\n///\n/// Permission is hereby granted, free of charge, to any person obtaining a\n/// copy of this software and associated documentation files (the \"Software\"),\n/// to deal in the Software without restriction, including without limitation\n/// the rights to use, copy, modify, merge, publish, distribute, sublicense,\n/// and/or sell copies of the Software, and to permit persons to whom the\n/// Software is furnished to do so, subject to the following conditions:\n///\n/// The above copyright notice and this permission notice shall be included in\n/// all copies or substantial portions of the Software.\n///\n/// THE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR\n/// IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,\n/// FITNESS FOR A PARTICULAR PURPOSE AND NON-INFRINGEMENT. IN NO EVENT SHALL\n/// THE AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER\n/// LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING\n/// FROM, OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER\n/// DEALINGS\n////\n\n// ----------------------------------------------------------------------------\n// Rules\n// ----------------------------------------------------------------------------\n\n// Scoped in typesetted content to match specificity of regular content\n.md-typeset {\n\n  // Headerlink\n  .headerlink {\n    display: inline-block;\n    margin-left: px2rem(10px);\n    color: var(--md-default-fg-color--lighter);\n    opacity: 0;\n    transition:\n      color      250ms,\n      opacity    125ms;\n\n    // [print]: Hide headerlinks\n    @media print {\n      display: none;\n    }\n\n    // Adjust for right-to-left languages\n    [dir=\"rtl\"] & {\n      margin-right: px2rem(10px);\n      margin-left: initial;\n    }\n  }\n\n  // Show headerlinks on parent hover\n  :hover  > .headerlink,\n  :target > .headerlink,\n  .headerlink:focus {\n    opacity: 1;\n    transition:\n      color      250ms,\n      opacity    125ms;\n  }\n\n  // Adjust color on parent target or focus/hover\n  :target > .headerlink,\n  .headerlink:focus,\n  .headerlink:hover {\n    color: var(--md-accent-fg-color);\n  }\n\n  // Adjust scroll offset for all elements with `id` attributes - general scroll\n  // margin offset for anything that can be targeted. Browser support is pretty\n  // decent by now, but Edge <79 and Safari (iOS and macOS) still don't support\n  // it properly, so we settle with a cross-browser anchor correction solution.\n  :target {\n    scroll-margin-top: px2rem(48px + 24px);\n  }\n\n  // Adjust scroll offset for headlines of level 1-3\n  h1:target,\n  h2:target,\n  h3:target {\n    scroll-margin-top: initial;\n\n    // Anchor correction hack\n    &::before {\n      display: block;\n      margin-top: -1 * px2rem(48px + 24px - 4px);\n      padding-top: px2rem(48px + 24px - 4px);\n      content: \"\";\n    }\n  }\n\n  // Adjust scroll offset for headlines of level 4\n  h4:target {\n    scroll-margin-top: initial;\n\n    // Anchor correction hack\n    &::before {\n      display: block;\n      margin-top: -1 * px2rem(48px + 24px - 3px);\n      padding-top: px2rem(48px + 24px - 3px);\n      content: \"\";\n    }\n  }\n\n  // Adjust scroll offset for headlines of level 5-6\n  h5:target,\n  h6:target {\n    scroll-margin-top: initial;\n\n    // Anchor correction hack\n    &::before {\n      display: block;\n      margin-top: -1 * px2rem(48px + 24px);\n      padding-top: px2rem(48px + 24px);\n      content: \"\";\n    }\n  }\n}\n", "////\n/// Copyright (c) 2016-2021 <PERSON> <<EMAIL>>\n///\n/// Permission is hereby granted, free of charge, to any person obtaining a\n/// copy of this software and associated documentation files (the \"Software\"),\n/// to deal in the Software without restriction, including without limitation\n/// the rights to use, copy, modify, merge, publish, distribute, sublicense,\n/// and/or sell copies of the Software, and to permit persons to whom the\n/// Software is furnished to do so, subject to the following conditions:\n///\n/// The above copyright notice and this permission notice shall be included in\n/// all copies or substantial portions of the Software.\n///\n/// THE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR\n/// IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,\n/// FITNESS FOR A PARTICULAR PURPOSE AND NON-INFRINGEMENT. IN NO EVENT SHALL\n/// THE AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER\n/// LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING\n/// FROM, OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER\n/// DEALINGS\n////\n\n// ----------------------------------------------------------------------------\n// Rules\n// ----------------------------------------------------------------------------\n\n// Scoped in typesetted content to match specificity of regular content\n.md-typeset {\n\n  // Arithmatex container\n  div.arithmatex {\n    overflow: auto;\n\n    // [mobile -]: Align with body copy\n    @include break-to-device(mobile) {\n      margin: 0 px2rem(-16px);\n    }\n\n    // Arithmatex content\n    > * {\n      width: min-content;\n      // stylelint-disable-next-line declaration-no-important\n      margin: 1em auto !important;\n      padding: 0 px2rem(16px);\n      touch-action: auto;\n    }\n  }\n}\n", "////\n/// Copyright (c) 2016-2021 <PERSON> <<EMAIL>>\n///\n/// Permission is hereby granted, free of charge, to any person obtaining a\n/// copy of this software and associated documentation files (the \"Software\"),\n/// to deal in the Software without restriction, including without limitation\n/// the rights to use, copy, modify, merge, publish, distribute, sublicense,\n/// and/or sell copies of the Software, and to permit persons to whom the\n/// Software is furnished to do so, subject to the following conditions:\n///\n/// The above copyright notice and this permission notice shall be included in\n/// all copies or substantial portions of the Software.\n///\n/// THE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR\n/// IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,\n/// FITNESS FOR A PARTICULAR PURPOSE AND NON-INFRINGEMENT. IN NO EVENT SHALL\n/// THE AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER\n/// LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING\n/// FROM, OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER\n/// DEALINGS\n////\n\n// ----------------------------------------------------------------------------\n// Rules\n// ----------------------------------------------------------------------------\n\n// Scoped in typesetted content to match specificity of regular content\n.md-typeset {\n\n  // Deletion, addition or comment\n  del.critic,\n  ins.critic,\n  .critic.comment {\n    box-decoration-break: clone;\n  }\n\n  // Deletion\n  del.critic {\n    background-color: var(--md-typeset-del-color);\n  }\n\n  // Addition\n  ins.critic {\n    background-color: var(--md-typeset-ins-color);\n  }\n\n  // Comment\n  .critic.comment {\n    color: var(--md-code-hl-comment-color);\n\n    // Comment opening mark\n    &::before {\n      content: \"/* \";\n    }\n\n    // Comment closing mark\n    &::after {\n      content: \" */\";\n    }\n  }\n\n  // Critic block\n  .critic.block {\n    display: block;\n    margin: 1em 0;\n    padding-right: px2rem(16px);\n    padding-left: px2rem(16px);\n    overflow: auto;\n    box-shadow: none;\n\n    // Adjust spacing on first child\n    > :first-child {\n      margin-top: 0.5em;\n    }\n\n    // Adjust spacing on last child\n    > :last-child {\n      margin-bottom: 0.5em;\n    }\n  }\n}\n", "////\n/// Copyright (c) 2016-2021 <PERSON> <<EMAIL>>\n///\n/// Permission is hereby granted, free of charge, to any person obtaining a\n/// copy of this software and associated documentation files (the \"Software\"),\n/// to deal in the Software without restriction, including without limitation\n/// the rights to use, copy, modify, merge, publish, distribute, sublicense,\n/// and/or sell copies of the Software, and to permit persons to whom the\n/// Software is furnished to do so, subject to the following conditions:\n///\n/// The above copyright notice and this permission notice shall be included in\n/// all copies or substantial portions of the Software.\n///\n/// THE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR\n/// IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,\n/// FITNESS FOR A PARTICULAR PURPOSE AND NON-INFRINGEMENT. IN NO EVENT SHALL\n/// THE AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER\n/// LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING\n/// FROM, OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER\n/// DEALINGS\n////\n\n// ----------------------------------------------------------------------------\n// Rules\n// ----------------------------------------------------------------------------\n\n// Icon definitions\n:root {\n  --md-details-icon: svg-load(\"material/chevron-right.svg\");\n}\n\n// ----------------------------------------------------------------------------\n\n// Scoped in typesetted content to match specificity of regular content\n.md-typeset {\n\n  // Details\n  details {\n    @extend .admonition;\n\n    display: flow-root;\n    padding-top: 0;\n    overflow: visible;\n\n    // Details title icon - rotate icon on transition to open state\n    &[open] > summary::after {\n      transform: rotate(90deg);\n    }\n\n    // Adjust spacing for details in closed state\n    &:not([open]) {\n      padding-bottom: 0;\n      box-shadow: none;\n\n      // Hack: we cannot set `overflow: hidden` on the `details` element (which\n      // is why we set it to `overflow: visible`, as the outline would not be\n      // visible when focusing. Therefore, we must set the border radius on the\n      // summary explicitly.\n      > summary {\n        border-radius: px2rem(2px);\n      }\n    }\n\n    // Hack: omit margin collapse\n    &::after {\n      display: table;\n      content: \"\";\n    }\n  }\n\n  // Details title\n  summary {\n    @extend .admonition-title;\n\n    display: block;\n    min-height: px2rem(20px);\n    padding: px2rem(8px) px2rem(36px) px2rem(8px) px2rem(40px);\n    border-top-left-radius: px2rem(2px);\n    border-top-right-radius: px2rem(2px);\n    cursor: pointer;\n\n    // Adjust for right-to-left languages\n    [dir=\"rtl\"] & {\n      padding: px2rem(8px) px2rem(44px) px2rem(8px) px2rem(36px);\n    }\n\n    // Hide outline for pointer devices\n    &:not(.focus-visible) {\n      outline: none;\n      -webkit-tap-highlight-color: transparent;\n    }\n\n    // Details marker\n    &::after {\n      position: absolute;\n      top: px2rem(8px);\n      right: px2rem(8px);\n      width: px2rem(20px);\n      height: px2rem(20px);\n      background-color: currentColor;\n      mask-image: var(--md-details-icon);\n      mask-repeat: no-repeat;\n      mask-size: contain;\n      transform: rotate(0deg);\n      transition: transform 250ms;\n      content: \"\";\n\n      // Adjust for right-to-left languages\n      [dir=\"rtl\"] & {\n        right: initial;\n        left: px2rem(8px);\n        transform: rotate(180deg);\n      }\n    }\n\n    // Hide native details marker\n    &::marker,\n    &::-webkit-details-marker {\n      display: none;\n    }\n  }\n}\n", "////\n/// Copyright (c) 2016-2021 <PERSON> <<EMAIL>>\n///\n/// Permission is hereby granted, free of charge, to any person obtaining a\n/// copy of this software and associated documentation files (the \"Software\"),\n/// to deal in the Software without restriction, including without limitation\n/// the rights to use, copy, modify, merge, publish, distribute, sublicense,\n/// and/or sell copies of the Software, and to permit persons to whom the\n/// Software is furnished to do so, subject to the following conditions:\n///\n/// The above copyright notice and this permission notice shall be included in\n/// all copies or substantial portions of the Software.\n///\n/// THE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR\n/// IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,\n/// FITNESS FOR A PARTICULAR PURPOSE AND NON-INFRINGEMENT. IN NO EVENT SHALL\n/// THE AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER\n/// LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING\n/// FROM, OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER\n/// DEALINGS\n////\n\n// ----------------------------------------------------------------------------\n// Rules\n// ----------------------------------------------------------------------------\n\n// Scoped in typesetted content to match specificity of regular content\n.md-typeset {\n\n  // Emoji and icon container\n  .emojione,\n  .twemoji,\n  .gemoji {\n    display: inline-flex;\n    height: px2em(18px);\n    vertical-align: text-top;\n\n    // Icon - inlined via mkdocs-material-extensions\n    svg {\n      width: px2em(18px);\n      max-height: 100%;\n      fill: currentColor;\n    }\n  }\n}\n", "////\n/// Copyright (c) 2016-2021 <PERSON> <<EMAIL>>\n///\n/// Permission is hereby granted, free of charge, to any person obtaining a\n/// copy of this software and associated documentation files (the \"Software\"),\n/// to deal in the Software without restriction, including without limitation\n/// the rights to use, copy, modify, merge, publish, distribute, sublicense,\n/// and/or sell copies of the Software, and to permit persons to whom the\n/// Software is furnished to do so, subject to the following conditions:\n///\n/// The above copyright notice and this permission notice shall be included in\n/// all copies or substantial portions of the Software.\n///\n/// THE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR\n/// IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,\n/// FITNESS FOR A PARTICULAR PURPOSE AND NON-INFRINGEMENT. IN NO EVENT SHALL\n/// THE AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER\n/// LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING\n/// FROM, OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER\n/// DEALINGS\n////\n\n// ----------------------------------------------------------------------------\n// Rules: syntax highlighting\n// ----------------------------------------------------------------------------\n\n// Code block\n.highlight {\n  .o,   // Operator\n  .ow { // Operator, word\n    color: var(--md-code-hl-operator-color);\n  }\n\n  .p {  // Punctuation\n    color: var(--md-code-hl-punctuation-color);\n  }\n\n  .cpf, // Comment, preprocessor file\n  .l,   // Literal\n  .s,   // Literal, string\n  .sb,  // Literal, string backticks\n  .sc,  // Literal, string char\n  .s2,  // Literal, string double\n  .si,  // Literal, string interpol\n  .s1,  // Literal, string single\n  .ss { // Literal, string symbol\n    color: var(--md-code-hl-string-color);\n  }\n\n  .cp,  // Comment, pre-processor\n  .se,  // Literal, string escape\n  .sh,  // Literal, string heredoc\n  .sr,  // Literal, string regex\n  .sx { // Literal, string other\n    color: var(--md-code-hl-special-color);\n  }\n\n  .m,   // Number\n  .mb,  // Number, binary\n  .mf,  // Number, float\n  .mh,  // Number, hex\n  .mi,  // Number, integer\n  .il,  // Number, integer long\n  .mo { // Number, octal\n    color: var(--md-code-hl-number-color);\n  }\n\n  .k,   // Keyword,\n  .kd,  // Keyword, declaration\n  .kn,  // Keyword, namespace\n  .kp,  // Keyword, pseudo\n  .kr,  // Keyword, reserved\n  .kt { // Keyword, type\n    color: var(--md-code-hl-keyword-color);\n  }\n\n  .kc,  // Keyword, constant\n  .n {  // Name\n    color: var(--md-code-hl-name-color);\n  }\n\n  .no,  // Name, constant\n  .nb,  // Name, builtin\n  .bp { // Name, builtin pseudo\n    color: var(--md-code-hl-constant-color);\n  }\n\n  .nc,  // Name, class\n  .ne,  // Name, exception\n  .nf,  // Name, function\n  .nn { // Name, namespace\n    color: var(--md-code-hl-function-color);\n  }\n\n  .nd,  // Name, decorator\n  .ni,  // Name, entity\n  .nl,  // Name, label\n  .nt { // Name, tag\n    color: var(--md-code-hl-keyword-color);\n  }\n\n  .c,   // Comment\n  .cm,  // Comment, multiline\n  .c1,  // Comment, single\n  .ch,  // Comment, shebang\n  .cs,  // Comment, special\n  .sd { // Literal, string doc\n    color: var(--md-code-hl-comment-color);\n  }\n\n  .na,  // Name, attribute\n  .nv,  // Variable,\n  .vc,  // Variable, class\n  .vg,  // Variable, global\n  .vi { // Variable, instance\n    color: var(--md-code-hl-variable-color);\n  }\n\n  .ge,  // Generic, emph\n  .gr,  // Generic, error\n  .gh,  // Generic, heading\n  .go,  // Generic, output\n  .gp,  // Generic, prompt\n  .gs,  // Generic, strong\n  .gu,  // Generic, subheading\n  .gt { // Generic, traceback\n    color: var(--md-code-hl-generic-color);\n  }\n\n  .gd,  // Diff, delete\n  .gi { // Diff, insert\n    margin: 0 px2em(-2px);\n    padding: 0 px2em(2px);\n    border-radius: px2rem(2px);\n  }\n\n  .gd { // Diff, delete\n    background-color: var(--md-typeset-del-color);\n  }\n\n  .gi { // Diff, insert\n    background-color: var(--md-typeset-ins-color);\n  }\n\n  // Highlighted line\n  .hll {\n    display: block;\n    margin: 0 px2em(-16px, 13.6px);\n    padding: 0 px2em(16px, 13.6px);\n    background-color: var(--md-code-hl-color);\n  }\n\n  // Code block line numbers (inline)\n  [data-linenos]::before {\n    position: sticky;\n    left: px2em(-16px, 13.6px);\n    float: left;\n    margin-right: px2em(16px, 13.6px);\n    margin-left: px2em(-16px, 13.6px);\n    padding-left: px2em(16px, 13.6px);\n    color: var(--md-default-fg-color--light);\n    background-color: var(--md-code-bg-color);\n    box-shadow: px2rem(-1px) 0 var(--md-default-fg-color--lightest) inset;\n    content: attr(data-linenos);\n    user-select: none;\n  }\n}\n\n// ----------------------------------------------------------------------------\n// Rules: layout\n// ----------------------------------------------------------------------------\n\n// Code block with line numbers\n.highlighttable {\n  display: flow-root;\n  overflow: hidden;\n\n  // Set table elements to block layout, because otherwise the whole flexbox\n  // hacking won't work correctly\n  tbody,\n  td {\n    display: block;\n    padding: 0;\n  }\n\n  // We need to use flexbox layout, because otherwise it's not possible to\n  // make the code container scroll while keeping the line numbers static\n  tr {\n    display: flex;\n  }\n\n  // The pre tags are nested inside a table, so we need to omit the margin\n  // because it collapses below all the overflows\n  pre {\n    margin: 0;\n  }\n\n  // Code block line numbers - disable user selection, so code can be easily\n  // copied without accidentally also copying the line numbers\n  .linenos {\n    padding: px2em(10.5px, 13.6px) px2em(16px, 13.6px);\n    padding-right: 0;\n    font-size: px2em(13.6px);\n    background-color: var(--md-code-bg-color);\n    user-select: none;\n  }\n\n  // Code block line numbers container\n  .linenodiv {\n    padding-right: px2em(8px, 13.6px);\n    box-shadow: px2rem(-1px) 0 var(--md-default-fg-color--lightest) inset;\n\n    // Adjust colors and alignment\n    pre {\n      color: var(--md-default-fg-color--light);\n      text-align: right;\n    }\n  }\n\n  // Code block container - stretch to remaining space\n  .code {\n    flex: 1;\n    overflow: hidden;\n  }\n}\n\n// ----------------------------------------------------------------------------\n\n// Scoped in typesetted content to match specificity of regular content\n.md-typeset {\n\n  // Code block with line numbers\n  .highlighttable {\n    margin: 1em 0;\n    direction: ltr;\n    border-radius: px2rem(2px);\n\n    // Omit rounded borders on contained code block\n    code {\n      border-radius: 0;\n    }\n  }\n\n  // [mobile -]: Align with body copy\n  @include break-to-device(mobile) {\n\n    // Top-level code block\n    > .highlight {\n      margin: 1em px2rem(-16px);\n\n      // Highlighted line\n      .hll {\n        margin: 0 px2rem(-16px);\n        padding: 0 px2rem(16px);\n      }\n\n      // Omit rounded borders\n      code {\n        border-radius: 0;\n      }\n    }\n\n    // Top-level code block with line numbers\n    > .highlighttable {\n      margin: 1em px2rem(-16px);\n      border-radius: 0;\n\n      // Highlighted line\n      .hll {\n        margin: 0 px2rem(-16px);\n        padding: 0 px2rem(16px);\n      }\n    }\n  }\n}\n", "////\n/// Copyright (c) 2016-2021 <PERSON> <<EMAIL>>\n///\n/// Permission is hereby granted, free of charge, to any person obtaining a\n/// copy of this software and associated documentation files (the \"Software\"),\n/// to deal in the Software without restriction, including without limitation\n/// the rights to use, copy, modify, merge, publish, distribute, sublicense,\n/// and/or sell copies of the Software, and to permit persons to whom the\n/// Software is furnished to do so, subject to the following conditions:\n///\n/// The above copyright notice and this permission notice shall be included in\n/// all copies or substantial portions of the Software.\n///\n/// THE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR\n/// IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,\n/// FITNESS FOR A PARTICULAR PURPOSE AND NON-INFRINGEMENT. IN NO EVENT SHALL\n/// THE AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER\n/// LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING\n/// FROM, OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER\n/// DEALINGS\n////\n\n// ----------------------------------------------------------------------------\n// Rules\n// ----------------------------------------------------------------------------\n\n// Scoped in typesetted content to match specificity of regular content\n.md-typeset {\n\n  // Keyboard key\n  .keys {\n\n    // Keyboard key icon\n    kbd::before,\n    kbd::after {\n      position: relative;\n      margin: 0;\n      color: inherit;\n      -moz-osx-font-smoothing: initial;\n      -webkit-font-smoothing: initial;\n    }\n\n    // Surrounding text\n    span {\n      padding: 0 px2em(3.2px);\n      color: var(--md-default-fg-color--light);\n    }\n\n    // Define keyboard keys with left icon\n    @each $name, $code in (\n\n      // Modifiers\n      \"alt\":           \"\\2387\",\n      \"left-alt\":      \"\\2387\",\n      \"right-alt\":     \"\\2387\",\n      \"command\":       \"\\2318\",\n      \"left-command\":  \"\\2318\",\n      \"right-command\": \"\\2318\",\n      \"control\":       \"\\2303\",\n      \"left-control\":  \"\\2303\",\n      \"right-control\": \"\\2303\",\n      \"meta\":          \"\\25C6\",\n      \"left-meta\":     \"\\25C6\",\n      \"right-meta\":    \"\\25C6\",\n      \"option\":        \"\\2325\",\n      \"left-option\":   \"\\2325\",\n      \"right-option\":  \"\\2325\",\n      \"shift\":         \"\\21E7\",\n      \"left-shift\":    \"\\21E7\",\n      \"right-shift\":   \"\\21E7\",\n      \"super\":         \"\\2756\",\n      \"left-super\":    \"\\2756\",\n      \"right-super\":   \"\\2756\",\n      \"windows\":       \"\\229E\",\n      \"left-windows\":  \"\\229E\",\n      \"right-windows\": \"\\229E\",\n\n      // Other keys\n      \"arrow-down\":    \"\\2193\",\n      \"arrow-left\":    \"\\2190\",\n      \"arrow-right\":   \"\\2192\",\n      \"arrow-up\":      \"\\2191\",\n      \"backspace\":     \"\\232B\",\n      \"backtab\":       \"\\21E4\",\n      \"caps-lock\":     \"\\21EA\",\n      \"clear\":         \"\\2327\",\n      \"context-menu\":  \"\\2630\",\n      \"delete\":        \"\\2326\",\n      \"eject\":         \"\\23CF\",\n      \"end\":           \"\\2913\",\n      \"escape\":        \"\\238B\",\n      \"home\":          \"\\2912\",\n      \"insert\":        \"\\2380\",\n      \"page-down\":     \"\\21DF\",\n      \"page-up\":       \"\\21DE\",\n      \"print-screen\":  \"\\2399\"\n    ) {\n      .key-#{$name} {\n        &::before {\n          padding-right: px2em(6.4px);\n          content: $code;\n        }\n      }\n    }\n\n    // Define keyboard keys with right icon\n    @each $name, $code in (\n      \"tab\":           \"\\21E5\",\n      \"num-enter\":     \"\\2324\",\n      \"enter\":         \"\\23CE\"\n    ) {\n      .key-#{$name} {\n        &::after {\n          padding-left: px2em(6.4px);\n          content: $code;\n        }\n      }\n    }\n  }\n}\n", "////\n/// Copyright (c) 2016-2021 <PERSON> <<EMAIL>>\n///\n/// Permission is hereby granted, free of charge, to any person obtaining a\n/// copy of this software and associated documentation files (the \"Software\"),\n/// to deal in the Software without restriction, including without limitation\n/// the rights to use, copy, modify, merge, publish, distribute, sublicense,\n/// and/or sell copies of the Software, and to permit persons to whom the\n/// Software is furnished to do so, subject to the following conditions:\n///\n/// The above copyright notice and this permission notice shall be included in\n/// all copies or substantial portions of the Software.\n///\n/// THE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR\n/// IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,\n/// FITNESS FOR A PARTICULAR PURPOSE AND NON-INFRINGEMENT. IN NO EVENT SHALL\n/// THE AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER\n/// LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING\n/// FROM, OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER\n/// DEALINGS\n////\n\n// ----------------------------------------------------------------------------\n// Rules\n// ----------------------------------------------------------------------------\n\n// Scoped in typesetted content to match specificity of regular content\n.md-typeset {\n\n  // Tabbed block content\n  .tabbed-content {\n    display: none;\n    order: 99;\n    width: 100%;\n    box-shadow: 0 px2rem(-1px) var(--md-default-fg-color--lightest);\n\n    // [print]: Show all tabs (even hidden ones) when printing\n    @media print {\n      display: block;\n      order: initial;\n    }\n\n    // Code block is the only child of a tab - remove margin and mirror\n    // previous (now deprecated) SuperFences code block grouping behavior\n    > pre:only-child,\n    > .highlight:only-child pre,\n    > .highlighttable:only-child {\n      margin: 0;\n\n      // Omit rounded borders\n      > code {\n        border-top-left-radius: 0;\n        border-top-right-radius: 0;\n      }\n    }\n\n    // Adjust spacing for nested tab\n    > .tabbed-set {\n      margin: 0;\n    }\n  }\n\n  // Tabbed block container\n  .tabbed-set {\n    position: relative;\n    display: flex;\n    flex-wrap: wrap;\n    margin: 1em 0;\n    border-radius: px2rem(2px);\n\n    // Tab radio button - the Tabbed extension will generate radio buttons with\n    // labels, so tabs can be triggered without the necessity for JavaScript.\n    // This is pretty cool, as it has great accessibility out-of-the box, so\n    // we just hide the radio button and toggle the label color for indication.\n    > input {\n      position: absolute;\n      width: 0;\n      height: 0;\n      opacity: 0;\n\n      // Tab label for checked radio button\n      &:checked + label {\n        color: var(--md-accent-fg-color);\n        border-color: var(--md-accent-fg-color);\n\n        // Show tabbed block content\n        + .tabbed-content {\n          display: block;\n        }\n      }\n\n      // Tab label on focus\n      &:focus + label {\n        outline-style: auto;\n      }\n\n      // Hide outline for pointer devices\n      &:not(.focus-visible) + label {\n        outline: none;\n        -webkit-tap-highlight-color: transparent;\n      }\n    }\n\n    // Tab label\n    > label {\n      z-index: 1;\n      width: auto;\n      padding: px2em(12px, 12.8px) 1.25em px2em(10px, 12.8px);\n      color: var(--md-default-fg-color--light);\n      font-weight: 700;\n      font-size: px2rem(12.8px);\n      border-bottom: px2rem(2px) solid transparent;\n      cursor: pointer;\n      transition: color 250ms;\n\n      // Tab label on hover\n      &:hover {\n        color: var(--md-accent-fg-color);\n      }\n    }\n  }\n}\n", "////\n/// Copyright (c) 2016-2021 <PERSON> <<EMAIL>>\n///\n/// Permission is hereby granted, free of charge, to any person obtaining a\n/// copy of this software and associated documentation files (the \"Software\"),\n/// to deal in the Software without restriction, including without limitation\n/// the rights to use, copy, modify, merge, publish, distribute, sublicense,\n/// and/or sell copies of the Software, and to permit persons to whom the\n/// Software is furnished to do so, subject to the following conditions:\n///\n/// The above copyright notice and this permission notice shall be included in\n/// all copies or substantial portions of the Software.\n///\n/// THE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR\n/// IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,\n/// FITNESS FOR A PARTICULAR PURPOSE AND NON-INFRINGEMENT. IN NO EVENT SHALL\n/// THE AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER\n/// LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING\n/// FROM, OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER\n/// DEALINGS\n////\n\n// ----------------------------------------------------------------------------\n// Rules\n// ----------------------------------------------------------------------------\n\n// Icon definitions\n:root {\n  --md-tasklist-icon:\n    svg-load(\"octicons/check-circle-fill-24.svg\");\n  --md-tasklist-icon--checked:\n    svg-load(\"octicons/check-circle-fill-24.svg\");\n}\n\n// ----------------------------------------------------------------------------\n\n// Scoped in typesetted content to match specificity of regular content\n.md-typeset {\n\n  // Tasklist item\n  .task-list-item {\n    position: relative;\n    list-style-type: none;\n\n    // Make checkbox items align with normal list items, but position\n    // everything in ems for correct layout at smaller font sizes\n    [type=\"checkbox\"] {\n      position: absolute;\n      top: 0.45em;\n      left: -2em;\n\n      // Adjust for right-to-left languages\n      [dir=\"rtl\"] & {\n        right: -2em;\n        left: initial;\n      }\n    }\n  }\n\n  // Hide native checkbox, when custom classes are enabled\n  .task-list-control [type=\"checkbox\"] {\n    z-index: -1;\n    opacity: 0;\n  }\n\n  // Tasklist indicator in unchecked state\n  .task-list-indicator::before {\n    position: absolute;\n    top: 0.15em;\n    left: px2em(-24px);\n    width: px2em(20px);\n    height: px2em(20px);\n    background-color: var(--md-default-fg-color--lightest);\n    mask-image: var(--md-tasklist-icon);\n    mask-repeat: no-repeat;\n    mask-size: contain;\n    content: \"\";\n\n    // Adjust for right-to-left languages\n    [dir=\"rtl\"] & {\n      right: px2em(-24px);\n      left: initial;\n    }\n  }\n\n  // Tasklist indicator in checked state\n  [type=\"checkbox\"]:checked + .task-list-indicator::before {\n    background-color: $clr-green-a400;\n    mask-image: var(--md-tasklist-icon--checked);\n  }\n}\n", "////\n/// Copyright (c) 2016-2021 <PERSON> <<EMAIL>>\n///\n/// Permission is hereby granted, free of charge, to any person obtaining a\n/// copy of this software and associated documentation files (the \"Software\"),\n/// to deal in the Software without restriction, including without limitation\n/// the rights to use, copy, modify, merge, publish, distribute, sublicense,\n/// and/or sell copies of the Software, and to permit persons to whom the\n/// Software is furnished to do so, subject to the following conditions:\n///\n/// The above copyright notice and this permission notice shall be included in\n/// all copies or substantial portions of the Software.\n///\n/// THE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR\n/// IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,\n/// FITNESS FOR A PARTICULAR PURPOSE AND NON-INFRINGEMENT. IN NO EVENT SHALL\n/// THE AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER\n/// LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING\n/// FROM, OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER\n/// DEALINGS\n////\n\n// ----------------------------------------------------------------------------\n// Rules\n// ----------------------------------------------------------------------------\n\n// Scoped in typesetted content to match specificity of regular content\n.md-typeset {\n\n  // [tablet +]: Allow for rendering content as sidebars\n  @include break-from-device(tablet) {\n\n    // Modifier to float block elements\n    .inline {\n      float: left;\n      width: px2rem(234px);\n      margin-top: 0;\n      margin-right: px2rem(16px);\n      margin-bottom: px2rem(16px);\n\n      // Adjust for right-to-left languages\n      [dir=\"rtl\"] & {\n        float: right;\n        margin-right: 0;\n        margin-left: px2rem(16px);\n      }\n\n      // Modifier to move to end (ltr: right, rtl: left)\n      &.end {\n        float: right;\n        margin-right: 0;\n        margin-left: px2rem(16px);\n\n        // Adjust for right-to-left languages\n        [dir=\"rtl\"] & {\n          float: left;\n          margin-right: px2rem(16px);\n          margin-left: 0;\n        }\n      }\n    }\n  }\n}\n"]}