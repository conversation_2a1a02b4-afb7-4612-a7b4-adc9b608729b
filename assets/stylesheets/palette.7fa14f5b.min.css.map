{"version": 3, "sources": ["src/assets/stylesheets/palette/_accent.scss", "src/assets/stylesheets/palette.scss", "src/assets/stylesheets/palette/_primary.scss", "src/assets/stylesheets/utilities/_break.scss", "src/assets/stylesheets/palette/_scheme.scss"], "names": [], "mappings": "AA8CE,2BACE,4BAAA,CACA,qDAAA,CAOE,yBAAA,CACA,+CCnDN,CDyCE,4BACE,4BAAA,CACA,oDAAA,CAOE,yBAAA,CACA,+CC5CN,CDkCE,8BACE,4BAAA,CACA,sDAAA,CAOE,yBAAA,CACA,+CCrCN,CD2BE,mCACE,4BAAA,CACA,sDAAA,CAOE,yBAAA,CACA,+CC9BN,CDoBE,8BACE,4BAAA,CACA,sDAAA,CAOE,yBAAA,CACA,+CCvBN,CDaE,4BACE,4BAAA,CACA,sDAAA,CAOE,yBAAA,CACA,+CChBN,CDME,kCACE,4BAAA,CACA,qDAAA,CAOE,yBAAA,CACA,+CCTN,CDDE,4BACE,4BAAA,CACA,qDAAA,CAOE,yBAAA,CACA,+CCFN,CDRE,4BACE,4BAAA,CACA,qDAAA,CAOE,yBAAA,CACA,+CCKN,CDfE,6BACE,4BAAA,CACA,oDAAA,CAOE,yBAAA,CACA,+CCYN,CDtBE,mCACE,4BAAA,CACA,qDAAA,CAOE,yBAAA,CACA,+CCmBN,CD7BE,4BACE,4BAAA,CACA,qDAAA,CAIE,qCAAA,CACA,4CC6BN,CDpCE,8BACE,4BAAA,CACA,qDAAA,CAIE,qCAAA,CACA,4CCoCN,CD3CE,6BACE,yBAAA,CACA,qDAAA,CAIE,qCAAA,CACA,4CC2CN,CDlDE,8BACE,4BAAA,CACA,qDAAA,CAIE,qCAAA,CACA,4CCkDN,CDzDE,mCACE,4BAAA,CACA,sDAAA,CAOE,yBAAA,CACA,+CCsDN,CC7DE,4BACE,6BAAA,CACA,oCAAA,CACA,mCAAA,CAOE,0BAAA,CACA,gDD0DN,CCrEE,6BACE,6BAAA,CACA,oCAAA,CACA,mCAAA,CAOE,0BAAA,CACA,gDDkEN,CC7EE,+BACE,6BAAA,CACA,oCAAA,CACA,mCAAA,CAOE,0BAAA,CACA,gDD0EN,CCrFE,oCACE,6BAAA,CACA,oCAAA,CACA,mCAAA,CAOE,0BAAA,CACA,gDDkFN,CC7FE,+BACE,6BAAA,CACA,oCAAA,CACA,mCAAA,CAOE,0BAAA,CACA,gDD0FN,CCrGE,6BACE,6BAAA,CACA,oCAAA,CACA,mCAAA,CAOE,0BAAA,CACA,gDDkGN,CC7GE,mCACE,6BAAA,CACA,oCAAA,CACA,mCAAA,CAOE,0BAAA,CACA,gDD0GN,CCrHE,6BACE,6BAAA,CACA,oCAAA,CACA,mCAAA,CAOE,0BAAA,CACA,gDDkHN,CC7HE,6BACE,6BAAA,CACA,oCAAA,CACA,mCAAA,CAOE,0BAAA,CACA,gDD0HN,CCrIE,8BACE,6BAAA,CACA,oCAAA,CACA,mCAAA,CAOE,0BAAA,CACA,gDDkIN,CC7IE,oCACE,6BAAA,CACA,oCAAA,CACA,mCAAA,CAOE,0BAAA,CACA,gDD0IN,CCrJE,6BACE,6BAAA,CACA,oCAAA,CACA,mCAAA,CAIE,sCAAA,CACA,6CDqJN,CC7JE,+BACE,6BAAA,CACA,oCAAA,CACA,mCAAA,CAIE,sCAAA,CACA,6CD6JN,CCrKE,8BACE,6BAAA,CACA,oCAAA,CACA,mCAAA,CAIE,sCAAA,CACA,6CDqKN,CC7KE,+BACE,6BAAA,CACA,oCAAA,CACA,mCAAA,CAIE,sCAAA,CACA,6CD6KN,CCrLE,oCACE,6BAAA,CACA,oCAAA,CACA,mCAAA,CAOE,0BAAA,CACA,gDDkLN,CC7LE,8BACE,6BAAA,CACA,oCAAA,CACA,mCAAA,CAOE,0BAAA,CACA,gDD0LN,CCrME,6BACE,6BAAA,CACA,oCAAA,CACA,mCAAA,CAOE,0BAAA,CACA,gDDkMN,CC7ME,kCACE,6BAAA,CACA,oCAAA,CACA,mCAAA,CAOE,0BAAA,CACA,gDD0MN,CChMA,8BACE,0BAAA,CACA,gDAAA,CACA,4CAAA,CACA,sCAAA,CACA,6CAAA,CAGA,4BDiMF,CElFI,mCDzGA,gDACE,gCD8LJ,CC3LI,iEACE,qBD6LN,CCzLI,2EACE,qBD2LN,CC5LI,kEACE,qBD2LN,CC5LI,uEACE,qBD2LN,CC5LI,6DACE,qBD2LN,CCvLI,sDACE,gCDyLN,CACF,CEhGI,sCDjFA,uCACE,0CDoLJ,CACF,CC3KA,8BACE,0BAAA,CACA,6CAAA,CACA,gCAAA,CACA,0BAAA,CACA,gDAAA,CAGA,4BD4KF,CCzKE,yCACE,qBD2KJ,CE9FI,wCDtEA,8CACE,gCDuKJ,CACF,CEtHI,mCD1CA,gDACE,oCDmKJ,CChKI,sDACE,mCDkKN,CACF,CE3GI,wCD/CA,iFACE,qBD6JJ,CACF,CEnII,sCDnBA,uCACE,qBDyJJ,CACF,CG1SA,cAGE,6BAKE,YAAA,CAGA,mDAAA,CACA,6DAAA,CACA,+DAAA,CACA,gEAAA,CACA,mDAAA,CACA,6DAAA,CACA,+DAAA,CACA,gEAAA,CAGA,gDAAA,CACA,gDAAA,CAGA,wCAAA,CACA,iCAAA,CACA,kCAAA,CACA,mCAAA,CACA,mCAAA,CACA,kCAAA,CACA,iCAAA,CAGA,sDAAA,CAGA,4CAAA,CAGA,uDAAA,CACA,6DAAA,CACA,2DAAA,CAGA,0DAAA,CAGA,qDAAA,CACA,wDHuRF,CGpRE,oHAIE,4BHmRJ,CACF", "file": "src/assets/stylesheets/palette.scss", "sourcesContent": ["////\n/// Copyright (c) 2016-2021 <PERSON> <<EMAIL>>\n///\n/// Permission is hereby granted, free of charge, to any person obtaining a\n/// copy of this software and associated documentation files (the \"Software\"),\n/// to deal in the Software without restriction, including without limitation\n/// the rights to use, copy, modify, merge, publish, distribute, sublicense,\n/// and/or sell copies of the Software, and to permit persons to whom the\n/// Software is furnished to do so, subject to the following conditions:\n///\n/// The above copyright notice and this permission notice shall be included in\n/// all copies or substantial portions of the Software.\n///\n/// THE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR\n/// IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,\n/// FITNESS FOR A PARTICULAR PURPOSE AND NON-INFRINGEMENT. IN NO EVENT SHALL\n/// THE AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER\n/// LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING\n/// FROM, OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER\n/// DEALINGS\n////\n\n// ----------------------------------------------------------------------------\n// Rules\n// ----------------------------------------------------------------------------\n\n@each $name, $color in (\n  \"red\":         $clr-red-a400,\n  \"pink\":        $clr-pink-a400,\n  \"purple\":      $clr-purple-a200,\n  \"deep-purple\": $clr-deep-purple-a200,\n  \"indigo\":      $clr-indigo-a200,\n  \"blue\":        $clr-blue-a200,\n  \"light-blue\":  $clr-light-blue-a700,\n  \"cyan\":        $clr-cyan-a700,\n  \"teal\":        $clr-teal-a700,\n  \"green\":       $clr-green-a700,\n  \"light-green\": $clr-light-green-a700,\n  \"lime\":        $clr-lime-a700,\n  \"yellow\":      $clr-yellow-a700,\n  \"amber\":       $clr-amber-a700,\n  \"orange\":      $clr-orange-a400,\n  \"deep-orange\": $clr-deep-orange-a200\n) {\n\n  // Color palette\n  [data-md-color-accent=\"#{$name}\"] {\n    --md-accent-fg-color:              hsla(#{hex2hsl($color)}, 1);\n    --md-accent-fg-color--transparent: hsla(#{hex2hsl($color)}, 0.1);\n\n    // Inverted text for lighter shades\n    @if index(\"lime\" \"yellow\" \"amber\" \"orange\", $name) {\n      --md-accent-bg-color:           hsla(0, 0%, 0%, 0.87);\n      --md-accent-bg-color--light:    hsla(0, 0%, 0%, 0.54);\n    } @else {\n      --md-accent-bg-color:           hsla(0, 0%, 100%, 1);\n      --md-accent-bg-color--light:    hsla(0, 0%, 100%, 0.7);\n    }\n  }\n}\n", "[data-md-color-accent=red] {\n  --md-accent-fg-color: hsla(348, 100%, 55%, 1);\n  --md-accent-fg-color--transparent: hsla(348, 100%, 55%, 0.1);\n  --md-accent-bg-color: hsla(0, 0%, 100%, 1);\n  --md-accent-bg-color--light: hsla(0, 0%, 100%, 0.7);\n}\n\n[data-md-color-accent=pink] {\n  --md-accent-fg-color: hsla(339, 100%, 48%, 1);\n  --md-accent-fg-color--transparent: hsla(339, 100%, 48%, 0.1);\n  --md-accent-bg-color: hsla(0, 0%, 100%, 1);\n  --md-accent-bg-color--light: hsla(0, 0%, 100%, 0.7);\n}\n\n[data-md-color-accent=purple] {\n  --md-accent-fg-color: hsla(291, 96%, 62%, 1);\n  --md-accent-fg-color--transparent: hsla(291, 96%, 62%, 0.1);\n  --md-accent-bg-color: hsla(0, 0%, 100%, 1);\n  --md-accent-bg-color--light: hsla(0, 0%, 100%, 0.7);\n}\n\n[data-md-color-accent=deep-purple] {\n  --md-accent-fg-color: hsla(256, 100%, 65%, 1);\n  --md-accent-fg-color--transparent: hsla(256, 100%, 65%, 0.1);\n  --md-accent-bg-color: hsla(0, 0%, 100%, 1);\n  --md-accent-bg-color--light: hsla(0, 0%, 100%, 0.7);\n}\n\n[data-md-color-accent=indigo] {\n  --md-accent-fg-color: hsla(231, 99%, 66%, 1);\n  --md-accent-fg-color--transparent: hsla(231, 99%, 66%, 0.1);\n  --md-accent-bg-color: hsla(0, 0%, 100%, 1);\n  --md-accent-bg-color--light: hsla(0, 0%, 100%, 0.7);\n}\n\n[data-md-color-accent=blue] {\n  --md-accent-fg-color: hsla(218, 100%, 63%, 1);\n  --md-accent-fg-color--transparent: hsla(218, 100%, 63%, 0.1);\n  --md-accent-bg-color: hsla(0, 0%, 100%, 1);\n  --md-accent-bg-color--light: hsla(0, 0%, 100%, 0.7);\n}\n\n[data-md-color-accent=light-blue] {\n  --md-accent-fg-color: hsla(203, 100%, 46%, 1);\n  --md-accent-fg-color--transparent: hsla(203, 100%, 46%, 0.1);\n  --md-accent-bg-color: hsla(0, 0%, 100%, 1);\n  --md-accent-bg-color--light: hsla(0, 0%, 100%, 0.7);\n}\n\n[data-md-color-accent=cyan] {\n  --md-accent-fg-color: hsla(188, 100%, 42%, 1);\n  --md-accent-fg-color--transparent: hsla(188, 100%, 42%, 0.1);\n  --md-accent-bg-color: hsla(0, 0%, 100%, 1);\n  --md-accent-bg-color--light: hsla(0, 0%, 100%, 0.7);\n}\n\n[data-md-color-accent=teal] {\n  --md-accent-fg-color: hsla(172, 100%, 37%, 1);\n  --md-accent-fg-color--transparent: hsla(172, 100%, 37%, 0.1);\n  --md-accent-bg-color: hsla(0, 0%, 100%, 1);\n  --md-accent-bg-color--light: hsla(0, 0%, 100%, 0.7);\n}\n\n[data-md-color-accent=green] {\n  --md-accent-fg-color: hsla(145, 100%, 39%, 1);\n  --md-accent-fg-color--transparent: hsla(145, 100%, 39%, 0.1);\n  --md-accent-bg-color: hsla(0, 0%, 100%, 1);\n  --md-accent-bg-color--light: hsla(0, 0%, 100%, 0.7);\n}\n\n[data-md-color-accent=light-green] {\n  --md-accent-fg-color: hsla(97, 81%, 48%, 1);\n  --md-accent-fg-color--transparent: hsla(97, 81%, 48%, 0.1);\n  --md-accent-bg-color: hsla(0, 0%, 100%, 1);\n  --md-accent-bg-color--light: hsla(0, 0%, 100%, 0.7);\n}\n\n[data-md-color-accent=lime] {\n  --md-accent-fg-color: hsla(75, 100%, 46%, 1);\n  --md-accent-fg-color--transparent: hsla(75, 100%, 46%, 0.1);\n  --md-accent-bg-color: hsla(0, 0%, 0%, 0.87);\n  --md-accent-bg-color--light: hsla(0, 0%, 0%, 0.54);\n}\n\n[data-md-color-accent=yellow] {\n  --md-accent-fg-color: hsla(50, 100%, 50%, 1);\n  --md-accent-fg-color--transparent: hsla(50, 100%, 50%, 0.1);\n  --md-accent-bg-color: hsla(0, 0%, 0%, 0.87);\n  --md-accent-bg-color--light: hsla(0, 0%, 0%, 0.54);\n}\n\n[data-md-color-accent=amber] {\n  --md-accent-fg-color: hsla(40, 100%, 50%, 1);\n  --md-accent-fg-color--transparent: hsla(40, 100%, 50%, 0.1);\n  --md-accent-bg-color: hsla(0, 0%, 0%, 0.87);\n  --md-accent-bg-color--light: hsla(0, 0%, 0%, 0.54);\n}\n\n[data-md-color-accent=orange] {\n  --md-accent-fg-color: hsla(34, 100%, 50%, 1);\n  --md-accent-fg-color--transparent: hsla(34, 100%, 50%, 0.1);\n  --md-accent-bg-color: hsla(0, 0%, 0%, 0.87);\n  --md-accent-bg-color--light: hsla(0, 0%, 0%, 0.54);\n}\n\n[data-md-color-accent=deep-orange] {\n  --md-accent-fg-color: hsla(14, 100%, 63%, 1);\n  --md-accent-fg-color--transparent: hsla(14, 100%, 63%, 0.1);\n  --md-accent-bg-color: hsla(0, 0%, 100%, 1);\n  --md-accent-bg-color--light: hsla(0, 0%, 100%, 0.7);\n}\n\n[data-md-color-primary=red] {\n  --md-primary-fg-color: hsla(1, 83%, 63%, 1);\n  --md-primary-fg-color--light: hsla(0, 69%, 67%, 1);\n  --md-primary-fg-color--dark: hsla(1, 77%, 55%, 1);\n  --md-primary-bg-color: hsla(0, 0%, 100%, 1);\n  --md-primary-bg-color--light: hsla(0, 0%, 100%, 0.7);\n}\n\n[data-md-color-primary=pink] {\n  --md-primary-fg-color: hsla(340, 82%, 52%, 1);\n  --md-primary-fg-color--light: hsla(340, 82%, 59%, 1);\n  --md-primary-fg-color--dark: hsla(336, 78%, 43%, 1);\n  --md-primary-bg-color: hsla(0, 0%, 100%, 1);\n  --md-primary-bg-color--light: hsla(0, 0%, 100%, 0.7);\n}\n\n[data-md-color-primary=purple] {\n  --md-primary-fg-color: hsla(291, 47%, 51%, 1);\n  --md-primary-fg-color--light: hsla(291, 47%, 60%, 1);\n  --md-primary-fg-color--dark: hsla(287, 65%, 40%, 1);\n  --md-primary-bg-color: hsla(0, 0%, 100%, 1);\n  --md-primary-bg-color--light: hsla(0, 0%, 100%, 0.7);\n}\n\n[data-md-color-primary=deep-purple] {\n  --md-primary-fg-color: hsla(262, 47%, 55%, 1);\n  --md-primary-fg-color--light: hsla(262, 47%, 63%, 1);\n  --md-primary-fg-color--dark: hsla(262, 52%, 47%, 1);\n  --md-primary-bg-color: hsla(0, 0%, 100%, 1);\n  --md-primary-bg-color--light: hsla(0, 0%, 100%, 0.7);\n}\n\n[data-md-color-primary=indigo] {\n  --md-primary-fg-color: hsla(231, 48%, 48%, 1);\n  --md-primary-fg-color--light: hsla(231, 44%, 56%, 1);\n  --md-primary-fg-color--dark: hsla(232, 54%, 41%, 1);\n  --md-primary-bg-color: hsla(0, 0%, 100%, 1);\n  --md-primary-bg-color--light: hsla(0, 0%, 100%, 0.7);\n}\n\n[data-md-color-primary=blue] {\n  --md-primary-fg-color: hsla(207, 90%, 54%, 1);\n  --md-primary-fg-color--light: hsla(207, 90%, 61%, 1);\n  --md-primary-fg-color--dark: hsla(210, 79%, 46%, 1);\n  --md-primary-bg-color: hsla(0, 0%, 100%, 1);\n  --md-primary-bg-color--light: hsla(0, 0%, 100%, 0.7);\n}\n\n[data-md-color-primary=light-blue] {\n  --md-primary-fg-color: hsla(199, 98%, 48%, 1);\n  --md-primary-fg-color--light: hsla(199, 92%, 56%, 1);\n  --md-primary-fg-color--dark: hsla(201, 98%, 41%, 1);\n  --md-primary-bg-color: hsla(0, 0%, 100%, 1);\n  --md-primary-bg-color--light: hsla(0, 0%, 100%, 0.7);\n}\n\n[data-md-color-primary=cyan] {\n  --md-primary-fg-color: hsla(187, 100%, 42%, 1);\n  --md-primary-fg-color--light: hsla(187, 71%, 50%, 1);\n  --md-primary-fg-color--dark: hsla(186, 100%, 33%, 1);\n  --md-primary-bg-color: hsla(0, 0%, 100%, 1);\n  --md-primary-bg-color--light: hsla(0, 0%, 100%, 0.7);\n}\n\n[data-md-color-primary=teal] {\n  --md-primary-fg-color: hsla(174, 100%, 29%, 1);\n  --md-primary-fg-color--light: hsla(174, 63%, 40%, 1);\n  --md-primary-fg-color--dark: hsla(173, 100%, 24%, 1);\n  --md-primary-bg-color: hsla(0, 0%, 100%, 1);\n  --md-primary-bg-color--light: hsla(0, 0%, 100%, 0.7);\n}\n\n[data-md-color-primary=green] {\n  --md-primary-fg-color: hsla(122, 39%, 49%, 1);\n  --md-primary-fg-color--light: hsla(123, 38%, 57%, 1);\n  --md-primary-fg-color--dark: hsla(123, 43%, 39%, 1);\n  --md-primary-bg-color: hsla(0, 0%, 100%, 1);\n  --md-primary-bg-color--light: hsla(0, 0%, 100%, 0.7);\n}\n\n[data-md-color-primary=light-green] {\n  --md-primary-fg-color: hsla(88, 50%, 53%, 1);\n  --md-primary-fg-color--light: hsla(88, 50%, 60%, 1);\n  --md-primary-fg-color--dark: hsla(92, 48%, 42%, 1);\n  --md-primary-bg-color: hsla(0, 0%, 100%, 1);\n  --md-primary-bg-color--light: hsla(0, 0%, 100%, 0.7);\n}\n\n[data-md-color-primary=lime] {\n  --md-primary-fg-color: hsla(66, 70%, 54%, 1);\n  --md-primary-fg-color--light: hsla(66, 70%, 61%, 1);\n  --md-primary-fg-color--dark: hsla(62, 61%, 44%, 1);\n  --md-primary-bg-color: hsla(0, 0%, 0%, 0.87);\n  --md-primary-bg-color--light: hsla(0, 0%, 0%, 0.54);\n}\n\n[data-md-color-primary=yellow] {\n  --md-primary-fg-color: hsla(54, 100%, 62%, 1);\n  --md-primary-fg-color--light: hsla(54, 100%, 67%, 1);\n  --md-primary-fg-color--dark: hsla(43, 96%, 58%, 1);\n  --md-primary-bg-color: hsla(0, 0%, 0%, 0.87);\n  --md-primary-bg-color--light: hsla(0, 0%, 0%, 0.54);\n}\n\n[data-md-color-primary=amber] {\n  --md-primary-fg-color: hsla(45, 100%, 51%, 1);\n  --md-primary-fg-color--light: hsla(45, 100%, 58%, 1);\n  --md-primary-fg-color--dark: hsla(38, 100%, 50%, 1);\n  --md-primary-bg-color: hsla(0, 0%, 0%, 0.87);\n  --md-primary-bg-color--light: hsla(0, 0%, 0%, 0.54);\n}\n\n[data-md-color-primary=orange] {\n  --md-primary-fg-color: hsla(36, 100%, 57%, 1);\n  --md-primary-fg-color--light: hsla(36, 100%, 57%, 1);\n  --md-primary-fg-color--dark: hsla(33, 100%, 49%, 1);\n  --md-primary-bg-color: hsla(0, 0%, 0%, 0.87);\n  --md-primary-bg-color--light: hsla(0, 0%, 0%, 0.54);\n}\n\n[data-md-color-primary=deep-orange] {\n  --md-primary-fg-color: hsla(14, 100%, 63%, 1);\n  --md-primary-fg-color--light: hsla(14, 100%, 70%, 1);\n  --md-primary-fg-color--dark: hsla(14, 91%, 54%, 1);\n  --md-primary-bg-color: hsla(0, 0%, 100%, 1);\n  --md-primary-bg-color--light: hsla(0, 0%, 100%, 0.7);\n}\n\n[data-md-color-primary=brown] {\n  --md-primary-fg-color: hsla(16, 25%, 38%, 1);\n  --md-primary-fg-color--light: hsla(16, 18%, 47%, 1);\n  --md-primary-fg-color--dark: hsla(14, 26%, 29%, 1);\n  --md-primary-bg-color: hsla(0, 0%, 100%, 1);\n  --md-primary-bg-color--light: hsla(0, 0%, 100%, 0.7);\n}\n\n[data-md-color-primary=grey] {\n  --md-primary-fg-color: hsla(0, 0%, 46%, 1);\n  --md-primary-fg-color--light: hsla(0, 0%, 62%, 1);\n  --md-primary-fg-color--dark: hsla(0, 0%, 38%, 1);\n  --md-primary-bg-color: hsla(0, 0%, 100%, 1);\n  --md-primary-bg-color--light: hsla(0, 0%, 100%, 0.7);\n}\n\n[data-md-color-primary=blue-grey] {\n  --md-primary-fg-color: hsla(199, 18%, 40%, 1);\n  --md-primary-fg-color--light: hsla(200, 18%, 46%, 1);\n  --md-primary-fg-color--dark: hsla(199, 18%, 33%, 1);\n  --md-primary-bg-color: hsla(0, 0%, 100%, 1);\n  --md-primary-bg-color--light: hsla(0, 0%, 100%, 0.7);\n}\n\n[data-md-color-primary=white] {\n  --md-primary-fg-color: hsla(0, 0%, 100%, 1);\n  --md-primary-fg-color--light: hsla(0, 0%, 100%, 0.7);\n  --md-primary-fg-color--dark: hsla(0, 0%, 0%, 0.07);\n  --md-primary-bg-color: hsla(0, 0%, 0%, 0.87);\n  --md-primary-bg-color--light: hsla(0, 0%, 0%, 0.54);\n  --md-typeset-a-color: hsla(231, 48%, 48%, 1);\n}\n@media screen and (min-width: 60em) {\n  [data-md-color-primary=white] .md-search__input {\n    background-color: rgba(0, 0, 0, 0.07);\n  }\n  [data-md-color-primary=white] .md-search__input + .md-search__icon {\n    color: rgba(0, 0, 0, 0.87);\n  }\n  [data-md-color-primary=white] .md-search__input::placeholder {\n    color: rgba(0, 0, 0, 0.54);\n  }\n  [data-md-color-primary=white] .md-search__input:hover {\n    background-color: rgba(0, 0, 0, 0.32);\n  }\n}\n@media screen and (min-width: 76.25em) {\n  [data-md-color-primary=white] .md-tabs {\n    border-bottom: 0.05rem solid rgba(0, 0, 0, 0.07);\n  }\n}\n\n[data-md-color-primary=black] {\n  --md-primary-fg-color: hsla(0, 0%, 0%, 1);\n  --md-primary-fg-color--light: hsla(0, 0%, 0%, 0.54);\n  --md-primary-fg-color--dark: hsla(0, 0%, 0%, 1);\n  --md-primary-bg-color: hsla(0, 0%, 100%, 1);\n  --md-primary-bg-color--light: hsla(0, 0%, 100%, 0.7);\n  --md-typeset-a-color: hsla(231, 48%, 48%, 1);\n}\n[data-md-color-primary=black] .md-header {\n  background-color: black;\n}\n@media screen and (max-width: 59.9375em) {\n  [data-md-color-primary=black] .md-nav__source {\n    background-color: rgba(0, 0, 0, 0.87);\n  }\n}\n@media screen and (min-width: 60em) {\n  [data-md-color-primary=black] .md-search__input {\n    background-color: rgba(255, 255, 255, 0.12);\n  }\n  [data-md-color-primary=black] .md-search__input:hover {\n    background-color: rgba(255, 255, 255, 0.3);\n  }\n}\n@media screen and (max-width: 76.1875em) {\n  html [data-md-color-primary=black] .md-nav--primary .md-nav__title[for=__drawer] {\n    background-color: black;\n  }\n}\n@media screen and (min-width: 76.25em) {\n  [data-md-color-primary=black] .md-tabs {\n    background-color: black;\n  }\n}\n\n@media screen {\n  [data-md-color-scheme=slate] {\n    --md-hue: 232;\n    --md-default-fg-color: hsla(var(--md-hue), 75%, 95%, 1);\n    --md-default-fg-color--light: hsla(var(--md-hue), 75%, 90%, 0.62);\n    --md-default-fg-color--lighter: hsla(var(--md-hue), 75%, 90%, 0.32);\n    --md-default-fg-color--lightest: hsla(var(--md-hue), 75%, 90%, 0.12);\n    --md-default-bg-color: hsla(var(--md-hue), 15%, 21%, 1);\n    --md-default-bg-color--light: hsla(var(--md-hue), 15%, 21%, 0.54);\n    --md-default-bg-color--lighter: hsla(var(--md-hue), 15%, 21%, 0.26);\n    --md-default-bg-color--lightest: hsla(var(--md-hue), 15%, 21%, 0.07);\n    --md-code-fg-color: hsla(var(--md-hue), 18%, 86%, 1);\n    --md-code-bg-color: hsla(var(--md-hue), 15%, 15%, 1);\n    --md-code-hl-color: hsla(218, 100%, 63%, 0.15);\n    --md-code-hl-number-color: hsla(6, 74%, 63%, 1);\n    --md-code-hl-special-color: hsla(340, 83%, 66%, 1);\n    --md-code-hl-function-color: hsla(291, 57%, 65%, 1);\n    --md-code-hl-constant-color: hsla(250, 62%, 70%, 1);\n    --md-code-hl-keyword-color: hsla(219, 66%, 64%, 1);\n    --md-code-hl-string-color: hsla(150, 58%, 44%, 1);\n    --md-typeset-a-color: var(--md-primary-fg-color--light);\n    --md-typeset-mark-color: hsla(218, 100%, 63%, 0.3);\n    --md-typeset-kbd-color: hsla(var(--md-hue), 15%, 94%, 0.12);\n    --md-typeset-kbd-accent-color: hsla(var(--md-hue), 15%, 94%, 0.2);\n    --md-typeset-kbd-border-color: hsla(var(--md-hue), 15%, 14%, 1);\n    --md-admonition-bg-color: hsla(var(--md-hue), 0%, 100%, 0.025);\n    --md-footer-bg-color: hsla(var(--md-hue), 15%, 12%, 0.87);\n    --md-footer-bg-color--dark: hsla(var(--md-hue), 15%, 10%, 1);\n  }\n  [data-md-color-scheme=slate][data-md-color-primary=black], [data-md-color-scheme=slate][data-md-color-primary=white] {\n    --md-typeset-a-color: hsla(231, 44%, 56%, 1);\n  }\n}\n\n/*# sourceMappingURL=palette.css.map */", "////\n/// Copyright (c) 2016-2021 <PERSON> <<EMAIL>>\n///\n/// Permission is hereby granted, free of charge, to any person obtaining a\n/// copy of this software and associated documentation files (the \"Software\"),\n/// to deal in the Software without restriction, including without limitation\n/// the rights to use, copy, modify, merge, publish, distribute, sublicense,\n/// and/or sell copies of the Software, and to permit persons to whom the\n/// Software is furnished to do so, subject to the following conditions:\n///\n/// The above copyright notice and this permission notice shall be included in\n/// all copies or substantial portions of the Software.\n///\n/// THE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR\n/// IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,\n/// FITNESS FOR A PARTICULAR PURPOSE AND NON-INFRINGEMENT. IN NO EVENT SHALL\n/// THE AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER\n/// LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING\n/// FROM, OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER\n/// DEALINGS\n////\n\n// ----------------------------------------------------------------------------\n// Rules\n// ----------------------------------------------------------------------------\n\n@each $name, $colors in (\n  \"red\":         $clr-red-400         $clr-red-300         $clr-red-600,\n  \"pink\":        $clr-pink-500        $clr-pink-400        $clr-pink-700,\n  \"purple\":      $clr-purple-400      $clr-purple-300      $clr-purple-600,\n  \"deep-purple\": $clr-deep-purple-400 $clr-deep-purple-300 $clr-deep-purple-500,\n  \"indigo\":      $clr-indigo-500      $clr-indigo-400      $clr-indigo-700,\n  \"blue\":        $clr-blue-500        $clr-blue-400        $clr-blue-700,\n  \"light-blue\":  $clr-light-blue-500  $clr-light-blue-400  $clr-light-blue-700,\n  \"cyan\":        $clr-cyan-500        $clr-cyan-400        $clr-cyan-700,\n  \"teal\":        $clr-teal-500        $clr-teal-400        $clr-teal-700,\n  \"green\":       $clr-green-500       $clr-green-400       $clr-green-700,\n  \"light-green\": $clr-light-green-500 $clr-light-green-400 $clr-light-green-700,\n  \"lime\":        $clr-lime-500        $clr-lime-400        $clr-lime-700,\n  \"yellow\":      $clr-yellow-500      $clr-yellow-400      $clr-yellow-700,\n  \"amber\":       $clr-amber-500       $clr-amber-400       $clr-amber-700,\n  \"orange\":      $clr-orange-400      $clr-orange-400      $clr-orange-600,\n  \"deep-orange\": $clr-deep-orange-400 $clr-deep-orange-300 $clr-deep-orange-600,\n  \"brown\":       $clr-brown-500       $clr-brown-400       $clr-brown-700,\n  \"grey\":        $clr-grey-600        $clr-grey-500        $clr-grey-700,\n  \"blue-grey\":   $clr-blue-grey-600   $clr-blue-grey-500   $clr-blue-grey-700\n) {\n\n  // Color palette\n  [data-md-color-primary=\"#{$name}\"] {\n    --md-primary-fg-color:             hsla(#{hex2hsl(nth($colors, 1))}, 1);\n    --md-primary-fg-color--light:      hsla(#{hex2hsl(nth($colors, 2))}, 1);\n    --md-primary-fg-color--dark:       hsla(#{hex2hsl(nth($colors, 3))}, 1);\n\n    // Inverted text for lighter shades\n    @if index(\"lime\" \"yellow\" \"amber\" \"orange\", $name) {\n      --md-primary-bg-color:           hsla(0, 0%, 0%, 0.87);\n      --md-primary-bg-color--light:    hsla(0, 0%, 0%, 0.54);\n    } @else {\n      --md-primary-bg-color:           hsla(0, 0%, 100%, 1);\n      --md-primary-bg-color--light:    hsla(0, 0%, 100%, 0.7);\n    }\n  }\n}\n\n// ----------------------------------------------------------------------------\n// Rules: white\n// ----------------------------------------------------------------------------\n\n// Color palette\n[data-md-color-primary=\"white\"] {\n  --md-primary-fg-color:               hsla(0, 0%, 100%, 1);\n  --md-primary-fg-color--light:        hsla(0, 0%, 100%, 0.7);\n  --md-primary-fg-color--dark:         hsla(0, 0%, 0%, 0.07);\n  --md-primary-bg-color:               hsla(0, 0%, 0%, 0.87);\n  --md-primary-bg-color--light:        hsla(0, 0%, 0%, 0.54);\n\n  // Typeset color shades\n  --md-typeset-a-color:                hsla(#{hex2hsl($clr-indigo-500)}, 1);\n\n  // [tablet portrait +]: Header-embedded search\n  @include break-from-device(tablet landscape) {\n\n    // Search input\n    .md-search__input {\n      background-color: hsla(0, 0%, 0%, 0.07);\n\n      // Search icon color\n      + .md-search__icon {\n        color: hsla(0, 0%, 0%, 0.87);\n      }\n\n      // Placeholder color\n      &::placeholder {\n        color: hsla(0, 0%, 0%, 0.54);\n      }\n\n      // Search input on hover\n      &:hover {\n        background-color: hsla(0, 0%, 0%, 0.32);\n      }\n    }\n  }\n\n  // [screen +]: Add bottom border for tabs\n  @include break-from-device(screen) {\n\n    // Navigation tabs\n    .md-tabs {\n      border-bottom: px2rem(1px) solid hsla(0, 0%, 0%, 0.07);\n    }\n  }\n}\n\n// ----------------------------------------------------------------------------\n// Rules: black\n// ----------------------------------------------------------------------------\n\n// Color palette\n[data-md-color-primary=\"black\"] {\n  --md-primary-fg-color:               hsla(0, 0%, 0%, 1);\n  --md-primary-fg-color--light:        hsla(0, 0%, 0%, 0.54);\n  --md-primary-fg-color--dark:         hsla(0, 0%, 0%, 1);\n  --md-primary-bg-color:               hsla(0, 0%, 100%, 1);\n  --md-primary-bg-color--light:        hsla(0, 0%, 100%, 0.7);\n\n  // Text color shades\n  --md-typeset-a-color:                hsla(#{hex2hsl($clr-indigo-500)}, 1);\n\n  // Header\n  .md-header {\n    background-color: hsla(0, 0%, 0%, 1);\n  }\n\n  // [tablet portrait -]: Layered navigation\n  @include break-to-device(tablet portrait) {\n\n    // Repository information container\n    .md-nav__source {\n      background-color: hsla(0, 0%, 0%, 0.87);\n    }\n  }\n\n  // [tablet landscape +]: Header-embedded search\n  @include break-from-device(tablet landscape) {\n\n    // Search input\n    .md-search__input {\n      background-color: hsla(0, 0%, 100%, 0.12);\n\n      // Search form on hover\n      &:hover {\n        background-color: hsla(0, 0%, 100%, 0.3);\n      }\n    }\n  }\n\n  // [tablet -]: Layered navigation\n  @include break-to-device(tablet) {\n\n    // Site title in main navigation\n    html & .md-nav--primary .md-nav__title[for=\"__drawer\"] {\n      background-color: hsla(0, 0%, 0%, 1);\n    }\n  }\n\n  // [screen +]: Set background color for tabs\n  @include break-from-device(screen) {\n\n    // Navigation tabs\n    .md-tabs {\n      background-color: hsla(0, 0%, 0%, 1);\n    }\n  }\n}\n", "////\n/// Copyright (c) 2016-2021 <PERSON> <<EMAIL>>\n///\n/// Permission is hereby granted, free of charge, to any person obtaining a\n/// copy of this software and associated documentation files (the \"Software\"),\n/// to deal in the Software without restriction, including without limitation\n/// the rights to use, copy, modify, merge, publish, distribute, sublicense,\n/// and/or sell copies of the Software, and to permit persons to whom the\n/// Software is furnished to do so, subject to the following conditions:\n///\n/// The above copyright notice and this permission notice shall be included in\n/// all copies or substantial portions of the Software.\n///\n/// THE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR\n/// IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,\n/// FITNESS FOR A PARTICULAR PURPOSE AND NON-INFRINGEMENT. IN NO EVENT SHALL\n/// THE AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER\n/// LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING\n/// FROM, OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER\n/// DEALINGS\n////\n\n// ----------------------------------------------------------------------------\n// Variables\n// ----------------------------------------------------------------------------\n\n///\n/// Device-specific breakpoints\n///\n/// @example\n///   $break-devices: (\n///     mobile: (\n///       portrait:  220px  479px,\n///       landscape: 480px  719px\n///     ),\n///     tablet: (\n///       portrait:  720px  959px,\n///       landscape: 960px  1219px\n///     ),\n///     screen: (\n///       small:     1220px 1599px,\n///       medium:    1600px 1999px,\n///       large:     2000px\n///     )\n///   );\n///\n$break-devices: () !default;\n\n// ----------------------------------------------------------------------------\n// Helpers\n// ----------------------------------------------------------------------------\n\n///\n/// Choose minimum and maximum device widths\n///\n@function break-select-min-max($devices) {\n  $min: 1000000;\n  $max: 0;\n  @each $key, $value in $devices {\n    @while type-of($value) == map {\n      $value: break-select-min-max($value);\n    }\n    @if type-of($value) == list {\n      @each $number in $value {\n        @if type-of($number) == number {\n          $min: min($number, $min);\n          @if $max {\n            $max: max($number, $max);\n          }\n        } @else {\n          @error \"Invalid number: #{$number}\";\n        }\n      }\n    } @else if type-of($value) == number {\n      $min: min($value, $min);\n      $max: null;\n    } @else {\n      @error \"Invalid value: #{$value}\";\n    }\n  }\n  @return $min, $max;\n}\n\n///\n/// Select minimum and maximum widths for a device breakpoint\n///\n@function break-select-device($device) {\n  $current: $break-devices;\n  @for $n from 1 through length($device) {\n    @if type-of($current) == map {\n      $current: map-get($current, nth($device, $n));\n    } @else {\n      @error \"Invalid device map: #{$devices}\";\n    }\n  }\n  @if type-of($current) == list or type-of($current) == number {\n    $current: (default: $current);\n  }\n  @return break-select-min-max($current);\n}\n\n// ----------------------------------------------------------------------------\n// Mixins\n// ----------------------------------------------------------------------------\n\n///\n/// A minimum-maximum media query breakpoint\n///\n@mixin break-at($breakpoint) {\n  @if type-of($breakpoint) == number {\n    @media screen and (min-width: $breakpoint) {\n      @content;\n    }\n  } @else if type-of($breakpoint) == list {\n    $min: nth($breakpoint, 1);\n    $max: nth($breakpoint, 2);\n    @if type-of($min) == number and type-of($max) == number {\n      @media screen and (min-width: $min) and (max-width: $max) {\n        @content;\n      }\n    } @else {\n      @error \"Invalid breakpoint: #{$breakpoint}\";\n    }\n  } @else {\n    @error \"Invalid breakpoint: #{$breakpoint}\";\n  }\n}\n\n///\n/// An orientation media query breakpoint\n///\n@mixin break-at-orientation($breakpoint) {\n  @if type-of($breakpoint) == string {\n    @media screen and (orientation: $breakpoint) {\n      @content;\n    }\n  } @else {\n    @error \"Invalid breakpoint: #{$breakpoint}\";\n  }\n}\n\n///\n/// A maximum-aspect-ratio media query breakpoint\n///\n@mixin break-at-ratio($breakpoint) {\n  @if type-of($breakpoint) == number {\n    @media screen and (max-aspect-ratio: $breakpoint) {\n      @content;\n    }\n  } @else {\n    @error \"Invalid breakpoint: #{$breakpoint}\";\n  }\n}\n\n///\n/// A minimum-maximum media query device breakpoint\n///\n@mixin break-at-device($device) {\n  @if type-of($device) == string {\n    $device: $device,;\n  }\n  @if type-of($device) == list {\n    $breakpoint: break-select-device($device);\n    @if nth($breakpoint, 2) {\n      $min: nth($breakpoint, 1);\n      $max: nth($breakpoint, 2);\n\n      @media screen and (min-width: $min) and (max-width: $max) {\n        @content;\n      }\n    } @else {\n      @error \"Invalid device: #{$device}\";\n    }\n  } @else {\n    @error \"Invalid device: #{$device}\";\n  }\n}\n\n///\n/// A minimum media query device breakpoint\n///\n@mixin break-from-device($device) {\n  @if type-of($device) == string {\n    $device: $device,;\n  }\n  @if type-of($device) == list {\n    $breakpoint: break-select-device($device);\n    $min: nth($breakpoint, 1);\n\n    @media screen and (min-width: $min) {\n      @content;\n    }\n  } @else {\n    @error \"Invalid device: #{$device}\";\n  }\n}\n\n///\n/// A maximum media query device breakpoint\n///\n@mixin break-to-device($device) {\n  @if type-of($device) == string {\n    $device: $device,;\n  }\n  @if type-of($device) == list {\n    $breakpoint: break-select-device($device);\n    $max: nth($breakpoint, 2);\n\n    @media screen and (max-width: $max) {\n      @content;\n    }\n  } @else {\n    @error \"Invalid device: #{$device}\";\n  }\n}\n", "////\n/// Copyright (c) 2016-2021 <PERSON> <<EMAIL>>\n///\n/// Permission is hereby granted, free of charge, to any person obtaining a\n/// copy of this software and associated documentation files (the \"Software\"),\n/// to deal in the Software without restriction, including without limitation\n/// the rights to use, copy, modify, merge, publish, distribute, sublicense,\n/// and/or sell copies of the Software, and to permit persons to whom the\n/// Software is furnished to do so, subject to the following conditions:\n///\n/// The above copyright notice and this permission notice shall be included in\n/// all copies or substantial portions of the Software.\n///\n/// THE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR\n/// IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,\n/// FITNESS FOR A PARTICULAR PURPOSE AND NON-INFRINGEMENT. IN NO EVENT SHALL\n/// THE AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER\n/// LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING\n/// FROM, OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER\n/// DEALINGS\n////\n\n// ----------------------------------------------------------------------------\n// Rules\n// ----------------------------------------------------------------------------\n\n// Only use dark mode on screens\n@media screen {\n\n  // Slate theme, i.e. dark mode\n  [data-md-color-scheme=\"slate\"] {\n\n    // Slate's hue in the range [0,360] - change this variable to alter the tone\n    // of the theme, e.g. to make it more redish or greenish. This is a slate-\n    // specific variable, but the same approach may be adapted to custom themes.\n    --md-hue: 232;\n\n    // Default color shades\n    --md-default-fg-color:             hsla(var(--md-hue), 75%, 95%, 1);\n    --md-default-fg-color--light:      hsla(var(--md-hue), 75%, 90%, 0.62);\n    --md-default-fg-color--lighter:    hsla(var(--md-hue), 75%, 90%, 0.32);\n    --md-default-fg-color--lightest:   hsla(var(--md-hue), 75%, 90%, 0.12);\n    --md-default-bg-color:             hsla(var(--md-hue), 15%, 21%, 1);\n    --md-default-bg-color--light:      hsla(var(--md-hue), 15%, 21%, 0.54);\n    --md-default-bg-color--lighter:    hsla(var(--md-hue), 15%, 21%, 0.26);\n    --md-default-bg-color--lightest:   hsla(var(--md-hue), 15%, 21%, 0.07);\n\n    // Code color shades\n    --md-code-fg-color:                hsla(var(--md-hue), 18%, 86%, 1);\n    --md-code-bg-color:                hsla(var(--md-hue), 15%, 15%, 1);\n\n    // Code highlighting color shades\n    --md-code-hl-color:                hsla(#{hex2hsl($clr-blue-a200)}, 0.15);\n    --md-code-hl-number-color:         hsla(6, 74%, 63%, 1);\n    --md-code-hl-special-color:        hsla(340, 83%, 66%, 1);\n    --md-code-hl-function-color:       hsla(291, 57%, 65%, 1);\n    --md-code-hl-constant-color:       hsla(250, 62%, 70%, 1);\n    --md-code-hl-keyword-color:        hsla(219, 66%, 64%, 1);\n    --md-code-hl-string-color:         hsla(150, 58%, 44%, 1);\n\n    // Typeset color shades\n    --md-typeset-a-color:              var(--md-primary-fg-color--light);\n\n    // Typeset `mark` color shades\n    --md-typeset-mark-color:           hsla(#{hex2hsl($clr-blue-a200)}, 0.3);\n\n    // Typeset `kbd` color shades\n    --md-typeset-kbd-color:            hsla(var(--md-hue), 15%, 94%, 0.12);\n    --md-typeset-kbd-accent-color:     hsla(var(--md-hue), 15%, 94%, 0.2);\n    --md-typeset-kbd-border-color:     hsla(var(--md-hue), 15%, 14%, 1);\n\n    // Admonition color shades\n    --md-admonition-bg-color:          hsla(var(--md-hue), 0%, 100%, 0.025);\n\n    // Footer color shades\n    --md-footer-bg-color:              hsla(var(--md-hue), 15%, 12%, 0.87);\n    --md-footer-bg-color--dark:        hsla(var(--md-hue), 15%, 10%, 1);\n\n    // Black and white primary colors\n    &[data-md-color-primary=\"black\"],\n    &[data-md-color-primary=\"white\"] {\n\n      // Typeset color shades\n      --md-typeset-a-color:            hsla(#{hex2hsl($clr-indigo-400)}, 1);\n    }\n  }\n}\n"]}