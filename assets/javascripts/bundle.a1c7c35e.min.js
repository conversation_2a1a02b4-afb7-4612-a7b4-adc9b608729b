(()=>{var qo=Object.create,Ct=Object.defineProperty,Ko=Object.getPrototypeOf,cr=Object.prototype.hasOwnProperty,Yo=Object.getOwnPropertyNames,Jo=Object.getOwnPropertyDescriptor,ur=Object.getOwnPropertySymbols,Xo=Object.prototype.propertyIsEnumerable;var N=Object.assign,Bo=e=>Ct(e,"__esModule",{value:!0});var lr=(e,t)=>{var r={};for(var n in e)cr.call(e,n)&&t.indexOf(n)<0&&(r[n]=e[n]);if(e!=null&&ur)for(var n of ur(e))t.indexOf(n)<0&&Xo.call(e,n)&&(r[n]=e[n]);return r},jt=(e,t)=>()=>(t||(t={exports:{}},e(t.exports,t)),t.exports);var Go=(e,t,r)=>{if(t&&typeof t=="object"||typeof t=="function")for(let n of Yo(t))!cr.call(e,n)&&n!=="default"&&Ct(e,n,{get:()=>t[n],enumerable:!(r=Jo(t,n))||r.enumerable});return e},it=e=>e&&e.__esModule?e:Go(Bo(Ct(e!=null?qo(Ko(e)):{},"default",{value:e,enumerable:!0})),e);var pr=jt((kt,fr)=>{(function(e,t){typeof kt=="object"&&typeof fr!="undefined"?t():typeof define=="function"&&define.amd?define(t):t()})(kt,function(){"use strict";function e(r){var n=!0,o=!1,i=null,a={text:!0,search:!0,url:!0,tel:!0,email:!0,password:!0,number:!0,date:!0,month:!0,week:!0,time:!0,datetime:!0,"datetime-local":!0};function c(S){return!!(S&&S!==document&&S.nodeName!=="HTML"&&S.nodeName!=="BODY"&&"classList"in S&&"contains"in S.classList)}function u(S){var Ye=S.type,Ht=S.tagName;return!!(Ht==="INPUT"&&a[Ye]&&!S.readOnly||Ht==="TEXTAREA"&&!S.readOnly||S.isContentEditable)}function s(S){S.classList.contains("focus-visible")||(S.classList.add("focus-visible"),S.setAttribute("data-focus-visible-added",""))}function l(S){!S.hasAttribute("data-focus-visible-added")||(S.classList.remove("focus-visible"),S.removeAttribute("data-focus-visible-added"))}function p(S){S.metaKey||S.altKey||S.ctrlKey||(c(r.activeElement)&&s(r.activeElement),n=!0)}function d(S){n=!1}function _(S){!c(S.target)||(n||u(S.target))&&s(S.target)}function $(S){!c(S.target)||(S.target.classList.contains("focus-visible")||S.target.hasAttribute("data-focus-visible-added"))&&(o=!0,window.clearTimeout(i),i=window.setTimeout(function(){o=!1},100),l(S.target))}function A(S){document.visibilityState==="hidden"&&(o&&(n=!0),ee())}function ee(){document.addEventListener("mousemove",F),document.addEventListener("mousedown",F),document.addEventListener("mouseup",F),document.addEventListener("pointermove",F),document.addEventListener("pointerdown",F),document.addEventListener("pointerup",F),document.addEventListener("touchmove",F),document.addEventListener("touchstart",F),document.addEventListener("touchend",F)}function P(){document.removeEventListener("mousemove",F),document.removeEventListener("mousedown",F),document.removeEventListener("mouseup",F),document.removeEventListener("pointermove",F),document.removeEventListener("pointerdown",F),document.removeEventListener("pointerup",F),document.removeEventListener("touchmove",F),document.removeEventListener("touchstart",F),document.removeEventListener("touchend",F)}function F(S){S.target.nodeName&&S.target.nodeName.toLowerCase()==="html"||(n=!1,P())}document.addEventListener("keydown",p,!0),document.addEventListener("mousedown",d,!0),document.addEventListener("pointerdown",d,!0),document.addEventListener("touchstart",d,!0),document.addEventListener("visibilitychange",A,!0),ee(),r.addEventListener("focus",_,!0),r.addEventListener("blur",$,!0),r.nodeType===Node.DOCUMENT_FRAGMENT_NODE&&r.host?r.host.setAttribute("data-js-focus-visible",""):r.nodeType===Node.DOCUMENT_NODE&&(document.documentElement.classList.add("js-focus-visible"),document.documentElement.setAttribute("data-js-focus-visible",""))}if(typeof window!="undefined"&&typeof document!="undefined"){window.applyFocusVisiblePolyfill=e;var t;try{t=new CustomEvent("focus-visible-polyfill-ready")}catch(r){t=document.createEvent("CustomEvent"),t.initCustomEvent("focus-visible-polyfill-ready",!1,!1,{})}window.dispatchEvent(t)}typeof document!="undefined"&&e(document)})});var Bt=jt((ot,Xt)=>{(function(t,r){typeof ot=="object"&&typeof Xt=="object"?Xt.exports=r():typeof define=="function"&&define.amd?define([],r):typeof ot=="object"?ot.ClipboardJS=r():t.ClipboardJS=r()})(ot,function(){return function(e){var t={};function r(n){if(t[n])return t[n].exports;var o=t[n]={i:n,l:!1,exports:{}};return e[n].call(o.exports,o,o.exports,r),o.l=!0,o.exports}return r.m=e,r.c=t,r.d=function(n,o,i){r.o(n,o)||Object.defineProperty(n,o,{enumerable:!0,get:i})},r.r=function(n){typeof Symbol!="undefined"&&Symbol.toStringTag&&Object.defineProperty(n,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(n,"__esModule",{value:!0})},r.t=function(n,o){if(o&1&&(n=r(n)),o&8||o&4&&typeof n=="object"&&n&&n.__esModule)return n;var i=Object.create(null);if(r.r(i),Object.defineProperty(i,"default",{enumerable:!0,value:n}),o&2&&typeof n!="string")for(var a in n)r.d(i,a,function(c){return n[c]}.bind(null,a));return i},r.n=function(n){var o=n&&n.__esModule?function(){return n.default}:function(){return n};return r.d(o,"a",o),o},r.o=function(n,o){return Object.prototype.hasOwnProperty.call(n,o)},r.p="",r(r.s=6)}([function(e,t){function r(n){var o;if(n.nodeName==="SELECT")n.focus(),o=n.value;else if(n.nodeName==="INPUT"||n.nodeName==="TEXTAREA"){var i=n.hasAttribute("readonly");i||n.setAttribute("readonly",""),n.select(),n.setSelectionRange(0,n.value.length),i||n.removeAttribute("readonly"),o=n.value}else{n.hasAttribute("contenteditable")&&n.focus();var a=window.getSelection(),c=document.createRange();c.selectNodeContents(n),a.removeAllRanges(),a.addRange(c),o=a.toString()}return o}e.exports=r},function(e,t){function r(){}r.prototype={on:function(n,o,i){var a=this.e||(this.e={});return(a[n]||(a[n]=[])).push({fn:o,ctx:i}),this},once:function(n,o,i){var a=this;function c(){a.off(n,c),o.apply(i,arguments)}return c._=o,this.on(n,c,i)},emit:function(n){var o=[].slice.call(arguments,1),i=((this.e||(this.e={}))[n]||[]).slice(),a=0,c=i.length;for(a;a<c;a++)i[a].fn.apply(i[a].ctx,o);return this},off:function(n,o){var i=this.e||(this.e={}),a=i[n],c=[];if(a&&o)for(var u=0,s=a.length;u<s;u++)a[u].fn!==o&&a[u].fn._!==o&&c.push(a[u]);return c.length?i[n]=c:delete i[n],this}},e.exports=r,e.exports.TinyEmitter=r},function(e,t,r){var n=r(3),o=r(4);function i(s,l,p){if(!s&&!l&&!p)throw new Error("Missing required arguments");if(!n.string(l))throw new TypeError("Second argument must be a String");if(!n.fn(p))throw new TypeError("Third argument must be a Function");if(n.node(s))return a(s,l,p);if(n.nodeList(s))return c(s,l,p);if(n.string(s))return u(s,l,p);throw new TypeError("First argument must be a String, HTMLElement, HTMLCollection, or NodeList")}function a(s,l,p){return s.addEventListener(l,p),{destroy:function(){s.removeEventListener(l,p)}}}function c(s,l,p){return Array.prototype.forEach.call(s,function(d){d.addEventListener(l,p)}),{destroy:function(){Array.prototype.forEach.call(s,function(d){d.removeEventListener(l,p)})}}}function u(s,l,p){return o(document.body,s,l,p)}e.exports=i},function(e,t){t.node=function(r){return r!==void 0&&r instanceof HTMLElement&&r.nodeType===1},t.nodeList=function(r){var n=Object.prototype.toString.call(r);return r!==void 0&&(n==="[object NodeList]"||n==="[object HTMLCollection]")&&"length"in r&&(r.length===0||t.node(r[0]))},t.string=function(r){return typeof r=="string"||r instanceof String},t.fn=function(r){var n=Object.prototype.toString.call(r);return n==="[object Function]"}},function(e,t,r){var n=r(5);function o(c,u,s,l,p){var d=a.apply(this,arguments);return c.addEventListener(s,d,p),{destroy:function(){c.removeEventListener(s,d,p)}}}function i(c,u,s,l,p){return typeof c.addEventListener=="function"?o.apply(null,arguments):typeof s=="function"?o.bind(null,document).apply(null,arguments):(typeof c=="string"&&(c=document.querySelectorAll(c)),Array.prototype.map.call(c,function(d){return o(d,u,s,l,p)}))}function a(c,u,s,l){return function(p){p.delegateTarget=n(p.target,u),p.delegateTarget&&l.call(c,p)}}e.exports=i},function(e,t){var r=9;if(typeof Element!="undefined"&&!Element.prototype.matches){var n=Element.prototype;n.matches=n.matchesSelector||n.mozMatchesSelector||n.msMatchesSelector||n.oMatchesSelector||n.webkitMatchesSelector}function o(i,a){for(;i&&i.nodeType!==r;){if(typeof i.matches=="function"&&i.matches(a))return i;i=i.parentNode}}e.exports=o},function(e,t,r){"use strict";r.r(t);var n=r(0),o=r.n(n),i=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(E){return typeof E}:function(E){return E&&typeof Symbol=="function"&&E.constructor===Symbol&&E!==Symbol.prototype?"symbol":typeof E},a=function(){function E(h,v){for(var y=0;y<v.length;y++){var L=v[y];L.enumerable=L.enumerable||!1,L.configurable=!0,"value"in L&&(L.writable=!0),Object.defineProperty(h,L.key,L)}}return function(h,v,y){return v&&E(h.prototype,v),y&&E(h,y),h}}();function c(E,h){if(!(E instanceof h))throw new TypeError("Cannot call a class as a function")}var u=function(){function E(h){c(this,E),this.resolveOptions(h),this.initSelection()}return a(E,[{key:"resolveOptions",value:function(){var v=arguments.length>0&&arguments[0]!==void 0?arguments[0]:{};this.action=v.action,this.container=v.container,this.emitter=v.emitter,this.target=v.target,this.text=v.text,this.trigger=v.trigger,this.selectedText=""}},{key:"initSelection",value:function(){this.text?this.selectFake():this.target&&this.selectTarget()}},{key:"selectFake",value:function(){var v=this,y=document.documentElement.getAttribute("dir")=="rtl";this.removeFake(),this.fakeHandlerCallback=function(){return v.removeFake()},this.fakeHandler=this.container.addEventListener("click",this.fakeHandlerCallback)||!0,this.fakeElem=document.createElement("textarea"),this.fakeElem.style.fontSize="12pt",this.fakeElem.style.border="0",this.fakeElem.style.padding="0",this.fakeElem.style.margin="0",this.fakeElem.style.position="absolute",this.fakeElem.style[y?"right":"left"]="-9999px";var L=window.pageYOffset||document.documentElement.scrollTop;this.fakeElem.style.top=L+"px",this.fakeElem.setAttribute("readonly",""),this.fakeElem.value=this.text,this.container.appendChild(this.fakeElem),this.selectedText=o()(this.fakeElem),this.copyText()}},{key:"removeFake",value:function(){this.fakeHandler&&(this.container.removeEventListener("click",this.fakeHandlerCallback),this.fakeHandler=null,this.fakeHandlerCallback=null),this.fakeElem&&(this.container.removeChild(this.fakeElem),this.fakeElem=null)}},{key:"selectTarget",value:function(){this.selectedText=o()(this.target),this.copyText()}},{key:"copyText",value:function(){var v=void 0;try{v=document.execCommand(this.action)}catch(y){v=!1}this.handleResult(v)}},{key:"handleResult",value:function(v){this.emitter.emit(v?"success":"error",{action:this.action,text:this.selectedText,trigger:this.trigger,clearSelection:this.clearSelection.bind(this)})}},{key:"clearSelection",value:function(){this.trigger&&this.trigger.focus(),document.activeElement.blur(),window.getSelection().removeAllRanges()}},{key:"destroy",value:function(){this.removeFake()}},{key:"action",set:function(){var v=arguments.length>0&&arguments[0]!==void 0?arguments[0]:"copy";if(this._action=v,this._action!=="copy"&&this._action!=="cut")throw new Error('Invalid "action" value, use either "copy" or "cut"')},get:function(){return this._action}},{key:"target",set:function(v){if(v!==void 0)if(v&&(typeof v=="undefined"?"undefined":i(v))==="object"&&v.nodeType===1){if(this.action==="copy"&&v.hasAttribute("disabled"))throw new Error('Invalid "target" attribute. Please use "readonly" instead of "disabled" attribute');if(this.action==="cut"&&(v.hasAttribute("readonly")||v.hasAttribute("disabled")))throw new Error(`Invalid "target" attribute. You can't cut text from elements with "readonly" or "disabled" attributes`);this._target=v}else throw new Error('Invalid "target" value, use a valid Element')},get:function(){return this._target}}]),E}(),s=u,l=r(1),p=r.n(l),d=r(2),_=r.n(d),$=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(E){return typeof E}:function(E){return E&&typeof Symbol=="function"&&E.constructor===Symbol&&E!==Symbol.prototype?"symbol":typeof E},A=function(){function E(h,v){for(var y=0;y<v.length;y++){var L=v[y];L.enumerable=L.enumerable||!1,L.configurable=!0,"value"in L&&(L.writable=!0),Object.defineProperty(h,L.key,L)}}return function(h,v,y){return v&&E(h.prototype,v),y&&E(h,y),h}}();function ee(E,h){if(!(E instanceof h))throw new TypeError("Cannot call a class as a function")}function P(E,h){if(!E)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return h&&(typeof h=="object"||typeof h=="function")?h:E}function F(E,h){if(typeof h!="function"&&h!==null)throw new TypeError("Super expression must either be null or a function, not "+typeof h);E.prototype=Object.create(h&&h.prototype,{constructor:{value:E,enumerable:!1,writable:!0,configurable:!0}}),h&&(Object.setPrototypeOf?Object.setPrototypeOf(E,h):E.__proto__=h)}var S=function(E){F(h,E);function h(v,y){ee(this,h);var L=P(this,(h.__proto__||Object.getPrototypeOf(h)).call(this));return L.resolveOptions(y),L.listenClick(v),L}return A(h,[{key:"resolveOptions",value:function(){var y=arguments.length>0&&arguments[0]!==void 0?arguments[0]:{};this.action=typeof y.action=="function"?y.action:this.defaultAction,this.target=typeof y.target=="function"?y.target:this.defaultTarget,this.text=typeof y.text=="function"?y.text:this.defaultText,this.container=$(y.container)==="object"?y.container:document.body}},{key:"listenClick",value:function(y){var L=this;this.listener=_()(y,"click",function(Je){return L.onClick(Je)})}},{key:"onClick",value:function(y){var L=y.delegateTarget||y.currentTarget;this.clipboardAction&&(this.clipboardAction=null),this.clipboardAction=new s({action:this.action(L),target:this.target(L),text:this.text(L),container:this.container,trigger:L,emitter:this})}},{key:"defaultAction",value:function(y){return Ye("action",y)}},{key:"defaultTarget",value:function(y){var L=Ye("target",y);if(L)return document.querySelector(L)}},{key:"defaultText",value:function(y){return Ye("text",y)}},{key:"destroy",value:function(){this.listener.destroy(),this.clipboardAction&&(this.clipboardAction.destroy(),this.clipboardAction=null)}}],[{key:"isSupported",value:function(){var y=arguments.length>0&&arguments[0]!==void 0?arguments[0]:["copy","cut"],L=typeof y=="string"?[y]:y,Je=!!document.queryCommandSupported;return L.forEach(function(Qo){Je=Je&&!!document.queryCommandSupported(Qo)}),Je}}]),h}(p.a);function Ye(E,h){var v="data-clipboard-"+E;if(!!h.hasAttribute(v))return h.getAttribute(v)}var Ht=t.default=S}]).default})});var wo=jt((Jb,So)=>{"use strict";var Ui=/["'&<>]/;So.exports=Wi;function Wi(e){var t=""+e,r=Ui.exec(t);if(!r)return t;var n,o="",i=0,a=0;for(i=r.index;i<t.length;i++){switch(t.charCodeAt(i)){case 34:n="&quot;";break;case 38:n="&amp;";break;case 39:n="&#39;";break;case 60:n="&lt;";break;case 62:n="&gt;";break;default:continue}a!==i&&(o+=t.substring(a,i)),a=i+1,o+=n}return a!==i?o+t.substring(a,i):o}});var nx=it(pr());var Ft=function(e,t){return Ft=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(r,n){r.__proto__=n}||function(r,n){for(var o in n)Object.prototype.hasOwnProperty.call(n,o)&&(r[o]=n[o])},Ft(e,t)};function z(e,t){if(typeof t!="function"&&t!==null)throw new TypeError("Class extends value "+String(t)+" is not a constructor or null");Ft(e,t);function r(){this.constructor=e}e.prototype=t===null?Object.create(t):(r.prototype=t.prototype,new r)}function mr(e,t,r,n){function o(i){return i instanceof r?i:new r(function(a){a(i)})}return new(r||(r=Promise))(function(i,a){function c(l){try{s(n.next(l))}catch(p){a(p)}}function u(l){try{s(n.throw(l))}catch(p){a(p)}}function s(l){l.done?i(l.value):o(l.value).then(c,u)}s((n=n.apply(e,t||[])).next())})}function dr(e,t){var r={label:0,sent:function(){if(i[0]&1)throw i[1];return i[1]},trys:[],ops:[]},n,o,i,a;return a={next:c(0),throw:c(1),return:c(2)},typeof Symbol=="function"&&(a[Symbol.iterator]=function(){return this}),a;function c(s){return function(l){return u([s,l])}}function u(s){if(n)throw new TypeError("Generator is already executing.");for(;r;)try{if(n=1,o&&(i=s[0]&2?o.return:s[0]?o.throw||((i=o.return)&&i.call(o),0):o.next)&&!(i=i.call(o,s[1])).done)return i;switch(o=0,i&&(s=[s[0]&2,i.value]),s[0]){case 0:case 1:i=s;break;case 4:return r.label++,{value:s[1],done:!1};case 5:r.label++,o=s[1],s=[0];continue;case 7:s=r.ops.pop(),r.trys.pop();continue;default:if(i=r.trys,!(i=i.length>0&&i[i.length-1])&&(s[0]===6||s[0]===2)){r=0;continue}if(s[0]===3&&(!i||s[1]>i[0]&&s[1]<i[3])){r.label=s[1];break}if(s[0]===6&&r.label<i[1]){r.label=i[1],i=s;break}if(i&&r.label<i[2]){r.label=i[2],r.ops.push(s);break}i[2]&&r.ops.pop(),r.trys.pop();continue}s=t.call(e,r)}catch(l){s=[6,l],o=0}finally{n=i=0}if(s[0]&5)throw s[1];return{value:s[0]?s[1]:void 0,done:!0}}}function ae(e){var t=typeof Symbol=="function"&&Symbol.iterator,r=t&&e[t],n=0;if(r)return r.call(e);if(e&&typeof e.length=="number")return{next:function(){return e&&n>=e.length&&(e=void 0),{value:e&&e[n++],done:!e}}};throw new TypeError(t?"Object is not iterable.":"Symbol.iterator is not defined.")}function C(e,t){var r=typeof Symbol=="function"&&e[Symbol.iterator];if(!r)return e;var n=r.call(e),o,i=[],a;try{for(;(t===void 0||t-- >0)&&!(o=n.next()).done;)i.push(o.value)}catch(c){a={error:c}}finally{try{o&&!o.done&&(r=n.return)&&r.call(n)}finally{if(a)throw a.error}}return i}function I(e,t){for(var r=0,n=t.length,o=e.length;r<n;r++,o++)e[o]=t[r];return e}function hr(e){if(!Symbol.asyncIterator)throw new TypeError("Symbol.asyncIterator is not defined.");var t=e[Symbol.asyncIterator],r;return t?t.call(e):(e=typeof ae=="function"?ae(e):e[Symbol.iterator](),r={},n("next"),n("throw"),n("return"),r[Symbol.asyncIterator]=function(){return this},r);function n(i){r[i]=e[i]&&function(a){return new Promise(function(c,u){a=e[i](a),o(c,u,a.done,a.value)})}}function o(i,a,c,u){Promise.resolve(u).then(function(s){i({value:s,done:c})},a)}}function g(e){return typeof e=="function"}function at(e){var t=function(n){Error.call(n),n.stack=new Error().stack},r=e(t);return r.prototype=Object.create(Error.prototype),r.prototype.constructor=r,r}var st=at(function(e){return function(r){e(this),this.message=r?r.length+` errors occurred during unsubscription:
`+r.map(function(n,o){return o+1+") "+n.toString()}).join(`
  `):"",this.name="UnsubscriptionError",this.errors=r}});function ve(e,t){if(e){var r=e.indexOf(t);0<=r&&e.splice(r,1)}}var te=function(){function e(t){this.initialTeardown=t,this.closed=!1,this._parentage=null,this._teardowns=null}return e.prototype.unsubscribe=function(){var t,r,n,o,i;if(!this.closed){this.closed=!0;var a=this._parentage;if(Array.isArray(a))try{for(var c=ae(a),u=c.next();!u.done;u=c.next()){var s=u.value;s.remove(this)}}catch(A){t={error:A}}finally{try{u&&!u.done&&(r=c.return)&&r.call(c)}finally{if(t)throw t.error}}else a==null||a.remove(this);var l=this.initialTeardown;if(g(l))try{l()}catch(A){i=A instanceof st?A.errors:[A]}var p=this._teardowns;if(p){this._teardowns=null;try{for(var d=ae(p),_=d.next();!_.done;_=d.next()){var $=_.value;try{br($)}catch(A){i=i!=null?i:[],A instanceof st?i=I(I([],C(i)),C(A.errors)):i.push(A)}}}catch(A){n={error:A}}finally{try{_&&!_.done&&(o=d.return)&&o.call(d)}finally{if(n)throw n.error}}}if(i)throw new st(i)}},e.prototype.add=function(t){var r;if(t&&t!==this)if(this.closed)br(t);else{if(t instanceof e){if(t.closed||t._hasParent(this))return;t._addParent(this)}(this._teardowns=(r=this._teardowns)!==null&&r!==void 0?r:[]).push(t)}},e.prototype._hasParent=function(t){var r=this._parentage;return r===t||Array.isArray(r)&&r.includes(t)},e.prototype._addParent=function(t){var r=this._parentage;this._parentage=Array.isArray(r)?(r.push(t),r):r?[r,t]:t},e.prototype._removeParent=function(t){var r=this._parentage;r===t?this._parentage=null:Array.isArray(r)&&ve(r,t)},e.prototype.remove=function(t){var r=this._teardowns;r&&ve(r,t),t instanceof e&&t._removeParent(this)},e.EMPTY=function(){var t=new e;return t.closed=!0,t}(),e}();var It=te.EMPTY;function ct(e){return e instanceof te||e&&"closed"in e&&g(e.remove)&&g(e.add)&&g(e.unsubscribe)}function br(e){g(e)?e():e.unsubscribe()}var pe={onUnhandledError:null,onStoppedNotification:null,Promise:void 0,useDeprecatedSynchronousErrorHandling:!1,useDeprecatedNextContext:!1};var Re={setTimeout:function(){for(var e=[],t=0;t<arguments.length;t++)e[t]=arguments[t];var r=Re.delegate;return((r==null?void 0:r.setTimeout)||setTimeout).apply(void 0,I([],C(e)))},clearTimeout:function(e){var t=Re.delegate;return((t==null?void 0:t.clearTimeout)||clearTimeout)(e)},delegate:void 0};function ut(e){Re.setTimeout(function(){var t=pe.onUnhandledError;if(t)t(e);else throw e})}function Y(){}var vr=function(){return Rt("C",void 0,void 0)}();function gr(e){return Rt("E",void 0,e)}function yr(e){return Rt("N",e,void 0)}function Rt(e,t,r){return{kind:e,value:t,error:r}}var Xe=function(e){z(t,e);function t(r){var n=e.call(this)||this;return n.isStopped=!1,r?(n.destination=r,ct(r)&&r.add(n)):n.destination=Zo,n}return t.create=function(r,n,o){return new $t(r,n,o)},t.prototype.next=function(r){this.isStopped?Pt(yr(r),this):this._next(r)},t.prototype.error=function(r){this.isStopped?Pt(gr(r),this):(this.isStopped=!0,this._error(r))},t.prototype.complete=function(){this.isStopped?Pt(vr,this):(this.isStopped=!0,this._complete())},t.prototype.unsubscribe=function(){this.closed||(this.isStopped=!0,e.prototype.unsubscribe.call(this))},t.prototype._next=function(r){this.destination.next(r)},t.prototype._error=function(r){this.destination.error(r),this.unsubscribe()},t.prototype._complete=function(){this.destination.complete(),this.unsubscribe()},t}(te);var $t=function(e){z(t,e);function t(r,n,o){var i=e.call(this)||this,a;if(g(r))a=r;else if(r){a=r.next,n=r.error,o=r.complete;var c;i&&pe.useDeprecatedNextContext?(c=Object.create(r),c.unsubscribe=function(){return i.unsubscribe()}):c=r,a=a==null?void 0:a.bind(c),n=n==null?void 0:n.bind(c),o=o==null?void 0:o.bind(c)}return i.destination={next:a?Vt(a,i):Y,error:Vt(n||xr,i),complete:o?Vt(o,i):Y},i}return t}(Xe);function Vt(e,t){return pe.useDeprecatedSynchronousErrorHandling?function(r){try{e(r)}catch(n){t.__syncError=n}}:e}function xr(e){if(pe.useDeprecatedSynchronousErrorHandling)throw e;ut(e)}function Pt(e,t){var r=pe.onStoppedNotification;r&&Re.setTimeout(function(){return r(e,t)})}var Zo={closed:!0,next:Y,error:xr,complete:Y};var Se=function(){return typeof Symbol=="function"&&Symbol.observable||"@@observable"}();function se(e){return e}function Sr(){for(var e=[],t=0;t<arguments.length;t++)e[t]=arguments[t];return Dt(e)}function Dt(e){return e.length===0?se:e.length===1?e[0]:function(r){return e.reduce(function(n,o){return o(n)},r)}}var O=function(){function e(t){t&&(this._subscribe=t)}return e.prototype.lift=function(t){var r=new e;return r.source=this,r.operator=t,r},e.prototype.subscribe=function(t,r,n){var o=ei(t)?t:new $t(t,r,n),i=this,a=i.operator,c=i.source;if(o.add(a?a.call(o,c):c||pe.useDeprecatedSynchronousErrorHandling?this._subscribe(o):this._trySubscribe(o)),pe.useDeprecatedSynchronousErrorHandling)for(var u=o;u;){if(u.__syncError)throw u.__syncError;u=u.destination}return o},e.prototype._trySubscribe=function(t){try{return this._subscribe(t)}catch(r){t.error(r)}},e.prototype.forEach=function(t,r){var n=this;return r=wr(r),new r(function(o,i){var a;a=n.subscribe(function(c){try{t(c)}catch(u){i(u),a==null||a.unsubscribe()}},i,o)})},e.prototype._subscribe=function(t){var r;return(r=this.source)===null||r===void 0?void 0:r.subscribe(t)},e.prototype[Se]=function(){return this},e.prototype.pipe=function(){for(var t=[],r=0;r<arguments.length;r++)t[r]=arguments[r];return t.length?Dt(t)(this):this},e.prototype.toPromise=function(t){var r=this;return t=wr(t),new t(function(n,o){var i;r.subscribe(function(a){return i=a},function(a){return o(a)},function(){return n(i)})})},e.create=function(t){return new e(t)},e}();function wr(e){var t;return(t=e!=null?e:pe.Promise)!==null&&t!==void 0?t:Promise}function ti(e){return e&&g(e.next)&&g(e.error)&&g(e.complete)}function ei(e){return e&&e instanceof Xe||ti(e)&&ct(e)}function ri(e){return g(e==null?void 0:e.lift)}function m(e){return function(t){if(ri(t))return t.lift(function(r){try{return e(r,this)}catch(n){this.error(n)}});throw new TypeError("Unable to lift unknown Observable type")}}var b=function(e){z(t,e);function t(r,n,o,i,a){var c=e.call(this,r)||this;return c.onFinalize=a,c._next=n?function(u){try{n(u)}catch(s){this.destination.error(s)}}:e.prototype._next,c._error=o?function(u){try{o(u)}catch(s){this.destination.error(s)}this.unsubscribe()}:e.prototype._error,c._complete=i?function(){try{i()}catch(u){this.destination.error(u)}this.unsubscribe()}:e.prototype._complete,c}return t.prototype.unsubscribe=function(){var r,n=this.closed;e.prototype.unsubscribe.call(this),!n&&((r=this.onFinalize)===null||r===void 0||r.call(this))},t}(Xe);var $e={schedule:function(e){var t=requestAnimationFrame,r=cancelAnimationFrame,n=$e.delegate;n&&(t=n.requestAnimationFrame,r=n.cancelAnimationFrame);var o=t(function(i){r=void 0,e(i)});return new te(function(){return r==null?void 0:r(o)})},requestAnimationFrame:function(){for(var e=[],t=0;t<arguments.length;t++)e[t]=arguments[t];var r=$e.delegate;return((r==null?void 0:r.requestAnimationFrame)||requestAnimationFrame).apply(void 0,I([],C(e)))},cancelAnimationFrame:function(){for(var e=[],t=0;t<arguments.length;t++)e[t]=arguments[t];var r=$e.delegate;return((r==null?void 0:r.cancelAnimationFrame)||cancelAnimationFrame).apply(void 0,I([],C(e)))},delegate:void 0};var Er=at(function(e){return function(){e(this),this.name="ObjectUnsubscribedError",this.message="object unsubscribed"}});var T=function(e){z(t,e);function t(){var r=e.call(this)||this;return r.observers=[],r.closed=!1,r.isStopped=!1,r.hasError=!1,r.thrownError=null,r}return t.prototype.lift=function(r){var n=new Or(this,this);return n.operator=r,n},t.prototype._throwIfClosed=function(){if(this.closed)throw new Er},t.prototype.next=function(r){var n,o;if(this._throwIfClosed(),!this.isStopped){var i=this.observers.slice();try{for(var a=ae(i),c=a.next();!c.done;c=a.next()){var u=c.value;u.next(r)}}catch(s){n={error:s}}finally{try{c&&!c.done&&(o=a.return)&&o.call(a)}finally{if(n)throw n.error}}}},t.prototype.error=function(r){if(this._throwIfClosed(),!this.isStopped){this.hasError=this.isStopped=!0,this.thrownError=r;for(var n=this.observers;n.length;)n.shift().error(r)}},t.prototype.complete=function(){if(this._throwIfClosed(),!this.isStopped){this.isStopped=!0;for(var r=this.observers;r.length;)r.shift().complete()}},t.prototype.unsubscribe=function(){this.isStopped=this.closed=!0,this.observers=null},t.prototype._trySubscribe=function(r){return this._throwIfClosed(),e.prototype._trySubscribe.call(this,r)},t.prototype._subscribe=function(r){return this._throwIfClosed(),this._checkFinalizedStatuses(r),this._innerSubscribe(r)},t.prototype._innerSubscribe=function(r){var n=this,o=this,i=o.hasError,a=o.isStopped,c=o.observers;return i||a?It:(c.push(r),new te(function(){return ve(n.observers,r)}))},t.prototype._checkFinalizedStatuses=function(r){var n=this,o=n.hasError,i=n.thrownError,a=n.isStopped;o?r.error(i):a&&r.complete()},t.prototype.asObservable=function(){var r=new O;return r.source=this,r},t.create=function(r,n){return new Or(r,n)},t}(O);var Or=function(e){z(t,e);function t(r,n){var o=e.call(this)||this;return o.destination=r,o.source=n,o}return t.prototype.next=function(r){var n,o;(o=(n=this.destination)===null||n===void 0?void 0:n.next)===null||o===void 0||o.call(n,r)},t.prototype.error=function(r){var n,o;(o=(n=this.destination)===null||n===void 0?void 0:n.error)===null||o===void 0||o.call(n,r)},t.prototype.complete=function(){var r,n;(n=(r=this.destination)===null||r===void 0?void 0:r.complete)===null||n===void 0||n.call(r)},t.prototype._subscribe=function(r){var n,o;return(o=(n=this.source)===null||n===void 0?void 0:n.subscribe(r))!==null&&o!==void 0?o:It},t}(T);var Be={now:function(){return(Be.delegate||Date).now()},delegate:void 0};var lt=function(e){z(t,e);function t(r,n,o){r===void 0&&(r=Infinity),n===void 0&&(n=Infinity),o===void 0&&(o=Be);var i=e.call(this)||this;return i.bufferSize=r,i.windowTime=n,i.timestampProvider=o,i.buffer=[],i.infiniteTimeWindow=!0,i.infiniteTimeWindow=n===Infinity,i.bufferSize=Math.max(1,r),i.windowTime=Math.max(1,n),i}return t.prototype.next=function(r){var n=this,o=n.isStopped,i=n.buffer,a=n.infiniteTimeWindow,c=n.timestampProvider,u=n.windowTime;o||(i.push(r),!a&&i.push(c.now()+u)),this.trimBuffer(),e.prototype.next.call(this,r)},t.prototype._subscribe=function(r){this._throwIfClosed(),this.trimBuffer();for(var n=this._innerSubscribe(r),o=this,i=o.infiniteTimeWindow,a=o.buffer,c=a.slice(),u=0;u<c.length&&!r.closed;u+=i?1:2)r.next(c[u]);return this._checkFinalizedStatuses(r),n},t.prototype.trimBuffer=function(){var r=this,n=r.bufferSize,o=r.timestampProvider,i=r.buffer,a=r.infiniteTimeWindow,c=(a?1:2)*n;if(n<Infinity&&c<i.length&&i.splice(0,i.length-c),!a){for(var u=o.now(),s=0,l=1;l<i.length&&i[l]<=u;l+=2)s=l;s&&i.splice(0,s+1)}},t}(T);var Tr=function(e){z(t,e);function t(r,n){return e.call(this)||this}return t.prototype.schedule=function(r,n){return n===void 0&&(n=0),this},t}(te);var Ge={setInterval:function(){for(var e=[],t=0;t<arguments.length;t++)e[t]=arguments[t];var r=Ge.delegate;return((r==null?void 0:r.setInterval)||setInterval).apply(void 0,I([],C(e)))},clearInterval:function(e){var t=Ge.delegate;return((t==null?void 0:t.clearInterval)||clearInterval)(e)},delegate:void 0};var ft=function(e){z(t,e);function t(r,n){var o=e.call(this,r,n)||this;return o.scheduler=r,o.work=n,o.pending=!1,o}return t.prototype.schedule=function(r,n){if(n===void 0&&(n=0),this.closed)return this;this.state=r;var o=this.id,i=this.scheduler;return o!=null&&(this.id=this.recycleAsyncId(i,o,n)),this.pending=!0,this.delay=n,this.id=this.id||this.requestAsyncId(i,this.id,n),this},t.prototype.requestAsyncId=function(r,n,o){return o===void 0&&(o=0),Ge.setInterval(r.flush.bind(r,this),o)},t.prototype.recycleAsyncId=function(r,n,o){if(o===void 0&&(o=0),o!=null&&this.delay===o&&this.pending===!1)return n;Ge.clearInterval(n)},t.prototype.execute=function(r,n){if(this.closed)return new Error("executing a cancelled action");this.pending=!1;var o=this._execute(r,n);if(o)return o;this.pending===!1&&this.id!=null&&(this.id=this.recycleAsyncId(this.scheduler,this.id,null))},t.prototype._execute=function(r,n){var o=!1,i;try{this.work(r)}catch(a){o=!0,i=!!a&&a||new Error(a)}if(o)return this.unsubscribe(),i},t.prototype.unsubscribe=function(){if(!this.closed){var r=this,n=r.id,o=r.scheduler,i=o.actions;this.work=this.state=this.scheduler=null,this.pending=!1,ve(i,this),n!=null&&(this.id=this.recycleAsyncId(o,n,null)),this.delay=null,e.prototype.unsubscribe.call(this)}},t}(Tr);var Ut=function(){function e(t,r){r===void 0&&(r=e.now),this.schedulerActionCtor=t,this.now=r}return e.prototype.schedule=function(t,r,n){return r===void 0&&(r=0),new this.schedulerActionCtor(this,t).schedule(n,r)},e.now=Be.now,e}();var pt=function(e){z(t,e);function t(r,n){n===void 0&&(n=Ut.now);var o=e.call(this,r,n)||this;return o.actions=[],o.active=!1,o.scheduled=void 0,o}return t.prototype.flush=function(r){var n=this.actions;if(this.active){n.push(r);return}var o;this.active=!0;do if(o=r.execute(r.state,r.delay))break;while(r=n.shift());if(this.active=!1,o){for(;r=n.shift();)r.unsubscribe();throw o}},t}(Ut);var Ze=new pt(ft),Mr=Ze;var Ar=function(e){z(t,e);function t(r,n){var o=e.call(this,r,n)||this;return o.scheduler=r,o.work=n,o}return t.prototype.requestAsyncId=function(r,n,o){return o===void 0&&(o=0),o!==null&&o>0?e.prototype.requestAsyncId.call(this,r,n,o):(r.actions.push(this),r.scheduled||(r.scheduled=$e.requestAnimationFrame(function(){return r.flush(void 0)})))},t.prototype.recycleAsyncId=function(r,n,o){if(o===void 0&&(o=0),o!=null&&o>0||o==null&&this.delay>0)return e.prototype.recycleAsyncId.call(this,r,n,o);r.actions.length===0&&($e.cancelAnimationFrame(n),r.scheduled=void 0)},t}(ft);var Lr=function(e){z(t,e);function t(){return e!==null&&e.apply(this,arguments)||this}return t.prototype.flush=function(r){this.active=!0,this.scheduled=void 0;var n=this.actions,o,i=-1;r=r||n.shift();var a=n.length;do if(o=r.execute(r.state,r.delay))break;while(++i<a&&(r=n.shift()));if(this.active=!1,o){for(;++i<a&&(r=n.shift());)r.unsubscribe();throw o}},t}(pt);var J=new Lr(Ar);var me=new O(function(e){return e.complete()});function Pe(e,t){return new O(function(r){var n=0;return t.schedule(function(){n===e.length?r.complete():(r.next(e[n++]),r.closed||this.schedule())})})}var Ve=function(e){return e&&typeof e.length=="number"&&typeof e!="function"};function mt(e){return g(e==null?void 0:e.then)}function ni(){return typeof Symbol!="function"||!Symbol.iterator?"@@iterator":Symbol.iterator}var De=ni();function _r(e,t){return new O(function(r){var n=new te;return n.add(t.schedule(function(){var o=e[Se]();n.add(o.subscribe({next:function(i){n.add(t.schedule(function(){return r.next(i)}))},error:function(i){n.add(t.schedule(function(){return r.error(i)}))},complete:function(){n.add(t.schedule(function(){return r.complete()}))}}))})),n})}function Hr(e,t){return new O(function(r){return t.schedule(function(){return e.then(function(n){r.add(t.schedule(function(){r.next(n),r.add(t.schedule(function(){return r.complete()}))}))},function(n){r.add(t.schedule(function(){return r.error(n)}))})})})}function Cr(e,t,r,n){n===void 0&&(n=0);var o=t.schedule(function(){try{r.call(this)}catch(i){e.error(i)}},n);return e.add(o),o}function jr(e,t){return new O(function(r){var n;return r.add(t.schedule(function(){n=e[De](),Cr(r,t,function(){var o=n.next(),i=o.value,a=o.done;a?r.complete():(r.next(i),this.schedule())})})),function(){return g(n==null?void 0:n.return)&&n.return()}})}function dt(e){return g(e[Se])}function ht(e){return g(e==null?void 0:e[De])}function kr(e,t){if(!e)throw new Error("Iterable cannot be null");return new O(function(r){var n=new te;return n.add(t.schedule(function(){var o=e[Symbol.asyncIterator]();n.add(t.schedule(function(){var i=this;o.next().then(function(a){a.done?r.complete():(r.next(a.value),i.schedule())})}))})),n})}function bt(e){return Symbol.asyncIterator&&g(e==null?void 0:e[Symbol.asyncIterator])}function vt(e){return new TypeError("You provided "+(e!==null&&typeof e=="object"?"an invalid object":"'"+e+"'")+" where a stream was expected. You can provide an Observable, Promise, Array, AsyncIterable, or Iterable.")}function Fr(e,t){if(e!=null){if(dt(e))return _r(e,t);if(Ve(e))return Pe(e,t);if(mt(e))return Hr(e,t);if(bt(e))return kr(e,t);if(ht(e))return jr(e,t)}throw vt(e)}function ge(e,t){return t?Fr(e,t):V(e)}function V(e){if(e instanceof O)return e;if(e!=null){if(dt(e))return oi(e);if(Ve(e))return Wt(e);if(mt(e))return ii(e);if(bt(e))return si(e);if(ht(e))return ai(e)}throw vt(e)}function oi(e){return new O(function(t){var r=e[Se]();if(g(r.subscribe))return r.subscribe(t);throw new TypeError("Provided object does not correctly implement Symbol.observable")})}function Wt(e){return new O(function(t){for(var r=0;r<e.length&&!t.closed;r++)t.next(e[r]);t.complete()})}function ii(e){return new O(function(t){e.then(function(r){t.closed||(t.next(r),t.complete())},function(r){return t.error(r)}).then(null,ut)})}function ai(e){return new O(function(t){for(var r=e[De]();!t.closed;){var n=r.next(),o=n.done,i=n.value;o?t.complete():t.next(i)}return function(){return g(r==null?void 0:r.return)&&r.return()}})}function si(e){return new O(function(t){ci(e,t).catch(function(r){return t.error(r)})})}function ci(e,t){var r,n,o,i;return mr(this,void 0,void 0,function(){var a,c;return dr(this,function(u){switch(u.label){case 0:u.trys.push([0,5,6,11]),r=hr(e),u.label=1;case 1:return[4,r.next()];case 2:if(n=u.sent(),!!n.done)return[3,4];a=n.value,t.next(a),u.label=3;case 3:return[3,1];case 4:return[3,11];case 5:return c=u.sent(),o={error:c},[3,11];case 6:return u.trys.push([6,,9,10]),n&&!n.done&&(i=r.return)?[4,i.call(r)]:[3,8];case 7:u.sent(),u.label=8;case 8:return[3,10];case 9:if(o)throw o.error;return[7];case 10:return[7];case 11:return t.complete(),[2]}})})}function de(e,t){return t?Pe(e,t):Wt(e)}function gt(e){return e&&g(e.schedule)}function Nt(e){return e[e.length-1]}function we(e){return g(Nt(e))?e.pop():void 0}function ue(e){return gt(Nt(e))?e.pop():void 0}function yt(e,t){return typeof Nt(e)=="number"?e.pop():t}function H(){for(var e=[],t=0;t<arguments.length;t++)e[t]=arguments[t];var r=ue(e);return r?Pe(e,r):de(e)}function Ir(e){return e instanceof Date&&!isNaN(e)}function f(e,t){return m(function(r,n){var o=0;r.subscribe(new b(n,function(i){n.next(e.call(t,i,o++))}))})}var ui=Array.isArray;function li(e,t){return ui(t)?e.apply(void 0,I([],C(t))):e(t)}function Ue(e){return f(function(t){return li(e,t)})}function X(e,t){return t===void 0&&(t=0),m(function(r,n){r.subscribe(new b(n,function(o){return n.add(e.schedule(function(){return n.next(o)},t))},function(o){return n.add(e.schedule(function(){return n.error(o)},t))},function(){return n.add(e.schedule(function(){return n.complete()},t))}))})}var fi=Array.isArray,pi=Object.getPrototypeOf,mi=Object.prototype,di=Object.keys;function Rr(e){if(e.length===1){var t=e[0];if(fi(t))return{args:t,keys:null};if(hi(t)){var r=di(t);return{args:r.map(function(n){return t[n]}),keys:r}}}return{args:e,keys:null}}function hi(e){return e&&typeof e=="object"&&pi(e)===mi}function $r(e,t){return e.reduce(function(r,n,o){return r[n]=t[o],r},{})}function B(){for(var e=[],t=0;t<arguments.length;t++)e[t]=arguments[t];var r=ue(e),n=we(e),o=Rr(e),i=o.args,a=o.keys;if(i.length===0)return ge([],r);var c=new O(zt(i,r,a?function(u){return $r(a,u)}:se));return n?c.pipe(Ue(n)):c}function zt(e,t,r){return r===void 0&&(r=se),function(n){Pr(t,function(){for(var o=e.length,i=new Array(o),a=o,c=o,u=function(l){Pr(t,function(){var p=ge(e[l],t),d=!1;p.subscribe(new b(n,function(_){i[l]=_,d||(d=!0,c--),c||n.next(r(i.slice()))},void 0,function(){--a||n.complete()}))},n)},s=0;s<o;s++)u(s)},n)}}function Pr(e,t,r){e?r.add(e.schedule(t)):t()}function Vr(e,t,r,n,o,i,a,c){var u=[],s=0,l=0,p=!1,d=function(){p&&!u.length&&!s&&t.complete()},_=function(A){return s<n?$(A):u.push(A)},$=function(A){i&&t.next(A),s++;var ee=!1;V(r(A,l++)).subscribe(new b(t,function(P){o==null||o(P),i?_(P):t.next(P)},void 0,function(){ee=!0},function(){if(ee)try{s--;for(var P=function(){var F=u.shift();a?t.add(a.schedule(function(){return $(F)})):$(F)};u.length&&s<n;)P();d()}catch(F){t.error(F)}}))};return e.subscribe(new b(t,_,void 0,function(){p=!0,d()})),function(){c==null||c()}}function re(e,t,r){return r===void 0&&(r=Infinity),g(t)?re(function(n,o){return f(function(i,a){return t(n,i,o,a)})(V(e(n,o)))},r):(typeof t=="number"&&(r=t),m(function(n,o){return Vr(n,o,e,r)}))}function We(e){return e===void 0&&(e=Infinity),re(se,e)}function Dr(){return We(1)}function et(){for(var e=[],t=0;t<arguments.length;t++)e[t]=arguments[t];return Dr()(de(e,ue(e)))}function Ee(e){return new O(function(t){V(e()).subscribe(t)})}var bi=["addListener","removeListener"],vi=["addEventListener","removeEventListener"],gi=["on","off"];function w(e,t,r,n){if(g(r)&&(n=r,r=void 0),n)return w(e,t,r).pipe(Ue(n));var o=C(Si(e)?vi.map(function(c){return function(u){return e[c](t,u,r)}}):yi(e)?bi.map(Ur(e,t)):xi(e)?gi.map(Ur(e,t)):[],2),i=o[0],a=o[1];return!i&&Ve(e)?re(function(c){return w(c,t,r)})(de(e)):new O(function(c){if(!i)throw new TypeError("Invalid event target");var u=function(){for(var s=[],l=0;l<arguments.length;l++)s[l]=arguments[l];return c.next(1<s.length?s:s[0])};return i(u),function(){return a(u)}})}function Ur(e,t){return function(r){return function(n){return e[r](t,n)}}}function yi(e){return g(e.addListener)&&g(e.removeListener)}function xi(e){return g(e.on)&&g(e.off)}function Si(e){return g(e.addEventListener)&&g(e.removeEventListener)}function Wr(e,t,r){e===void 0&&(e=0),r===void 0&&(r=Mr);var n=-1;return t!=null&&(gt(t)?r=t:n=t),new O(function(o){var i=Ir(e)?+e-r.now():e;i<0&&(i=0);var a=0;return r.schedule(function(){o.closed||(o.next(a++),0<=n?this.schedule(void 0,n):o.complete())},i)})}var wi=Array.isArray;function Oe(e){return e.length===1&&wi(e[0])?e[0]:e}function j(){for(var e=[],t=0;t<arguments.length;t++)e[t]=arguments[t];var r=ue(e),n=yt(e,Infinity),o=Oe(e);return o.length?o.length===1?V(o[0]):We(n)(de(o,r)):me}var G=new O(Y);function M(e,t){return m(function(r,n){var o=0;r.subscribe(new b(n,function(i){return e.call(t,i,o++)&&n.next(i)}))})}function Nr(){for(var e=[],t=0;t<arguments.length;t++)e[t]=arguments[t];var r=we(e),n=Oe(e);return n.length?new O(function(o){var i=n.map(function(){return[]}),a=n.map(function(){return!1});o.add(function(){i=a=null});for(var c=function(s){V(n[s]).subscribe(new b(o,function(l){if(i[s].push(l),i.every(function(d){return d.length})){var p=i.map(function(d){return d.shift()});o.next(r?r.apply(void 0,I([],C(p))):p),i.some(function(d,_){return!d.length&&a[_]})&&o.complete()}},void 0,function(){a[s]=!0,!i[s].length&&o.complete()}))},u=0;!o.closed&&u<n.length;u++)c(u);return function(){i=a=null}}):me}function Te(e,t){return t===void 0&&(t=null),t=t!=null?t:e,m(function(r,n){var o=[],i=0;r.subscribe(new b(n,function(a){var c,u,s,l,p=null;i++%t==0&&o.push([]);try{for(var d=ae(o),_=d.next();!_.done;_=d.next()){var $=_.value;$.push(a),e<=$.length&&(p=p!=null?p:[],p.push($))}}catch(P){c={error:P}}finally{try{_&&!_.done&&(u=d.return)&&u.call(d)}finally{if(c)throw c.error}}if(p)try{for(var A=ae(p),ee=A.next();!ee.done;ee=A.next()){var $=ee.value;ve(o,$),n.next($)}}catch(P){s={error:P}}finally{try{ee&&!ee.done&&(l=A.return)&&l.call(A)}finally{if(s)throw s.error}}},void 0,function(){var a,c;try{for(var u=ae(o),s=u.next();!s.done;s=u.next()){var l=s.value;n.next(l)}}catch(p){a={error:p}}finally{try{s&&!s.done&&(c=u.return)&&c.call(u)}finally{if(a)throw a.error}}n.complete()},function(){o=null}))})}function tt(e){return m(function(t,r){var n=null,o=!1,i;n=t.subscribe(new b(r,void 0,function(a){i=V(e(a,tt(e)(t))),n?(n.unsubscribe(),n=null,i.subscribe(r)):o=!0})),o&&(n.unsubscribe(),n=null,i.subscribe(r))})}function zr(e,t,r,n,o){return function(i,a){var c=r,u=t,s=0;i.subscribe(new b(a,function(l){var p=s++;u=c?e(u,l,p):(c=!0,l),n&&a.next(u)},void 0,o&&function(){c&&a.next(u),a.complete()}))}}function Qr(){for(var e=[],t=0;t<arguments.length;t++)e[t]=arguments[t];var r=we(e);return r?Sr(Qr.apply(void 0,I([],C(e))),Ue(r)):m(function(n,o){zt(I([n],C(Oe(e))))(o)})}function Qt(){for(var e=[],t=0;t<arguments.length;t++)e[t]=arguments[t];return Qr.apply(void 0,I([],C(e)))}function qr(e,t){return g(t)?re(e,t,1):re(e,1)}function Kr(e,t){return t===void 0&&(t=Ze),m(function(r,n){var o=null,i=null,a=null,c=function(){if(o){o.unsubscribe(),o=null;var s=i;i=null,n.next(s)}};function u(){var s=a+e,l=t.now();if(l<s){o=this.schedule(void 0,s-l);return}c()}r.subscribe(new b(n,function(s){i=s,a=t.now(),o||(o=t.schedule(u,e))},void 0,function(){c(),n.complete()},function(){i=o=null}))})}function xt(e){return e===void 0&&(e=null),m(function(t,r){var n=!1;t.subscribe(new b(r,function(o){n=!0,r.next(o)},void 0,function(){n||r.next(e),r.complete()}))})}function rt(e){return e<=0?function(){return me}:m(function(t,r){var n=0;t.subscribe(new b(r,function(o){++n<=e&&(r.next(o),e<=n&&r.complete())}))})}function Yr(){return m(function(e,t){e.subscribe(new b(t,Y))})}function ce(e){return m(function(t,r){t.subscribe(new b(r,function(){return r.next(e)}))})}function qt(e,t){return t?function(r){return et(t.pipe(rt(1),Yr()),r.pipe(qt(e)))}:re(function(r,n){return e(r,n).pipe(rt(1),ce(r))})}function Me(e,t){t===void 0&&(t=Ze);var r=Wr(e,t);return qt(function(){return r})}function Q(e,t){return t===void 0&&(t=se),e=e!=null?e:Ei,m(function(r,n){var o,i=!0;r.subscribe(new b(n,function(a){var c=t(a);(i||!e(o,c))&&(i=!1,o=c,n.next(a))}))})}function Ei(e,t){return e===t}function W(e,t){return Q(function(r,n){return t?t(r[e],n[e]):r[e]===n[e]})}function D(e){return m(function(t,r){t.subscribe(r),r.add(e)})}function Jr(e){return e<=0?function(){return me}:m(function(t,r){var n=[];t.subscribe(new b(r,function(o){n.push(o),e<n.length&&n.shift()},void 0,function(){var o,i;try{for(var a=ae(n),c=a.next();!c.done;c=a.next()){var u=c.value;r.next(u)}}catch(s){o={error:s}}finally{try{c&&!c.done&&(i=a.return)&&i.call(a)}finally{if(o)throw o.error}}r.complete()},function(){n=null}))})}function Oi(){for(var e=[],t=0;t<arguments.length;t++)e[t]=arguments[t];var r=ue(e),n=yt(e,Infinity);return e=Oe(e),m(function(o,i){We(n)(de(I([o],C(e)),r)).subscribe(i)})}function St(){for(var e=[],t=0;t<arguments.length;t++)e[t]=arguments[t];return Oi.apply(void 0,I([],C(e)))}function nt(e){return m(function(t,r){var n=!1,o=null;t.subscribe(new b(r,function(a){n=!0,o=a}));var i=function(){if(n){n=!1;var a=o;o=null,r.next(a)}};e.subscribe(new b(r,i,void 0,Y))})}function Xr(e,t){return m(zr(e,t,arguments.length>=2,!0))}function ne(e){e=e||{};var t=e.connector,r=t===void 0?function(){return new T}:t,n=e.resetOnComplete,o=n===void 0?!0:n,i=e.resetOnError,a=i===void 0?!0:i,c=e.resetOnRefCountZero,u=c===void 0?!0:c,s=null,l=null,p=0,d=!1,_=!1,$=function(){s=l=null,d=_=!1};return m(function(A,ee){return p++,l=l!=null?l:r(),l.subscribe(ee),s||(s=ge(A).subscribe({next:function(P){return l.next(P)},error:function(P){_=!0;var F=l;a&&$(),F.error(P)},complete:function(){d=!0;var P=l;o&&$(),P.complete()}})),function(){if(p--,u&&!p&&!_&&!d){var P=s;$(),P==null||P.unsubscribe()}}})}function oe(e,t,r){var n,o,i,a=!1;return e&&typeof e=="object"?(i=(n=e.bufferSize)!==null&&n!==void 0?n:Infinity,t=(o=e.windowTime)!==null&&o!==void 0?o:Infinity,a=!!e.refCount,r=e.scheduler):i=e!=null?e:Infinity,ne({connector:function(){return new lt(i,t,r)},resetOnError:!0,resetOnComplete:!1,resetOnRefCountZero:a})}function Kt(e){return M(function(t,r){return e<=r})}function Br(e){return m(function(t,r){var n=!1,o=new b(r,function(){o==null||o.unsubscribe(),n=!0},void 0,Y);V(e).subscribe(o),t.subscribe(new b(r,function(i){return n&&r.next(i)}))})}function U(){for(var e=[],t=0;t<arguments.length;t++)e[t]=arguments[t];var r=ue(e);return m(function(n,o){(r?et(e,n,r):et(e,n)).subscribe(o)})}function x(e,t){return m(function(r,n){var o=null,i=0,a=!1,c=function(){return a&&!o&&n.complete()};r.subscribe(new b(n,function(u){o==null||o.unsubscribe();var s=0,l=i++;V(e(u,l)).subscribe(o=new b(n,function(p){return n.next(t?t(u,p,l,s++):p)},void 0,function(){o=null,c()}))},void 0,function(){a=!0,c()}))})}function Gr(e,t){return t?x(function(){return e},t):x(function(){return e})}function Zr(e){return m(function(t,r){V(e).subscribe(new b(r,function(){return r.complete()},void 0,Y)),!r.closed&&t.subscribe(r)})}function en(e,t){return t===void 0&&(t=!1),m(function(r,n){var o=0;r.subscribe(new b(n,function(i){var a=e(i,o++);(a||t)&&n.next(i),!a&&n.complete()}))})}function k(e,t,r){var n=g(e)||t||r?{next:e,error:t,complete:r}:e;return n?m(function(o,i){o.subscribe(new b(i,function(a){var c;(c=n.next)===null||c===void 0||c.call(n,a),i.next(a)},function(a){var c;(c=n.error)===null||c===void 0||c.call(n,a),i.error(a)},function(){var a;(a=n.complete)===null||a===void 0||a.call(n),i.complete()}))}):se}var Ti={leading:!0,trailing:!1};function tn(e,t){var r=t===void 0?Ti:t,n=r.leading,o=r.trailing;return m(function(i,a){var c=!1,u=null,s=null,l=!1,p=function(){s==null||s.unsubscribe(),s=null,o&&($(),l&&a.complete())},d=function(){s=null,l&&a.complete()},_=function(A){return s=V(e(A)).subscribe(new b(a,p,void 0,d))},$=function(){c&&(a.next(u),!l&&_(u)),c=!1,u=null};i.subscribe(new b(a,function(A){c=!0,u=A,!(s&&!s.closed)&&(n?$():_(A))},void 0,function(){l=!0,!(o&&c&&s&&!s.closed)&&a.complete()}))})}function he(){for(var e=[],t=0;t<arguments.length;t++)e[t]=arguments[t];var r=we(e);return m(function(n,o){for(var i=e.length,a=new Array(i),c=e.map(function(){return!1}),u=!1,s=function(p){V(e[p]).subscribe(new b(o,function(d){a[p]=d,!u&&!c[p]&&(c[p]=!0,(u=c.every(se))&&(c=null))},void 0,Y))},l=0;l<i;l++)s(l);n.subscribe(new b(o,function(p){if(u){var d=I([p],C(a));o.next(r?r.apply(void 0,I([],C(d))):d)}}))})}function Mi(){for(var e=[],t=0;t<arguments.length;t++)e[t]=arguments[t];return m(function(r,n){Nr.apply(void 0,I([r],C(e))).subscribe(n)})}function rn(){for(var e=[],t=0;t<arguments.length;t++)e[t]=arguments[t];return Mi.apply(void 0,I([],C(e)))}function nn(){let e=new lt;return w(document,"DOMContentLoaded").pipe(ce(document)).subscribe(e),e}function ie(e,t=document){return t.querySelector(e)||void 0}function le(e,t=document){let r=ie(e,t);if(typeof r=="undefined")throw new ReferenceError(`Missing element: expected "${e}" to be present`);return r}function He(){return document.activeElement instanceof HTMLElement?document.activeElement:void 0}function q(e,t=document){return Array.from(t.querySelectorAll(e))}function Ne(e){return document.createElement(e)}function Ce(e,...t){e.replaceWith(...t)}function Ae(e,t=!0){t?e.focus():e.blur()}function on(e){return j(w(e,"focus"),w(e,"blur")).pipe(f(({type:t})=>t==="focus"),U(e===He()))}var an=new T,Ai=Ee(()=>H(new ResizeObserver(e=>{for(let t of e)an.next(t)}))).pipe(x(e=>G.pipe(U(e)).pipe(D(()=>e.disconnect()))),oe(1));function je(e){return{width:e.offsetWidth,height:e.offsetHeight}}function wt(e){return{width:e.scrollWidth,height:e.scrollHeight}}function ze(e){return Ai.pipe(k(t=>t.observe(e)),x(t=>an.pipe(M(({target:r})=>r===e),D(()=>t.unobserve(e)),f(({contentRect:r})=>({width:r.width,height:r.height})))),U(je(e)))}function sn(e){return{x:e.scrollLeft,y:e.scrollTop}}function Li(e){return j(w(e,"scroll"),w(window,"resize")).pipe(f(()=>sn(e)),U(sn(e)))}function cn(e,t=16){return Li(e).pipe(f(({y:r})=>{let n=je(e),o=wt(e);return r>=o.height-n.height-t}),Q())}function un(e){if(e instanceof HTMLInputElement)e.select();else throw new Error("Not implemented")}var Et={drawer:le("[data-md-toggle=drawer]"),search:le("[data-md-toggle=search]")};function ln(e){return Et[e].checked}function ke(e,t){Et[e].checked!==t&&Et[e].click()}function Ot(e){let t=Et[e];return w(t,"change").pipe(f(()=>t.checked),U(t.checked))}function _i(e){switch(e.tagName){case"INPUT":case"SELECT":case"TEXTAREA":return!0;default:return e.isContentEditable}}function fn(){return w(window,"keydown").pipe(M(e=>!(e.metaKey||e.ctrlKey)),f(e=>({mode:ln("search")?"search":"global",type:e.key,claim(){e.preventDefault(),e.stopPropagation()}})),M(({mode:e})=>{if(e==="global"){let t=He();if(typeof t!="undefined")return!_i(t)}return!0}),ne())}function pn(){return new URL(location.href)}function mn(e){location.href=e.href}function dn(){return new T}function hn(){return location.hash.substring(1)}function bn(e){let t=Ne("a");t.href=e,t.addEventListener("click",r=>r.stopPropagation()),t.click()}function Hi(){return w(window,"hashchange").pipe(f(hn),U(hn()),M(e=>e.length>0),ne())}function vn(){return Hi().pipe(x(e=>H(ie(`[id="${e}"]`))))}function Qe(e){let t=matchMedia(e);return w(t,"change").pipe(f(r=>r.matches),U(t.matches))}function gn(){return j(Qe("print").pipe(M(Boolean)),w(window,"beforeprint")).pipe(ce(void 0))}function Yt(e,t){return e.pipe(x(r=>r?t():G))}function Tt(e,t={credentials:"same-origin"}){return ge(fetch(e.toString(),t)).pipe(M(r=>r.status===200))}function Le(e,t){return Tt(e,t).pipe(x(r=>r.json()),oe(1))}function yn(e,t){let r=new DOMParser;return Tt(e,t).pipe(x(n=>n.text()),f(n=>r.parseFromString(n,"text/xml")),oe(1))}function xn(){return{x:Math.max(0,pageXOffset),y:Math.max(0,pageYOffset)}}function Jt({x:e,y:t}){window.scrollTo(e||0,t||0)}function Sn(){return j(w(window,"scroll",{passive:!0}),w(window,"resize",{passive:!0})).pipe(f(xn),U(xn()))}function wn(){return{width:innerWidth,height:innerHeight}}function En(){return w(window,"resize",{passive:!0}).pipe(f(wn),U(wn()))}function On(){return B([Sn(),En()]).pipe(f(([e,t])=>({offset:e,size:t})),oe(1))}function Mt(e,{viewport$:t,header$:r}){let n=t.pipe(W("size")),o=B([n,r]).pipe(f(()=>({x:e.offsetLeft,y:e.offsetTop})));return B([r,t,o]).pipe(f(([{height:i},{offset:a,size:c},{x:u,y:s}])=>({offset:{x:a.x-u,y:a.y-s+i},size:c})))}function Tn(e,{tx$:t}){let r=w(e,"message").pipe(f(({data:n})=>n));return t.pipe(tn(()=>r,{leading:!0,trailing:!0}),k(n=>e.postMessage(n)),Gr(r),ne())}var Ci=le("#__config"),qe=JSON.parse(Ci.textContent);qe.base=new URL(qe.base,pn()).toString().replace(/\/$/,"");function Z(){return qe}function At(e){return qe.features.includes(e)}function K(e,t){return typeof t!="undefined"?qe.translations[e].replace("#",t.toString()):qe.translations[e]}function _e(e,t=document){return le(`[data-md-component=${e}]`,t)}function be(e,t=document){return q(`[data-md-component=${e}]`,t)}var so=it(Bt());function Mn(e,t=0){e.setAttribute("tabindex",t.toString())}function An(e){e.removeAttribute("tabindex")}function Ln(e,t){e.setAttribute("data-md-state","lock"),e.style.top=`-${t}px`}function _n(e){let t=-1*parseInt(e.style.top,10);e.removeAttribute("data-md-state"),e.style.top="",t&&window.scrollTo(0,t)}function Hn(e,t){e.setAttribute("data-md-state",t)}function Cn(e){e.removeAttribute("data-md-state")}function jn(e,t){e.classList.toggle("md-nav__link--active",t)}function kn(e){e.classList.remove("md-nav__link--active")}function Fn(e,t){e.firstElementChild.innerHTML=t}function In(e,t){e.setAttribute("data-md-state",t)}function Rn(e){e.removeAttribute("data-md-state")}function $n(e,t){e.setAttribute("data-md-state",t)}function Pn(e){e.removeAttribute("data-md-state")}function Vn(e,t){e.setAttribute("data-md-state",t)}function Dn(e){e.removeAttribute("data-md-state")}function Un(e,t){e.placeholder=t}function Wn(e){e.placeholder=K("search.placeholder")}function Nn(e,t){if(typeof t=="string"||typeof t=="number")e.innerHTML+=t.toString();else if(t instanceof Node)e.appendChild(t);else if(Array.isArray(t))for(let r of t)Nn(e,r)}function R(e,t,...r){let n=document.createElement(e);if(t)for(let o of Object.keys(t))typeof t[o]!="boolean"?n.setAttribute(o,t[o]):t[o]&&n.setAttribute(o,"");for(let o of r)Nn(n,o);return n}function zn(e,t){let r=t;if(e.length>r){for(;e[r]!==" "&&--r>0;);return`${e.substring(0,r)}...`}return e}function ye(e){if(e>999){let t=+((e-950)%1e3>99);return`${((e+1e-6)/1e3).toFixed(t)}k`}else return e.toString()}function ji(e){let t=0;for(let r=0,n=e.length;r<n;r++)t=(t<<5)-t+e.charCodeAt(r),t|=0;return t}function Gt(e){let t=Z();return`${e}[${ji(t.base)}]`}function Qn(e,t){switch(t){case 0:e.textContent=K("search.result.none");break;case 1:e.textContent=K("search.result.one");break;default:e.textContent=K("search.result.other",ye(t))}}function qn(e){e.textContent=K("search.result.placeholder")}function Kn(e,t){e.appendChild(t)}function Yn(e){e.innerHTML=""}function Jn(e,t){e.style.top=`${t}px`}function Xn(e){e.style.top=""}function Bn(e,t){let r=e.firstElementChild;r.style.height=`${t-2*r.offsetTop}px`}function Gn(e){let t=e.firstElementChild;t.style.height=""}function Zn(e,t){e.lastElementChild.appendChild(t)}function eo(e,t){e.lastElementChild.setAttribute("data-md-state",t)}function to(e,t){e.setAttribute("data-md-state",t)}function Zt(e){e.removeAttribute("data-md-state")}function ro(e){return R("button",{class:"md-clipboard md-icon",title:K("clipboard.copy"),"data-clipboard-target":`#${e} > code`})}var Fe;(function(e){e[e.TEASER=1]="TEASER",e[e.PARENT=2]="PARENT"})(Fe||(Fe={}));function er(e,t){let r=t&2,n=t&1,o=Object.keys(e.terms).filter(a=>!e.terms[a]).map(a=>[R("del",null,a)," "]).flat().slice(0,-1),i=e.location;return R("a",{href:i,class:"md-search-result__link",tabIndex:-1},R("article",{class:["md-search-result__article",...r?["md-search-result__article--document"]:[]].join(" "),"data-md-score":e.score.toFixed(2)},r>0&&R("div",{class:"md-search-result__icon md-icon"}),R("h1",{class:"md-search-result__title"},e.title),n>0&&e.text.length>0&&R("p",{class:"md-search-result__teaser"},zn(e.text,320)),n>0&&o.length>0&&R("p",{class:"md-search-result__terms"},K("search.result.term.missing"),": ",o)))}function no(e){let t=e[0].score,r=[...e],n=r.findIndex(s=>!s.location.includes("#")),[o]=r.splice(n,1),i=r.findIndex(s=>s.score<t);i===-1&&(i=r.length);let a=r.slice(0,i),c=r.slice(i),u=[er(o,2|+(!n&&i===0)),...a.map(s=>er(s,1)),...c.length?[R("details",{class:"md-search-result__more"},R("summary",{tabIndex:-1},c.length>0&&c.length===1?K("search.result.more.one"):K("search.result.more.other",c.length)),c.map(s=>er(s,1)))]:[]];return R("li",{class:"md-search-result__item"},u)}function oo(e){return R("ul",{class:"md-source__facts"},e.map(t=>R("li",{class:"md-source__fact"},t)))}function io(e){return R("div",{class:"md-typeset__scrollwrap"},R("div",{class:"md-typeset__table"},e))}function ki(e){let t=Z(),r=new URL(`${e.version}/`,t.base);return R("li",{class:"md-version__item"},R("a",{href:r.toString(),class:"md-version__link"},e.title))}function ao(e){let t=Z(),[,r]=t.base.match(/([^/]+)\/?$/),n=e.find(({version:o,aliases:i})=>o===r||i.includes(r))||e[0];return R("div",{class:"md-version"},R("span",{class:"md-version__current"},n.title),R("ul",{class:"md-version__list"},e.map(ki)))}var Fi=0;function Ii(e,{viewport$:t}){let r=H(e).pipe(x(n=>{let o=n.closest("[data-tabs]");return o instanceof HTMLElement?j(...q("input",o).map(i=>w(i,"change"))):G}));return j(t.pipe(W("size")),r).pipe(f(()=>{let n=je(e);return{scroll:wt(e).width>n.width}}),W("scroll"))}function co(e,t){let r=new T;if(r.pipe(he(Qe("(hover)"))).subscribe(([{scroll:n},o])=>{n&&o?Mn(e):An(e)}),so.default.isSupported()){let n=e.closest("pre");n.id=`__code_${Fi++}`,n.insertBefore(ro(n.id),e)}return Ii(e,t).pipe(k(r),D(()=>r.complete()),f(n=>N({ref:e},n)))}function Ri(e,{target$:t,print$:r}){return t.pipe(f(n=>n.closest("details:not([open])")),M(n=>e===n),St(r),ce(e))}function uo(e,t){let r=new T;return r.subscribe(()=>{e.setAttribute("open",""),e.scrollIntoView()}),Ri(e,t).pipe(k(r),D(()=>r.complete()),ce({ref:e}))}var lo=Ne("table");function fo(e){return Ce(e,lo),Ce(lo,io(e)),H({ref:e})}function po(e,{target$:t,viewport$:r,print$:n}){return j(...q("pre > code",e).map(o=>co(o,{viewport$:r})),...q("table:not([class])",e).map(o=>fo(o)),...q("details",e).map(o=>uo(o,{target$:t,print$:n})))}function $i(e,{alert$:t}){return t.pipe(x(r=>j(H(!0),H(!1).pipe(Me(2e3))).pipe(f(n=>({message:r,open:n})))))}function mo(e,t){let r=new T;return r.pipe(X(J)).subscribe(({message:n,open:o})=>{Fn(e,n),o?In(e,"open"):Rn(e)}),$i(e,t).pipe(k(r),D(()=>r.complete()),f(n=>N({ref:e},n)))}function Pi({viewport$:e}){if(!At("header.autohide"))return H(!1);let t=e.pipe(f(({offset:{y:o}})=>o),Te(2,1),f(([o,i])=>[o<i,i]),W(0)),r=B([e,t]).pipe(M(([{offset:o},[,i]])=>Math.abs(i-o.y)>100),f(([,[o]])=>o),Q()),n=Ot("search");return B([e,n]).pipe(f(([{offset:o},i])=>o.y>400&&!i),Q(),x(o=>o?r:H(!1)),U(!1))}function ho(e,t){return Ee(()=>{let r=getComputedStyle(e);return H(r.position==="sticky"||r.position==="-webkit-sticky")}).pipe(Qt(ze(e),Pi(t)),f(([r,{height:n},o])=>({height:r?n:0,sticky:r,hidden:o})),Q((r,n)=>r.sticky===n.sticky&&r.height===n.height&&r.hidden===n.hidden),oe(1))}function bo(e,{header$:t,main$:r}){let n=new T;return n.pipe(W("active"),Qt(t),X(J)).subscribe(([{active:o},{hidden:i}])=>{o?$n(e,i?"hidden":"shadow"):Pn(e)}),r.subscribe(o=>n.next(o)),t.pipe(f(o=>N({ref:e},o)))}function Vi(e,{viewport$:t,header$:r}){return Mt(e,{header$:r,viewport$:t}).pipe(f(({offset:{y:n}})=>{let{height:o}=je(e);return{active:n>=o}}),W("active"))}function vo(e,t){let r=new T;r.pipe(X(J)).subscribe(({active:o})=>{o?Vn(e,"active"):Dn(e)});let n=ie("article h1");return typeof n=="undefined"?G:Vi(n,t).pipe(k(r),D(()=>r.complete()),f(o=>N({ref:e},o)))}function go(e,{viewport$:t,header$:r}){let n=r.pipe(f(({height:i})=>i),Q()),o=n.pipe(x(()=>ze(e).pipe(f(({height:i})=>({top:e.offsetTop,bottom:e.offsetTop+i})),W("bottom"))));return B([n,o,t]).pipe(f(([i,{top:a,bottom:c},{offset:{y:u},size:{height:s}}])=>(s=Math.max(0,s-Math.max(0,a-u,i)-Math.max(0,s+u-c)),{offset:a-i,height:s,active:a-i<=u})),Q((i,a)=>i.offset===a.offset&&i.height===a.height&&i.active===a.active))}var tr=it(Bt());function yo({alert$:e}){tr.default.isSupported()&&new O(t=>{new tr.default("[data-clipboard-target], [data-clipboard-text]").on("success",r=>t.next(r))}).subscribe(()=>e.next(K("clipboard.copied")))}function Di(e){if(e.length<2)return e;let[t,r]=e.sort((i,a)=>i.length-a.length).map(i=>i.replace(/[^/]+$/,"")),n=0;if(t===r)n=t.length;else for(;t.charCodeAt(n)===r.charCodeAt(n);)n++;let o=Z();return e.map(i=>i.replace(t.slice(0,n),`${o.base}/`))}function xo({document$:e,location$:t,viewport$:r}){let n=Z();if(location.protocol==="file:")return;"scrollRestoration"in history&&(history.scrollRestoration="manual",w(window,"beforeunload").subscribe(()=>{history.scrollRestoration="auto"}));let o=ie("link[rel=icon]");typeof o!="undefined"&&(o.href=o.href);let i=yn(`${n.base}/sitemap.xml`).pipe(f(s=>Di(q("loc",s).map(l=>l.textContent))),x(s=>w(document.body,"click").pipe(M(l=>!l.metaKey&&!l.ctrlKey),x(l=>{if(l.target instanceof Element){let p=l.target.closest("a");if(p&&!p.target&&s.includes(p.href))return l.preventDefault(),H({url:new URL(p.href)})}return G}))),ne()),a=w(window,"popstate").pipe(M(s=>s.state!==null),f(s=>({url:new URL(location.href),offset:s.state})),ne());j(i,a).pipe(Q((s,l)=>s.url.href===l.url.href),f(({url:s})=>s)).subscribe(t);let c=t.pipe(W("pathname"),x(s=>Tt(s.href).pipe(tt(()=>(mn(s),G)))),ne());i.pipe(nt(c)).subscribe(({url:s})=>{history.pushState({},"",s.toString())});let u=new DOMParser;c.pipe(x(s=>s.text()),f(s=>u.parseFromString(s,"text/html"))).subscribe(e),j(i,a).pipe(nt(e)).subscribe(({url:s,offset:l})=>{s.hash&&!l?bn(s.hash):Jt(l||{y:0})}),e.pipe(Kt(1)).subscribe(s=>{for(let l of["title","link[rel='canonical']","meta[name='author']","meta[name='description']","[data-md-component=announce]","[data-md-component=header-topic]","[data-md-component=container]","[data-md-component=logo], .md-logo","[data-md-component=skip]"]){let p=ie(l),d=ie(l,s);typeof p!="undefined"&&typeof d!="undefined"&&Ce(p,d)}}),e.pipe(Kt(1),f(()=>_e("container")),x(s=>H(...q("script",s))),qr(s=>{let l=Ne("script");if(s.src){for(let p of s.getAttributeNames())l.setAttribute(p,s.getAttribute(p));return Ce(s,l),new O(p=>{l.onload=()=>p.complete()})}else return l.textContent=s.textContent,Ce(s,l),me})).subscribe(),r.pipe(Br(i),Kr(250),W("offset")).subscribe(({offset:s})=>{history.replaceState(s,"")}),j(i,a).pipe(Te(2,1),M(([s,l])=>s.url.pathname===l.url.pathname),f(([,s])=>s)).subscribe(({offset:s})=>{Jt(s||{y:0})})}var Ni=it(wo());function Eo(e){return e.split(/"([^"]+)"/g).map((t,r)=>r&1?t.replace(/^\b|^(?![^\x00-\x7F]|$)|\s+/g," +"):t).join("").replace(/"|(?:^|\s+)[*+\-:^~]+(?=\s+|$)/g,"").trim()}var xe;(function(e){e[e.SETUP=0]="SETUP",e[e.READY=1]="READY",e[e.QUERY=2]="QUERY",e[e.RESULT=3]="RESULT"})(xe||(xe={}));function Oo(e){return e.type===1}function To(e){return e.type===2}function Lt(e){return e.type===3}function zi({config:e,docs:t,index:r}){e.lang.length===1&&e.lang[0]==="en"&&(e.lang=[K("search.config.lang")]),e.separator==="[\\s\\-]+"&&(e.separator=K("search.config.separator"));let n=K("search.config.pipeline").split(/\s*,\s*/).filter(Boolean);return{config:e,docs:t,index:r,pipeline:n}}function Mo(e,t){let r=Z(),n=new Worker(e),o=new T,i=Tn(n,{tx$:o}).pipe(f(a=>{if(Lt(a))for(let c of a.data)for(let u of c)u.location=`${r.base}/${u.location}`;return a}),ne());return ge(t).pipe(f(a=>({type:xe.SETUP,data:zi(a)}))).subscribe(o.next.bind(o)),{tx$:o,rx$:i}}function Ao(){let e=Z();Le(new URL("versions.json",e.base)).subscribe(t=>{le(".md-header__topic").appendChild(ao(t))})}function Qi(e){let t=(__search==null?void 0:__search.transform)||Eo,r=on(e),n=j(w(e,"keyup"),w(e,"focus").pipe(Me(1))).pipe(f(()=>t(e.value)),Q());return B([n,r]).pipe(f(([o,i])=>({value:o,focus:i})))}function Lo(e,{tx$:t}){let r=new T;return r.pipe(W("value"),f(({value:n})=>({type:xe.QUERY,data:n}))).subscribe(t.next.bind(t)),r.pipe(W("focus")).subscribe(({focus:n})=>{n?(ke("search",n),Un(e,"")):Wn(e)}),w(e.form,"reset").pipe(Zr(r.pipe(Jr(1)))).subscribe(()=>Ae(e)),Qi(e).pipe(k(r),D(()=>r.complete()),f(n=>N({ref:e},n)))}function _o(e,{rx$:t},{query$:r}){let n=new T,o=cn(e.parentElement).pipe(M(Boolean)),i=le(":scope > :first-child",e);n.pipe(X(J),he(r)).subscribe(([{data:u},{value:s}])=>{s?Qn(i,u.length):qn(i)});let a=le(":scope > :last-child",e);return n.pipe(X(J),k(()=>Yn(a)),x(({data:u})=>j(H(...u.slice(0,10)),H(...u.slice(10)).pipe(Te(4),rn(o),x(([s])=>H(...s)))))).subscribe(u=>{Kn(a,no(u))}),t.pipe(M(Lt),f(({data:u})=>({data:u})),U({data:[]})).pipe(k(n),D(()=>n.complete()),f(u=>N({ref:e},u)))}function Ho(e,{index$:t,keyboard$:r}){let n=Z(),o=Mo(n.search,t),i=_e("search-query",e),a=_e("search-result",e),{tx$:c,rx$:u}=o;c.pipe(M(To),nt(u.pipe(M(Oo))),rt(1)).subscribe(c.next.bind(c)),r.pipe(M(({mode:l})=>l==="search")).subscribe(l=>{let p=He();switch(l.type){case"Enter":p===i&&l.claim();break;case"Escape":case"Tab":ke("search",!1),Ae(i,!1);break;case"ArrowUp":case"ArrowDown":if(typeof p=="undefined")Ae(i);else{let d=[i,...q(":not(details) > [href], summary, details[open] [href]",a)],_=Math.max(0,(Math.max(0,d.indexOf(p))+d.length+(l.type==="ArrowUp"?-1:1))%d.length);Ae(d[_])}l.claim();break;default:i!==He()&&Ae(i)}}),r.pipe(M(({mode:l})=>l==="global")).subscribe(l=>{switch(l.type){case"f":case"s":case"/":Ae(i),un(i),l.claim();break}});let s=Lo(i,o);return j(s,_o(a,o,{query$:s}))}function qi(e,{viewport$:t,main$:r}){let n=e.parentElement.offsetTop-e.parentElement.parentElement.offsetTop;return B([r,t]).pipe(f(([{offset:o,height:i},{offset:{y:a}}])=>(i=i+Math.min(n,Math.max(0,a-o))-n,{height:i,locked:a>=o+n})),Q((o,i)=>o.height===i.height&&o.locked===i.locked))}function rr(e,n){var{header$:t}=n,r=lr(n,["header$"]);let o=new T;return o.pipe(X(J),he(t)).subscribe({next([{height:i},{height:a}]){Bn(e,i),Jn(e,a)},complete(){Xn(e),Gn(e)}}),qi(e,r).pipe(k(o),D(()=>o.complete()),f(i=>N({ref:e},i)))}function Co(e,t){let r=typeof t!="undefined"?`https://api.github.com/repos/${e}/${t}`:`https://api.github.com/users/${e}`;return Le(r).pipe(f(n=>{if(typeof t!="undefined"){let{stargazers_count:o,forks_count:i}=n;return[`${ye(o)} Stars`,`${ye(i)} Forks`]}else{let{public_repos:o}=n;return[`${ye(o)} Repositories`]}}),xt([]))}function jo(e,t){let r=`https://${e}/api/v4/projects/${encodeURIComponent(t)}`;return Le(r).pipe(f(({star_count:n,forks_count:o})=>[`${ye(n)} Stars`,`${ye(o)} Forks`]),xt([]))}function ko(e){let[t]=e.match(/(git(?:hub|lab))/i)||[];switch(t.toLowerCase()){case"github":let[,r,n]=e.match(/^.+github\.com\/([^/]+)\/?([^/]+)?/i);return Co(r,n);case"gitlab":let[,o,i]=e.match(/^.+?([^/]*gitlab[^/]+)\/(.+?)\/?$/i);return jo(o,i);default:return G}}var Ki;function Yi(e){return Ki||(Ki=Ee(()=>{let t=sessionStorage.getItem(Gt("__repo"));if(t)return H(JSON.parse(t));{let r=ko(e.href);return r.subscribe(n=>{try{sessionStorage.setItem(Gt("__repo"),JSON.stringify(n))}catch(o){}}),r}}).pipe(tt(()=>G),M(t=>t.length>0),f(t=>({facts:t})),oe(1)))}function Fo(e){let t=new T;return t.subscribe(({facts:r})=>{Zn(e,oo(r)),eo(e,"done")}),Yi(e).pipe(k(t),D(()=>t.complete()),f(r=>N({ref:e},r)))}function Ji(e,{viewport$:t,header$:r}){return Mt(e,{header$:r,viewport$:t}).pipe(f(({offset:{y:n}})=>({hidden:n>=10})),W("hidden"))}function Io(e,t){let r=new T;return r.pipe(X(J)).subscribe({next({hidden:n}){n?to(e,"hidden"):Zt(e)},complete(){Zt(e)}}),Ji(e,t).pipe(k(r),D(()=>r.complete()),f(n=>N({ref:e},n)))}function Xi(e,{viewport$:t,header$:r}){let n=new Map;for(let a of e){let c=decodeURIComponent(a.hash.substring(1)),u=ie(`[id="${c}"]`);typeof u!="undefined"&&n.set(a,u)}let o=r.pipe(f(a=>24+a.height));return ze(document.body).pipe(W("height"),f(()=>{let a=[];return[...n].reduce((c,[u,s])=>{for(;a.length&&n.get(a[a.length-1]).tagName>=s.tagName;)a.pop();let l=s.offsetTop;for(;!l&&s.parentElement;)s=s.parentElement,l=s.offsetTop;return c.set([...a=[...a,u]].reverse(),l)},new Map)}),f(a=>new Map([...a].sort(([,c],[,u])=>c-u))),x(a=>B([o,t]).pipe(Xr(([c,u],[s,{offset:{y:l}}])=>{for(;u.length;){let[,p]=u[0];if(p-s<l)c=[...c,u.shift()];else break}for(;c.length;){let[,p]=c[c.length-1];if(p-s>=l)u=[c.pop(),...u];else break}return[c,u]},[[],[...a]]),Q((c,u)=>c[0]===u[0]&&c[1]===u[1])))).pipe(f(([a,c])=>({prev:a.map(([u])=>u),next:c.map(([u])=>u)})),U({prev:[],next:[]}),Te(2,1),f(([a,c])=>a.prev.length<c.prev.length?{prev:c.prev.slice(Math.max(0,a.prev.length-1),c.prev.length),next:[]}:{prev:c.prev.slice(-1),next:c.next.slice(0,c.next.length-a.next.length)}))}function Ro(e,t){let r=new T;r.pipe(X(J)).subscribe(({prev:o,next:i})=>{for(let[a]of i)kn(a),Cn(a);for(let[a,[c]]of o.entries())jn(c,a===o.length-1),Hn(c,"blur")});let n=q("[href^=\\#]",e);return Xi(n,t).pipe(k(r),D(()=>r.complete()),f(o=>N({ref:e},o)))}function $o({document$:e,tablet$:t}){e.pipe(x(()=>H(...q("[data-md-state=indeterminate]"))),k(r=>{r.indeterminate=!0,r.checked=!1}),re(r=>w(r,"change").pipe(en(()=>r.hasAttribute("data-md-state")),ce(r))),he(t)).subscribe(([r,n])=>{r.removeAttribute("data-md-state"),n&&(r.checked=!1)})}function Bi(){return/(iPad|iPhone|iPod)/.test(navigator.userAgent)}function Po({document$:e}){e.pipe(x(()=>H(...q("[data-md-scrollfix]"))),k(t=>t.removeAttribute("data-md-scrollfix")),M(Bi),re(t=>w(t,"touchstart").pipe(ce(t)))).subscribe(t=>{let r=t.scrollTop;r===0?t.scrollTop=1:r+t.offsetHeight===t.scrollHeight&&(t.scrollTop=r-1)})}function Vo({viewport$:e,tablet$:t}){B([Ot("search"),t]).pipe(f(([r,n])=>r&&!n),x(r=>H(r).pipe(Me(r?400:100),X(J))),he(e)).subscribe(([r,{offset:{y:n}}])=>{r?Ln(document.body,n):_n(document.body)})}document.documentElement.classList.remove("no-js");document.documentElement.classList.add("js");var Ke=nn(),nr=dn(),or=vn(),ir=fn(),fe=On(),_t=Qe("(min-width: 960px)"),Do=Qe("(min-width: 1220px)"),Uo=gn(),Wo=Z(),Gi=document.forms.namedItem("search")?(__search==null?void 0:__search.index)||Le(`${Wo.base}/search/search_index.json`):G,ar=new T;yo({alert$:ar});At("navigation.instant")&&xo({document$:Ke,location$:nr,viewport$:fe});var No;((No=Wo.version)==null?void 0:No.provider)==="mike"&&Ao();j(nr,or).pipe(Me(125)).subscribe(()=>{ke("drawer",!1),ke("search",!1)});ir.pipe(M(({mode:e})=>e==="global")).subscribe(e=>{switch(e.type){case"p":case",":let t=ie("[href][rel=prev]");typeof t!="undefined"&&t.click();break;case"n":case".":let r=ie("[href][rel=next]");typeof r!="undefined"&&r.click();break}});$o({document$:Ke,tablet$:_t});Po({document$:Ke});Vo({viewport$:fe,tablet$:_t});var Ie=ho(_e("header"),{viewport$:fe}),sr=Ke.pipe(f(()=>_e("main")),x(e=>go(e,{viewport$:fe,header$:Ie})),oe(1)),Zi=j(...be("dialog").map(e=>mo(e,{alert$:ar})),...be("header").map(e=>bo(e,{viewport$:fe,header$:Ie,main$:sr})),...be("search").map(e=>Ho(e,{index$:Gi,keyboard$:ir})),...be("source").map(e=>Fo(e)),...be("tabs").map(e=>Io(e,{viewport$:fe,header$:Ie}))),ea=Ee(()=>j(...be("content").map(e=>po(e,{target$:or,viewport$:fe,print$:Uo})),...be("header-title").map(e=>vo(e,{viewport$:fe,header$:Ie})),...be("sidebar").map(e=>e.getAttribute("data-md-type")==="navigation"?Yt(Do,()=>rr(e,{viewport$:fe,header$:Ie,main$:sr})):Yt(_t,()=>rr(e,{viewport$:fe,header$:Ie,main$:sr}))),...be("toc").map(e=>Ro(e,{viewport$:fe,header$:Ie})))),zo=Ke.pipe(x(()=>ea),St(Zi),oe(1));zo.subscribe();window.document$=Ke;window.location$=nr;window.target$=or;window.keyboard$=ir;window.viewport$=fe;window.tablet$=_t;window.screen$=Do;window.print$=Uo;window.alert$=ar;window.component$=zo;})();
/*!
 * clipboard.js v2.0.6
 * https://clipboardjs.com/
 * 
 * Licensed MIT © Zeno Rocha
 */
/*!
 * escape-html
 * Copyright(c) 2012-2013 TJ Holowaychuk
 * Copyright(c) 2015 Andreas Lubbe
 * Copyright(c) 2015 Tiancheng "Timothy" Gu
 * MIT Licensed
 */
/*! *****************************************************************************
Copyright (c) Microsoft Corporation.

Permission to use, copy, modify, and/or distribute this software for any
purpose with or without fee is hereby granted.

THE SOFTWARE IS PROVIDED "AS IS" AND THE AUTHOR DISCLAIMS ALL WARRANTIES WITH
REGARD TO THIS SOFTWARE INCLUDING ALL IMPLIED WARRANTIES OF MERCHANTABILITY
AND FITNESS. IN NO EVENT SHALL THE AUTHOR BE LIABLE FOR ANY SPECIAL, DIRECT,
INDIRECT, OR CONSEQUENTIAL DAMAGES OR ANY DAMAGES WHATSOEVER RESULTING FROM
LOSS OF USE, DATA OR PROFITS, WHETHER IN AN ACTION OF CONTRACT, NEGLIGENCE OR
OTHER TORTIOUS ACTION, ARISING OUT OF OR IN CONNECTION WITH THE USE OR
PERFORMANCE OF THIS SOFTWARE.
***************************************************************************** */
//# sourceMappingURL=bundle.a1c7c35e.min.js.map

