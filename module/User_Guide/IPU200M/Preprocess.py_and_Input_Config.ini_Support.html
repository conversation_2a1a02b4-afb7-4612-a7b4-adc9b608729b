<!doctype html><html lang=en class=no-js> <head><meta charset=utf-8><meta name=viewport content="width=device-width,initial-scale=1"><meta name=description content="Personal Documentation System Template"><meta name=author content="<PERSON> Cho<PERSON>"><link rel=icon href=../../../images/favicon.ico><meta name=generator content="mkdocs-1.1.2, mkdocs-material-7.0.6"><title>附录. 前处理和配置文件注意要点 - IPU SDK</title><link rel=stylesheet href=../../../assets/stylesheets/main.2c0c5eaf.min.css><link rel=stylesheet href=../../../assets/stylesheets/palette.7fa14f5b.min.css><meta name=theme-color content=#009485><link rel=preconnect href=https://fonts.gstatic.com crossorigin><link rel=stylesheet href="https://fonts.googleapis.com/css?family=Roboto:300,400,400i,700%7CRoboto+Mono&display=fallback"><style>:root{--md-text-font-family:"Roboto";--md-code-font-family:"Roboto Mono"}</style><link rel=stylesheet href=../../../stylesheets/extra.css></head> <body dir=ltr data-md-color-scheme data-md-color-primary=teal data-md-color-accent=teal> <input class=md-toggle data-md-toggle=drawer type=checkbox id=__drawer autocomplete=off> <input class=md-toggle data-md-toggle=search type=checkbox id=__search autocomplete=off> <label class=md-overlay for=__drawer></label> <div data-md-component=skip> <a href=#1-preprocesspy class=md-skip> Skip to content </a> </div> <div data-md-component=announce> </div> <header class=md-header data-md-component=header> <nav class="md-header__inner md-grid" aria-label=Header> <a href=../../.. title="IPU SDK" class="md-header__button md-logo" aria-label="IPU SDK" data-md-component=logo> <svg xmlns=http://www.w3.org/2000/svg viewbox="0 0 24 24"><path d="M10 20v-6h4v6h5v-8h3L12 3 2 12h3v8h5z"/></svg> </a> <label class="md-header__button md-icon" for=__drawer> <svg xmlns=http://www.w3.org/2000/svg viewbox="0 0 24 24"><path d="M3 6h18v2H3V6m0 5h18v2H3v-2m0 5h18v2H3v-2z"/></svg> </label> <div class=md-header__title data-md-component=header-title> <div class=md-header__ellipsis> <div class=md-header__topic> <span class=md-ellipsis> IPU SDK </span> </div> <div class=md-header__topic data-md-component=header-topic> <span class=md-ellipsis> 附录. 前处理和配置文件注意要点 </span> </div> </div> </div> <div class=md-header__options> </div> <label class="md-header__button md-icon" for=__search> <svg xmlns=http://www.w3.org/2000/svg viewbox="0 0 24 24"><path d="M9.5 3A6.5 6.5 0 0 1 16 9.5c0 1.61-.59 3.09-1.56 4.23l.27.27h.79l5 5-1.5 1.5-5-5v-.79l-.27-.27A6.516 6.516 0 0 1 9.5 16 6.5 6.5 0 0 1 3 9.5 6.5 6.5 0 0 1 9.5 3m0 2C7 5 5 7 5 9.5S7 14 9.5 14 14 12 14 9.5 12 5 9.5 5z"/></svg> </label> <div class=md-search data-md-component=search role=dialog> <label class=md-search__overlay for=__search></label> <div class=md-search__inner role=search> <form class=md-search__form name=search> <input type=text class=md-search__input name=query aria-label=Search placeholder=Search autocapitalize=off autocorrect=off autocomplete=off spellcheck=false data-md-component=search-query data-md-state=active required> <label class="md-search__icon md-icon" for=__search> <svg xmlns=http://www.w3.org/2000/svg viewbox="0 0 24 24"><path d="M9.5 3A6.5 6.5 0 0 1 16 9.5c0 1.61-.59 3.09-1.56 4.23l.27.27h.79l5 5-1.5 1.5-5-5v-.79l-.27-.27A6.516 6.516 0 0 1 9.5 16 6.5 6.5 0 0 1 3 9.5 6.5 6.5 0 0 1 9.5 3m0 2C7 5 5 7 5 9.5S7 14 9.5 14 14 12 14 9.5 12 5 9.5 5z"/></svg> <svg xmlns=http://www.w3.org/2000/svg viewbox="0 0 24 24"><path d="M20 11v2H8l5.5 5.5-1.42 1.42L4.16 12l7.92-7.92L13.5 5.5 8 11h12z"/></svg> </label> <button type=reset class="md-search__icon md-icon" aria-label=Clear tabindex=-1> <svg xmlns=http://www.w3.org/2000/svg viewbox="0 0 24 24"><path d="M19 6.41L17.59 5 12 10.59 6.41 5 5 6.41 10.59 12 5 17.59 6.41 19 12 13.41 17.59 19 19 17.59 13.41 12 19 6.41z"/></svg> </button> </form> <div class=md-search__output> <div class=md-search__scrollwrap data-md-scrollfix> <div class=md-search-result data-md-component=search-result> <div class=md-search-result__meta> Initializing search </div> <ol class=md-search-result__list></ol> </div> </div> </div> </div> </div> </nav> </header> <div class=md-container data-md-component=container> <main class=md-main data-md-component=main> <div class="md-main__inner md-grid"> <div class="md-sidebar md-sidebar--primary" data-md-component=sidebar data-md-type=navigation> <div class=md-sidebar__scrollwrap> <div class=md-sidebar__inner> <nav class="md-nav md-nav--primary" aria-label=Navigation data-md-level=0> <label class=md-nav__title for=__drawer> <a href=../../.. title="IPU SDK" class="md-nav__button md-logo" aria-label="IPU SDK" data-md-component=logo> <svg xmlns=http://www.w3.org/2000/svg viewbox="0 0 24 24"><path d="M10 20v-6h4v6h5v-8h3L12 3 2 12h3v8h5z"/></svg> </a> IPU SDK </label> <ul class=md-nav__list data-md-scrollfix> <li class=md-nav__item> <a href=../../../index.html class=md-nav__link> 主页 </a> </li> <li class="md-nav__item md-nav__item--nested"> <input class="md-nav__toggle md-toggle" data-md-toggle=__nav_2 type=checkbox id=__nav_2> <label class=md-nav__link for=__nav_2> SDK介绍 <span class="md-nav__icon md-icon"></span> </label> <nav class=md-nav aria-label=SDK介绍 data-md-level=1> <label class=md-nav__title for=__nav_2> <span class="md-nav__icon md-icon"></span> SDK介绍 </label> <ul class=md-nav__list data-md-scrollfix> <li class=md-nav__item> <a href=../../Introduction/Introduction.html class=md-nav__link> SDK框架介绍 </a> </li> <li class=md-nav__item> <a href=../../Introduction/Docker.html class=md-nav__link> Docker环境 </a> </li> </ul> </nav> </li> <li class="md-nav__item md-nav__item--active md-nav__item--nested"> <input class="md-nav__toggle md-toggle" data-md-toggle=__nav_3 type=checkbox id=__nav_3 checked> <label class=md-nav__link for=__nav_3> 用户手册 <span class="md-nav__icon md-icon"></span> </label> <nav class=md-nav aria-label=用户手册 data-md-level=1> <label class=md-nav__title for=__nav_3> <span class="md-nav__icon md-icon"></span> 用户手册 </label> <ul class=md-nav__list data-md-scrollfix> <li class=md-nav__item> <a href=../Common/Environment_Construction.html class=md-nav__link> 1. 快速开始 </a> </li> <li class=md-nav__item> <a href=Convert.html class=md-nav__link> 2. Convert Tool </a> </li> <li class=md-nav__item> <a href=../Common/Calibrate.html class=md-nav__link> 3. Calibrator </a> </li> <li class=md-nav__item> <a href=../Common/Compile.html class=md-nav__link> 4. Compiler </a> </li> <li class=md-nav__item> <a href=Simulate.html class=md-nav__link> 5. Simulator </a> </li> <li class=md-nav__item> <a href=../Common/DumpDebug_Tool.html class=md-nav__link> 6. DumpDebug Tool </a> </li> <li class=md-nav__item> <a href=../Common/SigmaStar_Post_Processing_Module.html class=md-nav__link> 7. SigmaStar后处理模块 </a> </li> <li class=md-nav__item> <a href=../Common/Adding_A_New_Layer.html class=md-nav__link> 8. 如何添加新的Layer </a> </li> <li class=md-nav__item> <a href=Special_Model_Conversion.html class=md-nav__link> 9. 特殊模型转换要点 </a> </li> <li class=md-nav__item> <a href=DLA_SDK_Support.html class=md-nav__link> 10. DLA SDK 支持 </a> </li> <li class=md-nav__item> <a href=../Common/Running_Offline_Network_Model_On_Development_Board.html class=md-nav__link> 11. 在开发板上运行离线网络模型 </a> </li> <li class="md-nav__item md-nav__item--active"> <input class="md-nav__toggle md-toggle" data-md-toggle=toc type=checkbox id=__toc> <label class="md-nav__link md-nav__link--active" for=__toc> 附录. 前处理和配置文件注意要点 <span class="md-nav__icon md-icon"></span> </label> <a href=Preprocess.py_and_Input_Config.ini_Support.html class="md-nav__link md-nav__link--active"> 附录. 前处理和配置文件注意要点 </a> <nav class="md-nav md-nav--secondary" aria-label="Table of contents"> <label class=md-nav__title for=__toc> <span class="md-nav__icon md-icon"></span> Table of contents </label> <ul class=md-nav__list data-md-component=toc data-md-scrollfix> <li class=md-nav__item> <a href=#1-preprocesspy class=md-nav__link> 1. 前处理文件Preprocess.py </a> <nav class=md-nav aria-label="1. 前处理文件Preprocess.py"> <ul class=md-nav__list> <li class=md-nav__item> <a href=#11-training_input_formatsinput_formatsrgbbgr class=md-nav__link> 1.1. training_input_formats和input_formats均为RGB或者均为BGR </a> </li> <li class=md-nav__item> <a href=#12-training_input_formatsrgbinput_formatsgray class=md-nav__link> 1.2. training_input_formats为RGB和input_formats为GRAY </a> </li> <li class=md-nav__item> <a href=#13-training_input_formatsrgbbgrinput_formatsyuv_nv12 class=md-nav__link> 1.3. training_input_formats为RGB或BGR和input_formats为YUV_NV12 </a> </li> <li class=md-nav__item> <a href=#14-training_input_formatsinput_formatsrawdata_s16_nhwcrawdata_f32_nhwc class=md-nav__link> 1.4. training_input_formats和input_formats均为RAWDATA_S16_NHWC或者均为RAWDATA_F32_NHWC </a> </li> </ul> </nav> </li> <li class=md-nav__item> <a href=#2 class=md-nav__link> 2. 模型性能优化规则 </a> </li> </ul> </nav> </li> </ul> </nav> </li> <li class="md-nav__item md-nav__item--nested"> <input class="md-nav__toggle md-toggle" data-md-toggle=__nav_4 type=checkbox id=__nav_4> <label class=md-nav__link for=__nav_4> FAQ <span class="md-nav__icon md-icon"></span> </label> <nav class=md-nav aria-label=FAQ data-md-level=1> <label class=md-nav__title for=__nav_4> <span class="md-nav__icon md-icon"></span> FAQ </label> <ul class=md-nav__list data-md-scrollfix> <li class=md-nav__item> <a href=../../FAQ/Common/Env_Setting.html class=md-nav__link> 环境设置问题 </a> </li> <li class=md-nav__item> <a href=../../FAQ/IPU200M/Model_Conversion.html class=md-nav__link> 模型转换问题 </a> </li> <li class=md-nav__item> <a href=../../FAQ/IPU200M/Development_Board.html class=md-nav__link> 板端使用问题 </a> </li> <li class=md-nav__item> <a href=../../FAQ/IPU200M/Other_Anomalies.html class=md-nav__link> 其他异常问题 </a> </li> </ul> </nav> </li> </ul> </nav> </div> </div> </div> <div class="md-sidebar md-sidebar--secondary" data-md-component=sidebar data-md-type=toc> <div class=md-sidebar__scrollwrap> <div class=md-sidebar__inner> <nav class="md-nav md-nav--secondary" aria-label="Table of contents"> <label class=md-nav__title for=__toc> <span class="md-nav__icon md-icon"></span> Table of contents </label> <ul class=md-nav__list data-md-component=toc data-md-scrollfix> <li class=md-nav__item> <a href=#1-preprocesspy class=md-nav__link> 1. 前处理文件Preprocess.py </a> <nav class=md-nav aria-label="1. 前处理文件Preprocess.py"> <ul class=md-nav__list> <li class=md-nav__item> <a href=#11-training_input_formatsinput_formatsrgbbgr class=md-nav__link> 1.1. training_input_formats和input_formats均为RGB或者均为BGR </a> </li> <li class=md-nav__item> <a href=#12-training_input_formatsrgbinput_formatsgray class=md-nav__link> 1.2. training_input_formats为RGB和input_formats为GRAY </a> </li> <li class=md-nav__item> <a href=#13-training_input_formatsrgbbgrinput_formatsyuv_nv12 class=md-nav__link> 1.3. training_input_formats为RGB或BGR和input_formats为YUV_NV12 </a> </li> <li class=md-nav__item> <a href=#14-training_input_formatsinput_formatsrawdata_s16_nhwcrawdata_f32_nhwc class=md-nav__link> 1.4. training_input_formats和input_formats均为RAWDATA_S16_NHWC或者均为RAWDATA_F32_NHWC </a> </li> </ul> </nav> </li> <li class=md-nav__item> <a href=#2 class=md-nav__link> 2. 模型性能优化规则 </a> </li> </ul> </nav> </div> </div> </div> <div class=md-content data-md-component=content> <article class="md-content__inner md-typeset"> <h1>附录. 前处理和配置文件注意要点</h1> <h2 id=1-preprocesspy>1. 前处理文件Preprocess.py<a class=headerlink href=#1-preprocesspy title="Permanent link">&para;</a></h2> <p>由于不同网络模型的前处理方式不尽相同，为了能够在转换网络时尽可能减小精度的丢失，应该使用与训练相同的图片前处理方式，每种前处理方式需独立编写python文件。 编写图片前处理文件时，编写图片处理函数（函数名称不限），返回np.array 格式的图片数据，函数必须包含2个参数：</p> <ul> <li> <p>图片路径</p> </li> <li> <p>归一化标记（norm=True）</p> </li> </ul> <p>归一化标记是为了区分网络模型是否是浮点模型。因为在浮点网络模型阶段，图片的归一化需要在送进网络前处理好。但是定点网络模型和离线网络模型已经包含了input_config.ini文件的设置信息，能够将图片数据自行做归一化处理，因此送进网络模型的数据不需要做归一化，这与在SigmaStar硬件上处理方式相同。</p> <p>根据input_config.ini文件中training_input_formats 和 input_formats设置的不同，下面举例前处理文件的写法。</p> <h3 id=11-training_input_formatsinput_formatsrgbbgr>1.1. training_input_formats和input_formats均为RGB或者均为BGR<a class=headerlink href=#11-training_input_formatsinput_formatsrgbbgr title="Permanent link">&para;</a></h3> <p>PC上转换模型和simulator模型示例的preprocess.py如下。<br> 如果在每个channel上有不同的std值，需要在对应channel上除以std，如 std= [57.375, 57.12, 58.395]。 同时，input_config.ini文件中std_value每个通道对应的std_value值，以英文冒号( : )分隔，顺序为RGB。 </p> <table> <thead> <tr> <th align=left>training_input_formats</th> <th>input_formats</th> <th align=right>板上运行时数据对齐方式</th> </tr> </thead> <tbody> <tr> <td align=left>RGB/BGR</td> <td>RGB/BGR</td> <td align=right>不用对齐</td> </tr> <tr> <td align=left>RGB/BGR</td> <td>RGBA/BGRA</td> <td align=right>W = ALIGN_UP(W * 4, <code>xrgb_h_pitch_alignment</code>) / 4</td> </tr> </tbody> </table> <div class=highlight><pre><span></span><code><span class=kn>import</span> <span class=nn>cv2</span>
<span class=kn>import</span> <span class=nn>numpy</span> <span class=k>as</span> <span class=nn>np</span>

<span class=k>def</span> <span class=nf>get_image</span><span class=p>(</span><span class=n>img_path</span><span class=p>,</span> <span class=n>resizeH</span><span class=o>=</span><span class=mi>224</span><span class=p>,</span> <span class=n>resizeW</span><span class=o>=</span><span class=mi>224</span><span class=p>,</span> <span class=n>norm</span><span class=o>=</span><span class=kc>True</span><span class=p>,</span> <span class=n>meanB</span><span class=o>=</span><span class=mf>103.53</span><span class=p>,</span> <span class=n>meanG</span><span class=o>=</span><span class=mf>116.28</span><span class=p>,</span> <span class=n>meanR</span><span class=o>=</span><span class=mf>123.68</span><span class=p>,</span> <span class=n>std</span><span class=o>=</span><span class=mf>57.375</span><span class=p>,</span> <span class=n>rgb</span><span class=o>=</span><span class=kc>False</span><span class=p>,</span> <span class=n>nchw</span><span class=o>=</span><span class=kc>False</span><span class=p>):</span>
    <span class=n>img</span> <span class=o>=</span> <span class=n>cv2</span><span class=o>.</span><span class=n>imread</span><span class=p>(</span><span class=n>img_path</span><span class=p>)</span>
    <span class=k>if</span> <span class=n>img</span> <span class=ow>is</span> <span class=kc>None</span><span class=p>:</span>
        <span class=k>raise</span> <span class=ne>FileNotFoundError</span><span class=p>(</span><span class=s1>&#39;No such image: </span><span class=si>{}</span><span class=s1>&#39;</span><span class=o>.</span><span class=n>format</span><span class=p>(</span><span class=n>img_path</span><span class=p>))</span>

    <span class=n>img_norm</span> <span class=o>=</span> <span class=n>cv2</span><span class=o>.</span><span class=n>resize</span><span class=p>(</span><span class=n>img</span><span class=p>,</span> <span class=p>(</span><span class=n>resizeW</span><span class=p>,</span> <span class=n>resizeH</span><span class=p>),</span> <span class=n>interpolation</span><span class=o>=</span><span class=n>cv2</span><span class=o>.</span><span class=n>INTER_LINEAR</span><span class=p>)</span>

    <span class=k>if</span> <span class=n>norm</span><span class=p>:</span>
        <span class=n>img_norm</span> <span class=o>=</span> <span class=p>(</span><span class=n>img_norm</span> <span class=o>-</span> <span class=p>[</span><span class=n>meanB</span><span class=p>,</span> <span class=n>meanG</span><span class=p>,</span> <span class=n>meanR</span><span class=p>])</span> <span class=o>/</span> <span class=n>std</span>
        <span class=n>img_norm</span> <span class=o>=</span> <span class=n>img_norm</span><span class=o>.</span><span class=n>astype</span><span class=p>(</span><span class=s1>&#39;float32&#39;</span><span class=p>)</span>
    <span class=k>else</span><span class=p>:</span>
        <span class=n>img_norm</span> <span class=o>=</span> <span class=n>np</span><span class=o>.</span><span class=n>round</span><span class=p>(</span><span class=n>img_norm</span><span class=p>)</span><span class=o>.</span><span class=n>astype</span><span class=p>(</span><span class=s1>&#39;uint8&#39;</span><span class=p>)</span>

    <span class=k>if</span> <span class=n>rgb</span><span class=p>:</span>
        <span class=n>img_norm</span> <span class=o>=</span> <span class=n>cv2</span><span class=o>.</span><span class=n>cvtColor</span><span class=p>(</span><span class=n>img_norm</span><span class=p>,</span> <span class=n>cv2</span><span class=o>.</span><span class=n>COLOR_BGR2RGB</span><span class=p>)</span>

    <span class=k>if</span> <span class=n>nchw</span><span class=p>:</span>
        <span class=n>img_norm</span> <span class=o>=</span> <span class=n>np</span><span class=o>.</span><span class=n>transpose</span><span class=p>(</span><span class=n>img_norm</span><span class=p>,</span> <span class=n>axes</span><span class=o>=</span><span class=p>(</span><span class=mi>2</span><span class=p>,</span> <span class=mi>0</span><span class=p>,</span> <span class=mi>1</span><span class=p>))</span>

    <span class=k>return</span> <span class=n>np</span><span class=o>.</span><span class=n>expand_dims</span><span class=p>(</span><span class=n>img_norm</span><span class=p>,</span> <span class=mi>0</span><span class=p>)</span>

<span class=k>def</span> <span class=nf>image_preprocess</span><span class=p>(</span><span class=n>img_path</span><span class=p>,</span> <span class=n>norm</span><span class=o>=</span><span class=kc>True</span><span class=p>):</span>
    <span class=k>return</span> <span class=n>get_image</span><span class=p>(</span><span class=n>img_path</span><span class=p>,</span> <span class=n>norm</span><span class=o>=</span><span class=n>norm</span><span class=p>)</span>
</code></pre></div> <h3 id=12-training_input_formatsrgbinput_formatsgray>1.2. training_input_formats为RGB和input_formats为GRAY<a class=headerlink href=#12-training_input_formatsrgbinput_formatsgray title="Permanent link">&para;</a></h3> <p>PC上转换模型和simulator模型示例的preprocess.py如下。<br> 灰度模型，指输入是单通道图片的模型，即输入C维度上为1的模型。input_config.ini灰度图片请按如下方式配置：training_input_formats=RGB;input_formats=GRAY;</p> <table> <thead> <tr> <th align=left>training_input_formats</th> <th>input_formats</th> <th align=right>板上运行时数据对齐方式</th> </tr> </thead> <tbody> <tr> <td align=left>RGB/BGR</td> <td>GRAY</td> <td align=right>H = ALIGN_UP(H, <code>yuv420_v_pitch_alignment</code>)</td> </tr> <tr> <td align=left></td> <td></td> <td align=right>W = ALIGN_UP(W, <code>yuv420_h_pitch_alignment</code>)</td> </tr> </tbody> </table> <div class=highlight><pre><span></span><code><span class=kn>import</span> <span class=nn>cv2</span>
<span class=kn>import</span> <span class=nn>numpy</span> <span class=k>as</span> <span class=nn>np</span>

<span class=k>def</span> <span class=nf>get_image</span><span class=p>(</span><span class=n>img_path</span><span class=p>,</span> <span class=n>resizeH</span><span class=o>=</span><span class=mi>28</span><span class=p>,</span> <span class=n>resizeW</span><span class=o>=</span><span class=mi>28</span><span class=p>,</span> <span class=n>norm</span><span class=o>=</span><span class=kc>True</span><span class=p>,</span> <span class=n>meanR</span><span class=o>=</span><span class=mf>33.318</span><span class=p>,</span> <span class=n>std</span><span class=o>=</span><span class=mf>1.0</span><span class=p>,</span> <span class=n>rgb</span><span class=o>=</span><span class=kc>False</span><span class=p>,</span> <span class=n>nchw</span><span class=o>=</span><span class=kc>False</span><span class=p>):</span>
    <span class=n>img</span> <span class=o>=</span> <span class=n>cv2</span><span class=o>.</span><span class=n>imread</span><span class=p>(</span><span class=n>img_path</span><span class=p>,</span> <span class=n>flags</span><span class=o>=-</span><span class=mi>1</span><span class=p>)</span>
    <span class=k>if</span> <span class=n>img</span> <span class=ow>is</span> <span class=kc>None</span><span class=p>:</span>
        <span class=k>raise</span> <span class=ne>FileNotFoundError</span><span class=p>(</span><span class=s1>&#39;No such image: </span><span class=si>{}</span><span class=s1>&#39;</span><span class=o>.</span><span class=n>format</span><span class=p>(</span><span class=n>img_path</span><span class=p>))</span>
    <span class=k>try</span><span class=p>:</span>
        <span class=n>img_dim</span> <span class=o>=</span> <span class=n>img</span><span class=o>.</span><span class=n>shape</span><span class=p>[</span><span class=mi>2</span><span class=p>]</span>
    <span class=k>except</span> <span class=ne>IndexError</span><span class=p>:</span>
        <span class=n>img_dim</span> <span class=o>=</span> <span class=mi>1</span>
    <span class=k>if</span> <span class=n>img_dim</span> <span class=o>==</span> <span class=mi>3</span><span class=p>:</span>
        <span class=n>img</span> <span class=o>=</span> <span class=n>cv2</span><span class=o>.</span><span class=n>cvtColor</span><span class=p>(</span><span class=n>img</span><span class=p>,</span> <span class=n>cv2</span><span class=o>.</span><span class=n>COLOR_BGR2GRAY</span><span class=p>)</span>
    <span class=k>elif</span> <span class=n>img_dim</span> <span class=o>==</span> <span class=mi>4</span><span class=p>:</span>
        <span class=n>img</span> <span class=o>=</span> <span class=n>cv2</span><span class=o>.</span><span class=n>cvtColor</span><span class=p>(</span><span class=n>img</span><span class=p>,</span> <span class=n>cv2</span><span class=o>.</span><span class=n>COLOR_BGRA2GRAY</span><span class=p>)</span>
    <span class=n>img_norm</span> <span class=o>=</span> <span class=n>cv2</span><span class=o>.</span><span class=n>resize</span><span class=p>(</span><span class=n>img</span><span class=p>,</span> <span class=p>(</span><span class=n>resizeW</span><span class=p>,</span> <span class=n>resizeH</span><span class=p>),</span> <span class=n>interpolation</span><span class=o>=</span><span class=n>cv2</span><span class=o>.</span><span class=n>INTER_LINEAR</span><span class=p>)</span>

    <span class=k>if</span> <span class=n>norm</span><span class=p>:</span>
        <span class=n>img_norm</span> <span class=o>=</span> <span class=p>(</span><span class=n>img_norm</span> <span class=o>-</span> <span class=n>meanR</span><span class=p>)</span> <span class=o>/</span> <span class=n>std</span>
        <span class=n>img_norm</span> <span class=o>=</span> <span class=n>np</span><span class=o>.</span><span class=n>expand_dims</span><span class=p>(</span><span class=n>img_norm</span><span class=p>,</span> <span class=n>axis</span><span class=o>=</span><span class=mi>2</span><span class=p>)</span>
        <span class=n>dummy</span> <span class=o>=</span> <span class=n>np</span><span class=o>.</span><span class=n>zeros</span><span class=p>((</span><span class=mi>28</span><span class=p>,</span><span class=mi>28</span><span class=p>,</span><span class=mi>2</span><span class=p>))</span>
        <span class=n>img_norm</span> <span class=o>=</span> <span class=n>np</span><span class=o>.</span><span class=n>concatenate</span><span class=p>((</span><span class=n>img_norm</span><span class=p>,</span><span class=n>dummy</span><span class=p>),</span><span class=n>axis</span><span class=o>=</span><span class=mi>2</span><span class=p>)</span>
        <span class=n>img_norm</span> <span class=o>=</span> <span class=n>img_norm</span><span class=o>.</span><span class=n>astype</span><span class=p>(</span><span class=s1>&#39;float32&#39;</span><span class=p>)</span>
    <span class=k>else</span><span class=p>:</span>
        <span class=n>img_norm</span> <span class=o>=</span> <span class=n>np</span><span class=o>.</span><span class=n>expand_dims</span><span class=p>(</span><span class=n>img_norm</span><span class=p>,</span> <span class=mi>2</span><span class=p>)</span>
        <span class=n>img_norm</span> <span class=o>=</span> <span class=n>np</span><span class=o>.</span><span class=n>round</span><span class=p>(</span><span class=n>img_norm</span><span class=p>)</span><span class=o>.</span><span class=n>astype</span><span class=p>(</span><span class=s1>&#39;uint8&#39;</span><span class=p>)</span>

    <span class=k>if</span> <span class=n>rgb</span><span class=p>:</span>
        <span class=n>img_norm</span> <span class=o>=</span> <span class=n>cv2</span><span class=o>.</span><span class=n>cvtColor</span><span class=p>(</span><span class=n>img_norm</span><span class=p>,</span> <span class=n>cv2</span><span class=o>.</span><span class=n>COLOR_BGR2RGB</span><span class=p>)</span>

    <span class=k>if</span> <span class=n>nchw</span><span class=p>:</span>
        <span class=c1># NCHW</span>
        <span class=n>img_norm</span> <span class=o>=</span> <span class=n>np</span><span class=o>.</span><span class=n>transpose</span><span class=p>(</span><span class=n>img_norm</span><span class=p>,</span> <span class=n>axes</span><span class=o>=</span><span class=p>(</span><span class=mi>2</span><span class=p>,</span> <span class=mi>0</span><span class=p>,</span> <span class=mi>1</span><span class=p>))</span>

    <span class=k>return</span> <span class=n>np</span><span class=o>.</span><span class=n>expand_dims</span><span class=p>(</span><span class=n>img_norm</span><span class=p>,</span> <span class=mi>0</span><span class=p>)</span>

<span class=k>def</span> <span class=nf>image_preprocess</span><span class=p>(</span><span class=n>img_path</span><span class=p>,</span> <span class=n>norm</span><span class=o>=</span><span class=kc>True</span><span class=p>):</span>
    <span class=k>return</span> <span class=n>get_image</span><span class=p>(</span><span class=n>img_path</span><span class=p>,</span> <span class=n>norm</span><span class=o>=</span><span class=n>norm</span><span class=p>)</span>
</code></pre></div> <h3 id=13-training_input_formatsrgbbgrinput_formatsyuv_nv12>1.3. training_input_formats为RGB或BGR和input_formats为YUV_NV12<a class=headerlink href=#13-training_input_formatsrgbbgrinput_formatsyuv_nv12 title="Permanent link">&para;</a></h3> <p>PC上转换模型和simulator模型示例的preprocess.py如下。<br></p> <table> <thead> <tr> <th align=left>training_input_formats</th> <th>input_formats</th> <th align=right>板上运行时数据对齐方式</th> </tr> </thead> <tbody> <tr> <td align=left>RGB/BGR</td> <td>YUV_NV12</td> <td align=right>H = ALIGN_UP(H, <code>yuv420_v_pitch_alignment</code>)</td> </tr> <tr> <td align=left></td> <td></td> <td align=right>W = ALIGN_UP(W, <code>yuv420_h_pitch_alignment</code>)</td> </tr> </tbody> </table> <div class=highlight><pre><span></span><code><span class=kn>import</span> <span class=nn>cv2</span>
<span class=kn>import</span> <span class=nn>numpy</span> <span class=k>as</span> <span class=nn>np</span>

<span class=k>def</span> <span class=nf>get_image</span><span class=p>(</span><span class=n>img_path</span><span class=p>,</span> <span class=n>resizeH</span><span class=o>=</span><span class=mi>224</span><span class=p>,</span> <span class=n>resizeW</span><span class=o>=</span><span class=mi>224</span><span class=p>,</span> <span class=n>norm</span><span class=o>=</span><span class=kc>True</span><span class=p>,</span> <span class=n>meanB</span><span class=o>=</span><span class=mf>127.5</span><span class=p>,</span> <span class=n>meanG</span><span class=o>=</span><span class=mf>127.5</span><span class=p>,</span> <span class=n>meanR</span><span class=o>=</span><span class=mf>127.5</span><span class=p>,</span> <span class=n>std</span><span class=o>=</span><span class=mf>128.0</span><span class=p>,</span> <span class=n>rgb</span><span class=o>=</span><span class=kc>False</span><span class=p>,</span> <span class=n>nchw</span><span class=o>=</span><span class=kc>False</span><span class=p>):</span>
    <span class=n>img</span> <span class=o>=</span> <span class=n>cv2</span><span class=o>.</span><span class=n>imread</span><span class=p>(</span><span class=n>img_path</span><span class=p>)</span>
    <span class=k>if</span> <span class=n>img</span> <span class=ow>is</span> <span class=kc>None</span><span class=p>:</span>
        <span class=k>raise</span> <span class=ne>FileNotFoundError</span><span class=p>(</span><span class=s1>&#39;No such image: </span><span class=si>{}</span><span class=s1>&#39;</span><span class=o>.</span><span class=n>format</span><span class=p>(</span><span class=n>img_path</span><span class=p>))</span>

    <span class=n>img_norm</span> <span class=o>=</span> <span class=n>cv2</span><span class=o>.</span><span class=n>resize</span><span class=p>(</span><span class=n>img</span><span class=p>,</span> <span class=p>(</span><span class=n>resizeW</span><span class=p>,</span> <span class=n>resizeH</span><span class=p>),</span> <span class=n>interpolation</span><span class=o>=</span><span class=n>cv2</span><span class=o>.</span><span class=n>INTER_LINEAR</span><span class=p>)</span>

    <span class=k>if</span> <span class=n>norm</span><span class=p>:</span>
        <span class=n>img_norm</span> <span class=o>=</span> <span class=p>(</span><span class=n>img_norm</span> <span class=o>-</span> <span class=p>[</span><span class=n>meanB</span><span class=p>,</span> <span class=n>meanG</span><span class=p>,</span> <span class=n>meanR</span><span class=p>])</span> <span class=o>/</span> <span class=n>std</span>
        <span class=n>img_norm</span> <span class=o>=</span> <span class=n>img_norm</span><span class=o>.</span><span class=n>astype</span><span class=p>(</span><span class=s1>&#39;float32&#39;</span><span class=p>)</span>
    <span class=k>else</span><span class=p>:</span>
        <span class=n>img_norm</span> <span class=o>=</span> <span class=n>np</span><span class=o>.</span><span class=n>round</span><span class=p>(</span><span class=n>img_norm</span><span class=p>)</span><span class=o>.</span><span class=n>astype</span><span class=p>(</span><span class=s1>&#39;uint8&#39;</span><span class=p>)</span>

    <span class=k>if</span> <span class=n>rgb</span><span class=p>:</span>
        <span class=n>img_norm</span> <span class=o>=</span> <span class=n>cv2</span><span class=o>.</span><span class=n>cvtColor</span><span class=p>(</span><span class=n>img_norm</span><span class=p>,</span> <span class=n>cv2</span><span class=o>.</span><span class=n>COLOR_BGR2RGB</span><span class=p>)</span>

    <span class=k>if</span> <span class=n>nchw</span><span class=p>:</span>
        <span class=n>img_norm</span> <span class=o>=</span> <span class=n>np</span><span class=o>.</span><span class=n>transpose</span><span class=p>(</span><span class=n>img_norm</span><span class=p>,</span> <span class=n>axes</span><span class=o>=</span><span class=p>(</span><span class=mi>2</span><span class=p>,</span> <span class=mi>0</span><span class=p>,</span> <span class=mi>1</span><span class=p>))</span>

    <span class=k>return</span> <span class=n>np</span><span class=o>.</span><span class=n>expand_dims</span><span class=p>(</span><span class=n>img_norm</span><span class=p>,</span> <span class=mi>0</span><span class=p>)</span>

<span class=k>def</span> <span class=nf>image_preprocess</span><span class=p>(</span><span class=n>img_path</span><span class=p>,</span> <span class=n>norm</span><span class=o>=</span><span class=kc>True</span><span class=p>):</span>
    <span class=k>return</span> <span class=n>get_image</span><span class=p>(</span><span class=n>img_path</span><span class=p>,</span> <span class=n>norm</span><span class=o>=</span><span class=n>norm</span><span class=p>)</span>
</code></pre></div> <h3 id=14-training_input_formatsinput_formatsrawdata_s16_nhwcrawdata_f32_nhwc>1.4. training_input_formats和input_formats均为RAWDATA_S16_NHWC或者均为RAWDATA_F32_NHWC<a class=headerlink href=#14-training_input_formatsinput_formatsrawdata_s16_nhwcrawdata_f32_nhwc title="Permanent link">&para;</a></h3> <p>PC上转换模型和simulator模型示例的preprocess.py如下。<br> <strong>在PC上</strong> <br> 当input_config.ini中的training_input_formats和input_formats都填写RAWDATA_S16_NHWC时， 此时input_config.ini中mean_red、mean_green、mean_blue和std_value不会再在定点网络模型中生效， 所有前处理过程都将在输入模型前完成。mean_red、mean_green、mean_blue和std_value不要填写。 浮点模型运行时，使用方法与运行图片输入的模型相同。使用simulator.py运行定点模型时，前处理方法应与浮点前处理模型保持一致， 仍然输入norm=True时的方法，所以RAWDATA_S16_NHWC的网络前处理Python文件编写时norm为True和False的实现都应按照norm为True时编写。 simulator.py会读入原浮点数据，进行反量化和对齐排列后输入给定点模型。<br> 当input_config.ini中的training_input_formats和input_formats都填写RAWDATA_F32_NHWC时，simulator.py会读入原浮点数据，由模型内部的align算子自动对齐数据，不需要人为操作。<br> <strong>在开发板上</strong> <br> 在板上运行RAWDATA_S16_NHWC的网络时，也需要完成输入数据的定点化和对齐排列过程。定点化和对齐排列过程需要自行完成。<br> 在板上运行RAWDATA_F32_NHWC的网络时，不需要输入数据的定点化和对齐排列过程。由模型内部的align算子自动对齐数据。具体详见。<a href=Special_Model_Conversion.html#92-rawdata_f32_nhwc>9.2 RAWDATA_F32_NHWC模型转换要点</a></p> <table> <thead> <tr> <th align=left>training_input_formats</th> <th>input_formats</th> <th align=right>板上运行时数据对齐方式</th> </tr> </thead> <tbody> <tr> <td align=left>RAWDATA_F32_NHWC</td> <td>RAWDATA_F32_NHWC</td> <td align=right>不用对齐</td> </tr> <tr> <td align=left>RAWDATA_S16_NHWC</td> <td>RAWDATA_S16_NHWC</td> <td align=right>最后1个维度 = ALIGN_UP(最后1个维度, <code>8</code>)</td> </tr> </tbody> </table> <div class=highlight><pre><span></span><code><span class=kn>import</span> <span class=nn>cv2</span>
<span class=kn>import</span> <span class=nn>numpy</span> <span class=k>as</span> <span class=nn>np</span>

<span class=k>def</span> <span class=nf>get_image</span><span class=p>(</span><span class=n>img_path</span><span class=p>,</span> <span class=n>resizeH</span><span class=o>=</span><span class=mi>224</span><span class=p>,</span> <span class=n>resizeW</span><span class=o>=</span><span class=mi>224</span><span class=p>,</span> <span class=n>norm</span><span class=o>=</span><span class=kc>True</span><span class=p>,</span> <span class=n>rgb</span><span class=o>=</span><span class=kc>False</span><span class=p>,</span> <span class=n>nchw</span><span class=o>=</span><span class=kc>False</span><span class=p>):</span>
    <span class=n>img</span> <span class=o>=</span> <span class=n>cv2</span><span class=o>.</span><span class=n>imread</span><span class=p>(</span><span class=n>img_path</span><span class=p>)</span>
    <span class=k>if</span> <span class=n>img</span> <span class=ow>is</span> <span class=kc>None</span><span class=p>:</span>
        <span class=k>raise</span> <span class=ne>FileNotFoundError</span><span class=p>(</span><span class=s1>&#39;No such image: </span><span class=si>{}</span><span class=s1>&#39;</span><span class=o>.</span><span class=n>format</span><span class=p>(</span><span class=n>img_path</span><span class=p>))</span>

    <span class=n>img_norm</span> <span class=o>=</span> <span class=n>cv2</span><span class=o>.</span><span class=n>resize</span><span class=p>(</span><span class=n>img</span><span class=p>,</span> <span class=p>(</span><span class=n>resizeW</span><span class=p>,</span> <span class=n>resizeH</span><span class=p>),</span> <span class=n>interpolation</span><span class=o>=</span><span class=n>cv2</span><span class=o>.</span><span class=n>INTER_LINEAR</span><span class=p>)</span>
    <span class=n>img_norm</span> <span class=o>=</span> <span class=n>img_norm</span><span class=o>.</span><span class=n>astype</span><span class=p>(</span><span class=s1>&#39;float32&#39;</span><span class=p>)</span>

    <span class=k>if</span> <span class=n>rgb</span><span class=p>:</span>
        <span class=n>img_norm</span> <span class=o>=</span> <span class=n>cv2</span><span class=o>.</span><span class=n>cvtColor</span><span class=p>(</span><span class=n>img_norm</span><span class=p>,</span> <span class=n>cv2</span><span class=o>.</span><span class=n>COLOR_BGR2RGB</span><span class=p>)</span>

    <span class=k>if</span> <span class=n>nchw</span><span class=p>:</span>
        <span class=n>img_norm</span> <span class=o>=</span> <span class=n>np</span><span class=o>.</span><span class=n>transpose</span><span class=p>(</span><span class=n>img_norm</span><span class=p>,</span> <span class=n>axes</span><span class=o>=</span><span class=p>(</span><span class=mi>2</span><span class=p>,</span> <span class=mi>0</span><span class=p>,</span> <span class=mi>1</span><span class=p>))</span>
    <span class=k>return</span> <span class=n>np</span><span class=o>.</span><span class=n>expand_dims</span><span class=p>(</span><span class=n>img_norm</span><span class=p>,</span> <span class=mi>0</span><span class=p>)</span>

<span class=k>def</span> <span class=nf>image_preprocess</span><span class=p>(</span><span class=n>img_path</span><span class=p>,</span> <span class=n>norm</span><span class=o>=</span><span class=kc>True</span><span class=p>):</span>
    <span class=k>return</span> <span class=n>get_image</span><span class=p>(</span><span class=n>img_path</span><span class=p>,</span> <span class=n>norm</span><span class=o>=</span><span class=n>norm</span><span class=p>)</span>
</code></pre></div> <h2 id=2>2. 模型性能优化规则<a class=headerlink href=#2 title="Permanent link">&para;</a></h2> <p><strong>对于卷积的性能优化</strong> <br> 1. kernel size 3x3 最好，特别对于首层。<br> 2. kernel size 1x1 的时候，input tensor 最内维度shape值对齐到16 最好。<br></p> <p><strong>对于DMA算子</strong> <br> 1. concatenation算子比pack算子性能更好。<br> 2. split比slice算子性能更好。<br> 3. 尽量减少在最内维上做transpose。<br> 4. 单口elementwise 算子的const 操作数最好是右操作数，即input[1]。<br></p> <p><strong>综合部分</strong> <br> 1. Tensor的维度为4最好。<br> 2. Tensor最内维的shape值对齐到32最好。<br> 3. Softmax最好只对最内维度操作。<br> 4. ReduceMax、ReduceMin、ReduceSum最好坍塌的纬度是相邻的。<br></p> <p><strong>合并规则</strong> <br> 1. Pad + Conv2D/DepthwiseConv，pad会被合并到卷积中，pad几乎不占用时间。<br> 2. Conv2D/DepthwiseConv + 单口Mul + 单口Add，Mul和Add算子会和卷积一起运算。我们会把BatchNorm转换成Mul+Add,所以Conv2D/DepthwiseConv + BatchNorm可以合并。<br> 3. Pad + avgPooling 可以合并。<br> 4. 连续的transpose会自动合并为一个。<br> 5. 所有reshape算子都会被跳过。<br> 6. 连续的单口的相同elementwise算子会合并。<br> 7. 单独的单口mul和单口add，会变成MultAdd单一的算子。<br> <code>注意：这里单口意思为一个数据是constant的情况</code></p> </article> </div> </div> </main> <footer class=md-footer> <nav class="md-footer__inner md-grid" aria-label=Footer> <a href=../Common/Running_Offline_Network_Model_On_Development_Board.html class="md-footer__link md-footer__link--prev" rel=prev> <div class="md-footer__button md-icon"> <svg xmlns=http://www.w3.org/2000/svg viewbox="0 0 24 24"><path d="M20 11v2H8l5.5 5.5-1.42 1.42L4.16 12l7.92-7.92L13.5 5.5 8 11h12z"/></svg> </div> <div class=md-footer__title> <div class=md-ellipsis> <span class=md-footer__direction> Previous </span> 11. 在开发板上运行离线网络模型 </div> </div> </a> <a href=../../FAQ/Common/Env_Setting.html class="md-footer__link md-footer__link--next" rel=next> <div class=md-footer__title> <div class=md-ellipsis> <span class=md-footer__direction> Next </span> 环境设置问题 </div> </div> <div class="md-footer__button md-icon"> <svg xmlns=http://www.w3.org/2000/svg viewbox="0 0 24 24"><path d="M4 11v2h12l-5.5 5.5 1.42 1.42L19.84 12l-7.92-7.92L10.5 5.5 16 11H4z"/></svg> </div> </a> </nav> <div class="md-footer-meta md-typeset"> <div class="md-footer-meta__inner md-grid"> <div class=md-footer-copyright> <div class=md-footer-copyright__highlight> Copyright&copy; 2021 SigmaStar Technology. All rights reserved. Security Level: Confidential A. </div> Made with <a href=https://squidfunk.github.io/mkdocs-material/ target=_blank rel=noopener> Material for MkDocs </a> </div> </div> </div> </footer> </div> <div class=md-dialog data-md-component=dialog> <div class="md-dialog__inner md-typeset"></div> </div> <script id=__config type=application/json>{"base": "../../..", "features": [], "translations": {"clipboard.copy": "Copy to clipboard", "clipboard.copied": "Copied to clipboard", "search.config.lang": "en", "search.config.pipeline": "trimmer, stopWordFilter", "search.config.separator": "[\\s\\-]+", "search.placeholder": "Search", "search.result.placeholder": "Type to start searching", "search.result.none": "No matching documents", "search.result.one": "1 matching document", "search.result.other": "# matching documents", "search.result.more.one": "1 more on this page", "search.result.more.other": "# more on this page", "search.result.term.missing": "Missing"}, "search": "../../../assets/javascripts/workers/search.fb4a9340.min.js", "version": null}</script> <script src=../../../assets/javascripts/bundle.a1c7c35e.min.js></script> <script src=../../../search/search_index.js></script> <script src=../../../javascripts/extra.js></script> <script src="https://cdnjs.cloudflare.com/ajax/libs/mathjax/2.7.0/MathJax.js?config=TeX-MML-AM_CHTML"></script> <script src=https://cdnjs.cloudflare.com/ajax/libs/raphael/2.2.7/raphael.min.js></script> <script src=https://cdnjs.cloudflare.com/ajax/libs/underscore.js/1.8.3/underscore-min.js></script> <script src=https://cdnjs.cloudflare.com/ajax/libs/js-sequence-diagrams/1.0.6/sequence-diagram-min.js></script> <script src=https://cdnjs.cloudflare.com/ajax/libs/flowchart/1.6.5/flowchart.min.js></script> <script src=https://unpkg.com/freezeframe/dist/freezeframe.min.js></script> <script src=https://unpkg.com/mermaid@7.1.0/dist/mermaid.min.js></script> <script src=../../../javascripts/umlconvert.js></script> </body> </html>