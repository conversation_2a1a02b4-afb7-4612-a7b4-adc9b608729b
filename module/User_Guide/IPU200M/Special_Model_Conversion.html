<!doctype html><html lang=en class=no-js> <head><meta charset=utf-8><meta name=viewport content="width=device-width,initial-scale=1"><meta name=description content="Personal Documentation System Template"><meta name=author content="<PERSON> Cho<PERSON>"><link rel=icon href=../../../images/favicon.ico><meta name=generator content="mkdocs-1.1.2, mkdocs-material-7.0.6"><title>9. 特殊模型转换要点 - IPU SDK</title><link rel=stylesheet href=../../../assets/stylesheets/main.2c0c5eaf.min.css><link rel=stylesheet href=../../../assets/stylesheets/palette.7fa14f5b.min.css><meta name=theme-color content=#009485><link rel=preconnect href=https://fonts.gstatic.com crossorigin><link rel=stylesheet href="https://fonts.googleapis.com/css?family=Roboto:300,400,400i,700%7CRoboto+Mono&display=fallback"><style>:root{--md-text-font-family:"Roboto";--md-code-font-family:"Roboto Mono"}</style><link rel=stylesheet href=../../../stylesheets/extra.css></head> <body dir=ltr data-md-color-scheme data-md-color-primary=teal data-md-color-accent=teal> <input class=md-toggle data-md-toggle=drawer type=checkbox id=__drawer autocomplete=off> <input class=md-toggle data-md-toggle=search type=checkbox id=__search autocomplete=off> <label class=md-overlay for=__drawer></label> <div data-md-component=skip> <a href=#91 class=md-skip> Skip to content </a> </div> <div data-md-component=announce> </div> <header class=md-header data-md-component=header> <nav class="md-header__inner md-grid" aria-label=Header> <a href=../../.. title="IPU SDK" class="md-header__button md-logo" aria-label="IPU SDK" data-md-component=logo> <svg xmlns=http://www.w3.org/2000/svg viewbox="0 0 24 24"><path d="M10 20v-6h4v6h5v-8h3L12 3 2 12h3v8h5z"/></svg> </a> <label class="md-header__button md-icon" for=__drawer> <svg xmlns=http://www.w3.org/2000/svg viewbox="0 0 24 24"><path d="M3 6h18v2H3V6m0 5h18v2H3v-2m0 5h18v2H3v-2z"/></svg> </label> <div class=md-header__title data-md-component=header-title> <div class=md-header__ellipsis> <div class=md-header__topic> <span class=md-ellipsis> IPU SDK </span> </div> <div class=md-header__topic data-md-component=header-topic> <span class=md-ellipsis> 9. 特殊模型转换要点 </span> </div> </div> </div> <div class=md-header__options> </div> <label class="md-header__button md-icon" for=__search> <svg xmlns=http://www.w3.org/2000/svg viewbox="0 0 24 24"><path d="M9.5 3A6.5 6.5 0 0 1 16 9.5c0 1.61-.59 3.09-1.56 4.23l.27.27h.79l5 5-1.5 1.5-5-5v-.79l-.27-.27A6.516 6.516 0 0 1 9.5 16 6.5 6.5 0 0 1 3 9.5 6.5 6.5 0 0 1 9.5 3m0 2C7 5 5 7 5 9.5S7 14 9.5 14 14 12 14 9.5 12 5 9.5 5z"/></svg> </label> <div class=md-search data-md-component=search role=dialog> <label class=md-search__overlay for=__search></label> <div class=md-search__inner role=search> <form class=md-search__form name=search> <input type=text class=md-search__input name=query aria-label=Search placeholder=Search autocapitalize=off autocorrect=off autocomplete=off spellcheck=false data-md-component=search-query data-md-state=active required> <label class="md-search__icon md-icon" for=__search> <svg xmlns=http://www.w3.org/2000/svg viewbox="0 0 24 24"><path d="M9.5 3A6.5 6.5 0 0 1 16 9.5c0 1.61-.59 3.09-1.56 4.23l.27.27h.79l5 5-1.5 1.5-5-5v-.79l-.27-.27A6.516 6.516 0 0 1 9.5 16 6.5 6.5 0 0 1 3 9.5 6.5 6.5 0 0 1 9.5 3m0 2C7 5 5 7 5 9.5S7 14 9.5 14 14 12 14 9.5 12 5 9.5 5z"/></svg> <svg xmlns=http://www.w3.org/2000/svg viewbox="0 0 24 24"><path d="M20 11v2H8l5.5 5.5-1.42 1.42L4.16 12l7.92-7.92L13.5 5.5 8 11h12z"/></svg> </label> <button type=reset class="md-search__icon md-icon" aria-label=Clear tabindex=-1> <svg xmlns=http://www.w3.org/2000/svg viewbox="0 0 24 24"><path d="M19 6.41L17.59 5 12 10.59 6.41 5 5 6.41 10.59 12 5 17.59 6.41 19 12 13.41 17.59 19 19 17.59 13.41 12 19 6.41z"/></svg> </button> </form> <div class=md-search__output> <div class=md-search__scrollwrap data-md-scrollfix> <div class=md-search-result data-md-component=search-result> <div class=md-search-result__meta> Initializing search </div> <ol class=md-search-result__list></ol> </div> </div> </div> </div> </div> </nav> </header> <div class=md-container data-md-component=container> <main class=md-main data-md-component=main> <div class="md-main__inner md-grid"> <div class="md-sidebar md-sidebar--primary" data-md-component=sidebar data-md-type=navigation> <div class=md-sidebar__scrollwrap> <div class=md-sidebar__inner> <nav class="md-nav md-nav--primary" aria-label=Navigation data-md-level=0> <label class=md-nav__title for=__drawer> <a href=../../.. title="IPU SDK" class="md-nav__button md-logo" aria-label="IPU SDK" data-md-component=logo> <svg xmlns=http://www.w3.org/2000/svg viewbox="0 0 24 24"><path d="M10 20v-6h4v6h5v-8h3L12 3 2 12h3v8h5z"/></svg> </a> IPU SDK </label> <ul class=md-nav__list data-md-scrollfix> <li class=md-nav__item> <a href=../../../index.html class=md-nav__link> 主页 </a> </li> <li class="md-nav__item md-nav__item--nested"> <input class="md-nav__toggle md-toggle" data-md-toggle=__nav_2 type=checkbox id=__nav_2> <label class=md-nav__link for=__nav_2> SDK介绍 <span class="md-nav__icon md-icon"></span> </label> <nav class=md-nav aria-label=SDK介绍 data-md-level=1> <label class=md-nav__title for=__nav_2> <span class="md-nav__icon md-icon"></span> SDK介绍 </label> <ul class=md-nav__list data-md-scrollfix> <li class=md-nav__item> <a href=../../Introduction/Introduction.html class=md-nav__link> SDK框架介绍 </a> </li> <li class=md-nav__item> <a href=../../Introduction/Docker.html class=md-nav__link> Docker环境 </a> </li> </ul> </nav> </li> <li class="md-nav__item md-nav__item--active md-nav__item--nested"> <input class="md-nav__toggle md-toggle" data-md-toggle=__nav_3 type=checkbox id=__nav_3 checked> <label class=md-nav__link for=__nav_3> 用户手册 <span class="md-nav__icon md-icon"></span> </label> <nav class=md-nav aria-label=用户手册 data-md-level=1> <label class=md-nav__title for=__nav_3> <span class="md-nav__icon md-icon"></span> 用户手册 </label> <ul class=md-nav__list data-md-scrollfix> <li class=md-nav__item> <a href=../Common/Environment_Construction.html class=md-nav__link> 1. 快速开始 </a> </li> <li class=md-nav__item> <a href=Convert.html class=md-nav__link> 2. Convert Tool </a> </li> <li class=md-nav__item> <a href=../Common/Calibrate.html class=md-nav__link> 3. Calibrator </a> </li> <li class=md-nav__item> <a href=../Common/Compile.html class=md-nav__link> 4. Compiler </a> </li> <li class=md-nav__item> <a href=Simulate.html class=md-nav__link> 5. Simulator </a> </li> <li class=md-nav__item> <a href=../Common/DumpDebug_Tool.html class=md-nav__link> 6. DumpDebug Tool </a> </li> <li class=md-nav__item> <a href=../Common/SigmaStar_Post_Processing_Module.html class=md-nav__link> 7. SigmaStar后处理模块 </a> </li> <li class=md-nav__item> <a href=../Common/Adding_A_New_Layer.html class=md-nav__link> 8. 如何添加新的Layer </a> </li> <li class="md-nav__item md-nav__item--active"> <input class="md-nav__toggle md-toggle" data-md-toggle=toc type=checkbox id=__toc> <label class="md-nav__link md-nav__link--active" for=__toc> 9. 特殊模型转换要点 <span class="md-nav__icon md-icon"></span> </label> <a href=Special_Model_Conversion.html class="md-nav__link md-nav__link--active"> 9. 特殊模型转换要点 </a> <nav class="md-nav md-nav--secondary" aria-label="Table of contents"> <label class=md-nav__title for=__toc> <span class="md-nav__icon md-icon"></span> Table of contents </label> <ul class=md-nav__list data-md-component=toc data-md-scrollfix> <li class=md-nav__item> <a href=#91 class=md-nav__link> 9.1. 灰度模型转换要点 </a> <nav class=md-nav aria-label="9.1. 灰度模型转换要点"> <ul class=md-nav__list> <li class=md-nav__item> <a href=#911-input_config class=md-nav__link> 9.1.1. 灰度模型input_config配置信息要点 </a> </li> <li class=md-nav__item> <a href=#912 class=md-nav__link> 9.1.2. 灰度图片输入模型前处理方法 </a> </li> </ul> </nav> </li> <li class=md-nav__item> <a href=#92-rawdata_f32_nhwcrawdata_s16_nhwc class=md-nav__link> 9.2. RAWDATA_F32_NHWC与RAWDATA_S16_NHWC输入的模型 </a> <nav class=md-nav aria-label="9.2. RAWDATA_F32_NHWC与RAWDATA_S16_NHWC输入的模型"> <ul class=md-nav__list> <li class=md-nav__item> <a href=#9211-rawdata_f32_nhwc class=md-nav__link> 9.2.1.1 RAWDATA_F32_NHWC模型转换 </a> </li> <li class=md-nav__item> <a href=#9212-rawdata_f32_nhwc class=md-nav__link> 9.2.1.2 RAWDATA_F32_NHWC模型运行 </a> </li> <li class=md-nav__item> <a href=#9221-rawdata_s16_nhwc class=md-nav__link> ******* RAWDATA_S16_NHWC模型转换 </a> </li> <li class=md-nav__item> <a href=#9222-rawdata_s16_nhwc class=md-nav__link> 9.2.2.2 RAWDATA_S16_NHWC模型运行 </a> </li> </ul> </nav> </li> <li class=md-nav__item> <a href=#93 class=md-nav__link> 9.3. 分段网络转换要点 </a> <nav class=md-nav aria-label="9.3. 分段网络转换要点"> <ul class=md-nav__list> <li class=md-nav__item> <a href=#931 class=md-nav__link> 9.3.1 网络切分 </a> </li> <li class=md-nav__item> <a href=#932 class=md-nav__link> 9.3.2 转换网络 </a> </li> <li class=md-nav__item> <a href=#933 class=md-nav__link> 9.3.3 运行网络 </a> </li> </ul> </nav> </li> <li class=md-nav__item> <a href=#94 class=md-nav__link> 9.4. 多输入网络转换要点 </a> <nav class=md-nav aria-label="9.4. 多输入网络转换要点"> <ul class=md-nav__list> <li class=md-nav__item> <a href=#941 class=md-nav__link> 9.4.1 构建多输入网络 </a> </li> <li class=md-nav__item> <a href=#942 class=md-nav__link> 9.4.2 转换多输入网络 </a> </li> <li class=md-nav__item> <a href=#943-pc class=md-nav__link> 9.4.3 在PC上运行多输入网络 </a> </li> <li class=md-nav__item> <a href=#944 class=md-nav__link> 9.4.4 在板上运行多输入网络 </a> </li> </ul> </nav> </li> </ul> </nav> </li> <li class=md-nav__item> <a href=DLA_SDK_Support.html class=md-nav__link> 10. DLA SDK 支持 </a> </li> <li class=md-nav__item> <a href=../Common/Running_Offline_Network_Model_On_Development_Board.html class=md-nav__link> 11. 在开发板上运行离线网络模型 </a> </li> <li class=md-nav__item> <a href=Preprocess.py_and_Input_Config.ini_Support.html class=md-nav__link> 附录. 前处理和配置文件注意要点 </a> </li> </ul> </nav> </li> <li class="md-nav__item md-nav__item--nested"> <input class="md-nav__toggle md-toggle" data-md-toggle=__nav_4 type=checkbox id=__nav_4> <label class=md-nav__link for=__nav_4> FAQ <span class="md-nav__icon md-icon"></span> </label> <nav class=md-nav aria-label=FAQ data-md-level=1> <label class=md-nav__title for=__nav_4> <span class="md-nav__icon md-icon"></span> FAQ </label> <ul class=md-nav__list data-md-scrollfix> <li class=md-nav__item> <a href=../../FAQ/Common/Env_Setting.html class=md-nav__link> 环境设置问题 </a> </li> <li class=md-nav__item> <a href=../../FAQ/IPU200M/Model_Conversion.html class=md-nav__link> 模型转换问题 </a> </li> <li class=md-nav__item> <a href=../../FAQ/IPU200M/Development_Board.html class=md-nav__link> 板端使用问题 </a> </li> <li class=md-nav__item> <a href=../../FAQ/IPU200M/Other_Anomalies.html class=md-nav__link> 其他异常问题 </a> </li> </ul> </nav> </li> </ul> </nav> </div> </div> </div> <div class="md-sidebar md-sidebar--secondary" data-md-component=sidebar data-md-type=toc> <div class=md-sidebar__scrollwrap> <div class=md-sidebar__inner> <nav class="md-nav md-nav--secondary" aria-label="Table of contents"> <label class=md-nav__title for=__toc> <span class="md-nav__icon md-icon"></span> Table of contents </label> <ul class=md-nav__list data-md-component=toc data-md-scrollfix> <li class=md-nav__item> <a href=#91 class=md-nav__link> 9.1. 灰度模型转换要点 </a> <nav class=md-nav aria-label="9.1. 灰度模型转换要点"> <ul class=md-nav__list> <li class=md-nav__item> <a href=#911-input_config class=md-nav__link> 9.1.1. 灰度模型input_config配置信息要点 </a> </li> <li class=md-nav__item> <a href=#912 class=md-nav__link> 9.1.2. 灰度图片输入模型前处理方法 </a> </li> </ul> </nav> </li> <li class=md-nav__item> <a href=#92-rawdata_f32_nhwcrawdata_s16_nhwc class=md-nav__link> 9.2. RAWDATA_F32_NHWC与RAWDATA_S16_NHWC输入的模型 </a> <nav class=md-nav aria-label="9.2. RAWDATA_F32_NHWC与RAWDATA_S16_NHWC输入的模型"> <ul class=md-nav__list> <li class=md-nav__item> <a href=#9211-rawdata_f32_nhwc class=md-nav__link> 9.2.1.1 RAWDATA_F32_NHWC模型转换 </a> </li> <li class=md-nav__item> <a href=#9212-rawdata_f32_nhwc class=md-nav__link> 9.2.1.2 RAWDATA_F32_NHWC模型运行 </a> </li> <li class=md-nav__item> <a href=#9221-rawdata_s16_nhwc class=md-nav__link> ******* RAWDATA_S16_NHWC模型转换 </a> </li> <li class=md-nav__item> <a href=#9222-rawdata_s16_nhwc class=md-nav__link> 9.2.2.2 RAWDATA_S16_NHWC模型运行 </a> </li> </ul> </nav> </li> <li class=md-nav__item> <a href=#93 class=md-nav__link> 9.3. 分段网络转换要点 </a> <nav class=md-nav aria-label="9.3. 分段网络转换要点"> <ul class=md-nav__list> <li class=md-nav__item> <a href=#931 class=md-nav__link> 9.3.1 网络切分 </a> </li> <li class=md-nav__item> <a href=#932 class=md-nav__link> 9.3.2 转换网络 </a> </li> <li class=md-nav__item> <a href=#933 class=md-nav__link> 9.3.3 运行网络 </a> </li> </ul> </nav> </li> <li class=md-nav__item> <a href=#94 class=md-nav__link> 9.4. 多输入网络转换要点 </a> <nav class=md-nav aria-label="9.4. 多输入网络转换要点"> <ul class=md-nav__list> <li class=md-nav__item> <a href=#941 class=md-nav__link> 9.4.1 构建多输入网络 </a> </li> <li class=md-nav__item> <a href=#942 class=md-nav__link> 9.4.2 转换多输入网络 </a> </li> <li class=md-nav__item> <a href=#943-pc class=md-nav__link> 9.4.3 在PC上运行多输入网络 </a> </li> <li class=md-nav__item> <a href=#944 class=md-nav__link> 9.4.4 在板上运行多输入网络 </a> </li> </ul> </nav> </li> </ul> </nav> </div> </div> </div> <div class=md-content data-md-component=content> <article class="md-content__inner md-typeset"> <h1>9. 特殊模型转换要点</h1> <h2 id=91>9.1. 灰度模型转换要点<a class=headerlink href=#91 title="Permanent link">&para;</a></h2> <p>灰度模型，指输入是单通道图片的模型，即输入C维度上为1的模型。</p> <h3 id=911-input_config>9.1.1. 灰度模型input_config配置信息要点<a class=headerlink href=#911-input_config title="Permanent link">&para;</a></h3> <p>input_config.ini文件使用在2.2节input config配置信息设置有过介绍，对于灰度模型，要注意如下要点。</p> <p>input_config.ini文件中这两个配置必须写成</p> <div class=highlight><pre><span></span><code><span class=k>[INPUT_CONFIG]</span>
<span class=na>......</span>
<span class=na>training_input_formats</span><span class=o>=</span><span class=s>RGB;</span>
<span class=na>input_formats</span><span class=o>=</span><span class=s>GRAY;</span>
<span class=na>......</span>
</code></pre></div> <p>将灰度图片的有效数据只放在R通道上，因此仅需设置R通道的mean值（mean_red）</p> <div class=highlight><pre><span></span><code><span class=k>[INPUT_CONFIG]</span>
<span class=na>......</span>
<span class=na>mean_red</span><span class=o>=</span><span class=s>127.5;</span>
<span class=na>mean_green</span><span class=o>=</span><span class=s>0.0;</span>
<span class=na>mean_blue</span><span class=o>=</span><span class=s>0.0;</span>
<span class=c1>;std_value parameter for image models,</span>
<span class=na>std_value</span><span class=o>=</span><span class=s>1.0;</span>
</code></pre></div> <hr> <h3 id=912>9.1.2. 灰度图片输入模型前处理方法<a class=headerlink href=#912 title="Permanent link">&para;</a></h3> <p>灰度图片输入的前处理方法与3.2节图片前处理方法要求相同，函数必须包含2个参数：</p> <ul> <li> <p>图片路径</p> </li> <li> <p>归一化标记（norm=True）</p> </li> </ul> <p>参考代码<code>SGS_IPU_SDK/Scripts/calibrator/preprocess_method/caffe_lenet.py</code></p> <div class=highlight><pre><span></span><code><span class=kn>import</span> <span class=nn>cv2</span>
<span class=kn>import</span> <span class=nn>numpy</span> <span class=k>as</span> <span class=nn>np</span>
<span class=k>def</span> <span class=nf>get_image</span><span class=p>(</span><span class=n>img_path</span><span class=p>,</span> <span class=n>resizeH</span><span class=o>=</span><span class=mi>28</span><span class=p>,</span> <span class=n>resizeW</span><span class=o>=</span><span class=mi>28</span><span class=p>,</span> <span class=n>norm</span><span class=o>=</span><span class=kc>True</span><span class=p>,</span> <span class=n>meanR</span><span class=o>=</span><span class=mf>33.318</span><span class=p>,</span> <span class=n>std</span><span class=o>=</span><span class=mi>1</span><span class=p>):</span>
    <span class=n>img</span> <span class=o>=</span> <span class=n>cv2</span><span class=o>.</span><span class=n>imread</span><span class=p>(</span><span class=n>img_path</span><span class=p>,</span> <span class=n>flags</span><span class=o>=-</span><span class=mi>1</span><span class=p>)</span>
    <span class=k>try</span><span class=p>:</span>
        <span class=n>img_dim</span> <span class=o>=</span> <span class=n>img</span><span class=o>.</span><span class=n>shape</span><span class=p>[</span><span class=mi>2</span><span class=p>]</span>
    <span class=k>except</span> <span class=ne>IndexError</span><span class=p>:</span>
        <span class=n>img_dim</span> <span class=o>=</span> <span class=mi>1</span>
    <span class=k>if</span> <span class=n>img_dim</span> <span class=o>==</span> <span class=mi>3</span><span class=p>:</span>
        <span class=n>img</span> <span class=o>=</span> <span class=n>cv2</span><span class=o>.</span><span class=n>cvtColor</span><span class=p>(</span><span class=n>img</span><span class=p>,</span> <span class=n>cv2</span><span class=o>.</span><span class=n>COLOR_BGR2GRAY</span><span class=p>)</span>
    <span class=k>elif</span> <span class=n>img_dim</span> <span class=o>==</span> <span class=mi>4</span><span class=p>:</span>
        <span class=n>img</span> <span class=o>=</span> <span class=n>cv2</span><span class=o>.</span><span class=n>cvtColor</span><span class=p>(</span><span class=n>img</span><span class=p>,</span> <span class=n>cv2</span><span class=o>.</span><span class=n>COLOR_BGRA2GRAY</span><span class=p>)</span>
    <span class=n>img_norm</span> <span class=o>=</span> <span class=n>cv2</span><span class=o>.</span><span class=n>resize</span><span class=p>(</span><span class=n>img</span><span class=p>,</span> <span class=p>(</span><span class=n>resizeW</span><span class=p>,</span> <span class=n>resizeH</span><span class=p>),</span> <span class=n>interpolation</span><span class=o>=</span><span class=n>cv2</span><span class=o>.</span><span class=n>INTER_LINEAR</span><span class=p>)</span>
    <span class=k>if</span> <span class=n>norm</span><span class=p>:</span>
        <span class=n>img_norm</span> <span class=o>=</span> <span class=p>(</span><span class=n>img_norm</span> <span class=o>-</span> <span class=n>meanR</span><span class=p>)</span> <span class=o>/</span> <span class=n>std</span>
        <span class=n>img_norm</span> <span class=o>=</span> <span class=n>np</span><span class=o>.</span><span class=n>expand_dims</span><span class=p>(</span><span class=n>img_norm</span><span class=p>,</span> <span class=n>axis</span><span class=o>=</span><span class=mi>2</span><span class=p>)</span>
        <span class=n>dummy</span> <span class=o>=</span> <span class=n>np</span><span class=o>.</span><span class=n>zeros</span><span class=p>((</span><span class=mi>28</span><span class=p>,</span> <span class=mi>28</span><span class=p>,</span> <span class=mi>2</span><span class=p>))</span>
        <span class=n>img_norm</span> <span class=o>=</span> <span class=n>np</span><span class=o>.</span><span class=n>concatenate</span><span class=p>((</span><span class=n>img_norm</span><span class=p>,</span> <span class=n>dummy</span><span class=p>),</span> <span class=n>axis</span><span class=o>=</span><span class=mi>2</span><span class=p>)</span>
        <span class=n>img_norm</span> <span class=o>=</span> <span class=n>img_norm</span><span class=o>.</span><span class=n>astype</span><span class=p>(</span><span class=s1>&#39;float32&#39;</span><span class=p>)</span>
    <span class=k>return</span> <span class=n>img_norm</span>
<span class=k>def</span> <span class=nf>image_preprocess</span><span class=p>(</span><span class=n>img_path</span><span class=p>,</span> <span class=n>norm</span><span class=o>=</span><span class=kc>True</span><span class=p>):</span>
    <span class=k>return</span> <span class=n>get_image</span><span class=p>(</span><span class=n>img_path</span><span class=p>,</span> <span class=n>norm</span><span class=o>=</span><span class=n>norm</span><span class=p>)</span>
</code></pre></div> <p><strong>Please Note:</strong></p> <ul> <li>灰度图片输入模型在PC 上运行与开发板上运行不同。在PC 上需要将单通道图片扩维至3 通道，并在后两个通道 补0。在开发板上需要YUV 图片输入数据。</li> </ul> <p><img alt=box_process src=mymedia/********************************.png></p> <hr> <h2 id=92-rawdata_f32_nhwcrawdata_s16_nhwc>9.2. RAWDATA_F32_NHWC与RAWDATA_S16_NHWC输入的模型<a class=headerlink href=#92-rawdata_f32_nhwcrawdata_s16_nhwc title="Permanent link">&para;</a></h2> <p><code>RAWDATA_F32_NHWC</code>与<code>RAWDATA_S16_NHWC</code>可以用于分段网络的后端网络模型输入，或者非图片数据输入的网络模型。</p> <h3 id=9211-rawdata_f32_nhwc>9.2.1.1 RAWDATA_F32_NHWC模型转换<a class=headerlink href=#9211-rawdata_f32_nhwc title="Permanent link">&para;</a></h3> <p>转换方式与前述介绍基本相同，但配置 <strong>input_config.ini</strong> 中的</p> <p><code>input_formats</code>和<code>training_input_formats</code>都填写<code>RAWDATA_F32_NHWC</code>，<code>quantizations</code>配置为<code>TRUE</code>。</p> <p>当input_config.ini中的<code>input_formats</code>填写<code>RAWDATA_F32_NHWC</code>时，此时input_config.ini中<code>mean_red</code>、<code>mean_green</code>、<code>mean_blue</code>和<code>std_value</code>不会再在定点网络模型中生效，所有前处理过程都将在输入模型前完成。<code>mean_red</code>、<code>mean_green</code>、<code>mean_blue</code>和<code>std_value</code>不要填写。</p> <p>模型在calibrator.py转换时使用浮点数据，因此转换方法与正常网络相同。可以参考第3章Calibrator的使用方法。</p> <hr> <h3 id=9212-rawdata_f32_nhwc>9.2.1.2 RAWDATA_F32_NHWC模型运行<a class=headerlink href=#9212-rawdata_f32_nhwc title="Permanent link">&para;</a></h3> <p>浮点模型运行时，使用方法与运行图片输入的模型相同。</p> <p>使用simulator.py运行定点模型时，前处理方法应与浮点前处理模型保持一致，仍然输入norm=True时的方法，所以<code>RAWDATA_F32_NHWC</code>的网络前处理Python文件编写时norm为True和False的实现都应按照norm为True时编写。</p> <p>使用calibrator_custom.fixed_simulator时，输入数据类型为float32。</p> <p><div class=highlight><pre><span></span><code><span class=o>&gt;&gt;&gt;</span> <span class=kn>import</span> <span class=nn>calibrator_custom</span>
<span class=o>&gt;&gt;&gt;</span> <span class=n>model</span> <span class=o>=</span> <span class=n>calibrator_custom</span><span class=o>.</span><span class=n>fixed_simulator</span><span class=p>(</span><span class=s1>&#39;./mobilenet_v2_fixed.sim&#39;</span><span class=p>)</span>
<span class=o>&gt;&gt;&gt;</span> <span class=n>input_details</span> <span class=o>=</span> <span class=n>model</span><span class=o>.</span><span class=n>get_input_details</span><span class=p>()</span>
<span class=o>&gt;&gt;&gt;</span> <span class=nb>print</span><span class=p>(</span><span class=n>input_details</span><span class=p>)</span>
<span class=p>[{</span><span class=s1>&#39;shape&#39;</span><span class=p>:</span> <span class=n>array</span><span class=p>([</span> <span class=mi>1</span><span class=p>,</span> <span class=mi>513</span><span class=p>,</span> <span class=mi>513</span><span class=p>,</span> <span class=mi>3</span><span class=p>]),</span> <span class=s1>&#39;name&#39;</span><span class=p>:</span> <span class=s1>&#39;sub_7&#39;</span><span class=p>,</span> <span class=s1>&#39;dtype&#39;</span><span class=p>:</span> <span class=o>&lt;</span><span class=k>class</span> <span class=err>&#39;</span><span class=nc>numpy</span><span class=o>.</span><span class=n>float32</span><span class=s1>&#39;&gt;, &#39;</span><span class=n>index</span><span class=s1>&#39;: 0}]</span>
</code></pre></div> calibrator_custom.fixed_simulator中检查了模型的输入格式，计算出需要输入的shape信息。</p> <p>已使用float网络的前处理处理完图片，返回numpy.ndarray格式的变量img</p> <p><div class=highlight><pre><span></span><code><span class=o>&gt;&gt;&gt;</span> <span class=nb>print</span><span class=p>(</span><span class=n>img</span><span class=o>.</span><span class=n>shape</span><span class=p>)</span>
<span class=p>(</span><span class=mi>1</span><span class=p>,</span> <span class=mi>513</span><span class=p>,</span> <span class=mi>513</span><span class=p>,</span> <span class=mi>3</span><span class=p>)</span>
<span class=o>&gt;&gt;&gt;</span> <span class=nb>print</span><span class=p>(</span><span class=n>img</span><span class=o>.</span><span class=n>dtype</span><span class=p>)</span>
<span class=n>float32</span>
</code></pre></div> 输入模型数据</p> <div class=highlight><pre><span></span><code><span class=o>&gt;&gt;&gt;</span> <span class=n>model</span><span class=o>.</span><span class=n>set_input</span><span class=p>(</span><span class=n>input_details</span><span class=p>[</span><span class=mi>0</span><span class=p>][</span><span class=s1>&#39;index&#39;</span><span class=p>],</span> <span class=n>img</span><span class=p>)</span>
</code></pre></div> <p>在板上运行<code>RAWDATA_F32_NHWC</code>的网络时，可以参考如下代码，FillInputData函数的输入分别是浮点输入数据的数组，MI_IPU输入Tensor结构体和MI_IPU网络描述结构体。</p> <div class=highlight><pre><span></span><code><span class=kt>void</span> <span class=nf>FillInputData</span><span class=p>(</span><span class=n>MI_FLOAT</span><span class=o>*</span> <span class=n>pfData</span><span class=p>,</span> <span class=n>MI_IPU_TensorVector_t</span><span class=o>&amp;</span> <span class=n>InputTensorVector</span><span class=p>,</span> <span class=n>MI_IPU_SubNet_InputOutputDesc_t</span><span class=o>&amp;</span> <span class=n>desc</span><span class=p>)</span>
<span class=p>{</span>
    <span class=k>const</span> <span class=n>MI_U32</span> <span class=n>H</span> <span class=o>=</span> <span class=n>desc</span><span class=p>.</span><span class=n>astMI_InputTensorDescs</span><span class=p>[</span><span class=mi>0</span><span class=p>].</span><span class=n>u32TensorShape</span><span class=p>[</span><span class=mi>1</span><span class=p>];</span>
    <span class=k>const</span> <span class=n>MI_U32</span> <span class=n>W</span> <span class=o>=</span> <span class=n>desc</span><span class=p>.</span><span class=n>astMI_InputTensorDescs</span><span class=p>[</span><span class=mi>0</span><span class=p>].</span><span class=n>u32TensorShape</span><span class=p>[</span><span class=mi>2</span><span class=p>];</span>
    <span class=k>const</span> <span class=n>MI_U32</span> <span class=n>C</span> <span class=o>=</span> <span class=n>desc</span><span class=p>.</span><span class=n>astMI_InputTensorDescs</span><span class=p>[</span><span class=mi>0</span><span class=p>].</span><span class=n>u32TensorShape</span><span class=p>[</span><span class=mi>3</span><span class=p>];</span>
    <span class=n>MI_FLOAT</span><span class=o>*</span> <span class=n>pTensorData</span> <span class=o>=</span> <span class=p>(</span><span class=n>MI_FLOAT</span><span class=o>*</span><span class=p>)</span><span class=n>InputTensorVector</span><span class=p>.</span><span class=n>astArrayTensors</span><span class=p>[</span><span class=mi>0</span><span class=p>].</span><span class=n>ptTensorData</span><span class=p>[</span><span class=mi>0</span><span class=p>];</span>
    <span class=k>if</span> <span class=p>(</span><span class=n>desc</span><span class=p>.</span><span class=n>eElmFormat</span> <span class=o>==</span> <span class=n>MI_IPU_FORMAT_FP32</span><span class=p>)</span>
    <span class=p>{</span>
        <span class=n>memcpy</span><span class=p>(</span><span class=n>pTensorData</span><span class=p>,</span> <span class=n>pfData</span><span class=p>,</span> <span class=n>H</span> <span class=o>*</span> <span class=n>W</span> <span class=o>*</span> <span class=n>C</span> <span class=o>*</span> <span class=k>sizeof</span><span class=p>(</span><span class=n>MI_FLOAT</span><span class=p>));</span>
    <span class=p>}</span>
<span class=p>}</span>
</code></pre></div> <div class="admonition 注意事项"> <p class=admonition-title>注意事项</p> </div> <p>只有使用<code>RAWDATA_F32_NHWC</code>配置的网络在板上memcpy完数据后不要调用MI_SYS_FlushInvCache。</p> <h3 id=9221-rawdata_s16_nhwc>******* RAWDATA_S16_NHWC模型转换<a class=headerlink href=#9221-rawdata_s16_nhwc title="Permanent link">&para;</a></h3> <p>使用该种方式输入时，由于硬件的限制条件，所以需要提前排列好数据后才能进行计算。</p> <p>转换方式与前述介绍基本相同，但配置 <strong>input_config.ini</strong> 中的<code>training_input_formats</code></p> <p>和<code>input_formats</code>时都填写<code>RAWDATA_S16_NHWC</code>。</p> <p>当input_config.ini中的<code>training_input_formats</code>和<code>input_formats</code>都填写<code>RAWDATA_S16_NHWC</code>时，此时input_config.ini中<code>mean_red</code>、<code>mean_green</code>、<code>mean_blue</code>和<code>std_value</code>不会再在定点网络模型中生效，所有前处理过程都将在输入模型前完成。<code>mean_red</code>、<code>mean_green</code>、<code>mean_blue</code>和<code>std_value</code>不要填写。</p> <p>由于模型在calibrator.py转换时仍然使用浮点数据，因此转换方法与正常网络相同。可以参考第3章Calibrator的使用方法。</p> <hr> <h3 id=9222-rawdata_s16_nhwc>9.2.2.2 RAWDATA_S16_NHWC模型运行<a class=headerlink href=#9222-rawdata_s16_nhwc title="Permanent link">&para;</a></h3> <p>浮点模型运行时，使用方法与运行图片输入的模型相同。</p> <p>使用simulator.py运行定点模型时，前处理方法应与浮点前处理模型保持一致，仍然输入norm=True时的方法，所以<code>RAWDATA_S16_NHWC</code>的网络前处理Python文件编写时norm为True和False的实现都应按照norm为True时编写。simulator.py会读入原浮点数据，进行反量化和对齐排列后输入给定点模型。</p> <p>使用calibrator_custom.fixed_simulator时，定点化和对齐排列过程需要自行完成。以下为例，说明定点化和对齐排列过程的做法。</p> <p><div class=highlight><pre><span></span><code><span class=o>&gt;&gt;&gt;</span> <span class=kn>import</span> <span class=nn>calibrator_custom</span>
<span class=o>&gt;&gt;&gt;</span> <span class=n>model</span> <span class=o>=</span> <span class=n>calibrator_custom</span><span class=o>.</span><span class=n>fixed_simulator</span><span class=p>(</span><span class=s1>&#39;./mobilenet_v2_s16_fixed.sim&#39;</span><span class=p>)</span>
<span class=o>&gt;&gt;&gt;</span> <span class=n>input_details</span> <span class=o>=</span> <span class=n>model</span><span class=o>.</span><span class=n>get_input_details</span><span class=p>()</span>
<span class=o>&gt;&gt;&gt;</span> <span class=nb>print</span><span class=p>(</span><span class=n>input_details</span><span class=p>)</span>
<span class=p>[{</span><span class=s1>&#39;input_formats&#39;</span><span class=p>:</span> <span class=s1>&#39;RAWDATA_S16_NHWC&#39;</span><span class=p>,</span> <span class=s1>&#39;training_input_formats&#39;</span><span class=p>:</span> <span class=s1>&#39;RAWDATA_S16_NHWC&#39;</span><span class=p>,</span> <span class=s1>&#39;shape&#39;</span><span class=p>:</span> <span class=n>array</span><span class=p>([</span> <span class=mi>1</span><span class=p>,</span> <span class=mi>513</span><span class=p>,</span> <span class=mi>513</span><span class=p>,</span> <span class=mi>8</span><span class=p>]),</span> <span class=s1>&#39;name&#39;</span><span class=p>:</span> <span class=s1>&#39;sub_7&#39;</span><span class=p>,</span> <span class=s1>&#39;dtype&#39;</span><span class=p>:</span> <span class=o>&lt;</span><span class=k>class</span> <span class=err>&#39;</span><span class=nc>numpy</span><span class=o>.</span><span class=n>int16</span><span class=s1>&#39;&gt;, &#39;</span><span class=n>index</span><span class=s1>&#39;: 0, &#39;</span><span class=n>quantization</span><span class=s1>&#39;: (3.0518509447574615e-05, 0)}]</span>
</code></pre></div> calibrator_custom.fixed_simulator中检查了模型的输入格式，计算出需要输入的shape信息。</p> <p>已使用float网络的前处理处理完图片，返回numpy.ndarray格式的变量img</p> <p><div class=highlight><pre><span></span><code><span class=o>&gt;&gt;&gt;</span> <span class=nb>print</span><span class=p>(</span><span class=n>img</span><span class=o>.</span><span class=n>shape</span><span class=p>)</span>
<span class=p>(</span><span class=mi>1</span><span class=p>,</span> <span class=mi>513</span><span class=p>,</span> <span class=mi>513</span><span class=p>,</span> <span class=mi>3</span><span class=p>)</span>
</code></pre></div> 定点化img的数据，并转换数据类型</p> <p><div class=highlight><pre><span></span><code><span class=o>&gt;&gt;&gt;</span> <span class=n>ins</span><span class=p>,</span> <span class=n>zp</span> <span class=o>=</span> <span class=n>input_details</span><span class=p>[</span><span class=mi>0</span><span class=p>][</span><span class=s1>&#39;quantization&#39;</span><span class=p>]</span>
<span class=o>&gt;&gt;&gt;</span> <span class=n>img</span> <span class=o>=</span> <span class=p>(</span><span class=n>img</span> <span class=o>/</span> <span class=n>ins</span> <span class=o>+</span> <span class=n>zp</span><span class=p>)</span><span class=o>.</span><span class=n>astype</span><span class=p>(</span><span class=n>input_details</span><span class=p>[</span><span class=mi>0</span><span class=p>][</span><span class=s1>&#39;dtype&#39;</span><span class=p>])</span>
<span class=o>&gt;&gt;&gt;</span> <span class=nb>print</span><span class=p>(</span><span class=n>img</span><span class=o>.</span><span class=n>dtype</span><span class=p>)</span>
<span class=n>int16</span>
</code></pre></div> 对齐排列img数据，使之符合输入条件，对齐仅针对输入的最后一个维度会向上对齐。</p> <p><div class=highlight><pre><span></span><code><span class=o>&gt;&gt;&gt;</span> <span class=n>img_s16</span> <span class=o>=</span> <span class=n>np</span><span class=o>.</span><span class=n>zeros</span><span class=p>(</span><span class=n>input_details</span><span class=p>[</span><span class=mi>0</span><span class=p>][</span><span class=s1>&#39;shape&#39;</span><span class=p>])</span><span class=o>.</span><span class=n>astype</span><span class=p>(</span><span class=s1>&#39;int16&#39;</span><span class=p>)</span>
<span class=o>&gt;&gt;&gt;</span> <span class=n>img_s16</span><span class=p>[:,</span> <span class=p>:,</span> <span class=p>:,</span> <span class=p>:</span><span class=mi>3</span><span class=p>]</span> <span class=o>=</span> <span class=n>img</span>
<span class=o>&gt;&gt;&gt;</span> <span class=n>model</span><span class=o>.</span><span class=n>set_input</span><span class=p>(</span><span class=n>input_details</span><span class=p>[</span><span class=mi>0</span><span class=p>][</span><span class=s1>&#39;index&#39;</span><span class=p>],</span> <span class=n>img_s16</span><span class=p>)</span>
</code></pre></div> 由于上述操作步骤在simulator.py中已经做完，所以使用使用simulator.py运行定点模型运行时，前处理方法应与浮点前处理模型保持一致。</p> <p>在板上运行<code>RAWDATA_S16_NHWC</code>的网络时，也需要完成输入数据的定点化和对齐排列过程，可以参考如下代码，FillInputData函数的输入分别是浮点输入数据的数组，MI_IPU输入Tensor结构体和MI_IPU网络描述结构体。</p> <div class=highlight><pre><span></span><code><span class=cp>#define ALIGN_UP(x, align) (((x) + ((align) - 1)) &amp; ~((align) - 1))</span>
<span class=cp>#define CLIP3(x, min, max) ((x) &lt; (min) ? (min) : ((x) &gt; (max) ? (max) : (x)))</span>

<span class=kt>void</span> <span class=nf>FillInputData</span><span class=p>(</span><span class=n>MI_FLOAT</span><span class=o>*</span> <span class=n>pfData</span><span class=p>,</span> <span class=n>MI_IPU_TensorVector_t</span><span class=o>&amp;</span> <span class=n>InputTensorVector</span><span class=p>,</span> <span class=n>MI_IPU_SubNet_InputOutputDesc_t</span><span class=o>&amp;</span> <span class=n>desc</span><span class=p>)</span>
<span class=p>{</span>
<span class=k>const</span> <span class=n>MI_U32</span> <span class=n>H</span> <span class=o>=</span> <span class=n>desc</span><span class=p>.</span><span class=n>astMI_InputTensorDescs</span><span class=p>[</span><span class=mi>0</span><span class=p>].</span><span class=n>u32TensorShape</span><span class=p>[</span><span class=mi>1</span><span class=p>];</span>
<span class=k>const</span> <span class=n>MI_U32</span> <span class=n>W</span> <span class=o>=</span> <span class=n>desc</span><span class=p>.</span><span class=n>astMI_InputTensorDescs</span><span class=p>[</span><span class=mi>0</span><span class=p>].</span><span class=n>u32TensorShape</span><span class=p>[</span><span class=mi>2</span><span class=p>];</span>
<span class=k>const</span> <span class=n>MI_U32</span> <span class=n>C</span> <span class=o>=</span> <span class=n>desc</span><span class=p>.</span><span class=n>astMI_InputTensorDescs</span><span class=p>[</span><span class=mi>0</span><span class=p>].</span><span class=n>u32TensorShape</span><span class=p>[</span><span class=mi>3</span><span class=p>];</span>
<span class=k>const</span> <span class=n>MI_U32</span> <span class=n>inner_size</span> <span class=o>=</span> <span class=n>ALIGN_UP</span><span class=p>(</span><span class=n>C</span><span class=p>,</span> <span class=mi>8</span><span class=p>);</span> <span class=k>const</span> <span class=n>MI_U32</span> <span class=n>outer_size</span> <span class=o>=</span> <span class=n>H</span> <span class=o>*</span> <span class=n>W</span><span class=p>;</span>
<span class=k>const</span> <span class=n>MI_FLOAT</span> <span class=n>Scale</span> <span class=o>=</span> <span class=n>desc</span><span class=p>.</span><span class=n>astMI_InputTensorDescs</span><span class=p>[</span><span class=mi>0</span><span class=p>].</span><span class=n>fScalar</span><span class=p>;</span>
<span class=k>const</span> <span class=n>MI_S64</span> <span class=n>ZeroPoint</span> <span class=o>=</span> <span class=n>desc</span><span class=p>.</span><span class=n>astMI_InputTensorDescs</span><span class=p>[</span><span class=mi>0</span><span class=p>].</span><span class=n>s64ZeroPoint</span><span class=p>;</span>
<span class=n>MI_S16</span><span class=o>*</span> <span class=n>pData</span> <span class=o>=</span> <span class=p>(</span><span class=n>MI_S16</span><span class=o>*</span><span class=p>)</span><span class=n>InputTensorVector</span><span class=p>.</span><span class=n>astArrayTensors</span><span class=p>[</span><span class=mi>0</span><span class=p>].</span><span class=n>ptTensorData</span><span class=p>[</span><span class=mi>0</span><span class=p>];</span>
<span class=k>for</span> <span class=p>(</span><span class=n>MI_U32</span> <span class=n>i</span> <span class=o>=</span> <span class=mi>0</span><span class=p>;</span> <span class=n>i</span> <span class=o>&lt;</span> <span class=n>outer_size</span><span class=p>;</span> <span class=n>i</span><span class=o>++</span><span class=p>)</span> <span class=p>{</span>
    <span class=k>for</span> <span class=p>(</span><span class=n>MI_U32</span> <span class=n>j</span> <span class=o>=</span> <span class=mi>0</span><span class=p>;</span> <span class=n>j</span> <span class=o>&lt;</span> <span class=n>C</span><span class=p>;</span> <span class=n>j</span><span class=o>++</span><span class=p>)</span> <span class=p>{</span>
        <span class=o>*</span><span class=p>(</span><span class=n>pData</span> <span class=o>+</span> <span class=n>i</span> <span class=o>*</span> <span class=n>inner_size</span> <span class=o>+</span> <span class=n>j</span><span class=p>)</span> <span class=o>=</span> <span class=p>(</span><span class=n>MI_S16</span><span class=p>)</span><span class=n>CLIP3</span><span class=p>(</span><span class=n>round</span><span class=p>(</span><span class=n>pfData</span><span class=p>[</span><span class=n>i</span> <span class=o>*</span> <span class=n>C</span> <span class=o>+</span> <span class=n>j</span><span class=p>]</span> <span class=o>/</span> <span class=n>Scale</span> <span class=o>+</span> <span class=n>ZeroPoint</span><span class=p>),</span> <span class=mi>-32768</span> <span class=p>,</span> <span class=mi>32767</span><span class=p>);</span>
        <span class=p>}</span>
    <span class=p>}</span>
    <span class=n>MI_SYS_FlushInvCache</span><span class=p>(</span><span class=n>pData</span><span class=p>,</span> <span class=n>inner_size</span> <span class=o>*</span> <span class=n>outer_size</span> <span class=o>*</span> <span class=k>sizeof</span><span class=p>(</span><span class=n>MI_S16</span><span class=p>));</span>
<span class=p>}</span>
</code></pre></div> <hr> <h2 id=93>9.3. 分段网络转换要点<a class=headerlink href=#93 title="Permanent link">&para;</a></h2> <p>网络中有不支持的Layer，可将完整网络分段执行。前一段网络运行完成后将结果输入给自定义实现层，再将自定义层的输出结果作为第二段网络的输入运行。下面以Faster_RCNN网络为例，说明如何转换分段网络。</p> <h3 id=931>9.3.1 网络切分<a class=headerlink href=#931 title="Permanent link">&para;</a></h3> <p>Faster_RCNN网络中的Proposal Layer是不支持的，我们需要将网络从该层分成两段。</p> <p>如下图所示，图中的Python Layer是Proposal Layer，通过修改prototxt文件，将网络从Proposal Layer处拆成两段：第一段网络将有3个输出，其中rpn_cls_prob_reshape和rpn_bbox_pred两个输出结果将作为Proposal Layer的输入，conv5_3和Proposal Layer的输出将作为第二段网络的输入。</p> <p>第一段网络的输入是图片数据，input_config.ini和前处理python文件按照2.2和3.2节处理。第二段网络的输入不是图片数据，可以参考9.2节对两个输入同时采用RAWDATA_S16_NHWC的格式配置。配置完成后，通过</p> <p>ConvertTool.py分别将两个网络转换成float.sim模型文件。具体配置文件和转换命令如下：</p> <p>第一段网络：</p> <p><div class=highlight><pre><span></span><code>python3 ~/SGS_IPU_SDK/Scripts/ConvertTool/ConvertTool.py caffe \
--model_file test_stageone.prototxt \
--weight_file VGG16_faster_rcnn_final.caffemodel \
--input_arrays data \
--output_arrays rpn_cls_prob_reshape,rpn_bbox_pred,conv5_3 \
--input_config input_config.ini \
--output_file faster_rcnn_main_float.sim
</code></pre></div> <div class=highlight><pre><span></span><code><span class=k>[INPUT_CONFIG]</span>
<span class=na>inputs</span><span class=o>=</span><span class=s>data;</span>
<span class=na>input_formats</span><span class=o>=</span><span class=s>BGR;</span>
<span class=na>quantizations</span><span class=o>=</span><span class=s>TRUE;</span>
<span class=na>mean_red</span><span class=o>=</span><span class=s>122.7717;</span>
<span class=na>mean_green</span><span class=o>=</span><span class=s>115.9465;</span>
<span class=na>mean_blue</span><span class=o>=</span><span class=s>102.9801;</span>
<span class=na>std_value</span><span class=o>=</span><span class=s>1;</span>
<span class=k>[OUTPUT_CONFIG]</span>
<span class=na>outputs</span><span class=o>=</span><span class=s>rpn_cls_prob_reshape,rpn_bbox_pred,conv5_3;</span>
<span class=na>dequantizations</span><span class=o>=</span><span class=s>TRUE,TRUE,FALSE;</span>
</code></pre></div> 设定conv5_3 这个输出的dequantizations 为FALSE，由于该输出直接输入第二段网络，所以在板端运行时不需要 转换成float，可以直接将该输出送进第二段网络。</p> <p><img alt=box_process src=mymedia/caffe004.PNG></p> <p>第二段网络：</p> <p>ROIPooling的rois输入维度为（N×5），当后段网络全部是InnerProduct时，N才可以设置为300（如上图所示），如果后段网络中有卷积时，N仅可以设置为1，第二段网络需要循环执行N次。</p> <div class=highlight><pre><span></span><code>python3 ~/SGS_IPU_SDK/Scripts/ConvertTool/ConvertTool.py caffe \
--model_file second_stage.prototxt \
--weight_file VGG16_faster_rcnn_final.caffemodel \
--input_arrays conv5_3,rois \
--output_arrays cls_prob,bbox_pred \
--input_config input_config_stage2.ini \
--output_file faster_rcnn_stage2_float.sim
</code></pre></div> <div class=highlight><pre><span></span><code><span class=k>[INPUT_CONFIG]</span>
<span class=na>inputs</span><span class=o>=</span><span class=s>conv5_3,rois</span>
<span class=na>input_formats</span><span class=o>=</span><span class=s>RAWDATA_F32_NHWC,RAWDATA_F32_NHWC;</span>
<span class=na>quantizations</span><span class=o>=</span><span class=s>TRUE,TRUE;</span>
<span class=k>[OUTPUT_CONFIG]</span>
<span class=na>outputs</span><span class=o>=</span><span class=s>cls_prob,bbox_pred;</span>
<span class=na>dequantizations</span><span class=o>=</span><span class=s>TRUE,TRUE;</span>
</code></pre></div> <hr> <h3 id=932>9.3.2 转换网络<a class=headerlink href=#932 title="Permanent link">&para;</a></h3> <p>工具路径<code>SGS_IPU_SDK/Scripts/examples/caffe_faster_rcnn/faster_rcnn_calibrator.py</code> 该工具作为Faster_RCNN网络转换demo，直接运行即可将两段网络直接转换成fixed模型。</p> <div class=highlight><pre><span></span><code>python3 ~/SGS_IPU_SDK/Scripts/examples/caffe_faster_rcnn/faster_rcnn_calibrator.py \
-i ~/SGS_Models/resource/detection/voc_calibration_set32/ \
-m0 faster_rcnn_main_float.sim \
-m1 faster_rcnn_stage2_float.sim \
--input_config0 input_config.ini \
--input_config1 input_config_stage2.ini
</code></pre></div> <p>在转换分段网络时，首先定义网络的两段网络，再组织两段网络的运行方式，定义在forward方法里：</p> <p><div class=highlight><pre><span></span><code><span class=k>class</span> <span class=nc>Net</span><span class=p>(</span><span class=n>calibrator_custom</span><span class=o>.</span><span class=n>SIM_Calibrator</span><span class=p>):</span>
    <span class=k>def</span> <span class=fm>__init__</span><span class=p>(</span><span class=bp>self</span><span class=p>,</span> <span class=n>main_model_path</span><span class=p>,</span> <span class=n>main_input_config</span><span class=p>,</span> <span class=n>second_model_path</span><span class=p>,</span> <span class=n>second_input_config</span><span class=p>):</span>
        <span class=nb>super</span><span class=p>()</span><span class=o>.</span><span class=fm>__init__</span><span class=p>()</span>
        <span class=bp>self</span><span class=o>.</span><span class=n>main_model</span> <span class=o>=</span> <span class=n>calibrator_custom</span><span class=o>.</span><span class=n>calibrator</span><span class=p>(</span><span class=n>main_model_path</span><span class=p>,</span> <span class=n>main_input_config</span><span class=p>)</span>
        <span class=bp>self</span><span class=o>.</span><span class=n>second_model</span> <span class=o>=</span> <span class=n>calibrator_custom</span><span class=o>.</span><span class=n>calibrator</span><span class=p>(</span><span class=n>second_model_path</span><span class=p>,</span> <span class=n>second_input_config</span><span class=p>)</span>
        <span class=bp>self</span><span class=o>.</span><span class=n>rpn</span> <span class=o>=</span> <span class=n>rpn</span><span class=o>.</span><span class=n>ProposalLayer</span><span class=p>()</span>
    <span class=k>def</span> <span class=nf>forward</span><span class=p>(</span><span class=bp>self</span><span class=p>,</span> <span class=n>x</span><span class=p>):</span>
        <span class=n>out_details</span> <span class=o>=</span> <span class=bp>self</span><span class=o>.</span><span class=n>main_model</span><span class=o>.</span><span class=n>get_output_details</span><span class=p>()</span>
        <span class=n>input_data</span><span class=p>,</span> <span class=n>im_scale</span> <span class=o>=</span> <span class=n>fill_inputImg2main</span><span class=p>(</span><span class=n>x</span><span class=p>)</span>
        <span class=bp>self</span><span class=o>.</span><span class=n>main_model</span><span class=o>.</span><span class=n>set_input</span><span class=p>(</span><span class=mi>0</span><span class=p>,</span> <span class=n>input_data</span><span class=p>)</span>
        <span class=bp>self</span><span class=o>.</span><span class=n>main_model</span><span class=o>.</span><span class=n>invoke</span><span class=p>()</span>
        <span class=n>result_list</span> <span class=o>=</span> <span class=p>[]</span>
        <span class=k>for</span> <span class=n>idx</span><span class=p>,</span> <span class=n>_</span> <span class=ow>in</span> <span class=nb>enumerate</span><span class=p>(</span><span class=n>out_details</span><span class=p>):</span>
            <span class=n>result</span> <span class=o>=</span> <span class=bp>self</span><span class=o>.</span><span class=n>main_model</span><span class=o>.</span><span class=n>get_output</span><span class=p>(</span><span class=n>idx</span><span class=p>)</span>
            <span class=n>result_list</span><span class=o>.</span><span class=n>append</span><span class=p>(</span><span class=n>result</span><span class=p>)</span>
        <span class=n>im_info</span> <span class=o>=</span> <span class=n>np</span><span class=o>.</span><span class=n>array</span><span class=p>([</span><span class=n>x</span><span class=o>.</span><span class=n>shape</span><span class=p>[</span><span class=mi>0</span><span class=p>],</span> <span class=n>x</span><span class=o>.</span><span class=n>shape</span><span class=p>[</span><span class=mi>1</span><span class=p>],</span> <span class=n>im_scale</span><span class=p>])</span><span class=o>.</span><span class=n>reshape</span><span class=p>(</span><span class=mi>1</span><span class=p>,</span> <span class=mi>3</span><span class=p>)</span>
        <span class=n>bottom</span> <span class=o>=</span> <span class=p>[</span><span class=n>result_list</span><span class=p>[</span><span class=mi>0</span><span class=p>],</span> <span class=n>result_list</span><span class=p>[</span><span class=mi>1</span><span class=p>],</span> <span class=n>im_info</span><span class=p>]</span>
        <span class=n>roi</span> <span class=o>=</span> <span class=bp>self</span><span class=o>.</span><span class=n>rpn</span><span class=o>.</span><span class=n>forward</span><span class=p>(</span><span class=n>bottom</span><span class=p>)</span>
        <span class=n>out2_details</span> <span class=o>=</span> <span class=bp>self</span><span class=o>.</span><span class=n>second_model</span><span class=o>.</span><span class=n>get_output_details</span><span class=p>()</span>
        <span class=bp>self</span><span class=o>.</span><span class=n>second_model</span><span class=o>.</span><span class=n>set_input</span><span class=p>(</span><span class=mi>0</span><span class=p>,</span> <span class=n>result_list</span><span class=p>[</span><span class=mi>2</span><span class=p>])</span>
        <span class=bp>self</span><span class=o>.</span><span class=n>second_model</span><span class=o>.</span><span class=n>set_input</span><span class=p>(</span><span class=mi>1</span><span class=p>,</span> <span class=n>roi</span><span class=p>)</span>
        <span class=bp>self</span><span class=o>.</span><span class=n>second_model</span><span class=o>.</span><span class=n>invoke</span><span class=p>()</span>
        <span class=n>second_result</span> <span class=o>=</span> <span class=p>[]</span>
        <span class=k>for</span> <span class=n>idx</span><span class=p>,</span> <span class=n>_</span> <span class=ow>in</span> <span class=nb>enumerate</span><span class=p>(</span><span class=n>out2_details</span><span class=p>):</span>
            <span class=n>result</span> <span class=o>=</span> <span class=bp>self</span><span class=o>.</span><span class=n>second_model</span><span class=o>.</span><span class=n>get_output</span><span class=p>(</span><span class=n>idx</span><span class=p>)</span>
            <span class=n>second_result</span><span class=o>.</span><span class=n>append</span><span class=p>(</span><span class=n>result</span><span class=p>)</span>
        <span class=k>return</span> <span class=n>second_result</span>
</code></pre></div> Faster_RCNN网络中，使用了自定义的rpn层，因此将第一段网络的第1个和第2个输出以及图片信息作为rpn层的输入，第一段网络的第3个输出和rpn层的输出roi作为第二段网络的输入。 最后调用Net的convert方法，同时生成两段网络的fixed模型。</p> <div class=highlight><pre><span></span><code><span class=n>net</span> <span class=o>=</span> <span class=n>Net</span><span class=p>()</span>
<span class=n>net</span><span class=o>.</span><span class=n>convert</span><span class=p>(</span><span class=n>img_gen</span><span class=p>,</span> <span class=n>num_process</span><span class=o>=</span><span class=n>num_subsets</span><span class=p>,</span> <span class=n>fix_model</span><span class=o>=</span><span class=p>[</span><span class=n>out_main_model</span><span class=p>,</span> <span class=n>out_second_model</span><span class=p>])</span>
</code></pre></div> <hr> <h3 id=933>9.3.3 运行网络<a class=headerlink href=#933 title="Permanent link">&para;</a></h3> <p>工具路径<code>SGS_IPU_SDK/Scripts/examples/caffe_faster_rcnn/faster_rcnn_simulator.py</code> 该工具作为Faster_RCNN网络运行demo，直接运行两段网络。</p> <div class=highlight><pre><span></span><code>python3 ~/SGS_IPU_SDK/Scripts/examples/caffe_faster_rcnn/faster_rcnn_simulator.py \
-i ~/SGS_Models/resource/detection/004545.jpg \
-m0 faster_rcnn_main_float.sim \
-m1 faster_rcnn_stage2_float.sim \
-t Float
</code></pre></div> <p>运行两段模型的方法与转换网络时类似。</p> <p><div class=highlight><pre><span></span><code><span class=k>class</span> <span class=nc>Net</span><span class=p>(</span><span class=n>calibrator_custom</span><span class=o>.</span><span class=n>SIM_Simulator</span><span class=p>):</span>
    <span class=k>def</span> <span class=fm>__init__</span><span class=p>(</span><span class=bp>self</span><span class=p>,</span> <span class=n>main_model_path</span><span class=p>,</span> <span class=n>second_model_path</span><span class=p>,</span> <span class=n>phase</span><span class=p>):</span>
        <span class=nb>super</span><span class=p>()</span><span class=o>.</span><span class=fm>__init__</span><span class=p>()</span>
        <span class=k>if</span> <span class=n>phase</span> <span class=o>==</span> <span class=s1>&#39;Float&#39;</span><span class=p>:</span>
            <span class=bp>self</span><span class=o>.</span><span class=n>main_model</span> <span class=o>=</span> <span class=n>calibrator_custom</span><span class=o>.</span><span class=n>float_simulator</span><span class=p>(</span><span class=n>main_model_path</span><span class=p>)</span>
            <span class=bp>self</span><span class=o>.</span><span class=n>second_model</span> <span class=o>=</span> <span class=n>calibrator_custom</span><span class=o>.</span><span class=n>float_simulator</span><span class=p>(</span><span class=n>second_model_path</span><span class=p>)</span>
            <span class=bp>self</span><span class=o>.</span><span class=n>norm</span> <span class=o>=</span> <span class=kc>True</span>
        <span class=k>elif</span> <span class=n>phase</span> <span class=o>==</span> <span class=s1>&#39;Fixed&#39;</span><span class=p>:</span>
            <span class=bp>self</span><span class=o>.</span><span class=n>main_model</span> <span class=o>=</span> <span class=n>calibrator_custom</span><span class=o>.</span><span class=n>fixed_simulator</span><span class=p>(</span><span class=n>main_model_path</span><span class=p>)</span>
            <span class=bp>self</span><span class=o>.</span><span class=n>second_model</span> <span class=o>=</span> <span class=n>calibrator_custom</span><span class=o>.</span><span class=n>fixed_simulator</span><span class=p>(</span><span class=n>second_model_path</span><span class=p>)</span>
            <span class=bp>self</span><span class=o>.</span><span class=n>norm</span> <span class=o>=</span> <span class=kc>False</span>
        <span class=k>else</span><span class=p>:</span>
            <span class=bp>self</span><span class=o>.</span><span class=n>main_model</span> <span class=o>=</span> <span class=n>calibrator_custom</span><span class=o>.</span><span class=n>offline_simulator</span><span class=p>(</span><span class=n>main_model_path</span><span class=p>)</span>
            <span class=bp>self</span><span class=o>.</span><span class=n>second_model</span> <span class=o>=</span> <span class=n>calibrator_custom</span><span class=o>.</span><span class=n>offline_simulator</span><span class=p>(</span><span class=n>second_model_path</span><span class=p>)</span>
            <span class=bp>self</span><span class=o>.</span><span class=n>norm</span> <span class=o>=</span> <span class=kc>False</span>
        <span class=bp>self</span><span class=o>.</span><span class=n>rpn</span> <span class=o>=</span> <span class=n>rpn</span><span class=o>.</span><span class=n>ProposalLayer</span><span class=p>()</span>
    <span class=k>def</span> <span class=nf>forward</span><span class=p>(</span><span class=bp>self</span><span class=p>,</span> <span class=n>x</span><span class=p>):</span>
        <span class=c1># Run main model</span>
        <span class=n>out_details</span> <span class=o>=</span> <span class=bp>self</span><span class=o>.</span><span class=n>main_model</span><span class=o>.</span><span class=n>get_output_details</span><span class=p>()</span>
        <span class=n>input_data</span><span class=p>,</span> <span class=n>im_scale</span> <span class=o>=</span> <span class=n>fill_inputImg2main</span><span class=p>(</span><span class=n>x</span><span class=p>,</span> <span class=n>norm</span><span class=o>=</span><span class=n>norm</span><span class=p>)</span>
        <span class=bp>self</span><span class=o>.</span><span class=n>main_model</span><span class=o>.</span><span class=n>set_input</span><span class=p>(</span><span class=mi>0</span><span class=p>,</span> <span class=n>input_data</span><span class=p>)</span>
        <span class=bp>self</span><span class=o>.</span><span class=n>main_model</span><span class=o>.</span><span class=n>invoke</span><span class=p>()</span>
        <span class=k>def</span> <span class=nf>forward</span><span class=p>(</span><span class=bp>self</span><span class=p>,</span> <span class=n>x</span><span class=p>):</span>
            <span class=n>out_details</span> <span class=o>=</span> <span class=bp>self</span><span class=o>.</span><span class=n>main_model</span><span class=o>.</span><span class=n>get_output_details</span><span class=p>()</span>
            <span class=n>input_data</span><span class=p>,</span> <span class=n>im_scale</span> <span class=o>=</span> <span class=n>fill_inputImg2main</span><span class=p>(</span><span class=n>x</span><span class=p>)</span>
            <span class=bp>self</span><span class=o>.</span><span class=n>main_model</span><span class=o>.</span><span class=n>set_input</span><span class=p>(</span><span class=mi>0</span><span class=p>,</span> <span class=n>input_data</span><span class=p>)</span>
            <span class=bp>self</span><span class=o>.</span><span class=n>main_model</span><span class=o>.</span><span class=n>invoke</span><span class=p>()</span>
            <span class=n>result_list</span> <span class=o>=</span> <span class=p>[]</span>
            <span class=k>for</span> <span class=n>idx</span><span class=p>,</span> <span class=n>_</span> <span class=ow>in</span> <span class=nb>enumerate</span><span class=p>(</span><span class=n>out_details</span><span class=p>):</span>
                <span class=n>result</span> <span class=o>=</span> <span class=bp>self</span><span class=o>.</span><span class=n>main_model</span><span class=o>.</span><span class=n>get_output</span><span class=p>(</span><span class=n>idx</span><span class=p>)</span>
                <span class=n>result_list</span><span class=o>.</span><span class=n>append</span><span class=p>(</span><span class=n>result</span><span class=p>)</span>
            <span class=n>im_info</span> <span class=o>=</span> <span class=n>np</span><span class=o>.</span><span class=n>array</span><span class=p>([</span><span class=n>x</span><span class=o>.</span><span class=n>shape</span><span class=p>[</span><span class=mi>0</span><span class=p>],</span> <span class=n>x</span><span class=o>.</span><span class=n>shape</span><span class=p>[</span><span class=mi>1</span><span class=p>],</span> <span class=n>im_scale</span><span class=p>])</span><span class=o>.</span><span class=n>reshape</span><span class=p>(</span><span class=mi>1</span><span class=p>,</span> <span class=mi>3</span><span class=p>)</span>
            <span class=n>bottom</span> <span class=o>=</span> <span class=p>[</span><span class=n>result_list</span><span class=p>[</span><span class=mi>0</span><span class=p>],</span> <span class=n>result_list</span><span class=p>[</span><span class=mi>1</span><span class=p>],</span> <span class=n>im_info</span><span class=p>]</span>
            <span class=n>roi</span> <span class=o>=</span> <span class=bp>self</span><span class=o>.</span><span class=n>rpn</span><span class=o>.</span><span class=n>forward</span><span class=p>(</span><span class=n>bottom</span><span class=p>)</span>
            <span class=n>out2_details</span> <span class=o>=</span> <span class=bp>self</span><span class=o>.</span><span class=n>second_model</span><span class=o>.</span><span class=n>get_output_details</span><span class=p>()</span>
            <span class=bp>self</span><span class=o>.</span><span class=n>second_model</span><span class=o>.</span><span class=n>set_input</span><span class=p>(</span><span class=mi>0</span><span class=p>,</span> <span class=n>result_list</span><span class=p>[</span><span class=mi>2</span><span class=p>])</span>
            <span class=k>if</span> <span class=bp>self</span><span class=o>.</span><span class=n>norm</span><span class=p>:</span>
                <span class=bp>self</span><span class=o>.</span><span class=n>second_model</span><span class=o>.</span><span class=n>set_input</span><span class=p>(</span><span class=mi>1</span><span class=p>,</span> <span class=n>roi</span><span class=p>)</span>
            <span class=k>else</span><span class=p>:</span>
                <span class=bp>self</span><span class=o>.</span><span class=n>second_model</span><span class=o>.</span><span class=n>set_input</span><span class=p>(</span><span class=mi>1</span><span class=p>,</span> <span class=n>roi</span><span class=p>)</span>
            <span class=bp>self</span><span class=o>.</span><span class=n>second_model</span><span class=o>.</span><span class=n>invoke</span><span class=p>()</span>
            <span class=n>second_result</span> <span class=o>=</span> <span class=p>[]</span>
            <span class=k>for</span> <span class=n>idx</span><span class=p>,</span> <span class=n>_</span> <span class=ow>in</span> <span class=nb>enumerate</span><span class=p>(</span><span class=n>out2_details</span><span class=p>):</span>
                <span class=n>result</span> <span class=o>=</span> <span class=bp>self</span><span class=o>.</span><span class=n>second_model</span><span class=o>.</span><span class=n>get_output</span><span class=p>(</span><span class=n>idx</span><span class=p>)</span>
                <span class=n>second_result</span><span class=o>.</span><span class=n>append</span><span class=p>(</span><span class=n>result</span><span class=p>)</span>
            <span class=k>return</span> <span class=n>second_result</span>
</code></pre></div> 运行网络时先创建Net的实例，然后调用自身的方法就可以运行。具体参考5.2.2节。</p> <div class=highlight><pre><span></span><code><span class=n>net</span> <span class=o>=</span> <span class=n>Net</span><span class=p>()</span>
<span class=n>results</span> <span class=o>=</span> <span class=n>net</span><span class=p>(</span><span class=n>img_gen</span><span class=p>,</span> <span class=n>num_process</span><span class=o>=</span><span class=n>num_subsets</span><span class=p>)</span>
</code></pre></div> <hr> <h2 id=94>9.4. 多输入网络转换要点<a class=headerlink href=#94 title="Permanent link">&para;</a></h2> <h3 id=941>9.4.1 构建多输入网络<a class=headerlink href=#941 title="Permanent link">&para;</a></h3> <p>使用Tensorflow1.14.0构建一个双输入网络，该网络用来做矩阵乘法，计算两个向量的内积。</p> <p><div class=highlight><pre><span></span><code><span class=kn>import</span> <span class=nn>tensorflow</span> <span class=k>as</span> <span class=nn>tf</span>
<span class=kn>import</span> <span class=nn>numpy</span> <span class=k>as</span> <span class=nn>np</span>

<span class=n>SHAPE_DIMS</span> <span class=o>=</span> <span class=mi>256</span>

<span class=n>input_np0</span> <span class=o>=</span> <span class=n>np</span><span class=o>.</span><span class=n>random</span><span class=o>.</span><span class=n>rand</span><span class=p>(</span><span class=mi>1</span><span class=p>,</span> <span class=n>SHAPE_DIMS</span><span class=p>)</span><span class=o>.</span><span class=n>astype</span><span class=p>(</span><span class=n>np</span><span class=o>.</span><span class=n>float32</span><span class=p>)</span>
<span class=n>input_np1</span> <span class=o>=</span> <span class=n>np</span><span class=o>.</span><span class=n>random</span><span class=o>.</span><span class=n>rand</span><span class=p>(</span><span class=mi>1</span><span class=p>,</span> <span class=n>SHAPE_DIMS</span><span class=p>)</span><span class=o>.</span><span class=n>astype</span><span class=p>(</span><span class=n>np</span><span class=o>.</span><span class=n>float32</span><span class=p>)</span>

<span class=n>input_0</span> <span class=o>=</span> <span class=n>tf</span><span class=o>.</span><span class=n>placeholder</span><span class=p>(</span><span class=n>dtype</span><span class=o>=</span><span class=n>tf</span><span class=o>.</span><span class=n>float32</span><span class=p>,</span> <span class=n>shape</span><span class=o>=</span><span class=p>(</span><span class=mi>1</span><span class=p>,</span> <span class=n>SHAPE_DIMS</span><span class=p>),</span> <span class=n>name</span><span class=o>=</span><span class=s1>&#39;input0&#39;</span><span class=p>)</span>
<span class=n>input_1</span> <span class=o>=</span> <span class=n>tf</span><span class=o>.</span><span class=n>placeholder</span><span class=p>(</span><span class=n>dtype</span><span class=o>=</span><span class=n>tf</span><span class=o>.</span><span class=n>float32</span><span class=p>,</span> <span class=n>shape</span><span class=o>=</span><span class=p>(</span><span class=mi>1</span><span class=p>,</span> <span class=n>SHAPE_DIMS</span><span class=p>),</span> <span class=n>name</span><span class=o>=</span><span class=s1>&#39;input1&#39;</span><span class=p>)</span>
<span class=n>out</span> <span class=o>=</span> <span class=n>tf</span><span class=o>.</span><span class=n>matmul</span><span class=p>(</span><span class=n>input_0</span><span class=p>,</span> <span class=n>tf</span><span class=o>.</span><span class=n>reshape</span><span class=p>(</span><span class=n>input_1</span><span class=p>,</span> <span class=p>(</span><span class=n>SHAPE_DIMS</span><span class=p>,</span> <span class=mi>1</span><span class=p>)),</span> <span class=n>name</span><span class=o>=</span><span class=s1>&#39;output&#39;</span><span class=p>)</span>

<span class=k>with</span> <span class=n>tf</span><span class=o>.</span><span class=n>Session</span><span class=p>()</span> <span class=k>as</span> <span class=n>sess</span><span class=p>:</span>
    <span class=n>results</span> <span class=o>=</span> <span class=n>sess</span><span class=o>.</span><span class=n>run</span><span class=p>(</span><span class=n>out</span><span class=p>,</span> <span class=n>feed_dict</span><span class=o>=</span><span class=p>{</span><span class=n>input_0</span><span class=p>:</span> <span class=n>input_np0</span><span class=p>,</span> <span class=n>input_1</span><span class=p>:</span> <span class=n>input_np1</span><span class=p>})</span>

    <span class=c1># Convert .pb</span>
    <span class=n>output_graph_def</span> <span class=o>=</span> <span class=n>tf</span><span class=o>.</span><span class=n>graph_util</span><span class=o>.</span><span class=n>convert_variables_to_constants</span><span class=p>(</span>
        <span class=n>sess</span><span class=p>,</span>
        <span class=n>sess</span><span class=o>.</span><span class=n>graph_def</span><span class=p>,</span>
        <span class=p>[</span><span class=s1>&#39;output&#39;</span><span class=p>]</span>
    <span class=p>)</span>
    <span class=k>with</span> <span class=n>tf</span><span class=o>.</span><span class=n>gfile</span><span class=o>.</span><span class=n>GFile</span><span class=p>(</span><span class=s1>&#39;matmul.pb&#39;</span><span class=p>,</span> <span class=s1>&#39;wb&#39;</span><span class=p>)</span> <span class=k>as</span> <span class=n>f</span><span class=p>:</span>
        <span class=n>f</span><span class=o>.</span><span class=n>write</span><span class=p>(</span><span class=n>output_graph_def</span><span class=o>.</span><span class=n>SerializeToString</span><span class=p>())</span>
    <span class=nb>print</span><span class=p>(</span><span class=s1>&#39;matmul.pb Saved!&#39;</span><span class=p>)</span>

<span class=c1># convert to .tflite</span>
<span class=n>tflite_model</span> <span class=o>=</span> <span class=n>tf</span><span class=o>.</span><span class=n>lite</span><span class=o>.</span><span class=n>TFLiteConverter</span><span class=o>.</span><span class=n>from_frozen_graph</span><span class=p>(</span>
    <span class=s1>&#39;matmul.pb&#39;</span><span class=p>,</span>
    <span class=p>[</span><span class=s1>&#39;input0&#39;</span><span class=p>,</span> <span class=s1>&#39;input1&#39;</span><span class=p>],</span>
    <span class=p>[</span><span class=s1>&#39;output&#39;</span><span class=p>]</span>
<span class=p>)</span>
<span class=n>tflite_model</span> <span class=o>=</span> <span class=n>tflite_model</span><span class=o>.</span><span class=n>convert</span><span class=p>()</span>
<span class=k>with</span> <span class=nb>open</span><span class=p>(</span><span class=s1>&#39;matmul.tflite&#39;</span><span class=p>,</span> <span class=s1>&#39;wb&#39;</span><span class=p>)</span> <span class=k>as</span> <span class=n>f</span><span class=p>:</span>
    <span class=n>f</span><span class=o>.</span><span class=n>write</span><span class=p>(</span><span class=n>tflite_model</span><span class=p>)</span>
<span class=nb>print</span><span class=p>(</span><span class=s1>&#39;matmul.tflite Saved!&#39;</span><span class=p>)</span>
</code></pre></div> 运行该脚本，就能生成matmul.tflite模型文件了。</p> <p><img alt src=mymedia/9_5_2_matmul_tflite.png></p> <hr> <h3 id=942>9.4.2 转换多输入网络<a class=headerlink href=#942 title="Permanent link">&para;</a></h3> <p>下面将matmul.tflte转换成可以在板上运行的模型文件。 matmul.tflite模型不是图片输入，因此配置input_config.ini文件请参考9.2节。 配置input_config.ini脚本</p> <div class=highlight><pre><span></span><code><span class=k>[INPUT_CONFIG]</span>
<span class=na>inputs</span><span class=o>=</span><span class=s>input0,input1;</span>
<span class=na>input_formats</span><span class=o>=</span><span class=s>RAWDATA_F32_NHWC,RAWDATA_F32_NHWC;</span>
<span class=na>quantization</span><span class=o>=</span><span class=s>TRUE,TRUE;</span>

<span class=k>[OUTPUT_CONFIG]</span>
<span class=na>outputs</span><span class=o>=</span><span class=s>output;</span>
<span class=na>dequantization</span><span class=o>=</span><span class=s>TRUE;</span>
</code></pre></div> <p>请先在SGS_IPU_SDK⽬录下运⾏以下脚本，输出Library的路径（已经做过该步骤可忽略）： <div class=highlight><pre><span></span><code>cd ~/SGS_IPU_SDK
source cfg_env.sh
</code></pre></div> 然后使用ConvertTool工具转换matmul.tflite： <div class=highlight><pre><span></span><code>python3 ~/SGS_IPU_SDK/Scripts/ConvertTool/ConvertTool.py tflite \
--model_file /path/to/matmul.tflite \
--input_config /path/to/input_config.ini \
--output_file /path/to/matmul_float.sim
</code></pre></div></p> <p>多输入网络的calibrator过程需要使用calibrator_custom模块，详细可以参考3.4节。但与3.4.2节中不同的是输入的生成器需要配置成双输入。将以下示例文件保存为matmul_calibrator.py。</p> <div class=highlight><pre><span></span><code><span class=c1># -*- coding: utf-8 -*-</span>

<span class=kn>import</span> <span class=nn>calibrator_custom</span>
<span class=kn>import</span> <span class=nn>os</span>
<span class=kn>import</span> <span class=nn>sys</span>
<span class=kn>import</span> <span class=nn>numpy</span> <span class=k>as</span> <span class=nn>np</span>
<span class=kn>import</span> <span class=nn>argparse</span>
<span class=kn>from</span> <span class=nn>calibrator_custom</span> <span class=kn>import</span> <span class=n>utils</span>

<span class=k>class</span> <span class=nc>Net</span><span class=p>(</span><span class=n>calibrator_custom</span><span class=o>.</span><span class=n>SIM_Calibrator</span><span class=p>):</span>
    <span class=k>def</span> <span class=fm>__init__</span><span class=p>(</span><span class=bp>self</span><span class=p>,</span> <span class=n>model_path</span><span class=p>,</span> <span class=n>input_config</span><span class=p>):</span>
        <span class=nb>super</span><span class=p>()</span><span class=o>.</span><span class=fm>__init__</span><span class=p>()</span>
        <span class=bp>self</span><span class=o>.</span><span class=n>model</span> <span class=o>=</span> <span class=n>calibrator_custom</span><span class=o>.</span><span class=n>calibrator</span><span class=p>(</span><span class=n>model_path</span><span class=p>,</span> <span class=n>input_config</span><span class=p>)</span>

    <span class=k>def</span> <span class=nf>forward</span><span class=p>(</span><span class=bp>self</span><span class=p>,</span> <span class=n>x</span><span class=p>,</span> <span class=n>y</span><span class=p>):</span>
        <span class=n>out_details</span> <span class=o>=</span> <span class=bp>self</span><span class=o>.</span><span class=n>model</span><span class=o>.</span><span class=n>get_output_details</span><span class=p>()</span>
        <span class=bp>self</span><span class=o>.</span><span class=n>model</span><span class=o>.</span><span class=n>set_input</span><span class=p>(</span><span class=mi>0</span><span class=p>,</span> <span class=n>x</span><span class=p>)</span>
        <span class=bp>self</span><span class=o>.</span><span class=n>model</span><span class=o>.</span><span class=n>set_input</span><span class=p>(</span><span class=mi>1</span><span class=p>,</span> <span class=n>y</span><span class=p>)</span>
        <span class=bp>self</span><span class=o>.</span><span class=n>model</span><span class=o>.</span><span class=n>invoke</span><span class=p>()</span>
        <span class=n>result_list</span> <span class=o>=</span> <span class=p>[]</span>
        <span class=k>for</span> <span class=n>idx</span> <span class=ow>in</span> <span class=nb>range</span><span class=p>(</span><span class=nb>len</span><span class=p>(</span><span class=n>out_details</span><span class=p>)):</span>
            <span class=n>result</span> <span class=o>=</span> <span class=bp>self</span><span class=o>.</span><span class=n>model</span><span class=o>.</span><span class=n>get_output</span><span class=p>(</span><span class=n>idx</span><span class=p>)</span>
            <span class=n>result_list</span><span class=o>.</span><span class=n>append</span><span class=p>(</span><span class=n>result</span><span class=p>)</span>
        <span class=k>return</span> <span class=n>result_list</span>

<span class=k>def</span> <span class=nf>arg_parse</span><span class=p>():</span>
    <span class=n>parser</span> <span class=o>=</span> <span class=n>argparse</span><span class=o>.</span><span class=n>ArgumentParser</span><span class=p>(</span><span class=n>description</span><span class=o>=</span><span class=s1>&#39;Calibrator Tool&#39;</span><span class=p>)</span>
    <span class=n>parser</span><span class=o>.</span><span class=n>add_argument</span><span class=p>(</span><span class=s1>&#39;-m&#39;</span><span class=p>,</span> <span class=s1>&#39;--model&#39;</span><span class=p>,</span> <span class=nb>type</span><span class=o>=</span><span class=nb>str</span><span class=p>,</span> <span class=n>required</span><span class=o>=</span><span class=kc>True</span><span class=p>,</span>
                        <span class=n>help</span><span class=o>=</span><span class=s1>&#39;Model path.&#39;</span><span class=p>)</span>
    <span class=n>parser</span><span class=o>.</span><span class=n>add_argument</span><span class=p>(</span><span class=s1>&#39;--input_config&#39;</span><span class=p>,</span> <span class=nb>type</span><span class=o>=</span><span class=nb>str</span><span class=p>,</span> <span class=n>required</span><span class=o>=</span><span class=kc>True</span><span class=p>,</span>
                        <span class=n>help</span><span class=o>=</span><span class=s1>&#39;Input config path.&#39;</span><span class=p>)</span>
    <span class=n>parser</span><span class=o>.</span><span class=n>add_argument</span><span class=p>(</span><span class=s1>&#39;--quant_level&#39;</span><span class=p>,</span> <span class=nb>type</span><span class=o>=</span><span class=nb>str</span><span class=p>,</span> <span class=n>default</span><span class=o>=</span><span class=s1>&#39;L5&#39;</span><span class=p>,</span>
                        <span class=n>choices</span><span class=o>=</span><span class=p>[</span><span class=s1>&#39;L1&#39;</span><span class=p>,</span> <span class=s1>&#39;L2&#39;</span><span class=p>,</span> <span class=s1>&#39;L3&#39;</span><span class=p>,</span> <span class=s1>&#39;L4&#39;</span><span class=p>,</span> <span class=s1>&#39;L5&#39;</span><span class=p>],</span>
                        <span class=n>help</span><span class=o>=</span><span class=s1>&#39;Indicate Quantilization level. The higher the level,</span><span class=se>\</span>
<span class=s1>                        the slower the speed and the higher the accuracy.&#39;</span><span class=p>)</span>
    <span class=n>parser</span><span class=o>.</span><span class=n>add_argument</span><span class=p>(</span><span class=s1>&#39;--num_process&#39;</span><span class=p>,</span> <span class=n>default</span><span class=o>=</span><span class=mi>10</span><span class=p>,</span> <span class=nb>type</span><span class=o>=</span><span class=nb>int</span><span class=p>,</span>
                        <span class=n>help</span><span class=o>=</span><span class=s1>&#39;Amount of processes run at same time.&#39;</span><span class=p>)</span>
    <span class=n>parser</span><span class=o>.</span><span class=n>add_argument</span><span class=p>(</span><span class=s1>&#39;-o&#39;</span><span class=p>,</span> <span class=s1>&#39;--output&#39;</span><span class=p>,</span> <span class=n>default</span><span class=o>=</span><span class=kc>None</span><span class=p>,</span> <span class=nb>type</span><span class=o>=</span><span class=nb>str</span><span class=p>,</span>
                        <span class=n>help</span><span class=o>=</span><span class=s1>&#39;Output path for fixed model.&#39;</span><span class=p>)</span>

    <span class=k>return</span> <span class=n>parser</span><span class=o>.</span><span class=n>parse_args</span><span class=p>()</span>

<span class=k>def</span> <span class=nf>data_gen</span><span class=p>():</span>
    <span class=n>calibrator_data</span> <span class=o>=</span> <span class=n>np</span><span class=o>.</span><span class=n>random</span><span class=o>.</span><span class=n>rand</span><span class=p>(</span><span class=mi>100</span><span class=p>,</span> <span class=mi>1</span><span class=p>,</span> <span class=mi>256</span><span class=p>)</span><span class=o>.</span><span class=n>astype</span><span class=p>(</span><span class=n>np</span><span class=o>.</span><span class=n>float32</span><span class=p>)</span>
    <span class=n>bench_data</span> <span class=o>=</span> <span class=n>np</span><span class=o>.</span><span class=n>random</span><span class=o>.</span><span class=n>rand</span><span class=p>(</span><span class=mi>100</span><span class=p>,</span> <span class=mi>1</span><span class=p>,</span> <span class=mi>256</span><span class=p>)</span><span class=o>.</span><span class=n>astype</span><span class=p>(</span><span class=n>np</span><span class=o>.</span><span class=n>float32</span><span class=p>)</span>
    <span class=k>for</span> <span class=n>i</span><span class=p>,</span> <span class=n>j</span> <span class=ow>in</span> <span class=nb>zip</span><span class=p>(</span><span class=n>calibrator_data</span><span class=p>,</span> <span class=n>bench_data</span><span class=p>):</span>
        <span class=k>yield</span> <span class=p>[</span><span class=n>i</span><span class=p>,</span> <span class=n>j</span><span class=p>]</span>

<span class=k>def</span> <span class=nf>main</span><span class=p>():</span>
    <span class=n>args</span> <span class=o>=</span> <span class=n>arg_parse</span><span class=p>()</span>
    <span class=n>model_path</span> <span class=o>=</span> <span class=n>args</span><span class=o>.</span><span class=n>model</span>
    <span class=n>input_config</span> <span class=o>=</span> <span class=n>args</span><span class=o>.</span><span class=n>input_config</span>
    <span class=n>quant_level</span> <span class=o>=</span> <span class=n>args</span><span class=o>.</span><span class=n>quant_level</span>
    <span class=n>num_subsets</span> <span class=o>=</span> <span class=n>args</span><span class=o>.</span><span class=n>num_process</span>
    <span class=n>output</span> <span class=o>=</span> <span class=n>args</span><span class=o>.</span><span class=n>output</span>

    <span class=k>if</span> <span class=ow>not</span> <span class=n>os</span><span class=o>.</span><span class=n>path</span><span class=o>.</span><span class=n>exists</span><span class=p>(</span><span class=n>model_path</span><span class=p>):</span>
        <span class=k>raise</span> <span class=ne>FileNotFoundError</span><span class=p>(</span><span class=s1>&#39;No such </span><span class=si>{}</span><span class=s1> model&#39;</span><span class=o>.</span><span class=n>format</span><span class=p>(</span><span class=n>model_path</span><span class=p>))</span>

    <span class=k>if</span> <span class=ow>not</span> <span class=n>os</span><span class=o>.</span><span class=n>path</span><span class=o>.</span><span class=n>exists</span><span class=p>(</span><span class=n>input_config</span><span class=p>):</span>
        <span class=k>raise</span> <span class=ne>FileNotFoundError</span><span class=p>(</span><span class=s1>&#39;input_config.ini file not found.&#39;</span><span class=p>)</span>

    <span class=n>net</span> <span class=o>=</span> <span class=n>Net</span><span class=p>(</span><span class=n>model_path</span><span class=p>,</span> <span class=n>input_config</span><span class=p>)</span>
    <span class=nb>print</span><span class=p>(</span><span class=n>net</span><span class=p>)</span>

    <span class=c1># random generate data</span>
    <span class=c1># must change real data when using</span>
    <span class=n>img_gen</span> <span class=o>=</span> <span class=n>data_gen</span><span class=p>()</span>
    <span class=nb>print</span><span class=p>(</span><span class=s1>&#39;</span><span class=se>\033</span><span class=s1>[31m[WARNING] random generate data,</span><span class=se>\</span>
<span class=s1>          must change real data when using!</span><span class=se>\033</span><span class=s1>[0m&#39;</span><span class=p>,</span>
          <span class=n>file</span><span class=o>=</span><span class=n>sys</span><span class=o>.</span><span class=n>stderr</span><span class=p>)</span>

    <span class=n>out_model</span> <span class=o>=</span> <span class=n>utils</span><span class=o>.</span><span class=n>get_out_model_name</span><span class=p>(</span><span class=n>model_path</span><span class=p>,</span> <span class=n>output</span><span class=p>)</span>
    <span class=n>net</span><span class=o>.</span><span class=n>convert</span><span class=p>(</span><span class=n>img_gen</span><span class=p>,</span> <span class=n>num_process</span><span class=o>=</span><span class=n>num_subsets</span><span class=p>,</span>
                <span class=n>quant_level</span><span class=o>=</span><span class=n>quant_level</span><span class=p>,</span> <span class=n>fix_model</span><span class=o>=</span><span class=p>[</span><span class=n>out_model</span><span class=p>])</span>

<span class=k>if</span> <span class=vm>__name__</span> <span class=o>==</span> <span class=s1>&#39;__main__&#39;</span><span class=p>:</span>
    <span class=n>main</span><span class=p>()</span>
</code></pre></div> <p>使用编写的matmul_calibrator.py转换matmul_float.sim，生成matmul_fixed.sim。</p> <div class=highlight><pre><span></span><code>python3 matmul_calibrator.py \
-m /path/to/matmul_float.sim \
--input_config /path/to/input_config.ini
</code></pre></div> <p>需要注意的是，量化该模型的输入数据一定要换成真实数据，不然统计生成的fixed模型精度会出错。如果单独量化该模型比较困难，比如它是接在某个网络后面运行的，可以参考9.4节，按照多段网络处理，在calibrator_custom.SIM_Calibrator类里定义多个模型，可以直接生成出多个fixed网络。 最后使用compiler.py转换matmul_fixed.sim，生成matmul_fixed.sim_sgsimg.img。</p> <hr> <h3 id=943-pc>9.4.3 在PC上运行多输入网络<a class=headerlink href=#943-pc title="Permanent link">&para;</a></h3> <p>需要注意的是，运行Float的网络时，输入数据的type需要为float32，输入数据的shape与模型输入shape相同。但是由于matmul的Fixed和Offline模型是RAWDATA_S16_NHWC输入，输入数据需要先反量化到int16，然后对齐后才能输入到模型。所以定义calibrator_custom.SIM_Simulator的forward函数时需要注意该模型的特殊性：</p> <div class=highlight><pre><span></span><code><span class=k>class</span> <span class=nc>Net</span><span class=p>(</span><span class=n>calibrator_custom</span><span class=o>.</span><span class=n>SIM_Simulator</span><span class=p>):</span>
    <span class=k>def</span> <span class=fm>__init__</span><span class=p>(</span><span class=bp>self</span><span class=p>,</span> <span class=n>model_path</span><span class=p>,</span> <span class=n>phase</span><span class=p>):</span>
        <span class=nb>super</span><span class=p>()</span><span class=o>.</span><span class=fm>__init__</span><span class=p>()</span>
        <span class=k>if</span> <span class=n>phase</span> <span class=o>==</span> <span class=s1>&#39;Float&#39;</span><span class=p>:</span>
            <span class=bp>self</span><span class=o>.</span><span class=n>model</span> <span class=o>=</span> <span class=n>calibrator_custom</span><span class=o>.</span><span class=n>float_simulator</span><span class=p>(</span><span class=n>model_path</span><span class=p>)</span>
        <span class=k>elif</span> <span class=n>phase</span> <span class=o>==</span> <span class=s1>&#39;Fixed&#39;</span><span class=p>:</span>
            <span class=bp>self</span><span class=o>.</span><span class=n>model</span> <span class=o>=</span> <span class=n>calibrator_custom</span><span class=o>.</span><span class=n>fixed_simulator</span><span class=p>(</span><span class=n>model_path</span><span class=p>)</span>
        <span class=k>else</span><span class=p>:</span>
            <span class=bp>self</span><span class=o>.</span><span class=n>model</span> <span class=o>=</span> <span class=n>calibrator_custom</span><span class=o>.</span><span class=n>offline_simulator</span><span class=p>(</span><span class=n>model_path</span><span class=p>)</span>

    <span class=k>def</span> <span class=nf>forward</span><span class=p>(</span><span class=bp>self</span><span class=p>,</span> <span class=n>x</span><span class=p>,</span> <span class=n>y</span><span class=p>):</span>
        <span class=n>out_details</span> <span class=o>=</span> <span class=bp>self</span><span class=o>.</span><span class=n>model</span><span class=o>.</span><span class=n>get_output_details</span><span class=p>()</span>
        <span class=bp>self</span><span class=o>.</span><span class=n>model</span><span class=o>.</span><span class=n>set_input</span><span class=p>(</span><span class=mi>0</span><span class=p>,</span> <span class=n>x</span><span class=p>)</span>
        <span class=bp>self</span><span class=o>.</span><span class=n>model</span><span class=o>.</span><span class=n>set_input</span><span class=p>(</span><span class=mi>1</span><span class=p>,</span> <span class=n>y</span><span class=p>)</span>
        <span class=bp>self</span><span class=o>.</span><span class=n>model</span><span class=o>.</span><span class=n>invoke</span><span class=p>()</span>
        <span class=n>result_list</span> <span class=o>=</span> <span class=p>[]</span>
        <span class=k>for</span> <span class=n>idx</span><span class=p>,</span> <span class=n>_</span> <span class=ow>in</span> <span class=nb>enumerate</span><span class=p>(</span><span class=n>out_details</span><span class=p>):</span>
            <span class=n>result</span> <span class=o>=</span> <span class=bp>self</span><span class=o>.</span><span class=n>model</span><span class=o>.</span><span class=n>get_output</span><span class=p>(</span><span class=n>idx</span><span class=p>)</span>
            <span class=n>result_list</span><span class=o>.</span><span class=n>append</span><span class=p>(</span><span class=n>result</span><span class=p>)</span>
        <span class=k>return</span> <span class=n>result_list</span>
</code></pre></div> <hr> <h3 id=944>9.4.4 在板上运行多输入网络<a class=headerlink href=#944 title="Permanent link">&para;</a></h3> <p>由于matmul_fixed.sim_sgsimg.img网络输入是<code>RAWDATA_F32_NHWC</code>，可以参考9.2.2节中FillInputData函数，与在PC上相同，需要将输入数据处理后再送入模型。 多输入模型只需在FillInputData函数中增加对模型的第二个输入灌入数据：</p> <div class=highlight><pre><span></span><code><span class=c1>// input0</span>
<span class=n>I_FLOAT</span><span class=o>*</span> <span class=n>pData0</span> <span class=o>=</span> <span class=p>(</span><span class=n>MI_FLOAT</span><span class=o>*</span><span class=p>)</span><span class=n>InputTensorVector</span><span class=p>.</span><span class=n>astArrayTensors</span><span class=p>[</span><span class=mi>0</span><span class=p>].</span><span class=n>ptTensorData</span><span class=p>[</span><span class=mi>0</span><span class=p>];</span>
<span class=c1>// input1</span>
<span class=n>I_FLOAT</span><span class=o>*</span> <span class=n>pData1</span> <span class=o>=</span> <span class=p>(</span><span class=n>MI_FLOAT</span><span class=o>*</span><span class=p>)</span><span class=n>InputTensorVector</span><span class=p>.</span><span class=n>astArrayTensors</span><span class=p>[</span><span class=mi>1</span><span class=p>].</span><span class=n>ptTensorData</span><span class=p>[</span><span class=mi>0</span><span class=p>];</span>
</code></pre></div> </article> </div> </div> </main> <footer class=md-footer> <nav class="md-footer__inner md-grid" aria-label=Footer> <a href=../Common/Adding_A_New_Layer.html class="md-footer__link md-footer__link--prev" rel=prev> <div class="md-footer__button md-icon"> <svg xmlns=http://www.w3.org/2000/svg viewbox="0 0 24 24"><path d="M20 11v2H8l5.5 5.5-1.42 1.42L4.16 12l7.92-7.92L13.5 5.5 8 11h12z"/></svg> </div> <div class=md-footer__title> <div class=md-ellipsis> <span class=md-footer__direction> Previous </span> 8. 如何添加新的Layer </div> </div> </a> <a href=DLA_SDK_Support.html class="md-footer__link md-footer__link--next" rel=next> <div class=md-footer__title> <div class=md-ellipsis> <span class=md-footer__direction> Next </span> 10. DLA SDK 支持 </div> </div> <div class="md-footer__button md-icon"> <svg xmlns=http://www.w3.org/2000/svg viewbox="0 0 24 24"><path d="M4 11v2h12l-5.5 5.5 1.42 1.42L19.84 12l-7.92-7.92L10.5 5.5 16 11H4z"/></svg> </div> </a> </nav> <div class="md-footer-meta md-typeset"> <div class="md-footer-meta__inner md-grid"> <div class=md-footer-copyright> <div class=md-footer-copyright__highlight> Copyright&copy; 2021 SigmaStar Technology. All rights reserved. Security Level: Confidential A. </div> Made with <a href=https://squidfunk.github.io/mkdocs-material/ target=_blank rel=noopener> Material for MkDocs </a> </div> </div> </div> </footer> </div> <div class=md-dialog data-md-component=dialog> <div class="md-dialog__inner md-typeset"></div> </div> <script id=__config type=application/json>{"base": "../../..", "features": [], "translations": {"clipboard.copy": "Copy to clipboard", "clipboard.copied": "Copied to clipboard", "search.config.lang": "en", "search.config.pipeline": "trimmer, stopWordFilter", "search.config.separator": "[\\s\\-]+", "search.placeholder": "Search", "search.result.placeholder": "Type to start searching", "search.result.none": "No matching documents", "search.result.one": "1 matching document", "search.result.other": "# matching documents", "search.result.more.one": "1 more on this page", "search.result.more.other": "# more on this page", "search.result.term.missing": "Missing"}, "search": "../../../assets/javascripts/workers/search.fb4a9340.min.js", "version": null}</script> <script src=../../../assets/javascripts/bundle.a1c7c35e.min.js></script> <script src=../../../search/search_index.js></script> <script src=../../../javascripts/extra.js></script> <script src="https://cdnjs.cloudflare.com/ajax/libs/mathjax/2.7.0/MathJax.js?config=TeX-MML-AM_CHTML"></script> <script src=https://cdnjs.cloudflare.com/ajax/libs/raphael/2.2.7/raphael.min.js></script> <script src=https://cdnjs.cloudflare.com/ajax/libs/underscore.js/1.8.3/underscore-min.js></script> <script src=https://cdnjs.cloudflare.com/ajax/libs/js-sequence-diagrams/1.0.6/sequence-diagram-min.js></script> <script src=https://cdnjs.cloudflare.com/ajax/libs/flowchart/1.6.5/flowchart.min.js></script> <script src=https://unpkg.com/freezeframe/dist/freezeframe.min.js></script> <script src=https://unpkg.com/mermaid@7.1.0/dist/mermaid.min.js></script> <script src=../../../javascripts/umlconvert.js></script> </body> </html>