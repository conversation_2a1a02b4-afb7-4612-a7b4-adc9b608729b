<!doctype html><html lang=en class=no-js> <head><meta charset=utf-8><meta name=viewport content="width=device-width,initial-scale=1"><meta name=description content="Personal Documentation System Template"><meta name=author content="<PERSON> Cho<PERSON>"><link rel=icon href=../../../images/favicon.ico><meta name=generator content="mkdocs-1.1.2, mkdocs-material-7.0.6"><title>10. DLA SDK 支持 - IPU SDK</title><link rel=stylesheet href=../../../assets/stylesheets/main.2c0c5eaf.min.css><link rel=stylesheet href=../../../assets/stylesheets/palette.7fa14f5b.min.css><meta name=theme-color content=#009485><link rel=preconnect href=https://fonts.gstatic.com crossorigin><link rel=stylesheet href="https://fonts.googleapis.com/css?family=Roboto:300,400,400i,700%7CRoboto+Mono&display=fallback"><style>:root{--md-text-font-family:"Roboto";--md-code-font-family:"Roboto Mono"}</style><link rel=stylesheet href=../../../stylesheets/extra.css></head> <body dir=ltr data-md-color-scheme data-md-color-primary=teal data-md-color-accent=teal> <input class=md-toggle data-md-toggle=drawer type=checkbox id=__drawer autocomplete=off> <input class=md-toggle data-md-toggle=search type=checkbox id=__search autocomplete=off> <label class=md-overlay for=__drawer></label> <div data-md-component=skip> <a href=#101-caffe class=md-skip> Skip to content </a> </div> <div data-md-component=announce> </div> <header class=md-header data-md-component=header> <nav class="md-header__inner md-grid" aria-label=Header> <a href=../../.. title="IPU SDK" class="md-header__button md-logo" aria-label="IPU SDK" data-md-component=logo> <svg xmlns=http://www.w3.org/2000/svg viewbox="0 0 24 24"><path d="M10 20v-6h4v6h5v-8h3L12 3 2 12h3v8h5z"/></svg> </a> <label class="md-header__button md-icon" for=__drawer> <svg xmlns=http://www.w3.org/2000/svg viewbox="0 0 24 24"><path d="M3 6h18v2H3V6m0 5h18v2H3v-2m0 5h18v2H3v-2z"/></svg> </label> <div class=md-header__title data-md-component=header-title> <div class=md-header__ellipsis> <div class=md-header__topic> <span class=md-ellipsis> IPU SDK </span> </div> <div class=md-header__topic data-md-component=header-topic> <span class=md-ellipsis> 10. DLA SDK 支持 </span> </div> </div> </div> <div class=md-header__options> </div> <label class="md-header__button md-icon" for=__search> <svg xmlns=http://www.w3.org/2000/svg viewbox="0 0 24 24"><path d="M9.5 3A6.5 6.5 0 0 1 16 9.5c0 1.61-.59 3.09-1.56 4.23l.27.27h.79l5 5-1.5 1.5-5-5v-.79l-.27-.27A6.516 6.516 0 0 1 9.5 16 6.5 6.5 0 0 1 3 9.5 6.5 6.5 0 0 1 9.5 3m0 2C7 5 5 7 5 9.5S7 14 9.5 14 14 12 14 9.5 12 5 9.5 5z"/></svg> </label> <div class=md-search data-md-component=search role=dialog> <label class=md-search__overlay for=__search></label> <div class=md-search__inner role=search> <form class=md-search__form name=search> <input type=text class=md-search__input name=query aria-label=Search placeholder=Search autocapitalize=off autocorrect=off autocomplete=off spellcheck=false data-md-component=search-query data-md-state=active required> <label class="md-search__icon md-icon" for=__search> <svg xmlns=http://www.w3.org/2000/svg viewbox="0 0 24 24"><path d="M9.5 3A6.5 6.5 0 0 1 16 9.5c0 1.61-.59 3.09-1.56 4.23l.27.27h.79l5 5-1.5 1.5-5-5v-.79l-.27-.27A6.516 6.516 0 0 1 9.5 16 6.5 6.5 0 0 1 3 9.5 6.5 6.5 0 0 1 9.5 3m0 2C7 5 5 7 5 9.5S7 14 9.5 14 14 12 14 9.5 12 5 9.5 5z"/></svg> <svg xmlns=http://www.w3.org/2000/svg viewbox="0 0 24 24"><path d="M20 11v2H8l5.5 5.5-1.42 1.42L4.16 12l7.92-7.92L13.5 5.5 8 11h12z"/></svg> </label> <button type=reset class="md-search__icon md-icon" aria-label=Clear tabindex=-1> <svg xmlns=http://www.w3.org/2000/svg viewbox="0 0 24 24"><path d="M19 6.41L17.59 5 12 10.59 6.41 5 5 6.41 10.59 12 5 17.59 6.41 19 12 13.41 17.59 19 19 17.59 13.41 12 19 6.41z"/></svg> </button> </form> <div class=md-search__output> <div class=md-search__scrollwrap data-md-scrollfix> <div class=md-search-result data-md-component=search-result> <div class=md-search-result__meta> Initializing search </div> <ol class=md-search-result__list></ol> </div> </div> </div> </div> </div> </nav> </header> <div class=md-container data-md-component=container> <main class=md-main data-md-component=main> <div class="md-main__inner md-grid"> <div class="md-sidebar md-sidebar--primary" data-md-component=sidebar data-md-type=navigation> <div class=md-sidebar__scrollwrap> <div class=md-sidebar__inner> <nav class="md-nav md-nav--primary" aria-label=Navigation data-md-level=0> <label class=md-nav__title for=__drawer> <a href=../../.. title="IPU SDK" class="md-nav__button md-logo" aria-label="IPU SDK" data-md-component=logo> <svg xmlns=http://www.w3.org/2000/svg viewbox="0 0 24 24"><path d="M10 20v-6h4v6h5v-8h3L12 3 2 12h3v8h5z"/></svg> </a> IPU SDK </label> <ul class=md-nav__list data-md-scrollfix> <li class=md-nav__item> <a href=../../../index.html class=md-nav__link> 主页 </a> </li> <li class="md-nav__item md-nav__item--nested"> <input class="md-nav__toggle md-toggle" data-md-toggle=__nav_2 type=checkbox id=__nav_2> <label class=md-nav__link for=__nav_2> SDK介绍 <span class="md-nav__icon md-icon"></span> </label> <nav class=md-nav aria-label=SDK介绍 data-md-level=1> <label class=md-nav__title for=__nav_2> <span class="md-nav__icon md-icon"></span> SDK介绍 </label> <ul class=md-nav__list data-md-scrollfix> <li class=md-nav__item> <a href=../../Introduction/Introduction.html class=md-nav__link> SDK框架介绍 </a> </li> <li class=md-nav__item> <a href=../../Introduction/Docker.html class=md-nav__link> Docker环境 </a> </li> </ul> </nav> </li> <li class="md-nav__item md-nav__item--active md-nav__item--nested"> <input class="md-nav__toggle md-toggle" data-md-toggle=__nav_3 type=checkbox id=__nav_3 checked> <label class=md-nav__link for=__nav_3> 用户手册 <span class="md-nav__icon md-icon"></span> </label> <nav class=md-nav aria-label=用户手册 data-md-level=1> <label class=md-nav__title for=__nav_3> <span class="md-nav__icon md-icon"></span> 用户手册 </label> <ul class=md-nav__list data-md-scrollfix> <li class=md-nav__item> <a href=../Common/Environment_Construction.html class=md-nav__link> 1. 快速开始 </a> </li> <li class=md-nav__item> <a href=Convert.html class=md-nav__link> 2. Convert Tool </a> </li> <li class=md-nav__item> <a href=../Common/Calibrate.html class=md-nav__link> 3. Calibrator </a> </li> <li class=md-nav__item> <a href=../Common/Compile.html class=md-nav__link> 4. Compiler </a> </li> <li class=md-nav__item> <a href=Simulate.html class=md-nav__link> 5. Simulator </a> </li> <li class=md-nav__item> <a href=../Common/DumpDebug_Tool.html class=md-nav__link> 6. DumpDebug Tool </a> </li> <li class=md-nav__item> <a href=../Common/SigmaStar_Post_Processing_Module.html class=md-nav__link> 7. SigmaStar后处理模块 </a> </li> <li class=md-nav__item> <a href=../Common/Adding_A_New_Layer.html class=md-nav__link> 8. 如何添加新的Layer </a> </li> <li class=md-nav__item> <a href=Special_Model_Conversion.html class=md-nav__link> 9. 特殊模型转换要点 </a> </li> <li class="md-nav__item md-nav__item--active"> <input class="md-nav__toggle md-toggle" data-md-toggle=toc type=checkbox id=__toc> <label class="md-nav__link md-nav__link--active" for=__toc> 10. DLA SDK 支持 <span class="md-nav__icon md-icon"></span> </label> <a href=DLA_SDK_Support.html class="md-nav__link md-nav__link--active"> 10. DLA SDK 支持 </a> <nav class="md-nav md-nav--secondary" aria-label="Table of contents"> <label class=md-nav__title for=__toc> <span class="md-nav__icon md-icon"></span> Table of contents </label> <ul class=md-nav__list data-md-component=toc data-md-scrollfix> <li class=md-nav__item> <a href=#101-caffe class=md-nav__link> 10.1. Caffe支持算子 </a> </li> <li class=md-nav__item> <a href=#102-tensorflow class=md-nav__link> 10.2. TensorFlow支持算子 </a> </li> <li class=md-nav__item> <a href=#103-onnx class=md-nav__link> 10.3. Onnx支持算子 </a> </li> <li class=md-nav__item> <a href=#104-sigmastar-dla-sdk class=md-nav__link> 10.4. SigmaStar DLA SDK对模型的限制 </a> </li> </ul> </nav> </li> <li class=md-nav__item> <a href=../Common/Running_Offline_Network_Model_On_Development_Board.html class=md-nav__link> 11. 在开发板上运行离线网络模型 </a> </li> <li class=md-nav__item> <a href=Preprocess.py_and_Input_Config.ini_Support.html class=md-nav__link> 附录. 前处理和配置文件注意要点 </a> </li> </ul> </nav> </li> <li class="md-nav__item md-nav__item--nested"> <input class="md-nav__toggle md-toggle" data-md-toggle=__nav_4 type=checkbox id=__nav_4> <label class=md-nav__link for=__nav_4> FAQ <span class="md-nav__icon md-icon"></span> </label> <nav class=md-nav aria-label=FAQ data-md-level=1> <label class=md-nav__title for=__nav_4> <span class="md-nav__icon md-icon"></span> FAQ </label> <ul class=md-nav__list data-md-scrollfix> <li class=md-nav__item> <a href=../../FAQ/Common/Env_Setting.html class=md-nav__link> 环境设置问题 </a> </li> <li class=md-nav__item> <a href=../../FAQ/IPU200M/Model_Conversion.html class=md-nav__link> 模型转换问题 </a> </li> <li class=md-nav__item> <a href=../../FAQ/IPU200M/Development_Board.html class=md-nav__link> 板端使用问题 </a> </li> <li class=md-nav__item> <a href=../../FAQ/IPU200M/Other_Anomalies.html class=md-nav__link> 其他异常问题 </a> </li> </ul> </nav> </li> </ul> </nav> </div> </div> </div> <div class="md-sidebar md-sidebar--secondary" data-md-component=sidebar data-md-type=toc> <div class=md-sidebar__scrollwrap> <div class=md-sidebar__inner> <nav class="md-nav md-nav--secondary" aria-label="Table of contents"> <label class=md-nav__title for=__toc> <span class="md-nav__icon md-icon"></span> Table of contents </label> <ul class=md-nav__list data-md-component=toc data-md-scrollfix> <li class=md-nav__item> <a href=#101-caffe class=md-nav__link> 10.1. Caffe支持算子 </a> </li> <li class=md-nav__item> <a href=#102-tensorflow class=md-nav__link> 10.2. TensorFlow支持算子 </a> </li> <li class=md-nav__item> <a href=#103-onnx class=md-nav__link> 10.3. Onnx支持算子 </a> </li> <li class=md-nav__item> <a href=#104-sigmastar-dla-sdk class=md-nav__link> 10.4. SigmaStar DLA SDK对模型的限制 </a> </li> </ul> </nav> </div> </div> </div> <div class=md-content data-md-component=content> <article class="md-content__inner md-typeset"> <h1>10. DLA SDK 支持</h1> <h2 id=101-caffe>10.1. Caffe支持算子<a class=headerlink href=#101-caffe title="Permanent link">&para;</a></h2> <table border=1 style="border-collapse: collapse;"> <tr> <th>算子</th> <th>备注 </th> <th></th> </tr> <tr> <td>ArgMax</td> <td>only support top 1</td> <td></td> </tr> <tr> <td>BatchNorm</td> <td></td> <td></td> </tr> <tr> <td>Concat</td> <td>最大256个tensor concat</td> <td></td> </tr> <tr> <td>Convolution</td> <td> <pre>
限制条件：All tensor size < 2^31
若kernel size 为 do*h*w*di 则h * w < 64
group为1时：转换成Depthwise Convolution;
group为C时：转换为Convolution;
group为 (1, C) 时：拆解成若干Convolution
round(di/16)*round(do/16) < 512 * 1024
      </pre> </td> <td></td> </tr> <tr> <td>ConvolutionDepthwise</td> <td>原生支持Kernel_size为3*3，6*6，9*9，其余情况转换成Convolution处理限制条件：Pad范围：[0, 1]</td> <td></td> </tr> <tr> <td>CReLU</td> <td></td> <td></td> </tr> <tr> <td>ContinuationIndicator</td> <td></td> <td></td> </tr> <tr> <td>Crop</td> <td></td> <td></td> </tr> <tr> <td>Deconvolution</td> <td> <pre>
All tensor size < 2^31
若kernel size 为 do*h*w*di，h * w < 64
round(di/16)*round(do/16) < 512 * 1024
      </pre> </td> <td></td> </tr> <tr> <td>Dropout</td> <td></td> <td></td> </tr> <tr> <td>Eltwise</td> <td> <pre>
支持Add、Sub、Mul、Maximum
对于输入的两个tensor,shape满足下面条件
1. 4维向量，NCHW
2. NCHW ，const
3. NCHW ，C维向量
4. NCHW ，NCHW
      </pre> </td> <td></td> </tr> <tr> <td>Flatten</td> <td></td> <td></td> </tr> <tr> <td>InnerProduct</td> <td> <pre>
若weight size do*di
round(di/16)*round(do/16) < 512 * 1024</pre> </td> <td></td> </tr> <tr> <td>Permute</td> <td></td> <td></td> </tr> <tr> <td>Pooling</td> <td> <pre>
若kernel size为h*w
1.global_pooling为true
h*w <= 65025
2.global_pooling为false
h*w <= 19*19
      </pre> </td> <td></td> </tr> <tr> <td>PriorBox</td> <td></td> <td></td> </tr> <tr> <td>Power</td> <td>仅支持指数为正整数</td> <td></td> </tr> <tr> <td>Reshape</td> <td></td> <td></td> </tr> <tr> <td>Reverse</td> <td></td> <td></td> </tr> <tr> <td>ROIPooling</td> <td>ROIPooling的rois输入维度为（N×5），当后段网络全部是InnerProduct时，N才可以设置大于1，如果后段网络中有卷积时，N仅可以设置为1，第二段网络需要循环执行N次。使用方法和限制详见下方Please Note。</td> <td></td> </tr> <tr> <td>ReLU</td> <td>输入<=4维</td> <td></td> </tr> <tr> <td>PReLU</td> <td>输入<=4维</td> <td></td> </tr> <tr> <td>Sigmoid</td> <td></td> <td></td> </tr> <tr> <td>Slice</td> <td></td> <td></td> </tr> <tr> <td>Scale</td> <td> <pre>
对于输入的两个tensor,shape满足下面条件
1. 4维向量，NCHW
2. NCHW ，const
3. NCHW ，C维向量
4. NCHW ，NCHW
      </pre> </td> <td></td> </tr> <tr> <td>Softmax</td> <td>如需对指定维度进行运算，将要计算的维度转置到最后的维度（最内维度），最大支持32*512=16384</td> <td></td> </tr> <tr> <td>Split</td> <td></td> <td></td> </tr> <tr> <td>Tanh</td> <td></td> <td></td> </tr> <tr> <td>Threshold</td> <td>只支持4维输入</td> <td></td> </tr> <tr> <td>Tile</td> <td></td> <td></td> </tr> <tr> <td>Upsample</td> <td> <pre>
Upsample算子在caffe中没有，可以手动将Deconvolution修改成Upsample
只支持4维输入 Only support same scale on H and W
      </pre> </td> <td></td> </tr> <tr> <td>Reorg</td> <td>只支持stride = 2</td> <td></td> </tr> <tr> <td>LSTM</td> <td>输入c0,h0必须是const tensor,cn,hn不支持单独输出，只支持单向LSTM</td> <td></td> </tr> </table> <p><strong>Please Note:</strong></p> <ul> <li> <p>Upsample算子在prototxt中这样描述：</p> <p><div class=highlight><pre><span></span><code><span class=err>layer</span> <span class=err>{</span>
<span class=na>    bottom</span><span class=o>:</span> <span class=s>&quot;layer85-conv&quot;</span>
<span class=na>    top</span><span class=o>:</span> <span class=s>&quot;layer86-upsample&quot;</span>
<span class=na>    name</span><span class=o>:</span> <span class=s>&quot;layer86-upsample&quot;</span>
<span class=na>    type</span><span class=o>:</span> <span class=s>&quot;Upsample&quot;</span>
    <span class=err>upsample_param</span> <span class=err>{</span>
<span class=na>        scale</span><span class=o>:</span> <span class=s>2</span>
    <span class=err>}</span>
<span class=err>}</span>
</code></pre></div> scale参数与Deconvolution的Stride含义相同。但需注意Upsample相当于权重全为1的Deconvolution算子。</p> </li> <li> <p>ROIPooling算子在prototxt中这样描述：</p> <p><div class=highlight><pre><span></span><code><span class=err>layer</span> <span class=err>{</span>
<span class=na>    name</span><span class=o>:</span> <span class=s>&quot;roi_pool5&quot;</span>
<span class=na>    type</span><span class=o>:</span> <span class=s>&quot;ROIPooling&quot;</span>
<span class=na>    bottom</span><span class=o>:</span> <span class=s>&quot;conv5_3&quot;</span>
<span class=na>    bottom</span><span class=o>:</span> <span class=s>&quot;rois&quot;</span>
<span class=na>    top</span><span class=o>:</span> <span class=s>&quot;pool5&quot;</span>
    <span class=err>roi_pooling_param</span> <span class=err>{</span>
<span class=na>        pooled_w</span><span class=o>:</span> <span class=s>7</span>
<span class=na>        pooled_h</span><span class=o>:</span> <span class=s>7</span>
<span class=na>        spatial_scale</span><span class=o>:</span> <span class=s>0.0625</span>
    <span class=err>}</span>
<span class=err>}</span>
</code></pre></div> Roi_pooling_param仅支持pooled_w，pooled_h和spatial_scale。Float模型的rois输入为rpn层输出的坐标，Fixed和Offline模型的rois输入为rpn层输出坐标乘spatial_scale值后再量化到int16后送入模型。</p> </li> </ul> <h2 id=102-tensorflow>10.2. TensorFlow支持算子<a class=headerlink href=#102-tensorflow title="Permanent link">&para;</a></h2> <table border=1 style="border-collapse: collapse;"> <tr> <th>类别</th> <th>算子</th> <th>备注 </th> </tr> <tr> <td>Convolution</td> <td>Conv</td> <td>限制条件：Kernel_size：H * W < 64</td> </tr> <tr> <td>Convolution</td> <td>DepthwiseConv2dNative</td> <td>原生支持Kernel_size为3*3，6*6，9*9，其余情况转换成Convolution处理</td> </tr> <tr> <td>Convolution</td> <td>FullyConnected</td> <td></td> </tr> <tr> <td>Pooling</td> <td>Max pooling</td> <td></td> </tr> <tr> <td>Pooling</td> <td>Average Pooling</td> <td></td> </tr> <tr> <td>Activation</td> <td>ReLU</td> <td></td> </tr> <tr> <td>Activation</td> <td>PReLU</td> <td></td> </tr> <tr> <td>Activation</td> <td>ReLU6</td> <td></td> </tr> <tr> <td>Activation</td> <td>LeakyReLU</td> <td></td> </tr> <tr> <td>Activation</td> <td>Sigmoid</td> <td></td> </tr> <tr> <td>Math</td> <td>Less</td> <td></td> </tr> <tr> <td>Math</td> <td>Log</td> <td></td> </tr> <tr> <td>Math</td> <td>Greater</td> <td></td> </tr> <tr> <td>Math</td> <td>GreaterEqual</td> <td></td> </tr> <tr> <td>Math</td> <td>Equal</td> <td></td> </tr> <tr> <td>Math</td> <td>Add</td> <td></td> </tr> <tr> <td>Math</td> <td>Sub</td> <td></td> </tr> <tr> <td>Math</td> <td>Mul</td> <td></td> </tr> <tr> <td>Math</td> <td>RealDiv</td> <td>仅支持第二个操作数为常量Tensor</td> </tr> <tr> <td>Math</td> <td>FloorDiv</td> <td>仅支持第二个操作数为常量Tensor</td> </tr> <tr> <td>Math</td> <td>Maximum</td> <td></td> </tr> <tr> <td>Math</td> <td>Minimum</td> <td></td> </tr> <tr> <td>Math</td> <td>Mean</td> <td></td> </tr> <tr> <td>Math</td> <td>Max</td> <td></td> </tr> <tr> <td>Math</td> <td>Sqrt</td> <td></td> </tr> <tr> <td>Math</td> <td>Sin</td> <td></td> </tr> <tr> <td>Math</td> <td>Cos</td> <td></td> </tr> <tr> <td>Math</td> <td>Rsqrt</td> <td></td> </tr> <tr> <td>Math</td> <td>Round</td> <td></td> </tr> <tr> <td>Math</td> <td>Softmax</td> <td>如需对指定维度进行运算，将要计算的维度转置到最后的维度（最内维度）</td> </tr> <tr> <td>Math</td> <td>FusedBatchNorm</td> <td></td> </tr> <tr> <td>Math</td> <td>Exp</td> <td></td> </tr> <tr> <td>DMA</td> <td>Align</td> <td></td> </tr> <tr> <td>DMA</td> <td>ConcatV2</td> <td></td> </tr> <tr> <td>DMA</td> <td>Fill</td> <td></td> </tr> <tr> <td>DMA</td> <td>Gather</td> <td></td> </tr> <tr> <td>DMA</td> <td>GatherV2</td> <td></td> </tr> <tr> <td>DMA</td> <td>Pack</td> <td></td> </tr> <tr> <td>DMA</td> <td>Pad</td> <td></td> </tr> <tr> <td>DMA</td> <td>SpaceToBatchND</td> <td></td> </tr> <tr> <td>DMA</td> <td>BatchToSpaceND</td> <td></td> </tr> <tr> <td>DMA</td> <td>Zeroslike</td> <td></td> </tr> <tr> <td>DMA</td> <td>Split</td> <td></td> </tr> <tr> <td>DMA</td> <td>Slice</td> <td></td> </tr> <tr> <td>DMA</td> <td>Unpack</td> <td></td> </tr> <tr> <td>DMA</td> <td>Tile</td> <td></td> </tr> <tr> <td>DMA</td> <td>Reshape</td> <td></td> </tr> <tr> <td>DMA</td> <td>Transpose</td> <td></td> </tr> <tr> <td>DMA</td> <td>Resize_bilinear</td> <td></td> </tr> <tr> <td>DMA</td> <td>Resize_NearestNeighbor</td> <td></td> </tr> <tr> <td>Misc</td> <td>TopKV2</td> <td></td> </tr> <tr> <td>Misc</td> <td>shape</td> <td></td> </tr> </table> <h2 id=103-onnx>10.3. Onnx支持算子<a class=headerlink href=#103-onnx title="Permanent link">&para;</a></h2> <table border=1 style="border-collapse: collapse;"> <tr> <th>算子</th> <th>备注</th> <th></th> </tr> <tr> <td>Add</td> <td> <pre>
对于输入的两个tensor,shape满足下面条件
1. 4维向量，NCHW
2. NCHW ，const
3. NCHW ，C维向量
4. NCHW ，NCHW
        </pre> </td> <td></td> </tr> <tr> <td>Abs</td> <td></td> <td></td> </tr> <tr> <td>ArgMax</td> <td></td> <td></td> </tr> <tr> <td>AveragePool</td> <td>若kernel size为h*w，当该算子l输入为 h*w <= 19*19</td> <td></td> </tr> <tr> <td>BatchNorm</td> <td></td> <td></td> </tr> <tr> <td>Concat</td> <td>最大256个tensor concat</td> <td></td> </tr> <tr> <td>Convolution</td> <td> <pre>
All tensor size < 2^31
若kernel size 为 do*h*w*di 则h * w < 64
round(di/16)*round(do/16) < 512 * 1024
支持autoPad的SAME_UPPER属性;限制条件：Pads范围：[0, 7]
        </pre> </td> <td></td> </tr> <tr> <td>ConvTranspose</td> <td> <pre>
All tensor size < 2^32
若kernel size 为 do*h*w*di，h * w < 64
round(di/16)*round(do/16) < 512 * 1024
        </pre> </td> <td></td> </tr> <tr> <td>Clip</td> <td></td> <td></td> </tr> <tr> <td>DepthwiseConv2D</td> <td>原生支持Kernel_size为3*3，6*6，9*9，其余情况转换成Convolution处理</td> <td></td> </tr> <tr> <td>Div</td> <td> <pre>
对于输入的两个tensor,shape满足下面条件
1. 4维向量，NCHW
2. NCHW ，const
3. NCHW ，C维向量
4. NCHW ，NCHW
        </pre> </td> <td></td> </tr> <tr> <td>Dropout</td> <td></td> <td></td> </tr> <tr> <td>DepthToSpace</td> <td>只支持4维输入</td> <td></td> </tr> <tr> <td>Expand</td> <td></td> <td></td> </tr> <tr> <td>Exp</td> <td></td> <td></td> </tr> <tr> <td>Gather</td> <td>只支持indices为int32的常数</td> <td></td> </tr> <tr> <td>Gemm</td> <td> <pre>
输入<=4维，若weight size do*di
round(di/16)*round(do/16) < 512 * 1024
        </pre> </td> <td></td> </tr> <tr> <td>GlobalAveragePool</td> <td> <pre>
若kernel size为h*w
当该算子l输入为
h*w <= 65025
        </pre> </td> <td></td> </tr> <tr> <td>GlobalMaxPool</td> <td> <pre>
若kernel size为h*w
当该算子l输入为
h*w <= 65025
        </pre> </td> <td></td> </tr> <tr> <td>LSTM</td> <td> <pre>
激活函数固定，不能配置。参见用户手册特殊网络转换LSTM章节
输入c0,h0必须是const tensor,cn,hn不支持单独输出，
支持单向LSTM和双向LSTM
        </pre> </td> <td></td> </tr> <tr> <td>Log</td> <td></td> <td></td> </tr> <tr> <td>Sin</td> <td></td> <td></td> </tr> <tr> <td>Cos</td> <td></td> <td></td> </tr> <tr> <td>Matmul</td> <td> <pre>
输入<=4维，若weight size do*di
round(di/16)*round(do/16) < 512 * 1024
        </pre> </td> <td></td> </tr> <tr> <td>Mul</td> <td> <pre>
对于输入的两个tensor,shape满足下面条件
1. 4维向量，NCHW
2. NCHW ，const
3. NCHW ，C维向量
4. NCHW ，NCHW
        </pre> </td> <td></td> </tr> <tr> <td>MaxPool</td> <td> <pre>
若kernel size为h*w
当该算子l输入为
h*w <= 19*19 </pre> </td> <td></td> </tr> <tr> <td>Max</td> <td></td> <td></td> </tr> <tr> <td>Pad</td> <td>支持，only support constant</td> <td></td> </tr> <tr> <td>Reshape</td> <td></td> <td></td> </tr> <tr> <td>ReduceSum</td> <td>输入<=4维</td> <td></td> </tr> <tr> <td>ReduceMean</td> <td>输入<=4维</td> <td></td> </tr> <tr> <td>ReduceMax</td> <td>输入<=4维，仅支持同时对一个axis进行计算</td> <td></td> </tr> <tr> <td>Resize</td> <td> <pre>
当'model'为nearest
上采样时，'coordinate_transformation_mode'为'asymmetric',
'nearest_mode'only support round_prefer_floor(Default) and floor
coordinate_transformation_mode'为'align_corners',
'nearest_mode'only support round_prefer_floor(Default) and round_prefer_ceil.
下采样时，
'coordinate_transformation_mode'为'asymmetric',
'nearest_mode' is not support floor
        </pre> </td> <td></td> </tr> <tr> <td>ReLU</td> <td>只支持4维输入</td> <td></td> </tr> <tr> <td>PReLU</td> <td>输入<=4维</td> <td></td> </tr> <tr> <td>LeakyReLU</td> <td>输入<=4维</td> <td></td> </tr> <tr> <td>TanH</td> <td></td> <td></td> </tr> <tr> <td>Sigmoid</td> <td></td> <td></td> </tr> <tr> <td>Slice</td> <td></td> <td></td> </tr> <tr> <td>Softmax</td> <td>如需对指定维度进行运算，将要计算的维度转置到最后的维度（最内维度），最大支持32*512=16384</td> <td></td> </tr> <tr> <td>Split</td> <td></td> <td></td> </tr> <tr> <td>SpaceToDepth</td> <td></td> <td></td> </tr> <tr> <td>Squeeze</td> <td></td> <td></td> </tr> <tr> <td>Sub</td> <td> <pre>
对于输入的两个tensor,shape满足下面条件
1. 4维向量，NCHW
2. NCHW ，const
3. NCHW ，C维向量
4. NCHW ，NCHW
        </pre> </td> <td></td> </tr> <tr> <td>Transpose</td> <td></td> <td></td> </tr> <tr> <td>Tile</td> <td></td> <td></td> </tr> <tr> <td>Unsqueeze</td> <td></td> <td></td> </tr> <tr> <td>Upsample</td> <td>只支持4维输入Only support same scale on H and W</td> <td></td> </tr> </table> <h2 id=104-sigmastar-dla-sdk>10.4. SigmaStar DLA SDK对模型的限制<a class=headerlink href=#104-sigmastar-dla-sdk title="Permanent link">&para;</a></h2> <p>1. 对于指定维度的Softmax，我们只支持对最内维度的操作（多余多维Tensor所Softmax运算，我们只支持Softmax制定在最内维度做）。</p> <p>2. 除第一层Conv外，其他层的Conv DI维度（即NHWC 中C这个维度）越大效率会越高，最大支援2048。</p> <p>3. Math类算子（包括Add、Sub、Mul、Div等元素操作的算子），如果右操作数是scaler（单个数字） 或者 1 维向量（HW维度数据相同，C维度不同），效率会更高。</p> <p>4. 网络结构中尽量减少一个算子的输出被多个算子作为输入的情况，如ResNet的残差结构，GoogLeNet的Inception模块等。</p> </article> </div> </div> </main> <footer class=md-footer> <nav class="md-footer__inner md-grid" aria-label=Footer> <a href=Special_Model_Conversion.html class="md-footer__link md-footer__link--prev" rel=prev> <div class="md-footer__button md-icon"> <svg xmlns=http://www.w3.org/2000/svg viewbox="0 0 24 24"><path d="M20 11v2H8l5.5 5.5-1.42 1.42L4.16 12l7.92-7.92L13.5 5.5 8 11h12z"/></svg> </div> <div class=md-footer__title> <div class=md-ellipsis> <span class=md-footer__direction> Previous </span> 9. 特殊模型转换要点 </div> </div> </a> <a href=../Common/Running_Offline_Network_Model_On_Development_Board.html class="md-footer__link md-footer__link--next" rel=next> <div class=md-footer__title> <div class=md-ellipsis> <span class=md-footer__direction> Next </span> 11. 在开发板上运行离线网络模型 </div> </div> <div class="md-footer__button md-icon"> <svg xmlns=http://www.w3.org/2000/svg viewbox="0 0 24 24"><path d="M4 11v2h12l-5.5 5.5 1.42 1.42L19.84 12l-7.92-7.92L10.5 5.5 16 11H4z"/></svg> </div> </a> </nav> <div class="md-footer-meta md-typeset"> <div class="md-footer-meta__inner md-grid"> <div class=md-footer-copyright> <div class=md-footer-copyright__highlight> Copyright&copy; 2021 SigmaStar Technology. All rights reserved. Security Level: Confidential A. </div> Made with <a href=https://squidfunk.github.io/mkdocs-material/ target=_blank rel=noopener> Material for MkDocs </a> </div> </div> </div> </footer> </div> <div class=md-dialog data-md-component=dialog> <div class="md-dialog__inner md-typeset"></div> </div> <script id=__config type=application/json>{"base": "../../..", "features": [], "translations": {"clipboard.copy": "Copy to clipboard", "clipboard.copied": "Copied to clipboard", "search.config.lang": "en", "search.config.pipeline": "trimmer, stopWordFilter", "search.config.separator": "[\\s\\-]+", "search.placeholder": "Search", "search.result.placeholder": "Type to start searching", "search.result.none": "No matching documents", "search.result.one": "1 matching document", "search.result.other": "# matching documents", "search.result.more.one": "1 more on this page", "search.result.more.other": "# more on this page", "search.result.term.missing": "Missing"}, "search": "../../../assets/javascripts/workers/search.fb4a9340.min.js", "version": null}</script> <script src=../../../assets/javascripts/bundle.a1c7c35e.min.js></script> <script src=../../../search/search_index.js></script> <script src=../../../javascripts/extra.js></script> <script src="https://cdnjs.cloudflare.com/ajax/libs/mathjax/2.7.0/MathJax.js?config=TeX-MML-AM_CHTML"></script> <script src=https://cdnjs.cloudflare.com/ajax/libs/raphael/2.2.7/raphael.min.js></script> <script src=https://cdnjs.cloudflare.com/ajax/libs/underscore.js/1.8.3/underscore-min.js></script> <script src=https://cdnjs.cloudflare.com/ajax/libs/js-sequence-diagrams/1.0.6/sequence-diagram-min.js></script> <script src=https://cdnjs.cloudflare.com/ajax/libs/flowchart/1.6.5/flowchart.min.js></script> <script src=https://unpkg.com/freezeframe/dist/freezeframe.min.js></script> <script src=https://unpkg.com/mermaid@7.1.0/dist/mermaid.min.js></script> <script src=../../../javascripts/umlconvert.js></script> </body> </html>