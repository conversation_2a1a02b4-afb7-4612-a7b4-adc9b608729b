<!doctype html><html lang=en class=no-js> <head><meta charset=utf-8><meta name=viewport content="width=device-width,initial-scale=1"><meta name=description content="Personal Documentation System Template"><meta name=author content="<PERSON> Cho<PERSON>"><link rel=icon href=../../../images/favicon.ico><meta name=generator content="mkdocs-1.1.2, mkdocs-material-7.0.6"><title>5. Simulator - IPU SDK</title><link rel=stylesheet href=../../../assets/stylesheets/main.2c0c5eaf.min.css><link rel=stylesheet href=../../../assets/stylesheets/palette.7fa14f5b.min.css><meta name=theme-color content=#009485><link rel=preconnect href=https://fonts.gstatic.com crossorigin><link rel=stylesheet href="https://fonts.googleapis.com/css?family=Roboto:300,400,400i,700%7CRoboto+Mono&display=fallback"><style>:root{--md-text-font-family:"Roboto";--md-code-font-family:"Roboto Mono"}</style><link rel=stylesheet href=../../../stylesheets/extra.css></head> <body dir=ltr data-md-color-scheme data-md-color-primary=teal data-md-color-accent=teal> <input class=md-toggle data-md-toggle=drawer type=checkbox id=__drawer autocomplete=off> <input class=md-toggle data-md-toggle=search type=checkbox id=__search autocomplete=off> <label class=md-overlay for=__drawer></label> <div data-md-component=skip> <a href=#51 class=md-skip> Skip to content </a> </div> <div data-md-component=announce> </div> <header class=md-header data-md-component=header> <nav class="md-header__inner md-grid" aria-label=Header> <a href=../../.. title="IPU SDK" class="md-header__button md-logo" aria-label="IPU SDK" data-md-component=logo> <svg xmlns=http://www.w3.org/2000/svg viewbox="0 0 24 24"><path d="M10 20v-6h4v6h5v-8h3L12 3 2 12h3v8h5z"/></svg> </a> <label class="md-header__button md-icon" for=__drawer> <svg xmlns=http://www.w3.org/2000/svg viewbox="0 0 24 24"><path d="M3 6h18v2H3V6m0 5h18v2H3v-2m0 5h18v2H3v-2z"/></svg> </label> <div class=md-header__title data-md-component=header-title> <div class=md-header__ellipsis> <div class=md-header__topic> <span class=md-ellipsis> IPU SDK </span> </div> <div class=md-header__topic data-md-component=header-topic> <span class=md-ellipsis> 5. Simulator </span> </div> </div> </div> <div class=md-header__options> </div> <label class="md-header__button md-icon" for=__search> <svg xmlns=http://www.w3.org/2000/svg viewbox="0 0 24 24"><path d="M9.5 3A6.5 6.5 0 0 1 16 9.5c0 1.61-.59 3.09-1.56 4.23l.27.27h.79l5 5-1.5 1.5-5-5v-.79l-.27-.27A6.516 6.516 0 0 1 9.5 16 6.5 6.5 0 0 1 3 9.5 6.5 6.5 0 0 1 9.5 3m0 2C7 5 5 7 5 9.5S7 14 9.5 14 14 12 14 9.5 12 5 9.5 5z"/></svg> </label> <div class=md-search data-md-component=search role=dialog> <label class=md-search__overlay for=__search></label> <div class=md-search__inner role=search> <form class=md-search__form name=search> <input type=text class=md-search__input name=query aria-label=Search placeholder=Search autocapitalize=off autocorrect=off autocomplete=off spellcheck=false data-md-component=search-query data-md-state=active required> <label class="md-search__icon md-icon" for=__search> <svg xmlns=http://www.w3.org/2000/svg viewbox="0 0 24 24"><path d="M9.5 3A6.5 6.5 0 0 1 16 9.5c0 1.61-.59 3.09-1.56 4.23l.27.27h.79l5 5-1.5 1.5-5-5v-.79l-.27-.27A6.516 6.516 0 0 1 9.5 16 6.5 6.5 0 0 1 3 9.5 6.5 6.5 0 0 1 9.5 3m0 2C7 5 5 7 5 9.5S7 14 9.5 14 14 12 14 9.5 12 5 9.5 5z"/></svg> <svg xmlns=http://www.w3.org/2000/svg viewbox="0 0 24 24"><path d="M20 11v2H8l5.5 5.5-1.42 1.42L4.16 12l7.92-7.92L13.5 5.5 8 11h12z"/></svg> </label> <button type=reset class="md-search__icon md-icon" aria-label=Clear tabindex=-1> <svg xmlns=http://www.w3.org/2000/svg viewbox="0 0 24 24"><path d="M19 6.41L17.59 5 12 10.59 6.41 5 5 6.41 10.59 12 5 17.59 6.41 19 12 13.41 17.59 19 19 17.59 13.41 12 19 6.41z"/></svg> </button> </form> <div class=md-search__output> <div class=md-search__scrollwrap data-md-scrollfix> <div class=md-search-result data-md-component=search-result> <div class=md-search-result__meta> Initializing search </div> <ol class=md-search-result__list></ol> </div> </div> </div> </div> </div> </nav> </header> <div class=md-container data-md-component=container> <main class=md-main data-md-component=main> <div class="md-main__inner md-grid"> <div class="md-sidebar md-sidebar--primary" data-md-component=sidebar data-md-type=navigation> <div class=md-sidebar__scrollwrap> <div class=md-sidebar__inner> <nav class="md-nav md-nav--primary" aria-label=Navigation data-md-level=0> <label class=md-nav__title for=__drawer> <a href=../../.. title="IPU SDK" class="md-nav__button md-logo" aria-label="IPU SDK" data-md-component=logo> <svg xmlns=http://www.w3.org/2000/svg viewbox="0 0 24 24"><path d="M10 20v-6h4v6h5v-8h3L12 3 2 12h3v8h5z"/></svg> </a> IPU SDK </label> <ul class=md-nav__list data-md-scrollfix> <li class=md-nav__item> <a href=../../../index.html class=md-nav__link> 主页 </a> </li> <li class="md-nav__item md-nav__item--nested"> <input class="md-nav__toggle md-toggle" data-md-toggle=__nav_2 type=checkbox id=__nav_2> <label class=md-nav__link for=__nav_2> SDK介绍 <span class="md-nav__icon md-icon"></span> </label> <nav class=md-nav aria-label=SDK介绍 data-md-level=1> <label class=md-nav__title for=__nav_2> <span class="md-nav__icon md-icon"></span> SDK介绍 </label> <ul class=md-nav__list data-md-scrollfix> <li class=md-nav__item> <a href=../../Introduction/Introduction.html class=md-nav__link> SDK框架介绍 </a> </li> <li class=md-nav__item> <a href=../../Introduction/Docker.html class=md-nav__link> Docker环境 </a> </li> </ul> </nav> </li> <li class="md-nav__item md-nav__item--active md-nav__item--nested"> <input class="md-nav__toggle md-toggle" data-md-toggle=__nav_3 type=checkbox id=__nav_3 checked> <label class=md-nav__link for=__nav_3> 用户手册 <span class="md-nav__icon md-icon"></span> </label> <nav class=md-nav aria-label=用户手册 data-md-level=1> <label class=md-nav__title for=__nav_3> <span class="md-nav__icon md-icon"></span> 用户手册 </label> <ul class=md-nav__list data-md-scrollfix> <li class=md-nav__item> <a href=../Common/Environment_Construction.html class=md-nav__link> 1. 快速开始 </a> </li> <li class=md-nav__item> <a href=Convert.html class=md-nav__link> 2. Convert Tool </a> </li> <li class=md-nav__item> <a href=../Common/Calibrate.html class=md-nav__link> 3. Calibrator </a> </li> <li class=md-nav__item> <a href=../Common/Compile.html class=md-nav__link> 4. Compiler </a> </li> <li class="md-nav__item md-nav__item--active"> <input class="md-nav__toggle md-toggle" data-md-toggle=toc type=checkbox id=__toc> <label class="md-nav__link md-nav__link--active" for=__toc> 5. Simulator <span class="md-nav__icon md-icon"></span> </label> <a href=Simulate.html class="md-nav__link md-nav__link--active"> 5. Simulator </a> <nav class="md-nav md-nav--secondary" aria-label="Table of contents"> <label class=md-nav__title for=__toc> <span class="md-nav__icon md-icon"></span> Table of contents </label> <ul class=md-nav__list data-md-component=toc data-md-scrollfix> <li class=md-nav__item> <a href=#51 class=md-nav__link> 5.1. 使用方法 </a> <nav class=md-nav aria-label="5.1. 使用方法"> <ul class=md-nav__list> <li class=md-nav__item> <a href=#511 class=md-nav__link> 5.1.1 必选参数 </a> </li> <li class=md-nav__item> <a href=#512 class=md-nav__link> 5.1.2 可选参数 </a> </li> <li class=md-nav__item> <a href=#513 class=md-nav__link> 5.1.3 注意事项 </a> </li> </ul> </nav> </li> <li class=md-nav__item> <a href=#52-calibrator_customsimulator class=md-nav__link> 5.2. calibrator_custom.simulator </a> <nav class=md-nav aria-label="5.2. calibrator_custom.simulator"> <ul class=md-nav__list> <li class=md-nav__item> <a href=#521-calibrator_customsimulator class=md-nav__link> 5.2.1 calibrator_custom.simulator方法 </a> </li> <li class=md-nav__item> <a href=#522-calibrator_customsim_simulator class=md-nav__link> 5.2.2 calibrator_custom.SIM_Simulator </a> </li> </ul> </nav> </li> </ul> </nav> </li> <li class=md-nav__item> <a href=../Common/DumpDebug_Tool.html class=md-nav__link> 6. DumpDebug Tool </a> </li> <li class=md-nav__item> <a href=../Common/SigmaStar_Post_Processing_Module.html class=md-nav__link> 7. SigmaStar后处理模块 </a> </li> <li class=md-nav__item> <a href=../Common/Adding_A_New_Layer.html class=md-nav__link> 8. 如何添加新的Layer </a> </li> <li class=md-nav__item> <a href=Special_Model_Conversion.html class=md-nav__link> 9. 特殊模型转换要点 </a> </li> <li class=md-nav__item> <a href=DLA_SDK_Support.html class=md-nav__link> 10. DLA SDK 支持 </a> </li> <li class=md-nav__item> <a href=../Common/Running_Offline_Network_Model_On_Development_Board.html class=md-nav__link> 11. 在开发板上运行离线网络模型 </a> </li> <li class=md-nav__item> <a href=Preprocess.py_and_Input_Config.ini_Support.html class=md-nav__link> 附录. 前处理和配置文件注意要点 </a> </li> </ul> </nav> </li> <li class="md-nav__item md-nav__item--nested"> <input class="md-nav__toggle md-toggle" data-md-toggle=__nav_4 type=checkbox id=__nav_4> <label class=md-nav__link for=__nav_4> FAQ <span class="md-nav__icon md-icon"></span> </label> <nav class=md-nav aria-label=FAQ data-md-level=1> <label class=md-nav__title for=__nav_4> <span class="md-nav__icon md-icon"></span> FAQ </label> <ul class=md-nav__list data-md-scrollfix> <li class=md-nav__item> <a href=../../FAQ/Common/Env_Setting.html class=md-nav__link> 环境设置问题 </a> </li> <li class=md-nav__item> <a href=../../FAQ/IPU200M/Model_Conversion.html class=md-nav__link> 模型转换问题 </a> </li> <li class=md-nav__item> <a href=../../FAQ/IPU200M/Development_Board.html class=md-nav__link> 板端使用问题 </a> </li> <li class=md-nav__item> <a href=../../FAQ/IPU200M/Other_Anomalies.html class=md-nav__link> 其他异常问题 </a> </li> </ul> </nav> </li> </ul> </nav> </div> </div> </div> <div class="md-sidebar md-sidebar--secondary" data-md-component=sidebar data-md-type=toc> <div class=md-sidebar__scrollwrap> <div class=md-sidebar__inner> <nav class="md-nav md-nav--secondary" aria-label="Table of contents"> <label class=md-nav__title for=__toc> <span class="md-nav__icon md-icon"></span> Table of contents </label> <ul class=md-nav__list data-md-component=toc data-md-scrollfix> <li class=md-nav__item> <a href=#51 class=md-nav__link> 5.1. 使用方法 </a> <nav class=md-nav aria-label="5.1. 使用方法"> <ul class=md-nav__list> <li class=md-nav__item> <a href=#511 class=md-nav__link> 5.1.1 必选参数 </a> </li> <li class=md-nav__item> <a href=#512 class=md-nav__link> 5.1.2 可选参数 </a> </li> <li class=md-nav__item> <a href=#513 class=md-nav__link> 5.1.3 注意事项 </a> </li> </ul> </nav> </li> <li class=md-nav__item> <a href=#52-calibrator_customsimulator class=md-nav__link> 5.2. calibrator_custom.simulator </a> <nav class=md-nav aria-label="5.2. calibrator_custom.simulator"> <ul class=md-nav__list> <li class=md-nav__item> <a href=#521-calibrator_customsimulator class=md-nav__link> 5.2.1 calibrator_custom.simulator方法 </a> </li> <li class=md-nav__item> <a href=#522-calibrator_customsim_simulator class=md-nav__link> 5.2.2 calibrator_custom.SIM_Simulator </a> </li> </ul> </nav> </li> </ul> </nav> </div> </div> </div> <div class=md-content data-md-component=content> <article class="md-content__inner md-typeset"> <h1>5. Simulator</h1> <h2 id=51>5.1. 使用方法<a class=headerlink href=#51 title="Permanent link">&para;</a></h2> <p>Simulator工具的位置在 <strong>SGS_IPU_SDK/Scripts/calibrator/simulator.py</strong>。 该工具具有以下功能： </p> <ul> <li>对分类网络数据集验证；</li> <li>对检测网络数据集验证（支持COCO数据集）；</li> <li>对单张图片预测；</li> <li>绘制单张图片目标检测结果；</li> </ul> <p>在SGS_IPU_SDK ⽬录下运⾏以下脚本，输出Library的路径（已经做过该步骤可忽略）： <div class=highlight><pre><span></span><code>cd ~/SGS_IPU_SDK 
source cfg_env.sh
</code></pre></div></p> <p>进入到该工具目录，工具使用示例： </p> <ul> <li>测试ILSVRC数据集，并返回模型精度结果：</li> </ul> <div class=highlight><pre><span></span><code>python3 simulator.py \ 
-i ~/SGS_Models/resource/classify/ilsvrc2012_val_set100/ \ 
-m ~/SGS_Models/tensorflow/mobilenet_v2/mobilenet_v2_float.sim \ 
-l ~/SGS_Models/resource/classify/tensorflow_labels.txt \ 
-c Classification \ 
-t Float \ 
-n mobilenet_v2 \ 
--num_process 20
</code></pre></div> <p>或者可以使用传入 <strong>指定图片路径列表文件</strong> 的形式：</p> <div class=highlight><pre><span></span><code>python3 simulator.py \ 
-i ~/SGS_Models/resource/classify/ilsvrc2012_val_set100/file.list \ 
-m ~/SGS_Models/tensorflow/mobilenet_v2/mobilenet_v2_float.sim \ 
-l ~/SGS_Models/resource/classify/tensorflow_labels.txt \ 
-c Classification \ 
-t Float \ 
-n mobilenet_v2 \ 
--num_process 20
</code></pre></div> <ul> <li>测试COCO2017数据集，并返回模型检测结果：</li> </ul> <div class=highlight><pre><span></span><code>python3 simulator.py \ 
-i ~/SGS_Models/resource/detection/coco2017_val_set100 \ 
-m ~/SGS_Models/tensorflow/ssd_mobilenet_v1/ssd_mobilenet_v1_float.sim \ 
-l ~/SGS_Models/resource/detection/coco2017_val_set100.json \ 
-c Detection \ 
-t Float \ 
-n ssd_mobilenet_v1 \ 
--num_process 10
</code></pre></div> <p>或者可以使用传入 <strong>指定图片路径列表文件</strong> 的形式：</p> <div class=highlight><pre><span></span><code>python3 simulator.py \ 
-i ~/SGS_Models/resource/detection/coco2017_val_set100/file.list \ 
-m ~/SGS_Models/tensorflow/ssd_mobilenet_v1/ssd_mobilenet_v1_float.sim \ 
-l ~/SGS_Models/resource/detection/coco2017_val_set100.json \ 
-c Detection \ 
-t Float \ 
-n ssd_mobilenet_v1 \ 
--num_process 10
</code></pre></div> <ul> <li>测试单张图片，并将检测结果绘制在图片上，保存到 <strong>./results/</strong> 文件夹下：</li> </ul> <div class=highlight><pre><span></span><code>python3 simulator.py \ 
-i ~/SGS_Models/resource/detection/coco2017_val_set100/000000567877.jpg \ 
-m ~/SGS_Models/tensorflow/ssd_mobilenet_v1/ssd_mobilenet_v1_float.sim \ 
-c Detection \ 
-t Float \ 
-n ssd_mobilenet_v1 \ 
--draw_result ./results
</code></pre></div> <p>对该工具参数的具体说明如下。 </p> <hr> <h3 id=511>5.1.1 必选参数<a class=headerlink href=#511 title="Permanent link">&para;</a></h3> <p><code>-i</code>, <code>--image</code>: 图片文件 / 图片文件夹路径 / <strong>指定图片路径列表文件</strong> 。</p> <p><code>-m</code>, <code>--model</code>: 网络模型文件路径。</p> <p><code>-c</code>, <code>--category</code>: 模型的类别，主要有Classification / Detection / Unknown。 </p> <ul> <li><code>Classification</code>: 模型有1个输出，会根据输出排序输出分数由高至低的前5个。 </li> <li><code>Detection</code>: 模型有4个输出，会根据输出转换为输入图片的bbox位置以及类别。只支持SigmaStar后处理算子 [SigmaStar后处理模块]。其他后处理请使用Unknown。</li> <li><code>Unknown</code>: 模型输出不属于上述两种类型，会输出全部的Tensor数值。 </li> </ul> <p><code>-t</code>, <code>--type</code>: 模型的数据类型： </p> <ul> <li><code>Float</code>: 浮点模型。</li> <li><code>Fixed</code>: 定点在线模型。 </li> <li><code>Offline</code>: 定点离线模型。</li> </ul> <p><strong>Please Note:</strong> </p> <ul> <li>不加-n/--preprocess参数时，-i/--image不能给图片或图片文件夹路径。 </li> <li>-i/--image参数传入 <strong>指定图片路径列表文件</strong> 的形式时：</li> </ul> <div class=highlight><pre><span></span><code>网络模型为单输入时：
~/SGS_IPU_SDK/image_test/2007000364.jpg
~/SGS_IPU_SDK/image_test/ILSVRC2012_test_00000002.bmp
...


网络模型为多输入时：
~/SGS_IPU_SDK/image_test/2007000364.jpg,~/SGS_IPU_SDK/image_test/ILSVRC2012_test_00000002.bmp
~/SGS_IPU_SDK/image_test/2007000365.jpg,~/SGS_IPU_SDK/image_test/ILSVRC2012_test_00000003.bmp
...
</code></pre></div> <hr> <h3 id=512>5.1.2 可选参数<a class=headerlink href=#512 title="Permanent link">&para;</a></h3> <p><code>-l</code>, <code>--label</code>: 数据集的标签文件路径 / 图片文字描述的标签。 </p> <p><code>-n</code>, <code>--preprocess</code>: 模型名称，与图片前处理方法相关，详见 [3.2. 图片前处理方法]。 也可以完成前处理文件配置后，给定前处理文件路径。不加该参数，图片参数需要给原始数据，可采用--save_input保存图片数据后，根据该格式制定其他的原始数据。 </p> <div class="admonition 注意事项"> <p class=admonition-title>注意事项</p> <p>若模型为 <strong>多输入</strong> 时，<code>-n,--preprocess</code> 参数用法需要多个前处理方法，例如 <strong>-n preprocess1.py,preprocess2.py</strong> 或者 <strong>--preprocess preprocess1.py,preprocess2.py</strong></p> </div> <p><code>--tool</code>: sgs_simulator文件路径。 </p> <p><code>--skip_garbage</code>: Fixed和Offline模型输出结果跳过无用数据。</p> <p><code>--dump_rawdata</code>: 保存Fixed和Offline模型输入二进制数据，文件名为图片名 + .bin，保存在当前路径。</p> <p><code>--num_process</code>: 进程数，运行同时运行的进程数。（可选参数，不加该参数默认为单线程）验证数据集必须加此参数且进程数 &gt; 1。</p> <p><code>--draw_result</code>: 绘制目标检测网络选框结果，参数为结果保存文件夹路径（文件夹不存在会自动创建）和画框的阈值，使用逗号( , )分隔保存路径与阈值。输入阈值画出大于阈值的检测结果，不输入阈值则画出全部检测结果。</p> <p><code>--continue_run</code>: 接着上次数据集剩下的部分运行。</p> <p><code>--save_input</code>: 保存simulator 前处理后的文件，默认不保存。文件为文本文件，会在运行路径下创建文件夹tmp_image，文件保存在 <strong>./tmp_image/imagename</strong> 图片名。</p> <hr> <h3 id=513>5.1.3 注意事项<a class=headerlink href=#513 title="Permanent link">&para;</a></h3> <ul> <li>simulator工具会寻找系统变量从而获得对应阶段任务所需工具路径，因此默认情况下参数-t/--tool不用指定相关工具的位置。</li> <li>simulator工具每次运行结束后会在当前目录创建log目录，log目录下的output文件夹，存放模型推演的结果。</li> <li>当 <code>-i/--image</code> 的参数为单张图片的路径时，simulator只对该图片推演；当 <code>-i/--image</code> 的参数为图片文件夹的路径时，simulator会对文件夹内的图片全部推演，此时增加--num_process（参数 &gt; 1），可以开启多进程推演。</li> <li>当 <code>-l/--label</code> 的参数为数据集标签文件路径时必须使用--num_process参数，数据集验证支持ImageNet（Top1、Top5）、COCO目标检测(mmAP)；当 <code>-l/--label</code> 参数为图片文字描述标签时，不能使用选项--num_process。</li> <li><code>-c/--category</code> 的参数选择Unknown时，当input_config.ini中[OUTPUT_CONFIG]的dequantizations为'TRUE'时，会在转换Fixed模型时在增加Fix2float算子，该算子会转换定点数据到浮点数据，定点和离线模型的输出Tensor维度与模型维度相同</li> </ul> <div class=highlight><pre><span></span><code><span class=n>layer46</span><span class=o>-</span><span class=n>conv</span> <span class=n>Tensor</span><span class=p>:</span>
<span class=p>{</span>
<span class=n>tensor</span> <span class=n>dim</span><span class=p>:</span><span class=mi>4</span><span class=p>,</span> <span class=n>Original</span> <span class=n>shape</span><span class=p>:[</span><span class=mi>1</span> <span class=mi>13</span> <span class=mi>13</span> <span class=mi>255</span><span class=p>]</span>
<span class=n>The</span> <span class=n>following</span> <span class=n>tensor</span> <span class=n>data</span> <span class=n>shape</span> <span class=ow>is</span> <span class=n>alignment</span> <span class=n>shape</span><span class=o>.</span>
<span class=n>tensor</span> <span class=n>data</span><span class=p>:</span>
<span class=o>...</span>
</code></pre></div> <hr> <h2 id=52-calibrator_customsimulator>5.2. calibrator_custom.simulator<a class=headerlink href=#52-calibrator_customsimulator title="Permanent link">&para;</a></h2> <p>calibrator_custom.simulator是基于Python的快速量化和转换模型的模块。使用calibrator_custom.simulator可以更方便灵活的对多输入、多段网络进行量化和转换。calibrator_custom.simulator包含3种，用于解析不同阶段的模型。</p> <p><code>calibrator_custom.float_simulator</code> 用于解析float模型</p> <p><code>calibrator_custom.fixed_simulator</code> 用于解析fixed模型</p> <p><code>calibrator_custom.offline_simulator</code> 用于解析offline模型</p> <p>目前基于的docker环境，提供Python3.7的预编译的Python模块。由于fixed模型和offline模型使用32位运行库，因此需使用32位Python3.7环境运行。Docker中已更新32位Python3.7环境，使用python32即可运行。float模型使用64位Python运行。</p> <p>下面基于calibrator_custom.float_simulator，说明使用方法和相关API接口：</p> <div class=highlight><pre><span></span><code><span class=kn>import</span> <span class=nn>calibrator_custom</span>
<span class=n>model_path</span> <span class=o>=</span> <span class=s1>&#39;./mobilenet_v2_float.sim&#39;</span>
<span class=n>model</span> <span class=o>=</span> <span class=n>calibrator_custom</span><span class=o>.</span><span class=n>float_simulator</span><span class=p>(</span><span class=n>model_path</span><span class=p>)</span> 
</code></pre></div> <p>calibrator_custom.float_simulator</p> <p>使用calibrator_custom.float_simulator时，需要给定float.sim模型的路径，用于创建float_simulator的实例。参数给定错误，将无法成功创建float_simulator实例，并返回ValueError。</p> <hr> <h3 id=521-calibrator_customsimulator>5.2.1 calibrator_custom.simulator方法<a class=headerlink href=#521-calibrator_customsimulator title="Permanent link">&para;</a></h3> <p><code>get_input_details</code>: </p> <p>返回网络模型输入信息（list）</p> <p>Float模型返回如下：</p> <div class=highlight><pre><span></span><code> <span class=n>input_details</span> <span class=o>=</span> <span class=n>model</span><span class=o>.</span><span class=n>get_input_details</span><span class=p>()</span>
 <span class=nb>print</span><span class=p>(</span><span class=n>input_details</span><span class=p>)</span>
 <span class=p>[</span>
  <span class=p>{</span>
    <span class=s1>&#39;name&#39;</span><span class=p>:</span> <span class=s1>&#39;sub_7&#39;</span><span class=p>,</span> 
    <span class=s1>&#39;shape&#39;</span><span class=p>:</span> <span class=n>array</span><span class=p>([</span>  <span class=mi>1</span><span class=p>,</span> <span class=mi>513</span><span class=p>,</span> <span class=mi>513</span><span class=p>,</span> <span class=mi>3</span><span class=p>],</span> <span class=n>dtype</span><span class=o>=</span><span class=n>int32</span><span class=p>),</span> 
    <span class=s1>&#39;dtype&#39;</span><span class=p>:</span> <span class=o>&lt;</span><span class=k>class</span> <span class=err>&#39;</span><span class=nc>numpy</span><span class=o>.</span><span class=n>float32</span><span class=s1>&#39;&gt;, </span>
    <span class=s1>&#39;index&#39;</span><span class=p>:</span> <span class=mi>0</span>
  <span class=p>}</span>
<span class=p>]</span> 
</code></pre></div> <p>返回的list中根据模型输入个数包含以下dict信息：</p> <p><code>index</code>: 输入Tensor序号</p> <p><code>name</code>: 输入Tensor名称</p> <p><code>shape</code>: 输入Tensor的形状</p> <p><code>dtype</code>: 输入Tensor的数据类型</p> <p>Fixed和Offline模型返回如下：</p> <div class=highlight><pre><span></span><code> <span class=o>&gt;&gt;&gt;</span> <span class=n>input_details</span> <span class=o>=</span> <span class=n>model</span><span class=o>.</span><span class=n>get_input_details</span><span class=p>()</span>
 <span class=o>&gt;&gt;&gt;</span> <span class=nb>print</span><span class=p>(</span><span class=n>input_details</span><span class=p>)</span>
<span class=p>[</span>
  <span class=p>{</span>
    <span class=s1>&#39;index&#39;</span><span class=p>:</span> <span class=mi>0</span><span class=p>,</span> 
    <span class=s1>&#39;shape&#39;</span><span class=p>:</span> <span class=n>array</span><span class=p>([</span>  <span class=mi>1</span><span class=p>,</span> <span class=mi>513</span><span class=p>,</span> <span class=mi>513</span><span class=p>,</span> <span class=mi>3</span><span class=p>]),</span> 
    <span class=s1>&#39;dtype&#39;</span><span class=p>:</span> <span class=o>&lt;</span><span class=k>class</span> <span class=err>&#39;</span><span class=nc>numpy</span><span class=o>.</span><span class=n>uint8</span><span class=s1>&#39;&gt;, </span>
    <span class=s1>&#39;name&#39;</span><span class=p>:</span> <span class=s1>&#39;sub_7&#39;</span> <span class=p>,</span> 
    <span class=s1>&#39;input_formats&#39;</span><span class=p>:</span> <span class=s1>&#39;RGB&#39;</span><span class=p>,</span> 
    <span class=s1>&#39;training_input_formats&#39;</span><span class=p>:</span> <span class=s1>&#39;RGB&#39;</span>
  <span class=p>}</span>
<span class=p>]</span> 
</code></pre></div> <hr> <p><code>get_output_detail</code></p> <p>返回的list中根据模型输入个数包含以下dict信息：</p> <p><code>index</code>: 输入Tensor序号</p> <p><code>name</code>: 输入Tensor名称</p> <p><code>shape</code>: 输入Tensor的形状</p> <p><code>dtype</code>: 输入Tensor的数据类型</p> <p><code>input_formats</code>: 网络模型实际运行时的图片输入格式</p> <p><code>training_input_formats</code>: 网络模型训练是的图片输入格式</p> <p>返回网络模型输出信息（list）</p> <p>Float模型返回如下：</p> <div class=highlight><pre><span></span><code><span class=o>&gt;&gt;&gt;</span> <span class=n>output_details</span> <span class=o>=</span> <span class=n>model</span><span class=o>.</span><span class=n>get_output_details</span><span class=p>()</span>
<span class=o>&gt;&gt;&gt;</span> <span class=nb>print</span><span class=p>(</span><span class=n>output_details</span><span class=p>)</span> 
<span class=p>[</span>
  <span class=p>{</span>
    <span class=s1>&#39;name&#39;</span><span class=p>:</span> <span class=s1>&#39;MobilenetV2/Conv/Conv2D&#39;</span><span class=p>,</span> 
    <span class=s1>&#39;shape&#39;</span><span class=p>:</span> <span class=n>array</span><span class=p>([</span>  <span class=mi>1</span><span class=p>,</span> <span class=mi>257</span><span class=p>,</span> <span class=mi>257</span><span class=p>,</span>  <span class=mi>30</span><span class=p>],</span> <span class=n>dtype</span><span class=o>=</span><span class=n>int32</span><span class=p>),</span>
    <span class=s1>&#39;dtype&#39;</span><span class=p>:</span> <span class=o>&lt;</span><span class=k>class</span> <span class=err>&#39;</span><span class=nc>numpy</span><span class=o>.</span><span class=n>float32</span><span class=s1>&#39;&gt;, </span>
    <span class=s1>&#39;index&#39;</span><span class=p>:</span> <span class=mi>0</span>
  <span class=p>}</span>
<span class=p>]</span> 
</code></pre></div> <p>返回的list中根据模型输出个数包含以下dict信息：</p> <p><code>index</code>: 输出Tensor序号</p> <p><code>name</code>: 输出Tensor名称</p> <p><code>shape</code>: 输出Tensor的形状</p> <p><code>dtype</code>: 输出Tensor的数据类型</p> <p>Fixed和Offline模型返回如下：</p> <div class=highlight><pre><span></span><code> <span class=o>&gt;&gt;&gt;</span> <span class=n>output_details</span> <span class=o>=</span> <span class=n>model</span><span class=o>.</span><span class=n>get_output_details</span><span class=p>()</span>
 <span class=o>&gt;&gt;&gt;</span> <span class=nb>print</span><span class=p>(</span><span class=n>output_details</span><span class=p>)</span>
<span class=p>[</span>
  <span class=p>{</span>
    <span class=s1>&#39;index&#39;</span><span class=p>:</span> <span class=mi>0</span><span class=p>,</span> 
    <span class=s1>&#39;shape&#39;</span><span class=p>:</span> <span class=n>array</span><span class=p>([</span>  <span class=mi>1</span><span class=p>,</span> <span class=mi>257</span><span class=p>,</span> <span class=mi>257</span><span class=p>,</span>  <span class=mi>30</span><span class=p>]),</span> 
    <span class=s1>&#39;name&#39;</span><span class=p>:</span> <span class=s1>&#39;MobilenetV2/Conv/Conv2D&#39;</span><span class=p>,</span> 
    <span class=s1>&#39;dtype&#39;</span><span class=p>:</span> <span class=o>&lt;</span><span class=k>class</span> <span class=err>&#39;</span><span class=nc>numpy</span><span class=o>.</span><span class=n>int16</span><span class=s1>&#39;&gt;, </span>
    <span class=s1>&#39;quantization&#39;</span><span class=p>:</span> <span class=p>(</span><span class=mf>0.00013832777040079236</span><span class=p>,</span> <span class=mi>0</span><span class=p>)</span>
  <span class=p>}</span>
<span class=p>]</span> 
</code></pre></div> <p>返回的list中根据模型输出个数包含以下dict信息：</p> <p><code>index</code>: 输出Tensor序号</p> <p><code>name</code>: 输出Tensor名称</p> <p><code>shape</code>: 输出Tensor的形状</p> <p><code>dtype</code>: 输出Tensor的数据类型</p> <p><code>quantization</code>: 输出Tensor的scale和zero_point（需将模型输出Tensor乘scale得到浮点数）</p> <hr> <p><code>set_input</code>:</p> <p>设置网络模型输入数据</p> <div class=highlight><pre><span></span><code><span class=o>&gt;&gt;&gt;</span> <span class=n>model</span><span class=o>.</span><span class=n>set_input</span><span class=p>(</span><span class=mi>0</span><span class=p>,</span> <span class=n>img_data</span><span class=p>)</span>  
</code></pre></div> <p>输入数据，0为输入Tensor的index，可以在get_input_details()的返回值里拿到。img_data是与model输入shape和dtype相同的numpy.ndarray格式数据，错误的shape或dtype，将导致set_input返回ValueError。如果模型有多个输入，可以多次调用set_input，根据get_input_details()的返回值里拿到index设置对应Tensor的输入数据。</p> <hr> <p><code>invoke</code></p> <p>模型运行一次</p> <div class=highlight><pre><span></span><code><span class=o>&gt;&gt;&gt;</span> <span class=n>model</span><span class=o>.</span><span class=n>invoke</span><span class=p>()</span>
</code></pre></div> <p>调用invoke前请先使用set_input设置输入数据，未调用set_input直接调用invoke会返回ValueError。</p> <hr> <p><code>get_output</code></p> <p>获取网络模型输出数据</p> <div class=highlight><pre><span></span><code><span class=o>&gt;&gt;&gt;</span> <span class=n>result</span> <span class=o>=</span> <span class=n>model</span><span class=o>.</span><span class=n>get_output</span><span class=p>(</span><span class=mi>0</span><span class=p>)</span>
</code></pre></div> <p>获取输出数据，0为输出Tensor的index，可以在get_output_details()的返回值里拿到，返回numpy.ndarray格式输出数据。如果模型有多个输出，可以多次调用get_output，根据get_output_details()的返回值里拿到index获取对应Tensor的输出数据。</p> <p>假设模型输出的shape为[1, 257, 257, 30]</p> <p>Float模型查看结果的shape</p> <div class=highlight><pre><span></span><code><span class=o>&gt;&gt;&gt;</span> <span class=nb>print</span><span class=p>(</span><span class=n>result</span><span class=o>.</span><span class=n>shape</span><span class=p>)</span>
<span class=p>(</span><span class=mi>1</span><span class=p>,</span> <span class=mi>257</span><span class=p>,</span> <span class=mi>257</span><span class=p>,</span> <span class=mi>30</span><span class=p>)</span>
</code></pre></div> <p>Fixed和Offline模型查看结果shape</p> <div class=highlight><pre><span></span><code> <span class=o>&gt;&gt;&gt;</span> <span class=nb>print</span><span class=p>(</span><span class=n>result</span><span class=o>.</span><span class=n>shape</span><span class=p>)</span>
 <span class=p>(</span><span class=mi>1</span><span class=p>,</span> <span class=mi>257</span><span class=p>,</span> <span class=mi>257</span><span class=p>,</span> <span class=mi>32</span><span class=p>)</span>
</code></pre></div> <p>Fixed和Offline模型输出Tensor因为模拟了在硬件的储存方式，Tensor的最后一个维度会向上对齐，因此输出的维度为Tensor对齐的维度。最后对齐的数据是无用数据，可以使用如下方法去除：</p> <div class=highlight><pre><span></span><code> <span class=o>&gt;&gt;&gt;</span> <span class=n>result</span> <span class=o>=</span> <span class=n>result</span><span class=p>[</span><span class=o>...</span><span class=p>,</span> <span class=p>:</span><span class=n>output_details</span><span class=p>[</span><span class=mi>0</span><span class=p>][</span><span class=s1>&#39;shape&#39;</span><span class=p>][</span><span class=o>-</span><span class=mi>1</span><span class=p>]]</span>
 <span class=o>&gt;&gt;&gt;</span> <span class=nb>print</span><span class=p>(</span><span class=n>result</span><span class=o>.</span><span class=n>shape</span><span class=p>)</span>
 <span class=p>(</span><span class=mi>1</span><span class=p>,</span> <span class=mi>257</span><span class=p>,</span> <span class=mi>257</span><span class=p>,</span> <span class=mi>30</span><span class=p>)</span>
</code></pre></div> <p>配置input_config.ini中[OUTPUT_CONFIG]的dequantizations为TRUE时，定点和离线模型的输出Tensor的shape与Float模型维度相同。</p> <hr> <p><code>get_tensor_details</code></p> <p>返回网络模型每个Tensor的信息（list）</p> <p>Float模型返回如下：</p> <div class=highlight><pre><span></span><code> <span class=o>&gt;&gt;&gt;</span> <span class=n>tensor_details</span> <span class=o>=</span> <span class=n>model</span><span class=o>.</span><span class=n>get_tensor_details</span><span class=p>()</span>
 <span class=o>&gt;&gt;&gt;</span> <span class=nb>print</span><span class=p>(</span><span class=n>tensor_details</span><span class=p>)</span>
<span class=p>[</span>
  <span class=p>{</span>
    <span class=s1>&#39;name&#39;</span><span class=p>:</span> <span class=s1>&#39;MobilenetV2/Conv/Conv2D&#39;</span><span class=p>,</span> 
    <span class=s1>&#39;shape&#39;</span><span class=p>:</span> <span class=n>array</span><span class=p>([</span>  <span class=mi>1</span><span class=p>,</span> <span class=mi>257</span><span class=p>,</span> <span class=mi>257</span><span class=p>,</span>  <span class=mi>30</span><span class=p>],</span> <span class=n>dtype</span><span class=o>=</span><span class=n>int32</span><span class=p>),</span> 
    <span class=s1>&#39;dtype&#39;</span><span class=p>:</span> <span class=s1>&#39;FLOAT32&#39;</span><span class=p>,</span> 
    <span class=s1>&#39;qtype&#39;</span><span class=p>:</span> <span class=s1>&#39;INT16&#39;</span>
  <span class=p>},</span> 
  <span class=p>{</span>
    <span class=s1>&#39;name&#39;</span><span class=p>:</span> <span class=s1>&#39;MobilenetV2/Conv/Conv2D_bias&#39;</span><span class=p>,</span> 
    <span class=s1>&#39;shape&#39;</span><span class=p>:</span> <span class=n>array</span><span class=p>(</span> <span class=p>[</span> <span class=mi>2</span><span class=p>,</span> <span class=mi>30</span><span class=p>],</span> <span class=n>dtype</span><span class=o>=</span><span class=n>int32</span><span class=p>),</span> 
    <span class=s1>&#39;dtype&#39;</span><span class=p>:</span> <span class=s1>&#39;FLOAT32&#39;</span><span class=p>,</span> 
    <span class=s1>&#39;qtype&#39;</span><span class=p>:</span> <span class=s1>&#39;INT16&#39;</span>
  <span class=p>},</span> 
  <span class=p>{</span>
    <span class=s1>&#39;name&#39;</span><span class=p>:</span> <span class=s1>&#39;MobilenetV2/Conv/weights/read&#39;</span><span class=p>,</span> 
    <span class=s1>&#39;shape&#39;</span><span class=p>:</span> <span class=n>array</span><span class=p>(</span> <span class=p>[</span><span class=mi>30</span><span class=p>,</span>  <span class=mi>3</span><span class=p>,</span>  <span class=mi>3</span><span class=p>,</span>  <span class=mi>3</span><span class=p>],</span> <span class=n>dtype</span><span class=o>=</span><span class=n>int32</span><span class=p>),</span> 
    <span class=s1>&#39;dtype&#39;</span><span class=p>:</span> <span class=s1>&#39;FLOAT32&#39;</span><span class=p>,</span> 
    <span class=s1>&#39;qtype&#39;</span><span class=p>:</span>  <span class=s1>&#39;INT8&#39;</span>
  <span class=p>},</span> 
  <span class=p>{</span>
    <span class=s1>&#39;name&#39;</span><span class=p>:</span> <span class=s1>&#39;sub_7&#39;</span><span class=p>,</span> 
    <span class=s1>&#39;shape&#39;</span><span class=p>:</span> <span class=n>array</span><span class=p>([</span>  <span class=mi>1</span><span class=p>,</span> <span class=mi>513</span><span class=p>,</span> <span class=mi>513</span><span class=p>,</span> <span class=mi>3</span><span class=p>],</span> <span class=n>dtype</span><span class=o>=</span><span class=n>int32</span><span class=p>),</span> 
    <span class=s1>&#39;dtype&#39;</span><span class=p>:</span> <span class=s1>&#39;FLOAT32&#39;</span><span class=p>,</span> 
    <span class=s1>&#39;qtype&#39;</span><span class=p>:</span> <span class=s1>&#39;UINT8&#39;</span>
  <span class=p>}</span>
<span class=p>]</span> 
</code></pre></div> <p>返回的list中根据模型Tensor个数包含以下dict信息：</p> <p><code>name</code>: Tensor名称</p> <p><code>shape</code>: Tensor的形状</p> <p><code>dtype</code>: Tensor的数据类型</p> <p><code>qtype</code>: 定点模型该Tensor可能的数据类型（quantization type）</p> <p>Fixed和Offline模型如果input_config.ini中[OUTPUT_CONFIG]的dequantizations配置为FALSE时返回如下：</p> <div class=highlight><pre><span></span><code> <span class=o>&gt;&gt;&gt;</span> <span class=n>tensor_details</span> <span class=o>=</span> <span class=n>model</span><span class=o>.</span><span class=n>get_tensor_details</span><span class=p>()</span>
 <span class=o>&gt;&gt;&gt;</span> <span class=nb>print</span><span class=p>(</span><span class=n>tensor_details</span><span class=p>)</span>
 <span class=p>[</span>
    <span class=p>{</span>
      <span class=s1>&#39;shape&#39;</span><span class=p>:</span> <span class=n>array</span><span class=p>([</span>  <span class=mi>1</span><span class=p>,</span> <span class=mi>257</span><span class=p>,</span> <span class=mi>257</span><span class=p>,</span>  <span class=mi>30</span><span class=p>]),</span> 
      <span class=s1>&#39;quantization&#39;</span><span class=p>:</span> <span class=p>[(</span><span class=mf>0.00013832777040079236</span><span class=p>,</span> <span class=mi>0</span><span class=p>)],</span> 
      <span class=s1>&#39;min&#39;</span><span class=p>:</span> <span class=p>[</span><span class=o>-</span><span class=mf>4.230099201202393</span><span class=p>],</span> 
      <span class=s1>&#39;max&#39;</span><span class=p>:</span> <span class=p>[</span><span class=mf>4.532586097717285</span><span class=p>],</span> 
      <span class=s1>&#39;name&#39;</span><span class=p>:</span> <span class=s1>&#39;MobilenetV2/Conv/Conv2D&#39;</span><span class=p>,</span>
      <span class=s1>&#39;dtype&#39;</span><span class=p>:</span> <span class=s1>&#39;INT16&#39;</span>
    <span class=p>},</span> 
    <span class=p>{</span>
      <span class=s1>&#39;shape&#39;</span><span class=p>:</span>  <span class=n>array</span><span class=p>([</span> <span class=mi>2</span><span class=p>,</span> <span class=mi>30</span><span class=p>]),</span> 
      <span class=s1>&#39;quantization&#39;</span><span class=p>:</span> <span class=p>[],</span> 
      <span class=s1>&#39;min&#39;</span><span class=p>:</span> <span class=p>[</span><span class=mf>0.0</span><span class=p>],</span> 
      <span class=s1>&#39;max &#39;</span><span class=p>:</span> <span class=p>[</span><span class=mf>1.0</span><span class=p>],</span> 
      <span class=s1>&#39;name&#39;</span><span class=p>:</span> <span class=s1>&#39;MobilenetV2/Conv/Conv2D_bias&#39;</span><span class=p>,</span> 
      <span class=s1>&#39;dtype&#39;</span><span class=p>:</span><span class=s1>&#39;INT16&#39;</span>
    <span class=p>},</span> 
    <span class=p>{</span>
      <span class=s1>&#39;shape&#39;</span><span class=p>:</span> <span class=n>array</span><span class=p>([</span><span class=mi>30</span><span class=p>,</span>  <span class=mi>3</span><span class=p>,</span>  <span class=mi>3</span><span class=p>,</span>  <span class=mi>3</span><span class=p>]),</span> 
      <span class=s1>&#39;quantization&#39;</span><span class=p>:</span> <span class=p>[(</span><span class=mf>0.004813921172171831</span><span class=p>,</span> <span class=mi>0</span><span class=p>)],</span> 
      <span class=s1>&#39;min&#39;</span><span class=p>:</span> <span class=p>[</span><span class=o>-</span><span class=mf>0.5498989820480347</span><span class=p>],</span> 
      <span class=s1>&#39;max&#39;</span><span class=p>:</span> <span class=p>[</span><span class=mf>0.6113680005073547</span><span class=p>],</span> 
      <span class=s1>&#39;name&#39;</span><span class=p>:</span> <span class=s1>&#39;MobilenetV2/Conv/weights/read&#39;</span><span class=p>,</span> 
      <span class=s1>&#39;dtype&#39;</span><span class=p>:</span> <span class=s1>&#39;INT8&#39;</span>
    <span class=p>},</span> 
    <span class=p>{</span>
      <span class=s1>&#39;shape&#39;</span><span class=p>:</span> <span class=n>array</span><span class=p>([</span>  <span class=mi>1</span><span class=p>,</span> <span class=mi>513</span><span class=p>,</span> <span class=mi>513</span><span class=p>,</span> <span class=mi>3</span> <span class=p>]),</span> 
      <span class=s1>&#39;quantization&#39;</span><span class=p>:</span> <span class=p>[(</span><span class=mf>0.007843137718737125</span><span class=p>,</span> <span class=mi>128</span><span class=p>)],</span> 
      <span class=s1>&#39;min&#39;</span><span class=p>:</span> <span class=p>[</span><span class=o>-</span><span class=mi>1</span> <span class=o>.</span><span class=mi>0</span><span class=p>],</span> 
      <span class=s1>&#39;max&#39;</span><span class=p>:</span> <span class=p>[</span><span class=mf>1.0</span><span class=p>],</span> 
      <span class=s1>&#39;name&#39;</span><span class=p>:</span> <span class=s1>&#39;sub_7&#39;</span><span class=p>,</span> 
      <span class=s1>&#39;dtype&#39;</span><span class=p>:</span> <span class=s1>&#39;UINT8&#39;</span>
    <span class=p>}</span>
  <span class=p>]</span> 
</code></pre></div> <p>返回的list中根据模型Tensor个数包含以下dict信息：</p> <p><code>name</code>: Tensor名称</p> <p><code>shape</code>: Tensor的形状</p> <p><code>dtype</code>: Tensor的数据类型</p> <p><code>quantization</code>: Tensor的scale和zero_point</p> <p><code>min</code>: Tensor的最小值</p> <p><code>max</code>: Tensor的最大值</p> <p>配置input_config.ini中[OUTPUT_CONFIG]的dequantizations为TRUE时，会在转换Fixed模型时在增加Fix2float算子，该算子会转换定点数据到浮点数据，因此model.get_tensor_details()将不再有'quantization'信息。</p> <hr> <h3 id=522-calibrator_customsim_simulator>5.2.2 calibrator_custom.SIM_Simulator<a class=headerlink href=#522-calibrator_customsim_simulator title="Permanent link">&para;</a></h3> <p>对于多输入、多段网络同时转换时，提供calibrator_custom.SIM_Simulator，方便进行简单定义后，统一运行。</p> <p>calibrator_custom.SIM_Simulator是已经实现好的class，当中只有forward方法未实现，使用时仅需实现该方法，即可转换完成。</p> <p>下面以 <em>SGS_IPU_SDK/Scripts/examples/sim_simulator.py</em> 为例，说明calibrator_custom.SIM_Simulator的使用方法：</p> <div class=highlight><pre><span></span><code><span class=kn>import</span> <span class=nn>calibrator_custom</span>
<span class=k>class</span> <span class=nc>Net</span><span class=p>(</span><span class=n>calibrator_custom</span><span class=o>.</span><span class=n>SIM_Simulator</span><span class=p>):</span>
    <span class=k>def</span> <span class=fm>__init__</span><span class=p>(</span><span class=bp>self</span><span class=p>):</span>
        <span class=nb>super</span><span class=p>()</span><span class=o>.</span><span class=fm>__init__</span><span class=p>()</span> 
        <span class=bp>self</span><span class=o>.</span><span class=n>model</span> <span class=o>=</span> <span class=n>calibrator_custom</span><span class=o>.</span><span class=n>fixed_simulator</span><span class=p>(</span><span class=n>model_path</span><span class=p>)</span>
    <span class=k>def</span> <span class=nf>forward</span><span class=p>(</span><span class=bp>self</span><span class=p>,</span> <span class=n>x</span><span class=p>):</span>
        <span class=n>out_details</span> <span class=o>=</span> <span class=bp>self</span><span class=o>.</span><span class=n>model</span><span class=o>.</span><span class=n>get_output_details</span><span class=p>()</span>
        <span class=bp>self</span><span class=o>.</span><span class=n>model</span><span class=o>.</span><span class=n>set_input</span><span class=p>(</span><span class=mi>0</span><span class=p>,</span> <span class=n>x</span><span class=p>)</span>
        <span class=bp>self</span><span class=o>.</span><span class=n>model</span><span class=o>.</span><span class=n>invoke</span><span class=p>()</span>
        <span class=n>result_list</span> <span class=o>=</span> <span class=p>[]</span>
        <span class=k>for</span> <span class=n>idx</span> <span class=ow>in</span> <span class=nb>range</span><span class=p>(</span><span class=nb>len</span><span class=p>(</span><span class=n>out_details</span><span class=p>)):</span>
            <span class=n>result</span> <span class=o>=</span> <span class=bp>self</span><span class=o>.</span><span class=n>model</span><span class=o>.</span><span class=n>get_output</span><span class=p>(</span><span class=n>idx</span><span class=p>)</span>
            <span class=c1># for Fixed and Offline model</span>
            <span class=k>if</span> <span class=n>result</span><span class=o>.</span><span class=n>shape</span><span class=p>[</span><span class=o>-</span><span class=mi>1</span><span class=p>]</span> <span class=o>!=</span> <span class=n>out_details</span><span class=p>[</span><span class=n>idx</span><span class=p>][</span><span class=s1>&#39;shape&#39;</span><span class=p>][</span><span class=o>-</span><span class=mi>1</span><span class=p>]:</span>
                <span class=n>result</span> <span class=o>=</span> <span class=n>result</span><span class=p>[</span><span class=o>...</span><span class=p>,</span> <span class=p>:</span><span class=n>out_details</span><span class=p>[</span><span class=n>idx</span><span class=p>][</span><span class=s1>&#39;shape&#39;</span><span class=p>][</span><span class=o>-</span><span class=mi>1</span><span class=p>]]</span>
            <span class=k>if</span> <span class=n>out_details</span><span class=p>[</span><span class=n>idx</span><span class=p>][</span><span class=s1>&#39;dtype&#39;</span><span class=p>]</span> <span class=o>==</span> <span class=n>np</span><span class=o>.</span><span class=n>int16</span><span class=p>:</span>
                <span class=n>scale</span><span class=p>,</span> <span class=n>_</span> <span class=o>=</span> <span class=n>out_details</span><span class=p>[</span><span class=n>idx</span><span class=p>][</span><span class=s1>&#39;quantization&#39;</span><span class=p>]</span>
                <span class=n>result</span> <span class=o>=</span> <span class=n>np</span><span class=o>.</span><span class=n>dot</span><span class=p>(</span><span class=n>result</span><span class=p>,</span> <span class=n>scale</span><span class=p>)</span>
            <span class=n>result_list</span><span class=o>.</span><span class=n>append</span><span class=p>(</span><span class=n>result</span><span class=p>)</span>
        <span class=k>return</span> <span class=n>result_list</span>
</code></pre></div> <p>定义forward方法，forward的参数为模型输入，如有多个输入，可增加forward的参数。</p> <hr> <p>创建calibrator_custom.SIM_Simulator的实例 <div class=highlight><pre><span></span><code>  <span class=n>net</span> <span class=o>=</span> <span class=n>Net</span><span class=p>()</span>
</code></pre></div></p> <hr> <p>调用calibrator_custom.SIM_Simulator实例的方法 <div class=highlight><pre><span></span><code>  <span class=n>result</span> <span class=o>=</span> <span class=n>net</span><span class=p>(</span><span class=n>img_gen</span><span class=p>,</span> <span class=n>num_process</span><span class=o>=</span><span class=mi>4</span><span class=p>)</span>
</code></pre></div></p> <hr> <p>调用calibrator_custom.SIM_Simulator实例，需要给定输入图片的numpy.ndarray或者图片生成器。</p> <p>当num_process大于1时，img_gen必须为图片生成器。</p> <ul> <li>图片生成器（img_gen）</li> </ul> <p>为方便多输入、多段网络转换模型，通过生成器方便组织输入图片的序列。如模型有多个输入，生成器应该按照定义forward时的输入顺序，返回有多个numpy.ndarray的list。</p> <p>calibrator_custom.utils.image_preprocess_func使用预先定义好的前处理方法 <div class=highlight><pre><span></span><code><span class=n>preprocess_func</span> <span class=o>=</span> <span class=n>calibrator_custom</span><span class=o>.</span><span class=n>utils</span><span class=o>.</span><span class=n>image_preprocess_func</span><span class=p>(</span><span class=n>model_name</span><span class=p>)</span>
<span class=k>def</span> <span class=nf>image_generator</span><span class=p>(</span><span class=n>folder_path</span><span class=p>,</span> <span class=n>preprocess_func</span><span class=p>,</span> <span class=n>norm</span><span class=p>):</span>
    <span class=n>images</span> <span class=o>=</span> <span class=p>[</span><span class=n>os</span><span class=o>.</span><span class=n>path</span><span class=o>.</span><span class=n>join</span><span class=p>(</span><span class=n>folder_path</span><span class=p>,</span> <span class=n>img</span><span class=p>)</span> <span class=k>for</span> <span class=n>img</span> <span class=ow>in</span> <span class=n>os</span><span class=o>.</span><span class=n>listdir</span><span class=p>(</span><span class=n>folder_path</span><span class=p>)]</span>
    <span class=k>for</span> <span class=n>image</span> <span class=ow>in</span> <span class=n>images</span><span class=p>:</span>
        <span class=n>img</span> <span class=o>=</span> <span class=n>preprocess_func</span><span class=p>(</span><span class=n>image</span><span class=p>,</span> <span class=n>norm</span><span class=p>)</span>
        <span class=k>yield</span> <span class=p>[</span><span class=n>img</span><span class=p>]</span>
<span class=n>img_gen</span> <span class=o>=</span> <span class=n>image_generator</span><span class=p>(</span><span class=s1>&#39;./images&#39;</span><span class=p>,</span> <span class=n>preprocess_func</span><span class=p>,</span> <span class=kc>False</span><span class=p>)</span>
</code></pre></div></p> </article> </div> </div> </main> <footer class=md-footer> <nav class="md-footer__inner md-grid" aria-label=Footer> <a href=../Common/Compile.html class="md-footer__link md-footer__link--prev" rel=prev> <div class="md-footer__button md-icon"> <svg xmlns=http://www.w3.org/2000/svg viewbox="0 0 24 24"><path d="M20 11v2H8l5.5 5.5-1.42 1.42L4.16 12l7.92-7.92L13.5 5.5 8 11h12z"/></svg> </div> <div class=md-footer__title> <div class=md-ellipsis> <span class=md-footer__direction> Previous </span> 4. Compiler </div> </div> </a> <a href=../Common/DumpDebug_Tool.html class="md-footer__link md-footer__link--next" rel=next> <div class=md-footer__title> <div class=md-ellipsis> <span class=md-footer__direction> Next </span> 6. DumpDebug Tool </div> </div> <div class="md-footer__button md-icon"> <svg xmlns=http://www.w3.org/2000/svg viewbox="0 0 24 24"><path d="M4 11v2h12l-5.5 5.5 1.42 1.42L19.84 12l-7.92-7.92L10.5 5.5 16 11H4z"/></svg> </div> </a> </nav> <div class="md-footer-meta md-typeset"> <div class="md-footer-meta__inner md-grid"> <div class=md-footer-copyright> <div class=md-footer-copyright__highlight> Copyright&copy; 2021 SigmaStar Technology. All rights reserved. Security Level: Confidential A. </div> Made with <a href=https://squidfunk.github.io/mkdocs-material/ target=_blank rel=noopener> Material for MkDocs </a> </div> </div> </div> </footer> </div> <div class=md-dialog data-md-component=dialog> <div class="md-dialog__inner md-typeset"></div> </div> <script id=__config type=application/json>{"base": "../../..", "features": [], "translations": {"clipboard.copy": "Copy to clipboard", "clipboard.copied": "Copied to clipboard", "search.config.lang": "en", "search.config.pipeline": "trimmer, stopWordFilter", "search.config.separator": "[\\s\\-]+", "search.placeholder": "Search", "search.result.placeholder": "Type to start searching", "search.result.none": "No matching documents", "search.result.one": "1 matching document", "search.result.other": "# matching documents", "search.result.more.one": "1 more on this page", "search.result.more.other": "# more on this page", "search.result.term.missing": "Missing"}, "search": "../../../assets/javascripts/workers/search.fb4a9340.min.js", "version": null}</script> <script src=../../../assets/javascripts/bundle.a1c7c35e.min.js></script> <script src=../../../search/search_index.js></script> <script src=../../../javascripts/extra.js></script> <script src="https://cdnjs.cloudflare.com/ajax/libs/mathjax/2.7.0/MathJax.js?config=TeX-MML-AM_CHTML"></script> <script src=https://cdnjs.cloudflare.com/ajax/libs/raphael/2.2.7/raphael.min.js></script> <script src=https://cdnjs.cloudflare.com/ajax/libs/underscore.js/1.8.3/underscore-min.js></script> <script src=https://cdnjs.cloudflare.com/ajax/libs/js-sequence-diagrams/1.0.6/sequence-diagram-min.js></script> <script src=https://cdnjs.cloudflare.com/ajax/libs/flowchart/1.6.5/flowchart.min.js></script> <script src=https://unpkg.com/freezeframe/dist/freezeframe.min.js></script> <script src=https://unpkg.com/mermaid@7.1.0/dist/mermaid.min.js></script> <script src=../../../javascripts/umlconvert.js></script> </body> </html>