<!doctype html><html lang=en class=no-js> <head><meta charset=utf-8><meta name=viewport content="width=device-width,initial-scale=1"><meta name=description content="Personal Documentation System Template"><meta name=author content="<PERSON> Cho<PERSON>"><link rel=icon href=../../../images/favicon.ico><meta name=generator content="mkdocs-1.1.2, mkdocs-material-7.0.6"><title>2. Convert Tool - IPU SDK</title><link rel=stylesheet href=../../../assets/stylesheets/main.2c0c5eaf.min.css><link rel=stylesheet href=../../../assets/stylesheets/palette.7fa14f5b.min.css><meta name=theme-color content=#009485><link rel=preconnect href=https://fonts.gstatic.com crossorigin><link rel=stylesheet href="https://fonts.googleapis.com/css?family=Roboto:300,400,400i,700%7CRoboto+Mono&display=fallback"><style>:root{--md-text-font-family:"Roboto";--md-code-font-family:"Roboto Mono"}</style><link rel=stylesheet href=../../../stylesheets/extra.css></head> <body dir=ltr data-md-color-scheme data-md-color-primary=teal data-md-color-accent=teal> <input class=md-toggle data-md-toggle=drawer type=checkbox id=__drawer autocomplete=off> <input class=md-toggle data-md-toggle=search type=checkbox id=__search autocomplete=off> <label class=md-overlay for=__drawer></label> <div data-md-component=skip> <a href=#21 class=md-skip> Skip to content </a> </div> <div data-md-component=announce> </div> <header class=md-header data-md-component=header> <nav class="md-header__inner md-grid" aria-label=Header> <a href=../../.. title="IPU SDK" class="md-header__button md-logo" aria-label="IPU SDK" data-md-component=logo> <svg xmlns=http://www.w3.org/2000/svg viewbox="0 0 24 24"><path d="M10 20v-6h4v6h5v-8h3L12 3 2 12h3v8h5z"/></svg> </a> <label class="md-header__button md-icon" for=__drawer> <svg xmlns=http://www.w3.org/2000/svg viewbox="0 0 24 24"><path d="M3 6h18v2H3V6m0 5h18v2H3v-2m0 5h18v2H3v-2z"/></svg> </label> <div class=md-header__title data-md-component=header-title> <div class=md-header__ellipsis> <div class=md-header__topic> <span class=md-ellipsis> IPU SDK </span> </div> <div class=md-header__topic data-md-component=header-topic> <span class=md-ellipsis> 2. Convert Tool </span> </div> </div> </div> <div class=md-header__options> </div> <label class="md-header__button md-icon" for=__search> <svg xmlns=http://www.w3.org/2000/svg viewbox="0 0 24 24"><path d="M9.5 3A6.5 6.5 0 0 1 16 9.5c0 1.61-.59 3.09-1.56 4.23l.27.27h.79l5 5-1.5 1.5-5-5v-.79l-.27-.27A6.516 6.516 0 0 1 9.5 16 6.5 6.5 0 0 1 3 9.5 6.5 6.5 0 0 1 9.5 3m0 2C7 5 5 7 5 9.5S7 14 9.5 14 14 12 14 9.5 12 5 9.5 5z"/></svg> </label> <div class=md-search data-md-component=search role=dialog> <label class=md-search__overlay for=__search></label> <div class=md-search__inner role=search> <form class=md-search__form name=search> <input type=text class=md-search__input name=query aria-label=Search placeholder=Search autocapitalize=off autocorrect=off autocomplete=off spellcheck=false data-md-component=search-query data-md-state=active required> <label class="md-search__icon md-icon" for=__search> <svg xmlns=http://www.w3.org/2000/svg viewbox="0 0 24 24"><path d="M9.5 3A6.5 6.5 0 0 1 16 9.5c0 1.61-.59 3.09-1.56 4.23l.27.27h.79l5 5-1.5 1.5-5-5v-.79l-.27-.27A6.516 6.516 0 0 1 9.5 16 6.5 6.5 0 0 1 3 9.5 6.5 6.5 0 0 1 9.5 3m0 2C7 5 5 7 5 9.5S7 14 9.5 14 14 12 14 9.5 12 5 9.5 5z"/></svg> <svg xmlns=http://www.w3.org/2000/svg viewbox="0 0 24 24"><path d="M20 11v2H8l5.5 5.5-1.42 1.42L4.16 12l7.92-7.92L13.5 5.5 8 11h12z"/></svg> </label> <button type=reset class="md-search__icon md-icon" aria-label=Clear tabindex=-1> <svg xmlns=http://www.w3.org/2000/svg viewbox="0 0 24 24"><path d="M19 6.41L17.59 5 12 10.59 6.41 5 5 6.41 10.59 12 5 17.59 6.41 19 12 13.41 17.59 19 19 17.59 13.41 12 19 6.41z"/></svg> </button> </form> <div class=md-search__output> <div class=md-search__scrollwrap data-md-scrollfix> <div class=md-search-result data-md-component=search-result> <div class=md-search-result__meta> Initializing search </div> <ol class=md-search-result__list></ol> </div> </div> </div> </div> </div> </nav> </header> <div class=md-container data-md-component=container> <main class=md-main data-md-component=main> <div class="md-main__inner md-grid"> <div class="md-sidebar md-sidebar--primary" data-md-component=sidebar data-md-type=navigation> <div class=md-sidebar__scrollwrap> <div class=md-sidebar__inner> <nav class="md-nav md-nav--primary" aria-label=Navigation data-md-level=0> <label class=md-nav__title for=__drawer> <a href=../../.. title="IPU SDK" class="md-nav__button md-logo" aria-label="IPU SDK" data-md-component=logo> <svg xmlns=http://www.w3.org/2000/svg viewbox="0 0 24 24"><path d="M10 20v-6h4v6h5v-8h3L12 3 2 12h3v8h5z"/></svg> </a> IPU SDK </label> <ul class=md-nav__list data-md-scrollfix> <li class=md-nav__item> <a href=../../../index.html class=md-nav__link> 主页 </a> </li> <li class="md-nav__item md-nav__item--nested"> <input class="md-nav__toggle md-toggle" data-md-toggle=__nav_2 type=checkbox id=__nav_2> <label class=md-nav__link for=__nav_2> SDK介绍 <span class="md-nav__icon md-icon"></span> </label> <nav class=md-nav aria-label=SDK介绍 data-md-level=1> <label class=md-nav__title for=__nav_2> <span class="md-nav__icon md-icon"></span> SDK介绍 </label> <ul class=md-nav__list data-md-scrollfix> <li class=md-nav__item> <a href=../../Introduction/Introduction.html class=md-nav__link> SDK框架介绍 </a> </li> <li class=md-nav__item> <a href=../../Introduction/Docker.html class=md-nav__link> Docker环境 </a> </li> </ul> </nav> </li> <li class="md-nav__item md-nav__item--active md-nav__item--nested"> <input class="md-nav__toggle md-toggle" data-md-toggle=__nav_3 type=checkbox id=__nav_3 checked> <label class=md-nav__link for=__nav_3> 用户手册 <span class="md-nav__icon md-icon"></span> </label> <nav class=md-nav aria-label=用户手册 data-md-level=1> <label class=md-nav__title for=__nav_3> <span class="md-nav__icon md-icon"></span> 用户手册 </label> <ul class=md-nav__list data-md-scrollfix> <li class=md-nav__item> <a href=../Common/Environment_Construction.html class=md-nav__link> 1. 快速开始 </a> </li> <li class="md-nav__item md-nav__item--active"> <input class="md-nav__toggle md-toggle" data-md-toggle=toc type=checkbox id=__toc> <label class="md-nav__link md-nav__link--active" for=__toc> 2. Convert Tool <span class="md-nav__icon md-icon"></span> </label> <a href=Convert.html class="md-nav__link md-nav__link--active"> 2. Convert Tool </a> <nav class="md-nav md-nav--secondary" aria-label="Table of contents"> <label class=md-nav__title for=__toc> <span class="md-nav__icon md-icon"></span> Table of contents </label> <ul class=md-nav__list data-md-component=toc data-md-scrollfix> <li class=md-nav__item> <a href=#21 class=md-nav__link> 2.1. 使用方法 </a> <nav class=md-nav aria-label="2.1. 使用方法"> <ul class=md-nav__list> <li class=md-nav__item> <a href=#211-tensorflow_graphdef class=md-nav__link> 2.1.1 tensorflow_graphdef 框架指令 </a> </li> <li class=md-nav__item> <a href=#212-tensorflow_savemodel class=md-nav__link> 2.1.2 tensorflow_savemodel 框架指令 </a> </li> <li class=md-nav__item> <a href=#213-keras class=md-nav__link> 2.1.3 keras框架指令 </a> </li> <li class=md-nav__item> <a href=#214-tflite class=md-nav__link> 2.1.4 tflite框架指令 </a> </li> <li class=md-nav__item> <a href=#215-caffe class=md-nav__link> 2.1.5 caffe框架指令 </a> </li> <li class=md-nav__item> <a href=#216-onnx class=md-nav__link> 2.1.6 Onnx框架指令 </a> </li> </ul> </nav> </li> <li class=md-nav__item> <a href=#22-input-config class=md-nav__link> 2.2. input config配置信息设置 </a> <nav class=md-nav aria-label="2.2. input config配置信息设置"> <ul class=md-nav__list> <li class=md-nav__item> <a href=#221-input_config class=md-nav__link> 2.2.1 INPUT_CONFIG </a> </li> <li class=md-nav__item> <a href=#222-output_config class=md-nav__link> 2.2.2 OUTPUT_CONFIG </a> </li> <li class=md-nav__item> <a href=#223-conv_config class=md-nav__link> 2.2.3 CONV_CONFIG </a> </li> <li class=md-nav__item> <a href=#224-optimize_config class=md-nav__link> 2.2.4 OPTIMIZE_CONFIG </a> </li> <li class=md-nav__item> <a href=#225 class=md-nav__link> 2.2.5 输入输出配置总结 </a> </li> </ul> </nav> </li> </ul> </nav> </li> <li class=md-nav__item> <a href=../Common/Calibrate.html class=md-nav__link> 3. Calibrator </a> </li> <li class=md-nav__item> <a href=../Common/Compile.html class=md-nav__link> 4. Compiler </a> </li> <li class=md-nav__item> <a href=Simulate.html class=md-nav__link> 5. Simulator </a> </li> <li class=md-nav__item> <a href=../Common/DumpDebug_Tool.html class=md-nav__link> 6. DumpDebug Tool </a> </li> <li class=md-nav__item> <a href=../Common/SigmaStar_Post_Processing_Module.html class=md-nav__link> 7. SigmaStar后处理模块 </a> </li> <li class=md-nav__item> <a href=../Common/Adding_A_New_Layer.html class=md-nav__link> 8. 如何添加新的Layer </a> </li> <li class=md-nav__item> <a href=Special_Model_Conversion.html class=md-nav__link> 9. 特殊模型转换要点 </a> </li> <li class=md-nav__item> <a href=DLA_SDK_Support.html class=md-nav__link> 10. DLA SDK 支持 </a> </li> <li class=md-nav__item> <a href=../Common/Running_Offline_Network_Model_On_Development_Board.html class=md-nav__link> 11. 在开发板上运行离线网络模型 </a> </li> <li class=md-nav__item> <a href=Preprocess.py_and_Input_Config.ini_Support.html class=md-nav__link> 附录. 前处理和配置文件注意要点 </a> </li> </ul> </nav> </li> <li class="md-nav__item md-nav__item--nested"> <input class="md-nav__toggle md-toggle" data-md-toggle=__nav_4 type=checkbox id=__nav_4> <label class=md-nav__link for=__nav_4> FAQ <span class="md-nav__icon md-icon"></span> </label> <nav class=md-nav aria-label=FAQ data-md-level=1> <label class=md-nav__title for=__nav_4> <span class="md-nav__icon md-icon"></span> FAQ </label> <ul class=md-nav__list data-md-scrollfix> <li class=md-nav__item> <a href=../../FAQ/Common/Env_Setting.html class=md-nav__link> 环境设置问题 </a> </li> <li class=md-nav__item> <a href=../../FAQ/IPU200M/Model_Conversion.html class=md-nav__link> 模型转换问题 </a> </li> <li class=md-nav__item> <a href=../../FAQ/IPU200M/Development_Board.html class=md-nav__link> 板端使用问题 </a> </li> <li class=md-nav__item> <a href=../../FAQ/IPU200M/Other_Anomalies.html class=md-nav__link> 其他异常问题 </a> </li> </ul> </nav> </li> </ul> </nav> </div> </div> </div> <div class="md-sidebar md-sidebar--secondary" data-md-component=sidebar data-md-type=toc> <div class=md-sidebar__scrollwrap> <div class=md-sidebar__inner> <nav class="md-nav md-nav--secondary" aria-label="Table of contents"> <label class=md-nav__title for=__toc> <span class="md-nav__icon md-icon"></span> Table of contents </label> <ul class=md-nav__list data-md-component=toc data-md-scrollfix> <li class=md-nav__item> <a href=#21 class=md-nav__link> 2.1. 使用方法 </a> <nav class=md-nav aria-label="2.1. 使用方法"> <ul class=md-nav__list> <li class=md-nav__item> <a href=#211-tensorflow_graphdef class=md-nav__link> 2.1.1 tensorflow_graphdef 框架指令 </a> </li> <li class=md-nav__item> <a href=#212-tensorflow_savemodel class=md-nav__link> 2.1.2 tensorflow_savemodel 框架指令 </a> </li> <li class=md-nav__item> <a href=#213-keras class=md-nav__link> 2.1.3 keras框架指令 </a> </li> <li class=md-nav__item> <a href=#214-tflite class=md-nav__link> 2.1.4 tflite框架指令 </a> </li> <li class=md-nav__item> <a href=#215-caffe class=md-nav__link> 2.1.5 caffe框架指令 </a> </li> <li class=md-nav__item> <a href=#216-onnx class=md-nav__link> 2.1.6 Onnx框架指令 </a> </li> </ul> </nav> </li> <li class=md-nav__item> <a href=#22-input-config class=md-nav__link> 2.2. input config配置信息设置 </a> <nav class=md-nav aria-label="2.2. input config配置信息设置"> <ul class=md-nav__list> <li class=md-nav__item> <a href=#221-input_config class=md-nav__link> 2.2.1 INPUT_CONFIG </a> </li> <li class=md-nav__item> <a href=#222-output_config class=md-nav__link> 2.2.2 OUTPUT_CONFIG </a> </li> <li class=md-nav__item> <a href=#223-conv_config class=md-nav__link> 2.2.3 CONV_CONFIG </a> </li> <li class=md-nav__item> <a href=#224-optimize_config class=md-nav__link> 2.2.4 OPTIMIZE_CONFIG </a> </li> <li class=md-nav__item> <a href=#225 class=md-nav__link> 2.2.5 输入输出配置总结 </a> </li> </ul> </nav> </li> </ul> </nav> </div> </div> </div> <div class=md-content data-md-component=content> <article class="md-content__inner md-typeset"> <h1>2. Convert Tool</h1> <h2 id=21>2.1. 使用方法<a class=headerlink href=#21 title="Permanent link">&para;</a></h2> <p>Convert Tool工具的位置在 <strong>SGS_IPU_SDK/Scripts/ConvertTool/ConvertTool.py</strong>。 目前Convert Tool支持从tensorflow_graphdef，tensorflow_savemodel，keras，tflite，caffe，onnx 六种框架模型转换为SGS浮点网络模型。 使用前先在SGS_IPU_SDK⽬录下运⾏以下脚本，输出Library的路径（已经做过该步骤可忽略）：</p> <div class=highlight><pre><span></span><code>cd ~/SGS_IPU_SDK
source cfg_env.sh
</code></pre></div> <p>下面是Convert Tool目前支持的platform information： <div class=highlight><pre><span></span><code><span class=err>python3</span> <span class=err>ConvertTool.py</span> <span class=err>-h</span>
<span class=na>usage</span><span class=o>:</span> <span class=s>ConvertTool.py [-h]</span>
    <span class=err>{tensorflow_graphdef,tensorflow_savemodel,keras,tflite,caffe,onnx}</span> <span class=err>...</span>
<span class=na>Convert</span> <span class=s>Tool</span>
<span class=na>    positional arguments</span><span class=o>:</span> <span class=s>{tensorflow_graphdef,tensorflow_savemodel,keras,tflite,caffe,onnx}</span>
        <span class=err>platform</span> <span class=err>info</span>

    <span class=err>tensorflow_graphdef</span>
        <span class=err>tensorflow</span> <span class=err>graphdef</span> <span class=err>commands</span>

    <span class=err>tensorflow_savemodel</span>
        <span class=err>tensorflow</span> <span class=err>save_model</span> <span class=err>commands</span>

    <span class=err>keras</span>
        <span class=err>keras</span> <span class=err>commands</span>

    <span class=err>tflite</span>
        <span class=err>tflite</span> <span class=err>commands</span>

    <span class=err>caffe</span>
        <span class=err>caffe</span> <span class=err>commands</span>

    <span class=err>onnx</span>
       <span class=err>onnx</span> <span class=err>commands</span>

<span class=na>optional arguments</span><span class=o>:</span>
    <span class=err>-h,</span> <span class=err>--help</span> <span class=err>show</span> <span class=err>this</span> <span class=err>help</span> <span class=err>message</span> <span class=err>and</span> <span class=err>exit</span>
</code></pre></div></p> <p>如果想进一步看看各个平台转换所需要的参数信息，可以执行 <code>python3 ConvertTool.py {platform} –h</code>，各个平台具体信息和查看指令如下：</p> <hr> <h3 id=211-tensorflow_graphdef>2.1.1 tensorflow_graphdef 框架指令<a class=headerlink href=#211-tensorflow_graphdef title="Permanent link">&para;</a></h3> <div class=highlight><pre><span></span><code><span class=err>python3</span> <span class=err>ConvertTool.py</span> <span class=err>tensorflow_graphdef</span> <span class=err>-h</span>
<span class=na>usage</span><span class=o>:</span> <span class=s>ConvertTool.py tensorflow_graphdef [-h]</span>
    <span class=err>--graph_def_file</span> <span class=err>GRAPH_DEF_FILE</span>
    <span class=err>--input_arrays</span> <span class=err>INPUT_ARRAYS</span> <span class=err>(optional)</span>
    <span class=err>--output_arrays</span> <span class=err>OUTPUT_ARRAYS</span> <span class=err>(optional)</span>
    <span class=err>--input_config</span> <span class=err>INPUT_CONFIG</span>
    <span class=err>--output_file</span> <span class=err>OUTPUT_FILE</span>
    <span class=err>--input_shapes</span> <span class=err>INPUT_SHAPES</span> <span class=err>(optional)</span>

<span class=na>optional arguments</span><span class=o>:</span>
    <span class=err>-h,</span> <span class=err>--help</span>
        <span class=err>show</span> <span class=err>this</span> <span class=err>help</span> <span class=err>message</span> <span class=err>and</span> <span class=err>exit.</span>

    <span class=err>--graph_def_file</span>
        <span class=err>GRAPH_DEF_FILE</span> <span class=err>Full</span> <span class=err>filepath</span> <span class=err>of</span> <span class=err>file</span> <span class=err>containing</span> <span class=err>frozen</span> <span class=err>GraphDef.</span>

    <span class=err>--input_arrays</span>
        <span class=err>INPUT_ARRAYS</span> <span class=err>Names</span> <span class=err>of</span> <span class=err>the</span> <span class=err>input</span> <span class=err>arrays,</span> <span class=err>comma-separated.</span>

    <span class=err>--output_arrays</span>
        <span class=err>OUTPUT_ARRAYS</span> <span class=err>Names</span> <span class=err>of</span> <span class=err>the</span> <span class=err>output</span> <span class=err>arrays,</span> <span class=err>comma-separated.</span>

    <span class=err>--input_config</span>
        <span class=err>INPUT_CONFIG</span> <span class=err>Input</span> <span class=err>config</span> <span class=err>path.</span>

    <span class=err>--output_file</span>
        <span class=err>OUTPUT_FILE</span> <span class=err>Full</span> <span class=err>filepath</span> <span class=err>of</span> <span class=err>out</span> <span class=err>Model</span> <span class=err>path.</span>

    <span class=err>--input_shapes</span>
        <span class=err>INPUT_SHAPES</span> <span class=err>Shapes</span> <span class=err>corresponding</span> <span class=err>to</span> <span class=err>--input_arrays,</span> <span class=err>colon-</span> <span class=err>separated.</span> <span class=err>For</span> <span class=err>many</span> <span class=err>models</span> <span class=err>each</span> <span class=err>shape</span> <span class=err>takes</span> <span class=err>the</span> <span class=err>form</span> <span class=err>batch</span> <span class=err>size,</span> <span class=err>input</span> <span class=err>array</span> <span class=err>height,</span> <span class=err>input</span> <span class=err>array</span> <span class=err>width,</span> <span class=err>input</span> <span class=err>array</span> <span class=err>depth.</span> 
<span class=na>        Default</span><span class=o>:</span> <span class=s>None</span>
</code></pre></div> <p>工具使用示例： <div class=highlight><pre><span></span><code>python3 ConvertTool.py tensorflow_graphdef \
--graph_def_file ~/SGS_Models/tensorflow/resnet_v2_50/resnet_v2_50.pb \
--output_file ./resnet_v2_float.sim \
--input_shapes 1,299,299,3 \
--input_config ~/SGS_Models/tensorflow/resnet_v2_50/input_config.ini
</code></pre></div></p> <p>相关参数说明：</p> <p><code>--graph_def_file</code>: 输入的模型为TensorFlow frozen的graphdef的pb格式文件路径。</p> <p><code>--output_file</code>: 输出的模型文件，flatbuffer格式，sim后缀文件。</p> <p><code>--input_shapes</code>: 网络输入Tensor的shape，格式为NHWC，dimention 之间以逗号( , )分隔，shape个数和inputs个数对应，多个shape之间以冒号( : )分隔。</p> <p><code>--input_config</code>: input_config.ini文件路径，该文件为input tensor的配置信息。</p> <p>可选参数：</p> <p><code>--input_arrays</code>: 网络输入Tensor的名字，以字符串类型指定，多个inputs的话，中间以逗号( , )分隔，如：--input_arrays='Input1','Input2'。</p> <p><code>--output_arrays</code>: 网络输出Tensor的名字，以字符串类型指定，多个outputs的话，中间以逗号( , )分隔。</p> <hr> <h3 id=212-tensorflow_savemodel>2.1.2 tensorflow_savemodel 框架指令<a class=headerlink href=#212-tensorflow_savemodel title="Permanent link">&para;</a></h3> <div class=highlight><pre><span></span><code><span class=err>python3</span> <span class=err>ConvertTool.py</span> <span class=err>tensorflow_savemodel</span> <span class=err>-h</span>
<span class=na>usage</span><span class=o>:</span> <span class=s>ConvertTool.py tensorflow_savemodel [-h]</span>
    <span class=err>--saved_model_dir</span> <span class=err>SAVED_MODEL_DIR</span>
    <span class=err>--input_config</span> <span class=err>INPUT_CONFIG</span>
    <span class=err>--output_file</span> <span class=err>OUTPUT_FILE</span>
    <span class=err>--output_arrays</span> <span class=err>OUTPUT_ARRAYS</span> <span class=err>(optional)</span>
    <span class=err>--input_arrays</span> <span class=err>INPUT_ARRAYS</span> <span class=err>(optional)</span>
    <span class=err>--input_shapes</span> <span class=err>INPUT_SHAPES</span> <span class=err>(optional)</span>
    <span class=err>--tag_set</span> <span class=err>TAG_SET</span> <span class=err>(optional)</span>
    <span class=err>--signature_key</span> <span class=err>SIGNATURE_KEY</span> <span class=err>(optional)</span>

<span class=na>optional arguments</span><span class=o>:</span>
    <span class=err>-h,</span> <span class=err>--help</span>
        <span class=err>show</span> <span class=err>this</span> <span class=err>help</span> <span class=err>message</span> <span class=err>and</span> <span class=err>exit.</span>

    <span class=err>--saved_model_dir</span>
        <span class=err>SAVED_MODEL_DIR</span> <span class=err>SavedModel</span> <span class=err>directory</span> <span class=err>to</span> <span class=err>convert.</span>

    <span class=err>--input_config</span>
        <span class=err>INPUT_CONFIG</span> <span class=err>Input</span> <span class=err>config</span> <span class=err>path.</span>

    <span class=err>--output_file</span>
        <span class=err>OUTPUT_FILE</span> <span class=err>Full</span> <span class=err>filepath</span> <span class=err>of</span> <span class=err>out</span> <span class=err>Model</span> <span class=err>path.</span> <span class=err>--debug</span> <span class=err>Run</span> <span class=err>gdb</span> <span class=err>in</span> <span class=err>Debug</span> <span class=err>mode.</span>

    <span class=err>--input_arrays</span>
        <span class=err>INPUT_ARRAYS</span> <span class=err>Names</span> <span class=err>of</span> <span class=err>the</span> <span class=err>input</span> <span class=err>arrays,</span> <span class=err>comma-separated.</span>
<span class=na>        Default</span><span class=o>:</span> <span class=s>None.</span>

    <span class=err>--output_arrays</span>
        <span class=err>OUTPUT_ARRAYS</span> <span class=err>Names</span> <span class=err>of</span> <span class=err>the</span> <span class=err>output</span> <span class=err>arrays,</span> <span class=err>comma-separated.</span>
<span class=na>        Default</span><span class=o>:</span> <span class=s>None.</span>

    <span class=err>--input_shapes</span>
        <span class=err>INPUT_SHAPES</span> <span class=err>Shapes</span> <span class=err>corresponding</span> <span class=err>to</span> <span class=err>--input_arrays,</span> <span class=err>colon-</span> <span class=err>separated.</span> <span class=err>For</span> <span class=err>many</span> <span class=err>models</span> <span class=err>each</span> <span class=err>shape</span> <span class=err>takes</span> <span class=err>the</span> <span class=err>form</span> <span class=err>batch</span> <span class=err>size,</span> <span class=err>input</span> <span class=err>array</span> <span class=err>height,</span> <span class=err>input</span> <span class=err>array</span> <span class=err>width,</span> <span class=err>input</span> <span class=err>array</span> <span class=err>depth</span>
<span class=na>        Default</span><span class=o>:</span> <span class=s>None.</span>

    <span class=err>--tag_set</span>
        <span class=err>TAG_SET</span> <span class=err>Set</span> <span class=err>of</span> <span class=err>tags</span> <span class=err>identifying</span> <span class=err>the</span> <span class=err>MetaGraphDef</span> <span class=err>within</span> <span class=err>the</span> <span class=err>SavedModel</span> <span class=err>to</span> <span class=err>analyze.</span> <span class=err>All</span> <span class=err>tags</span> <span class=err>in</span> <span class=err>the</span> <span class=err>tag</span> <span class=err>set</span> <span class=err>must</span> <span class=err>be</span> <span class=err>present.</span>
<span class=na>        Default</span><span class=o>:</span> <span class=s>None.</span>

    <span class=err>--signature_key</span>
        <span class=err>SIGNATURE_KEY</span> <span class=err>Key</span> <span class=err>identifying</span> <span class=err>SignatureDef</span> <span class=err>containing</span> <span class=err>inputs</span> <span class=err>and</span> <span class=err>outputs.</span>
<span class=na>        Default</span><span class=o>:</span> <span class=s>DEFAULT_SERVING_SIGNATURE_DEF_KEY</span>
</code></pre></div> <p>工具使用示例：</p> <div class=highlight><pre><span></span><code>python3 ConvertTool.py tensorflow_savemodel \
--saved_model_dir ~/test/tensorflow_model/save_model \
--input_config ~/test/tensorflow_model/input_config.ini \
--output_file ~/test/tensorflow_model/save_model_float.sim \
--tag_set test_saved_model \
--signature_key test_signature
</code></pre></div> <p>相关参数说明：</p> <p><code>--saved_model_dir</code>: 输入的模型为TensorFlow saved_model.builder生成的文件路径。</p> <p><code>--output_file</code>: 输出的模型文件，flatbuffer格式，sim后缀文件。</p> <p><code>--input_config</code>: input_config.ini文件路径，该文件为input tensor的配置信息。</p> <p>可选参数：</p> <p><code>--input_arrays</code>: 网络输入Tensor的名字，以字符串类型指定，多个inputs的话，中间以逗号( , )分隔，如：--input_arrays='Input1','Input2'，若不指定则从saved_model_dir中获取。</p> <p><code>--output_arrays</code>: 网络输出Tensor的名字，以字符串类型指定，多个outputs的话，中间以逗号( , )分隔。若不指定则从saved_model_dir中获取。</p> <p><code>--input_shapes</code>: 网络输入Tensor的shape，格式为NHWC，dimention 之间以逗号( , )分隔，shape个数和inputs 个数对应，多个shape之间以冒号( : )分隔。若不指定则从saved_model_dir中获取。</p> <p><code>--tag_set</code>: 需要和save model时所指定的tag匹配，如果不做设定，则默认为‘serve’。</p> <p><code>--signature_key</code>: 需要和save model时所指定的signature匹配。如果不做设定，则默认为 ‘DEFAULT_SERVING_SIGNATURE_DEF_KEY’</p> <hr> <h3 id=213-keras>2.1.3 keras框架指令<a class=headerlink href=#213-keras title="Permanent link">&para;</a></h3> <div class=highlight><pre><span></span><code><span class=err>python3</span> <span class=err>ConvertTool.py</span> <span class=err>keras</span> <span class=err>-h</span>
<span class=na>usage</span><span class=o>:</span> <span class=s>ConvertTool.py keras [-h]</span>
    <span class=err>--model_file</span> <span class=err>MODEL_FILE</span>
    <span class=err>--input_config</span> <span class=err>INPUT_CONFIG</span>
    <span class=err>--output_file</span> <span class=err>OUTPUT_FILE</span>
    <span class=err>--input_arrays</span> <span class=err>INPUT_ARRAYS</span> <span class=err>(optional)</span>
    <span class=err>--output_arrays</span> <span class=err>OUTPUT_ARRAYS</span> <span class=err>(optional)</span>
    <span class=err>--input_shapes</span> <span class=err>INPUT_SHAPES</span> <span class=err>(optional)</span>
    <span class=err>--custom_objects</span> <span class=err>CUSTOM_OBJECTS</span> <span class=err>(optional)</span>

<span class=na>optional arguments</span><span class=o>:</span>
    <span class=err>-h,</span> <span class=err>--help</span> <span class=err>show</span> <span class=err>this</span> <span class=err>help</span> <span class=err>message</span> <span class=err>and</span> <span class=err>exit</span>
    <span class=err>--model_file</span> <span class=err>MODEL_FILE</span> <span class=err>Full</span> <span class=err>filepath</span> <span class=err>of</span> <span class=err>HDF5</span> <span class=err>file</span> <span class=err>containing</span> <span class=err>the</span> <span class=err>tf.keras</span> <span class=err>model.</span>

    <span class=err>--input_config</span> <span class=err>INPUT_CONFIG</span> <span class=err>Input</span> <span class=err>config</span> <span class=err>path.</span>

    <span class=err>--output_file</span> <span class=err>OUTPUT_FILE</span> <span class=err>Full</span> <span class=err>filepath</span> <span class=err>of</span> <span class=err>out</span> <span class=err>Model</span> <span class=err>path.</span>

    <span class=err>--input_arrays</span> <span class=err>INPUT_ARRAYS</span> <span class=err>Names</span> <span class=err>of</span> <span class=err>the</span> <span class=err>input</span> <span class=err>arrays,</span> <span class=err>comma-separated.</span>
<span class=na>    Default</span><span class=o>:</span> <span class=s>None.</span>

    <span class=err>--input_shapes</span> <span class=err>INPUT_SHAPES</span> <span class=err>Shapes</span> <span class=err>corresponding</span> <span class=err>to</span> <span class=err>--input_arrays,</span> <span class=err>colon-</span> <span class=err>separated.</span> <span class=err>For</span> <span class=err>many</span> <span class=err>models</span> <span class=err>each</span> <span class=err>shape</span> <span class=err>takes</span> <span class=err>the</span> <span class=err>form</span> <span class=err>batch</span> <span class=err>size,</span> <span class=err>input</span> <span class=err>array</span> <span class=err>height,</span> <span class=err>input</span> <span class=err>array</span> <span class=err>width,</span> <span class=err>input</span> <span class=err>array</span> <span class=err>depth.</span>
<span class=na>    Default</span><span class=o>:</span> <span class=s>None.</span>

    <span class=err>--output_arrays</span> <span class=err>OUTPUT_ARRAYS</span> <span class=err>Names</span> <span class=err>of</span> <span class=err>the</span> <span class=err>output</span> <span class=err>arrays,</span> <span class=err>comma-separated.</span>
<span class=na>    Default</span><span class=o>:</span> <span class=s>None.</span>

    <span class=err>--custom_objects</span> <span class=err>CUSTOM_OBJECTS</span> <span class=err>Dict</span> <span class=err>mapping</span> <span class=err>names</span> <span class=err>(strings)</span> <span class=err>to</span> <span class=err>custom</span> <span class=err>classes</span> <span class=err>or</span> <span class=err>functions</span> <span class=err>to</span> <span class=err>be</span> <span class=err>considered</span> <span class=err>during</span> <span class=err>model</span> <span class=err>deserialization.</span>
<span class=na>    Default</span><span class=o>:</span> <span class=s>None.</span>
</code></pre></div> <p>工具使用示例：</p> <div class=highlight><pre><span></span><code>python3 ConvertTool.py keras \
--model_file ./TEST_h5/resnet50/resnet50.h5 \
--input_config ./TEST_h5/resnet50/input_config.ini \
--output_file ./TEST_h5/resnet50/resnet50_float.sim
</code></pre></div> <p>相关参数说明：</p> <p><code>--model_file</code>: 输入的模型为keras h5格式文件路径。 <code>--output_file</code>: 输出的模型文件，flatbuffer格式，sim后缀文件。 <code>--input_config</code>: input_config.ini文件路径，该文件为input tensor的配置信息。</p> <p>可选参数：</p> <p><code>--input_arrays</code>: 网络输入Tensor的名字，以字符串类型指定，多个inputs的话，中间以逗号( , )分隔，如：--input_arrays='Input1','Input2'。</p> <p><code>--output_arrays</code>: 网络输出Tensor的名字，以字符串类型指定，多个outputs的话，中间以逗号( , )分隔。</p> <p><code>--input_shapes</code>: 网络输入Tensor的shape，格式为NHWC，dimention 之间以逗号( , )分隔，shape个数和inputs 个数对应，多个shape之间以冒号( : )分隔。</p> <p><code>--custom_objects</code>: Dict mapping names (strings) to custom classes or functions to be considered during model deserialization (default None).</p> <hr> <h3 id=214-tflite>2.1.4 tflite框架指令<a class=headerlink href=#214-tflite title="Permanent link">&para;</a></h3> <div class=highlight><pre><span></span><code><span class=err>python3</span> <span class=err>ConvertTool.py</span> <span class=err>tflite</span> <span class=err>-h</span>
<span class=na>usage</span><span class=o>:</span> <span class=s>ConvertTool.py tflite [-h]</span>
    <span class=err>--model_file</span> <span class=err>MODEL_FILE</span>
    <span class=err>--input_config</span> <span class=err>INPUT_CONFIG</span>
    <span class=err>--output_file</span> <span class=err>OUTPUT_FILE</span>

<span class=na>optional arguments</span><span class=o>:</span>
    <span class=err>-h,</span> <span class=err>--help</span> <span class=err>show</span> <span class=err>this</span> <span class=err>help</span> <span class=err>message</span> <span class=err>and</span> <span class=err>exit</span>

    <span class=err>--model_file</span> <span class=err>MODEL_FILE</span> <span class=err>Full</span> <span class=err>filepath</span> <span class=err>of</span> <span class=err>tflite</span> <span class=err>file</span> <span class=err>containing</span> <span class=err>the</span> <span class=err>tflite</span> <span class=err>model.</span>

    <span class=err>--input_config</span> <span class=err>INPUT_CONFIG</span> <span class=err>Input</span> <span class=err>config</span> <span class=err>path.</span>

    <span class=err>--output_file</span> <span class=err>OUTPUT_FILE</span> <span class=err>Full</span> <span class=err>filepath</span> <span class=err>of</span> <span class=err>out</span> <span class=err>Model</span> <span class=err>path.</span>
</code></pre></div> <p>工具使用示例：</p> <div class=highlight><pre><span></span><code>python3 ConvertTool.py tflite \
--model_file ~/test/tensorflow_model/Debug_save_model_float.tflite \
--input_config ~/test/tensorflow_model/input_config.ini \
--output_file ~/test/tensorflow_model/save_model_float.sim
</code></pre></div> <p>相关参数说明：</p> <p><code>--model_file</code>: 输入的模型为tflite格式文件路径(必须为非量化模型)。</p> <p><code>--output_file</code>: 输出的模型文件，flatbuffer格式，sim后缀文件。</p> <p><code>--input_config</code>: input_config.ini文件路径，该文件为input tensor的配置信息。</p> <hr> <h3 id=215-caffe>2.1.5 caffe框架指令<a class=headerlink href=#215-caffe title="Permanent link">&para;</a></h3> <div class=highlight><pre><span></span><code><span class=err>python3</span> <span class=err>ConvertTool.py</span> <span class=err>caffe</span> <span class=err>-h</span>
<span class=na>usage</span><span class=o>:</span> <span class=s>ConvertTool.py caffe [-h]</span>
    <span class=err>--model_file</span> <span class=err>MODEL_FILE</span>
    <span class=err>--weight_file</span> <span class=err>WEIGHT_FILE</span>
    <span class=err>--input_config</span> <span class=err>INPUT_CONFIG</span>
    <span class=err>--output_file</span> <span class=err>OUTPUT_FILE</span>
    <span class=err>--input_arrays</span> <span class=err>INPUT_ARRAYS</span> <span class=err>(optional)</span>
    <span class=err>--output_arrays</span> <span class=err>OUTPUT_ARRAYS</span> <span class=err>(optional)</span>

<span class=na>optional arguments</span><span class=o>:</span>
    <span class=err>-h,</span> <span class=err>--help</span> <span class=err>show</span> <span class=err>this</span> <span class=err>help</span> <span class=err>message</span> <span class=err>and</span> <span class=err>exit</span>

    <span class=err>--model_file</span> <span class=err>MODEL_FILE</span> <span class=err>Full</span> <span class=err>filepath</span> <span class=err>of</span> <span class=err>tflite</span> <span class=err>file</span> <span class=err>containing</span> <span class=err>the</span> <span class=err>caffe</span> <span class=err>model.</span>

    <span class=err>--weight_file</span> <span class=err>WEIGHT_FILE</span> <span class=err>Full</span> <span class=err>filepath</span> <span class=err>of</span> <span class=err>tflite</span> <span class=err>file</span> <span class=err>containing</span> <span class=err>the</span> <span class=err>caffe</span> <span class=err>weight.</span>

    <span class=err>--input_config</span> <span class=err>INPUT_CONFIG</span> <span class=err>Input</span> <span class=err>config</span> <span class=err>path.</span>

    <span class=err>--output_file</span> <span class=err>OUTPUT_FILE</span> <span class=err>Full</span> <span class=err>filepath</span> <span class=err>of</span> <span class=err>out</span> <span class=err>Model</span> <span class=err>path.</span>

    <span class=err>--input_arrays</span> <span class=err>INPUT_ARRAYS</span> <span class=err>Names</span> <span class=err>of</span> <span class=err>the</span> <span class=err>input</span> <span class=err>arrays,</span> <span class=err>comma-separated.</span>
<span class=na>    Default</span><span class=o>:</span> <span class=s>None.</span>

    <span class=err>--output_arrays</span> <span class=err>OUTPUT_ARRAYS</span> <span class=err>Names</span> <span class=err>of</span> <span class=err>the</span> <span class=err>output</span> <span class=err>arrays,</span> <span class=err>comma-separated.</span>
<span class=na>    Default</span><span class=o>:</span> <span class=s>None.</span>

    <span class=err>--debug</span> <span class=err>Run</span> <span class=err>gdb</span> <span class=err>in</span> <span class=err>Debug</span> <span class=err>mode.</span>
</code></pre></div> <p>工具使用示例：</p> <div class=highlight><pre><span></span><code>python3 ConvertTool.py caffe \
--model_file ~/SGS_Models/caffe/caffe_resnet50_conv/caffe_resnet50_conv.prototxt \
--weight_file ~/SGS_Models/caffe/caffe_resnet50_conv/caffe_resnet50_conv.caffemodel \
--input_config ~/SGS_Models/caffe/caffe_resnet50_conv/input_config.ini \
--output_file ./resnet50_float.sim
</code></pre></div> <p>相关参数说明：</p> <p><code>--model_file</code>: Caffe模型文件的路径</p> <p><code>--weight_file</code>: Caffe权重文件的路径</p> <p><code>--input_config</code>: input_config.ini文件路径，该文件为input config的配置信息。</p> <p><code>--output_file</code>: 转换模型的输出路径。</p> <p>可选参数：</p> <p><code>--input_arrays</code>: Caffe模型输入的节点名称，使用input的名字，如有多个输出节点，请用逗号( , )分隔</p> <p><code>--output_arrays</code>: 模型输出的节点名称，使用最后layer的top名字，如有多个输出节点，请用逗号( , )分隔</p> <p><code>--output_pack_model_arrays</code>: 输出Tensors数据默认排布为NHWC,默认为None；支持两种指定输出数据排布为NCHW的表达方式 （1）caffe模型使用‘caffe’，输出Tensors的数据排布为NCHW；（2）设为输出Tensors名字，该输出Tensor的数据排布为NCHW。</p> <hr> <h3 id=216-onnx>2.1.6 Onnx框架指令<a class=headerlink href=#216-onnx title="Permanent link">&para;</a></h3> <div class=highlight><pre><span></span><code><span class=err>python3</span> <span class=err>Path_to_SGS_IPU_SDK/Scripts/ConvertTool</span> <span class=err>onnx</span> <span class=err>-h</span>
<span class=na>usage</span><span class=o>:</span> <span class=s>ConvertTool.py onnx [-h]</span>
<span class=err>--model_file</span> <span class=err>MODEL_FILE</span>
<span class=err>--input_arrays</span>  <span class=err>INPUT_ARRAYS</span> <span class=err>(optional)</span>
<span class=err>--input_shapes</span> <span class=err>INPUT_SHAPES</span>
<span class=err>--output_arrays</span> <span class=err>OUTPUT_ARRAYS</span> <span class=err>(optional)</span>
<span class=err>--input_config</span>  <span class=err>INPUT_CONFIG</span>
<span class=err>--output_file</span> <span class=err>OUTPUT_FILE</span>

<span class=na>optional arguments</span><span class=o>:</span>  <span class=s>-h, --help show this help message and exit</span>
<span class=err>--model_file</span> <span class=err>MODEL_FILE</span>  <span class=err>Full</span> <span class=err>filepath</span> <span class=err>of</span> <span class=err>tflite</span> <span class=err>file</span> <span class=err>containing</span> <span class=err>the</span> <span class=err>onnx</span>  <span class=err>model.</span>

<span class=err>--input_arrays</span> <span class=err>INPUT_ARRAYS</span>  <span class=err>Names</span> <span class=err>of</span> <span class=err>the</span> <span class=err>input</span> <span class=err>arrays,</span> <span class=err>comma-separated.</span> <span class=err>(default</span>  <span class=err>None).</span>

<span class=err>--input_shapes</span> <span class=err>INPUT_SHAPES</span>  <span class=err>Shapes</span> <span class=err>corresponding</span> <span class=err>to</span> <span class=err>--input_arrays,</span> <span class=err>colon-separated.</span> <span class=err>For</span> <span class=err>many</span> <span class=err>models</span> <span class=err>each</span> <span class=err>shape</span> <span class=err>takes</span> <span class=err>the</span> <span class=err>form</span> <span class=err>N</span>  <span class=err>C</span> <span class=err>H</span> <span class=err>W</span> <span class=err>(default</span> <span class=err>None)</span>

<span class=err>--output_arrays</span> <span class=err>OUTPUT_ARRAYS</span>  <span class=err>Names</span> <span class=err>of</span> <span class=err>the</span> <span class=err>output</span> <span class=err>arrays,</span> <span class=err>comma-separated.</span> <span class=err>(default</span>  <span class=err>None)</span>

<span class=err>--input_config</span> <span class=err>INPUT_CONFIG</span>  <span class=err>Input</span> <span class=err>config</span> <span class=err>path.</span>

<span class=err>--output_file</span> <span class=err>OUTPUT_FILE</span>  <span class=err>Full</span> <span class=err>filepath</span> <span class=err>of</span> <span class=err>out</span> <span class=err>Model</span> <span class=err>path.</span>
</code></pre></div> <p>工具使用示例：</p> <div class=highlight><pre><span></span><code>python3 ~/SGS_IPU_SDK/Scripts/ConvertTool/ConvertTool.py onnx \
--model_file  ~/SGS_Models/onnx/ onnx_mobilenet_v2/ onnx_mobilenet_v2.onnx \
--input_shapes 1,3,224,224 \
--input_config ~/SGS_Models/onnx/ onnx_mobilenet_v2/input_config.ini \
--output_file ./onnx_mobilenet_v2_float.sim
</code></pre></div> <p>相关参数说明：</p> <p><code>--model_file</code>: Onnx模型文件的路径</p> <p><code>--input_shapes</code>: Onnx 模型输入shape， 多输入用冒号( : )分隔</p> <p><code>--input_config</code>: input_config.ini文件路径，该文件为input config的配置信息。</p> <p><code>--output_file</code>: 转换模型的输出路径。</p> <p>可选参数：</p> <p><code>--input_arrays</code>: Onnx模型输入的节点名称，使用input的名字,多输入用逗号( , )分隔</p> <p><code>--output_arrays</code>: 模型输出的节点名称，使用最后layer的top名字，如有多个输出节点，请用逗号( , )分隔</p> <p><code>--output_pack_model_arrays</code>: 输出Tensors数据默认排布为NHWC,默认为None；支持两种指定输出数据排布为NCHW的表达方式 （1）onnx模型使用‘onnx’，输出Tensors的数据排布为NCHW；（2）设为输出Tensors名字，该输出Tensor的数据排布为NCHW。</p> <hr> <div class="admonition 注意"> <p class=admonition-title>注意</p> <p>1.如果模型中带有后处理的算子，请先去除，目前只支持主干网络的转换。</p> <p>2.Convert Tool转换工具转换完成后会生成两个文件，例如 <code>--output_file</code> 指定为 ./resnet50_float.sim，转换完成后会生成Debug_resnet50_float.sim和resnet50_float.sim。其中，resnet50_float.sim是真正转换好的文件，Debug_resnet50_float.sim是经过转换后的中间文件，该文件未经过优化，与原框架模型拥有相同的网络结构，因此可以作为转换后调试使用，但是无法在IPU SDK中运行。</p> </div> <hr> <h2 id=22-input-config>2.2. input config配置信息设置<a class=headerlink href=#22-input-config title="Permanent link">&para;</a></h2> <p>工具的参数 <code>--input_config</code> 需要指定input tensor的配置信息文件input_config.ini路径，该文件的主要功能有：</p> <div class=codehilite><pre><span></span><code>•   配置网络模型图片前处理的归一化信息；

•   配置网络模型输入输出的量化处理信息；

•   配置网络模型中卷积的量化信息。
</code></pre></div> <p>配置input_config.ini文件主要是为了能将网络模型快速适配到SigmaStar芯片中使用。在网络模型的训练中，不同的框架和训练数据集需要网络使用不同的图片归一化方法，而在实际使用中，为了能够让网络模型的预测更加准确，需要还原训练模型时的图片前处理归一化方法。</p> <p>将RGB三个通道的均值和std_value设置好后，在转换模型时会写入到模型内部，这样在硬件上实际使用时，仅需要将图片resize到网络模型的输入的尺寸，图片归一化的工作在网络内部完成。</p> <p>另外，实际硬件上使用时图片输入格式与训练时使用的RGB可能有很大区别，正确配置这些选项能使转换好的模型内拥有这些配置信息，能够直接在SigmaStar的硬件上部署。</p> <div class=highlight><pre><span></span><code><span class=k>[INPUT_CONFIG]</span>
<span class=c1>;Names of the input arrays, comma-separated.image input must be the first.</span>
<span class=na>inputs</span><span class=o>=</span><span class=s>&#39;data&#39;;</span>
<span class=c1>;Memory formats of input arrays, comma-separated.</span>
<span class=c1>;One of RGB, BGR, RGBA, BGRA, YUV_NV12, RAWDATA_S16_NHWC, RAWDATA_F32_NHWC.</span>
<span class=c1>;Each entry in the list should match an entry in inputs arrays.</span>
<span class=na>training_input_formats</span><span class=o>=</span><span class=s>BGR;</span>
<span class=na>input_formats</span><span class=o>=</span><span class=s>BGR;</span>
<span class=c1>;Indicate the input data need qauntize or not.</span>
<span class=c1>;Each entry in the list should match an entry in inputs arrays.</span>
<span class=na>quantizations</span><span class=o>=</span><span class=s>TRUE;</span>
<span class=c1>;mean_values parameter for image models,</span>
<span class=c1>;Each entry in the list match RGB channel of（RGB,BGR,RGBA,BGRA,YUV_NV12）</span>
<span class=na>mean_red</span><span class=o>=</span><span class=s>0.0;</span>
<span class=na>mean_green</span><span class=o>=</span><span class=s>0.0;</span>
<span class=na>mean_blue</span><span class=o>=</span><span class=s>0.0;</span>
<span class=c1>;std_value parameter for image models,</span>
<span class=na>std_value</span><span class=o>=</span><span class=s>1.0;</span>

<span class=k>[OUTPUT_CONFIG]</span>
<span class=c1>;Names of the output arrays, comma-separated.</span>
<span class=na>outputs</span><span class=o>=</span><span class=s>&#39;prob&#39;;</span>
<span class=c1>;Indicate the output data need deqauntize or not.</span>
<span class=c1>;Each entry in the list should match an entry in outputs arrays.</span>
<span class=na>dequantizations</span><span class=o>=</span><span class=s>TRUE;</span>

<span class=k>[CONV_CONFIG]</span>
<span class=c1>;input_format=ALL_INT16;</span>
<span class=na>tensor_arrays</span><span class=o>=</span><span class=s>&#39;conv1-1,conv2-1&#39;;</span>
</code></pre></div> <p>该文件主要分为三个设置信息： <div class=highlight><pre><span></span><code>[INPUT_CONFIG]
[OUTPUT_CONFIG]
[CONV_CONFIG]
</code></pre></div> 针对这三个设置信息具体说明：</p> <div class="admonition 注意"> <p class=admonition-title>注意</p> <p>String类型的值，如tensor name，需用('')将内容包含起来，例如outputs='detectionBoxes,detectionClasses,detectionScores,numDetections';</p> </div> <hr> <h3 id=221-input_config>2.2.1 INPUT_CONFIG<a class=headerlink href=#221-input_config title="Permanent link">&para;</a></h3> <p><code>inputs</code>: 网络输入Tensor的name，如果有多个输入Tensor，请用逗号( , )分隔。模型输入Tensor顺序与<code>inputs</code>的配置顺序一致。所有输入name的长度不能超过1024个字符。</p> <p><code>training_input_formats</code>: 网络训练时的图片格式，数量和顺序与inputs一一对应，英文逗号( , )分隔。这些格式包括RGB，BGR，RAWDATA_S16_NHWC(若<code>training_input_formats</code>设置为<code>RAWDATA_S16_NHWC</code>,则<code>input_formats</code>也必须设置为<code>RAWDATA_S16_NHWC</code>) 其中之一。training_input_formats可以和input_formats 不一样。例如在在SigmaStar开发板上，input_formats是YUV_NV12，但是training input formats 是RGB。</p> <p><code>input_formats</code>: 网络模型在SigmaStar芯片上运行的图片输入格式，数量和顺序与inputs一一对应，逗号( , )分隔。 这些格式包括：</p> <ul> <li>RGB</li> <li>BGR</li> <li>RGBA</li> <li>BGRA</li> <li>YUV_NV12</li> <li>RAWDATA_F32_NHWC</li> <li>RAWDATA_S16_NHWC</li> </ul> <blockquote> <p>注意： * <code>training_input_formats</code> 和 <code>input_formats</code> 不能配置为如下情况：<br> training_input_formats=RGB;<br> input_formats=BGR;<br> 或者<br> training_input_formats=BGR;<br> input_formats=RGB;<br> * 灰度图片请按如下方式配置：<br> training_input_formats=RGB;<br> input_formats=GRAY;<br> 具体详见<a href=Special_Model_Conversion.html>9.1 灰度模型转换要点</a><br> * 配置<code>RAWDATA_F32_NHWC</code>或<code>RAWDATA_S16_NHWC</code>时，<code>mean_red</code>、<code>mean_green</code>、<code>mean_blue</code>和<code>std_value</code>不要配置，具体详见<a href=Special_Model_Conversion.html#92-rawdata_f32_nhwc>9.2 RAWDATA_F32_NHWC模型转换要点</a></p> </blockquote> <p><code>quantizations</code>: 用来标识所有输入Tensor的数据是否需要做量化，TRUE或者FALSE，默认值为TRUE,数量等于inputs 个数。如果有多个输入Tensor，以英文逗号( , )分隔且中间不可有空格。</p> <p><code>mean_red / mean_green / mean_blue</code>: 网络训练阶段，一般会对图片做前处理，对于RGB通道的图片，使用如下公式对图片进行预处理： (RGB - [mean_red, mean_green, mean_blue])/std_value mean_red / mean_green / mean_blue 就是相应通道上的mean值。如果这个网络没有做任何归一化处理，这个值设为0即可。每个mean数量等于inputs 个数。如果有多个输入Tensor，以英文逗号( , )分隔且中间不可有空格。</p> <p><code>std_value</code>: 如上公式，如果没有做任何归一化处理，这个值设为1即可。如果每个通道都有对应的std_value值，以英文冒号( : )分隔，顺序为RGB。如果有多个输入Tensor，以英文逗号( , )分隔且中间不可有空格。</p> <p><code>yuv420_h_pitch_alignment</code>：用来标识YUV数据作为网络输入时H方向对齐的数量，默认为16。</p> <p><code>yuv420_v_pitch_alignment</code>：用来标识YUV数据作为网络输入时V方向对齐的数量，默认为2。</p> <p><code>xrgb_h_pitch_alignment</code>：用来标识XRGB数据作为网络输入时H方向对齐的数量，默认为16。</p> <div class="admonition 注意"> <p class=admonition-title>注意</p> <p>以上三个配置只能配置默认值的整数倍。 例如：yuv420_h_pitch_alignment=32;</p> </div> <hr> <h3 id=222-output_config>2.2.2 OUTPUT_CONFIG<a class=headerlink href=#222-output_config title="Permanent link">&para;</a></h3> <p><code>outputs</code>: 网络输出Tensor的name(可用Netron工具打开模型查看)，如果有多个输出Tensor，以英文逗号( , )分隔。转换带后处理网络时，Backbone网络的outputs与完整网络outputs的名称不同，其余设置应完全一致。模型输出Tensor顺序与<code>outputs</code>的配置顺序一致。所有输出name的长度不能超过1024个字符。</p> <p><code>dequantizations</code>: 用来标识所有输出Tensor的数据是否需要做反量化，TRUE或者FALSE，默认值为TRUE,数量等于outputs。如果有多个输出Tensor，以英文逗号( , )分隔且中间不可有空格。该选项仅在板上运行时生效。该选项设为TRUE时，会在转换到Fixed模型后在对应的输出增加Fix2float算子，完成输出数据乘scale转换成float的操作。</p> <p><code>output_formats</code>: 用来标识输出Tensor的数据排布格式，NCHW或者NHWC，数量等于outputs个数，模型输出Tensor顺序与<code>output_formats</code>的配置顺序一致。如果有多个输出Tensor，以英文逗号（,）分隔且中间不可有空格。NCHW表示对应顺序的outputTensor按NCHW的数据格式排布（与原模型相同）；NHWC表示对应顺序的outputTensor按NHWC的格式排布（默认格式）。</p> <div class="admonition 注意"> <p class=admonition-title>注意</p> <p>（1）如果是多输出模型，若仅需指定部分输出为NCHW，则按序设置参数，无需转换的用None表示，如：caffe，None,caffe；当使用输出Tensors名字进行设置时，若仅指定部分输出为NCHW，则仅给出需要转换的tensor name即可，如：output0，output2。 （2）若配置了output_pack_model_arrays:参数，将自动在ini中填充output_formats的信息。 （3）如果想指定输出tensor的数据排布，除支持配置output_pack_model_arrays:参数的方式外，还可支持通过在ini中配置[OUTPUT_CONFIG]的output_formats信息来指定输出tensor的数据排布，使用NCHW，NHWC来进行指定，如：output_formats = NHWC,NCHW,NHWC。</p> </div> <hr> <h3 id=223-conv_config>2.2.3 CONV_CONFIG<a class=headerlink href=#223-conv_config title="Permanent link">&para;</a></h3> <p><code>input_format</code>: 指定网络中所有卷积的量化方式，默认L5，可选方案ALL_UINT8，ALL_INT16， CONV2D_INT16，DEPTHWISE_INT16。</p> <ul> <li><code>ALL_UINT8</code>: 指定所有卷积按照UINT8量化。</li> <li><code>ALL_INT16</code>: 指定所有卷积按照INT16量化。</li> <li><code>CONV2D_INT16</code>: 只指定所有普通卷积按照INT16量化。</li> <li><code>DEPTHWISE_INT16</code>: 只指定所有Depthwise 卷积按照INT16量化。</li> </ul> <p>在 <code>UINT8 模式</code> 下，卷积运行所占带宽小，运行速度快；在 <code>INT16模式</code> 下，可以极大的提高卷积的精度，但是运行的速度会有影响。</p> <p><code>tensor_arrays</code>: 指定网络中某些层的卷积量化方式。整个网络卷积都采用默认的UINT8 ，但是某些卷积层需要更高的精度，这时直接填写那些卷积层的第一个输入的 input id name即可。多层时，name以逗号( , )分隔。</p> <div class="admonition 注意"> <p class=admonition-title>注意</p> <p>卷积第一个输入的input id 名字可通过netron工具查看。指定量化时，网络中的第一层卷积不生效。</p> </div> <hr> <h3 id=224-optimize_config>2.2.4 OPTIMIZE_CONFIG<a class=headerlink href=#224-optimize_config title="Permanent link">&para;</a></h3> <p><code>skip_concatenation</code>: 网络中的Concatenation层，如果只针对最后一个维度合并，会走进该优化中。Fixed模型会将Concatenation层转换为skip_concatenation，skip_concatenation层会大幅减小运行时间。</p> <h3 id=225>2.2.5 输入输出配置总结<a class=headerlink href=#225 title="Permanent link">&para;</a></h3> <table> <thead> <tr> <th align=left>training_input_formats</th> <th>input_formats</th> <th align=right>板上运行时数据对齐方式</th> </tr> </thead> <tbody> <tr> <td align=left>RGB/BGR</td> <td>RGB/BGR</td> <td align=right>不用对齐</td> </tr> <tr> <td align=left>RGB/BGR</td> <td>RGBA/BGRA</td> <td align=right>W = ALIGN_UP(W * 4, <code>xrgb_h_pitch_alignment</code>) / 4</td> </tr> <tr> <td align=left>RGB/BGR</td> <td>YUV_NV12/GRAY</td> <td align=right>H = ALIGN_UP(H, <code>yuv420_v_pitch_alignment</code>)</td> </tr> <tr> <td align=left></td> <td></td> <td align=right>W = ALIGN_UP(W, <code>yuv420_h_pitch_alignment</code>)</td> </tr> <tr> <td align=left>RAWDATA_F32_NHWC</td> <td>RAWDATA_F32_NHWC</td> <td align=right>不用对齐</td> </tr> <tr> <td align=left>RAWDATA_S16_NHWC</td> <td>RAWDATA_S16_NHWC</td> <td align=right>最后1个维度 = ALIGN_UP(最后1个维度, <code>8</code>)</td> </tr> </tbody> </table> <p>如果配置<code>dequantizations</code>为TRUE，生成的模型对应输出会增加Fix2Float算子，输出数据类型为float32，数据与输出shape一致，没有对齐要求。<br> 如果配置<code>dequantizations</code>为FALSE，在板端运行时输出数据类型为int16，输出的数据对齐方法为：最后1个维度 = ALIGN_UP(最后1个维度, <code>8</code>)。 具体详见5.2.1节去除无用数据。</p> </article> </div> </div> </main> <footer class=md-footer> <nav class="md-footer__inner md-grid" aria-label=Footer> <a href=../Common/Environment_Construction.html class="md-footer__link md-footer__link--prev" rel=prev> <div class="md-footer__button md-icon"> <svg xmlns=http://www.w3.org/2000/svg viewbox="0 0 24 24"><path d="M20 11v2H8l5.5 5.5-1.42 1.42L4.16 12l7.92-7.92L13.5 5.5 8 11h12z"/></svg> </div> <div class=md-footer__title> <div class=md-ellipsis> <span class=md-footer__direction> Previous </span> 1. 快速开始 </div> </div> </a> <a href=../Common/Calibrate.html class="md-footer__link md-footer__link--next" rel=next> <div class=md-footer__title> <div class=md-ellipsis> <span class=md-footer__direction> Next </span> 3. Calibrator </div> </div> <div class="md-footer__button md-icon"> <svg xmlns=http://www.w3.org/2000/svg viewbox="0 0 24 24"><path d="M4 11v2h12l-5.5 5.5 1.42 1.42L19.84 12l-7.92-7.92L10.5 5.5 16 11H4z"/></svg> </div> </a> </nav> <div class="md-footer-meta md-typeset"> <div class="md-footer-meta__inner md-grid"> <div class=md-footer-copyright> <div class=md-footer-copyright__highlight> Copyright&copy; 2021 SigmaStar Technology. All rights reserved. Security Level: Confidential A. </div> Made with <a href=https://squidfunk.github.io/mkdocs-material/ target=_blank rel=noopener> Material for MkDocs </a> </div> </div> </div> </footer> </div> <div class=md-dialog data-md-component=dialog> <div class="md-dialog__inner md-typeset"></div> </div> <script id=__config type=application/json>{"base": "../../..", "features": [], "translations": {"clipboard.copy": "Copy to clipboard", "clipboard.copied": "Copied to clipboard", "search.config.lang": "en", "search.config.pipeline": "trimmer, stopWordFilter", "search.config.separator": "[\\s\\-]+", "search.placeholder": "Search", "search.result.placeholder": "Type to start searching", "search.result.none": "No matching documents", "search.result.one": "1 matching document", "search.result.other": "# matching documents", "search.result.more.one": "1 more on this page", "search.result.more.other": "# more on this page", "search.result.term.missing": "Missing"}, "search": "../../../assets/javascripts/workers/search.fb4a9340.min.js", "version": null}</script> <script src=../../../assets/javascripts/bundle.a1c7c35e.min.js></script> <script src=../../../search/search_index.js></script> <script src=../../../javascripts/extra.js></script> <script src="https://cdnjs.cloudflare.com/ajax/libs/mathjax/2.7.0/MathJax.js?config=TeX-MML-AM_CHTML"></script> <script src=https://cdnjs.cloudflare.com/ajax/libs/raphael/2.2.7/raphael.min.js></script> <script src=https://cdnjs.cloudflare.com/ajax/libs/underscore.js/1.8.3/underscore-min.js></script> <script src=https://cdnjs.cloudflare.com/ajax/libs/js-sequence-diagrams/1.0.6/sequence-diagram-min.js></script> <script src=https://cdnjs.cloudflare.com/ajax/libs/flowchart/1.6.5/flowchart.min.js></script> <script src=https://unpkg.com/freezeframe/dist/freezeframe.min.js></script> <script src=https://unpkg.com/mermaid@7.1.0/dist/mermaid.min.js></script> <script src=../../../javascripts/umlconvert.js></script> </body> </html>