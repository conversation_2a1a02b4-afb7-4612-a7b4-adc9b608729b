<!doctype html><html lang=en class=no-js> <head><meta charset=utf-8><meta name=viewport content="width=device-width,initial-scale=1"><meta name=description content="Personal Documentation System Template"><meta name=author content="<PERSON> Cho<PERSON>"><link rel=icon href=../../../images/favicon.ico><meta name=generator content="mkdocs-1.1.2, mkdocs-material-7.0.6"><title>11. 在开发板上运行离线网络模型 - IPU SDK</title><link rel=stylesheet href=../../../assets/stylesheets/main.2c0c5eaf.min.css><link rel=stylesheet href=../../../assets/stylesheets/palette.7fa14f5b.min.css><meta name=theme-color content=#009485><link rel=preconnect href=https://fonts.gstatic.com crossorigin><link rel=stylesheet href="https://fonts.googleapis.com/css?family=Roboto:300,400,400i,700%7CRoboto+Mono&display=fallback"><style>:root{--md-text-font-family:"Roboto";--md-code-font-family:"Roboto Mono"}</style><link rel=stylesheet href=../../../stylesheets/extra.css></head> <body dir=ltr data-md-color-scheme data-md-color-primary=teal data-md-color-accent=teal> <input class=md-toggle data-md-toggle=drawer type=checkbox id=__drawer autocomplete=off> <input class=md-toggle data-md-toggle=search type=checkbox id=__search autocomplete=off> <label class=md-overlay for=__drawer></label> <div data-md-component=skip> <a href=#111-ipu class=md-skip> Skip to content </a> </div> <div data-md-component=announce> </div> <header class=md-header data-md-component=header> <nav class="md-header__inner md-grid" aria-label=Header> <a href=../../.. title="IPU SDK" class="md-header__button md-logo" aria-label="IPU SDK" data-md-component=logo> <svg xmlns=http://www.w3.org/2000/svg viewbox="0 0 24 24"><path d="M10 20v-6h4v6h5v-8h3L12 3 2 12h3v8h5z"/></svg> </a> <label class="md-header__button md-icon" for=__drawer> <svg xmlns=http://www.w3.org/2000/svg viewbox="0 0 24 24"><path d="M3 6h18v2H3V6m0 5h18v2H3v-2m0 5h18v2H3v-2z"/></svg> </label> <div class=md-header__title data-md-component=header-title> <div class=md-header__ellipsis> <div class=md-header__topic> <span class=md-ellipsis> IPU SDK </span> </div> <div class=md-header__topic data-md-component=header-topic> <span class=md-ellipsis> 11. 在开发板上运行离线网络模型 </span> </div> </div> </div> <div class=md-header__options> </div> <label class="md-header__button md-icon" for=__search> <svg xmlns=http://www.w3.org/2000/svg viewbox="0 0 24 24"><path d="M9.5 3A6.5 6.5 0 0 1 16 9.5c0 1.61-.59 3.09-1.56 4.23l.27.27h.79l5 5-1.5 1.5-5-5v-.79l-.27-.27A6.516 6.516 0 0 1 9.5 16 6.5 6.5 0 0 1 3 9.5 6.5 6.5 0 0 1 9.5 3m0 2C7 5 5 7 5 9.5S7 14 9.5 14 14 12 14 9.5 12 5 9.5 5z"/></svg> </label> <div class=md-search data-md-component=search role=dialog> <label class=md-search__overlay for=__search></label> <div class=md-search__inner role=search> <form class=md-search__form name=search> <input type=text class=md-search__input name=query aria-label=Search placeholder=Search autocapitalize=off autocorrect=off autocomplete=off spellcheck=false data-md-component=search-query data-md-state=active required> <label class="md-search__icon md-icon" for=__search> <svg xmlns=http://www.w3.org/2000/svg viewbox="0 0 24 24"><path d="M9.5 3A6.5 6.5 0 0 1 16 9.5c0 1.61-.59 3.09-1.56 4.23l.27.27h.79l5 5-1.5 1.5-5-5v-.79l-.27-.27A6.516 6.516 0 0 1 9.5 16 6.5 6.5 0 0 1 3 9.5 6.5 6.5 0 0 1 9.5 3m0 2C7 5 5 7 5 9.5S7 14 9.5 14 14 12 14 9.5 12 5 9.5 5z"/></svg> <svg xmlns=http://www.w3.org/2000/svg viewbox="0 0 24 24"><path d="M20 11v2H8l5.5 5.5-1.42 1.42L4.16 12l7.92-7.92L13.5 5.5 8 11h12z"/></svg> </label> <button type=reset class="md-search__icon md-icon" aria-label=Clear tabindex=-1> <svg xmlns=http://www.w3.org/2000/svg viewbox="0 0 24 24"><path d="M19 6.41L17.59 5 12 10.59 6.41 5 5 6.41 10.59 12 5 17.59 6.41 19 12 13.41 17.59 19 19 17.59 13.41 12 19 6.41z"/></svg> </button> </form> <div class=md-search__output> <div class=md-search__scrollwrap data-md-scrollfix> <div class=md-search-result data-md-component=search-result> <div class=md-search-result__meta> Initializing search </div> <ol class=md-search-result__list></ol> </div> </div> </div> </div> </div> </nav> </header> <div class=md-container data-md-component=container> <main class=md-main data-md-component=main> <div class="md-main__inner md-grid"> <div class="md-sidebar md-sidebar--primary" data-md-component=sidebar data-md-type=navigation> <div class=md-sidebar__scrollwrap> <div class=md-sidebar__inner> <nav class="md-nav md-nav--primary" aria-label=Navigation data-md-level=0> <label class=md-nav__title for=__drawer> <a href=../../.. title="IPU SDK" class="md-nav__button md-logo" aria-label="IPU SDK" data-md-component=logo> <svg xmlns=http://www.w3.org/2000/svg viewbox="0 0 24 24"><path d="M10 20v-6h4v6h5v-8h3L12 3 2 12h3v8h5z"/></svg> </a> IPU SDK </label> <ul class=md-nav__list data-md-scrollfix> <li class=md-nav__item> <a href=../../../index.html class=md-nav__link> 主页 </a> </li> <li class="md-nav__item md-nav__item--nested"> <input class="md-nav__toggle md-toggle" data-md-toggle=__nav_2 type=checkbox id=__nav_2> <label class=md-nav__link for=__nav_2> SDK介绍 <span class="md-nav__icon md-icon"></span> </label> <nav class=md-nav aria-label=SDK介绍 data-md-level=1> <label class=md-nav__title for=__nav_2> <span class="md-nav__icon md-icon"></span> SDK介绍 </label> <ul class=md-nav__list data-md-scrollfix> <li class=md-nav__item> <a href=../../Introduction/Introduction.html class=md-nav__link> SDK框架介绍 </a> </li> <li class=md-nav__item> <a href=../../Introduction/Docker.html class=md-nav__link> Docker环境 </a> </li> </ul> </nav> </li> <li class="md-nav__item md-nav__item--active md-nav__item--nested"> <input class="md-nav__toggle md-toggle" data-md-toggle=__nav_3 type=checkbox id=__nav_3 checked> <label class=md-nav__link for=__nav_3> 用户手册 <span class="md-nav__icon md-icon"></span> </label> <nav class=md-nav aria-label=用户手册 data-md-level=1> <label class=md-nav__title for=__nav_3> <span class="md-nav__icon md-icon"></span> 用户手册 </label> <ul class=md-nav__list data-md-scrollfix> <li class=md-nav__item> <a href=Environment_Construction.html class=md-nav__link> 1. 快速开始 </a> </li> <li class=md-nav__item> <a href=../IPU200M/Convert.html class=md-nav__link> 2. Convert Tool </a> </li> <li class=md-nav__item> <a href=Calibrate.html class=md-nav__link> 3. Calibrator </a> </li> <li class=md-nav__item> <a href=Compile.html class=md-nav__link> 4. Compiler </a> </li> <li class=md-nav__item> <a href=../IPU200M/Simulate.html class=md-nav__link> 5. Simulator </a> </li> <li class=md-nav__item> <a href=DumpDebug_Tool.html class=md-nav__link> 6. DumpDebug Tool </a> </li> <li class=md-nav__item> <a href=SigmaStar_Post_Processing_Module.html class=md-nav__link> 7. SigmaStar后处理模块 </a> </li> <li class=md-nav__item> <a href=Adding_A_New_Layer.html class=md-nav__link> 8. 如何添加新的Layer </a> </li> <li class=md-nav__item> <a href=../IPU200M/Special_Model_Conversion.html class=md-nav__link> 9. 特殊模型转换要点 </a> </li> <li class=md-nav__item> <a href=../IPU200M/DLA_SDK_Support.html class=md-nav__link> 10. DLA SDK 支持 </a> </li> <li class="md-nav__item md-nav__item--active"> <input class="md-nav__toggle md-toggle" data-md-toggle=toc type=checkbox id=__toc> <label class="md-nav__link md-nav__link--active" for=__toc> 11. 在开发板上运行离线网络模型 <span class="md-nav__icon md-icon"></span> </label> <a href=Running_Offline_Network_Model_On_Development_Board.html class="md-nav__link md-nav__link--active"> 11. 在开发板上运行离线网络模型 </a> <nav class="md-nav md-nav--secondary" aria-label="Table of contents"> <label class=md-nav__title for=__toc> <span class="md-nav__icon md-icon"></span> Table of contents </label> <ul class=md-nav__list data-md-component=toc data-md-scrollfix> <li class=md-nav__item> <a href=#111-ipu class=md-nav__link> 11.1. 创建IPU设备 </a> </li> <li class=md-nav__item> <a href=#112-ipu class=md-nav__link> 11.2. 创建IPU通道 </a> </li> <li class=md-nav__item> <a href=#113-tensor class=md-nav__link> 11.3. 获取模型输入输出Tensor属性 </a> </li> <li class=md-nav__item> <a href=#114-tensor class=md-nav__link> 11.4. 获取输入输出Tensor </a> </li> <li class=md-nav__item> <a href=#115 class=md-nav__link> 11.5. 模型数据输入 </a> <nav class=md-nav aria-label="11.5. 模型数据输入"> <ul class=md-nav__list> <li class=md-nav__item> <a href=#1151 class=md-nav__link> 11.5.1. 拷贝数据 </a> </li> <li class=md-nav__item> <a href=#1152 class=md-nav__link> 11.5.2.零拷贝数据 </a> </li> </ul> </nav> </li> <li class=md-nav__item> <a href=#116 class=md-nav__link> 11.6. 模型推演 </a> </li> <li class=md-nav__item> <a href=#117-tensor class=md-nav__link> 11.7. 释放输入输出Tensor </a> </li> <li class=md-nav__item> <a href=#118-ipu class=md-nav__link> 11.8. 销毁IPU通道 </a> </li> <li class=md-nav__item> <a href=#119-ipu class=md-nav__link> 11.9. 销毁IPU设备 </a> </li> </ul> </nav> </li> <li class=md-nav__item> <a href=../IPU200M/Preprocess.py_and_Input_Config.ini_Support.html class=md-nav__link> 附录. 前处理和配置文件注意要点 </a> </li> </ul> </nav> </li> <li class="md-nav__item md-nav__item--nested"> <input class="md-nav__toggle md-toggle" data-md-toggle=__nav_4 type=checkbox id=__nav_4> <label class=md-nav__link for=__nav_4> FAQ <span class="md-nav__icon md-icon"></span> </label> <nav class=md-nav aria-label=FAQ data-md-level=1> <label class=md-nav__title for=__nav_4> <span class="md-nav__icon md-icon"></span> FAQ </label> <ul class=md-nav__list data-md-scrollfix> <li class=md-nav__item> <a href=../../FAQ/Common/Env_Setting.html class=md-nav__link> 环境设置问题 </a> </li> <li class=md-nav__item> <a href=../../FAQ/IPU200M/Model_Conversion.html class=md-nav__link> 模型转换问题 </a> </li> <li class=md-nav__item> <a href=../../FAQ/IPU200M/Development_Board.html class=md-nav__link> 板端使用问题 </a> </li> <li class=md-nav__item> <a href=../../FAQ/IPU200M/Other_Anomalies.html class=md-nav__link> 其他异常问题 </a> </li> </ul> </nav> </li> </ul> </nav> </div> </div> </div> <div class="md-sidebar md-sidebar--secondary" data-md-component=sidebar data-md-type=toc> <div class=md-sidebar__scrollwrap> <div class=md-sidebar__inner> <nav class="md-nav md-nav--secondary" aria-label="Table of contents"> <label class=md-nav__title for=__toc> <span class="md-nav__icon md-icon"></span> Table of contents </label> <ul class=md-nav__list data-md-component=toc data-md-scrollfix> <li class=md-nav__item> <a href=#111-ipu class=md-nav__link> 11.1. 创建IPU设备 </a> </li> <li class=md-nav__item> <a href=#112-ipu class=md-nav__link> 11.2. 创建IPU通道 </a> </li> <li class=md-nav__item> <a href=#113-tensor class=md-nav__link> 11.3. 获取模型输入输出Tensor属性 </a> </li> <li class=md-nav__item> <a href=#114-tensor class=md-nav__link> 11.4. 获取输入输出Tensor </a> </li> <li class=md-nav__item> <a href=#115 class=md-nav__link> 11.5. 模型数据输入 </a> <nav class=md-nav aria-label="11.5. 模型数据输入"> <ul class=md-nav__list> <li class=md-nav__item> <a href=#1151 class=md-nav__link> 11.5.1. 拷贝数据 </a> </li> <li class=md-nav__item> <a href=#1152 class=md-nav__link> 11.5.2.零拷贝数据 </a> </li> </ul> </nav> </li> <li class=md-nav__item> <a href=#116 class=md-nav__link> 11.6. 模型推演 </a> </li> <li class=md-nav__item> <a href=#117-tensor class=md-nav__link> 11.7. 释放输入输出Tensor </a> </li> <li class=md-nav__item> <a href=#118-ipu class=md-nav__link> 11.8. 销毁IPU通道 </a> </li> <li class=md-nav__item> <a href=#119-ipu class=md-nav__link> 11.9. 销毁IPU设备 </a> </li> </ul> </nav> </div> </div> </div> <div class=md-content data-md-component=content> <article class="md-content__inner md-typeset"> <h1>11. 在开发板上运行离线网络模型</h1> <p>建议在开发板上运行离线网络模型之前，使用Simulator对模型进行验证。Simulator能够在PC端模拟板上运行环境。 首先确保开发板已经烧录好最新的固件，使用Demo中的dla_classify可对单张图片推演一次，结果为推演结果的TOP5。下面分解dla_classify.cpp文件，针对使用MI_IPU API的顺序举例说明。 dla_classify.cpp文件路径为：<code>sdk/verify/mi_demo/source/dla_classify/dla_classify.cpp</code></p> <hr> <h2 id=111-ipu>11.1. 创建IPU设备<a class=headerlink href=#111-ipu title="Permanent link">&para;</a></h2> <div class=highlight><pre><span></span><code><span class=n>MI_S32</span> <span class=nf>IPUCreateDevice</span><span class=p>(</span><span class=kt>char</span><span class=o>*</span> <span class=n>pFirmwarePath</span><span class=p>,</span> <span class=n>MI_U32</span> <span class=n>u32VarBufSize</span><span class=p>)</span>
<span class=p>{</span>
    <span class=n>MI_S32</span> <span class=n>s32Ret</span> <span class=o>=</span> <span class=n>MI_SUCCESS</span><span class=p>;</span>
    <span class=n>MI_IPU_DevAttr_t</span> <span class=n>stDevAttr</span><span class=p>;</span>
    <span class=n>stDevAttr</span><span class=p>.</span><span class=n>u32MaxVariableBufSize</span> <span class=o>=</span> <span class=n>u32VarBufSize</span><span class=p>;</span>
    <span class=n>stDevAttr</span><span class=p>.</span><span class=n>u32YUV420_W_Pitch_Alignment</span> <span class=o>=</span> <span class=mi>16</span><span class=p>;</span>
    <span class=n>stDevAttr</span><span class=p>.</span><span class=n>u32YUV420_H_Pitch_Alignment</span> <span class=o>=</span> <span class=mi>2</span><span class=p>;</span>
    <span class=n>stDevAttr</span><span class=p>.</span><span class=n>u32XRGB_W_Pitch_Alignment</span> <span class=o>=</span> <span class=mi>16</span><span class=p>;</span>
    <span class=n>s32Ret</span> <span class=o>=</span> <span class=n>MI_IPU_CreateDevice</span><span class=p>(</span><span class=o>&amp;</span><span class=n>stDevAttr</span><span class=p>,</span> <span class=nb>NULL</span><span class=p>,</span> <span class=n>pFirmwarePath</span><span class=p>,</span> <span class=mi>0</span><span class=p>);</span>
    <span class=k>return</span> <span class=n>s32Ret</span><span class=p>;</span>
 <span class=p>}</span>
</code></pre></div> <p>输入参数：</p> <div class=codehilite><pre><span></span><code>```ini
pFirmwarePath：Firmware文件的路径，传NULL时会调用/config/dla/ipu_firmware.bin

u32VarBufSize：模型内部Tensor使用的memory的最大大小
```
</code></pre></div> <div class=highlight><pre><span></span><code>    <span class=n>u32VarBufSize获取方法如下</span><span class=err>：</span>

    <span class=err>方法</span><span class=mi>1</span> <span class=o>:</span><span class=err>可以通过</span><span class=n>parse_net工具获取</span>

         <span class=n>parse_net工具路径</span><span class=err>：</span><span class=n>SGS_IPU_SDK</span><span class=o>/</span><span class=n>bin</span><span class=o>/</span><span class=n>parse_net</span>

         <span class=err>使用时直接在</span><span class=n>parse_net命令后加上定点网络模型路径即可</span><span class=err>，可参考以下命令：</span>

         <span class=p>.</span><span class=o>/</span><span class=n>parse_net</span> <span class=n>xxx_fixed</span><span class=p>.</span><span class=n>sim</span> <span class=o>|</span> <span class=n>grep</span> <span class=n>Variable</span>
         <span class=err>输出：</span>
         <span class=o>|</span> <span class=o>|--&gt;</span> <span class=n>SubGraph</span><span class=p>[</span><span class=mi>0</span><span class=p>]</span><span class=o>:</span> <span class=n>Variable</span> <span class=n>buffer</span> <span class=mh>0xd2f00</span> <span class=err>@</span> <span class=mh>0x82f3b40</span><span class=p>.</span>
         <span class=err>其中</span><span class=mh>0xd2f00</span><span class=err>向</span><span class=mi>2</span><span class=err>对齐后为</span><span class=n>u32VarBufSize大小</span><span class=err>。</span>
         <span class=err>在</span><span class=n>C</span><span class=o>/</span><span class=n>C</span><span class=o>++</span><span class=err>中，向</span><span class=mi>2</span><span class=err>对齐的定义为：</span>
         <span class=cp>#define alignment_up(a, size) ((a + size - 1) &amp; (~(size - 1)))</span>
         <span class=err>所以</span><span class=n>u32VarBufSize为864000</span><span class=err>。</span>

    <span class=err>方法</span><span class=mi>2</span> <span class=o>:</span><span class=err>使用</span><span class=n>MI的API获取</span>

         <span class=n>MI_IPU_OfflineModelStaticInfo_t</span> <span class=n>OfflineModelInfo</span><span class=p>;</span>
         <span class=n>ret</span> <span class=o>=</span> <span class=n>MI_IPU_GetOfflineModeStaticInfo</span><span class=p>(</span><span class=nb>NULL</span><span class=p>,</span> <span class=n>modelFilePath</span><span class=p>,</span> <span class=o>&amp;</span><span class=n>OfflineModelInfo</span><span class=p>);</span>
         <span class=k>if</span> <span class=p>(</span><span class=n>ret</span> <span class=o>!=</span> <span class=n>MI_SUCCESS</span><span class=p>)</span>
         <span class=p>{</span>
            <span class=n>std</span><span class=o>::</span><span class=n>cerr</span> <span class=o>&lt;&lt;</span> <span class=s>&quot;get model variable buffer size failed!&quot;</span> <span class=o>&lt;&lt;</span> <span class=n>std</span><span class=o>::</span><span class=n>endl</span><span class=p>;</span>
            <span class=k>return</span><span class=p>;</span>
         <span class=p>}</span>
         <span class=n>u32VarBufSize</span> <span class=o>=</span> <span class=n>OfflineModelInfo</span><span class=p>.</span><span class=n>u32VariableBufferSize</span>

         <span class=err>如果有多个模型需要运行，选取最大的</span><span class=n>u32VarBufSize创建IPU设备即可</span><span class=err>。</span>
</code></pre></div> <p>输出参数：MI_IPU API 错误码，具体参考《MI_IPU_API手册》</p> <hr> <h2 id=112-ipu>11.2. 创建IPU通道<a class=headerlink href=#112-ipu title="Permanent link">&para;</a></h2> <div class=highlight><pre><span></span><code><span class=n>MI_S32</span> <span class=nf>IPUCreateChannel</span><span class=p>(</span><span class=n>MI_U32</span><span class=o>*</span> <span class=n>u32Channel</span><span class=p>,</span> <span class=kt>char</span><span class=o>*</span> <span class=n>pModelImage</span><span class=p>)</span>
<span class=p>{</span>
    <span class=n>MI_S32</span> <span class=n>s32Ret</span> <span class=p>;</span>
    <span class=n>MI_IPUChnAttr_t</span> <span class=n>stChnAttr</span><span class=p>;</span>

    <span class=c1>//create channel</span>
    <span class=n>memset</span><span class=p>(</span><span class=o>&amp;</span><span class=n>stChnAttr</span><span class=p>,</span> <span class=mi>0</span><span class=p>,</span> <span class=k>sizeof</span><span class=p>(</span><span class=n>stChnAttr</span><span class=p>));</span>
    <span class=n>stChnAttr</span><span class=p>.</span><span class=n>u32InputBufDepth</span> <span class=o>=</span> <span class=mi>2</span><span class=p>;</span>
    <span class=n>stChnAttr</span><span class=p>.</span><span class=n>u32OutputBufDepth</span> <span class=o>=</span> <span class=mi>2</span><span class=p>;</span>
    <span class=k>return</span> <span class=n>MI_IPU_CreateCHN</span><span class=p>(</span><span class=n>u32Channel</span><span class=p>,</span> <span class=o>&amp;</span><span class=n>stChnAttr</span><span class=p>,</span> <span class=nb>NULL</span><span class=p>,</span> <span class=n>pModelImage</span><span class=p>);</span>
<span class=p>}</span>
<span class=n>MI_U32</span> <span class=n>u32Channel</span><span class=p>;</span>
<span class=n>ret</span> <span class=o>=</span> <span class=n>IPUCreateChannel</span><span class=p>(</span><span class=o>&amp;</span><span class=n>u32ChannelID</span><span class=p>,</span> <span class=err>“</span><span class=p>.</span><span class=o>/</span><span class=n>mobilenet_v1_fixed</span><span class=p>.</span><span class=n>img</span><span class=err>”</span><span class=p>);</span>
</code></pre></div> <p>输入参数：</p> <div class=highlight><pre><span></span><code><span class=na>s32Channel：创建IPU通道的ID</span>

<span class=na>pModelImage：离线网络模型文件路径离线</span>
</code></pre></div> <p>输出参数：</p> <p>MI_IPU API 错误码，具体参考《MI_IPU_API手册》</p> <hr> <h2 id=113-tensor>11.3. 获取模型输入输出Tensor属性<a class=headerlink href=#113-tensor title="Permanent link">&para;</a></h2> <div class=highlight><pre><span></span><code><span class=n>MI_IPU_SubNet_InputOutputDesc_t</span> <span class=n>desc</span><span class=p>;</span>
<span class=n>MI_IPU_GetInOutTensorDesc</span><span class=p>(</span><span class=n>u32ChannelID</span><span class=p>,</span> <span class=o>&amp;</span><span class=n>desc</span><span class=p>);</span>
</code></pre></div> <p>输入参数：</p> <div class=highlight><pre><span></span><code><span class=na>u32ChnId：IPU通道的ID</span>
<span class=na>pstDesc：IPU子网络输入/输出描述结构体</span>
</code></pre></div> <p>输出参数：MI_IPU API 错误码，具体参考《MI_IPU_API手册》</p> <hr> <h2 id=114-tensor>11.4. 获取输入输出Tensor<a class=headerlink href=#114-tensor title="Permanent link">&para;</a></h2> <div class=highlight><pre><span></span><code><span class=n>MI_IPU_TensorVector_t</span> <span class=n>InputTensorVector</span><span class=p>;</span>
<span class=n>MI_IPU_TensorVector_t</span> <span class=n>OutputTensorVector</span><span class=p>;</span>
<span class=n>MI_IPU_GetInputTensors</span><span class=p>(</span><span class=n>u32ChannelID</span><span class=p>,</span> <span class=o>&amp;</span><span class=n>InputTensorVector</span><span class=p>);</span>
<span class=n>MI_IPU_GetOutputTensors</span><span class=p>(</span><span class=n>u32ChannelID</span><span class=p>,</span> <span class=o>&amp;</span><span class=n>OutputTensorVector</span><span class=p>);</span>
</code></pre></div> <p>输入参数：</p> <div class=highlight><pre><span></span><code><span class=na>u32ChannelID：IPU通道的ID</span>

<span class=na>InputTensorVector：输入IPU Tensor数组结构体</span>

<span class=na>OutputTensorVector：输出IPU Tensor数组结构体</span>
</code></pre></div> <p>输出参数：</p> <p>MI_IPU API 错误码，具体参考《MI_IPU_API手册》</p> <hr> <h2 id=115>11.5. 模型数据输入<a class=headerlink href=#115 title="Permanent link">&para;</a></h2> <h3 id=1151>11.5.1. 拷贝数据<a class=headerlink href=#1151 title="Permanent link">&para;</a></h3> <p>将数据拷贝至模型输入Tensor的虚拟地址，拷贝完成后调用MI_SYS_FlushInvCache：</p> <div class=highlight><pre><span></span><code><span class=n>MI_U8</span><span class=o>*</span> <span class=n>pdata</span> <span class=o>=</span> <span class=p>(</span><span class=n>MI_U8</span> <span class=o>*</span><span class=p>)</span><span class=n>InputTensorVector</span><span class=p>.</span><span class=n>astArrayTensors</span><span class=p>[</span><span class=mi>0</span><span class=p>].</span><span class=n>ptTensorData</span><span class=p>[</span><span class=mi>0</span><span class=p>];</span>
<span class=n>MI_U8</span><span class=o>*</span> <span class=n>pSrc</span> <span class=o>=</span> <span class=p>(</span><span class=n>MI_U8</span> <span class=o>*</span><span class=p>)</span><span class=n>Input_Data</span><span class=p>;</span>
<span class=k>for</span><span class=p>(</span><span class=kt>int</span> <span class=n>i</span> <span class=o>=</span> <span class=mi>0</span><span class=p>;</span> <span class=n>i</span> <span class=o>&lt;</span> <span class=n>imageSize</span><span class=p>;</span> <span class=n>i</span><span class=o>++</span><span class=p>)</span>
<span class=p>{</span>
    <span class=o>*</span><span class=p>(</span><span class=n>pdata</span> <span class=o>+</span> <span class=n>i</span><span class=p>)</span> <span class=o>=</span> <span class=o>*</span><span class=p>(</span><span class=n>pSrc</span> <span class=o>+</span> <span class=n>i</span><span class=p>);</span>
<span class=p>}</span>
<span class=n>MI_SYS_FlushInvCache</span><span class=p>(</span><span class=n>pdata</span><span class=p>,</span> <span class=n>imageSize</span><span class=p>);</span>
</code></pre></div> <ul> <li> <p>注意：ptTensorData / phyTensorAddr 只用到0地址。</p> </li> <li> <p>模型中input formats设置为<code>BGR</code>或者<code>RGB</code>时，stride不要做alignment，做了alignment反而有问题。</p> </li> <li> <p>模型中input formats设置为<code>BGRA</code>或者<code>RGBA</code>（A channel在高地址），alignmet规则是stride = alignment_up(width*4, 16 )。</p> <p>input_formats设置为<code>BGRA</code>，对应MI_SYS_PixelFormat_e为<code>E_MI_SYSPIXEL_FRAME_ARGB8888</code>。</p> <p>input_formats设置为<code>RGBA</code>，对应MI_SYS_PixelFormat_e为<code>E_MI_SYSPIXEL_FRAME_ABGR8888</code>。</p> </li> <li> <p>input formats设置为<code>YUV_NV12</code>或者<code>GRAY</code>时，alignment规则是stride = alignment_up(width,16)，height需要2 alignment。</p> </li> <li> <p>input_formats设置为<code>YUV_NV12</code>或者<code>GRAY</code>时，对应MI_SYS_PixelFormat_e为<code>E_MI_SYSPIXEL_FRAME_YUV_SEMIPLANAR_420</code>。</p> </li> </ul> <hr> <h3 id=1152>11.5.2.零拷贝数据<a class=headerlink href=#1152 title="Permanent link">&para;</a></h3> <p>如果使用MI的其他模块，为模型输入做好了数据准备，可以不用拷贝数据，直接给传递其他MI模块的物理地址。</p> <p>以下以MI_SYS模块示例模型输入零拷贝数据，需注意：</p> <p>创建IPU通道时，输入InputBufDepth设为零，不再使用MI_IPU_GetInputTensors分配输入空间</p> <p><code>stChnAttr.u32InputBufDepth = 0;</code></p> <p>由于不使用MI_IPU_GetInputTensors，需手动将网络描述的网络模型输入个数赋给</p> <p>InputTensorVector.u32TensorCount。</p> <p><code>InputTensorVector.u32TensorCount = desc.u32InputTensorCount;</code></p> <p>使用MI_SYS的API分配空间:</p> <div class=highlight><pre><span></span><code><span class=n>MI_S32</span> <span class=n>s32ret</span> <span class=o>=</span> <span class=mi>0</span><span class=p>;</span>
<span class=n>MI_PHY</span> <span class=n>phyAddr</span> <span class=o>=</span> <span class=mi>0</span><span class=p>;</span>
<span class=kt>void</span><span class=o>*</span> <span class=n>pVirAddr</span> <span class=o>=</span> <span class=nb>NULL</span><span class=p>;</span>
<span class=n>s32ret</span> <span class=o>=</span> <span class=n>MI_SYS_MMA_Alloc</span><span class=p>(</span><span class=nb>NULL</span><span class=p>,</span> <span class=n>BufSize</span><span class=p>,</span> <span class=o>&amp;</span><span class=n>phyAddr</span><span class=p>);</span>
<span class=k>if</span> <span class=p>(</span><span class=n>s32ret</span> <span class=o>!=</span> <span class=n>MI_SUCCESS</span><span class=p>)</span>
<span class=p>{</span>
    <span class=k>throw</span> <span class=n>std</span><span class=o>::</span><span class=n>runtime_error</span><span class=p>(</span><span class=s>&quot;Alloc buffer failed!&quot;</span><span class=p>);</span>
<span class=p>}</span>
<span class=n>s32ret</span> <span class=o>=</span> <span class=n>MI_SYS_Mmap</span><span class=p>(</span><span class=n>phyAddr</span><span class=p>,</span> <span class=n>BufSize</span><span class=p>,</span> <span class=o>&amp;</span><span class=n>pVirAddr</span><span class=p>,</span> <span class=n>TRUE</span><span class=p>);</span>
<span class=k>if</span> <span class=p>(</span><span class=n>s32ret</span> <span class=o>!=</span> <span class=n>MI_SUCCESS</span><span class=p>)</span>
<span class=p>{</span>
    <span class=n>MI_SYS_MMA_Free</span><span class=p>(</span><span class=n>phyAddr</span><span class=p>);</span>
    <span class=k>throw</span> <span class=n>std</span><span class=o>::</span><span class=n>runtime_error</span><span class=p>(</span><span class=s>&quot;Mmap buffer failed!&quot;</span><span class=p>);</span>
<span class=p>}</span>
</code></pre></div> <p>传递虚拟地址和物理地址给InputTensorVector <div class=highlight><pre><span></span><code><span class=n>InputTensorVector</span><span class=p>.</span><span class=n>astArrayTensors</span><span class=p>[</span><span class=mi>0</span><span class=p>].</span><span class=n>ptTensorData</span><span class=p>[</span><span class=mi>0</span><span class=p>]</span> <span class=o>=</span> <span class=n>pVirAddr</span><span class=p>;</span>
<span class=n>InputTensorVector</span><span class=p>.</span><span class=n>astArrayTensors</span><span class=p>[</span><span class=mi>0</span><span class=p>].</span><span class=n>phyTensorAddr</span><span class=p>[</span><span class=mi>0</span><span class=p>]</span> <span class=o>=</span> <span class=n>phyAddr</span><span class=p>;</span>
</code></pre></div></p> <p>之后可以进行模型推演。 使用零拷贝数据，不需要再释放输入Tensor，即不再调用MI_IPU_PutInputTensors函数，释放Buffer调用MI_SYS的API完成。 释放MI_SYS的Buffer <div class=highlight><pre><span></span><code><span class=n>MI_SYS_Munmap</span><span class=p>(</span><span class=n>pVirAddr</span><span class=p>,</span> <span class=n>BufSize</span><span class=p>);</span>
<span class=n>MI_SYS_MMA_Free</span><span class=p>(</span><span class=n>phyAddr</span><span class=p>);</span>
</code></pre></div></p> <hr> <h2 id=116>11.6. 模型推演<a class=headerlink href=#116 title="Permanent link">&para;</a></h2> <div class=highlight><pre><span></span><code><span class=n>ret</span> <span class=o>=</span> <span class=n>MI_IPU_Invoke</span><span class=p>(</span><span class=n>u32ChannelID</span><span class=p>,</span> <span class=o>&amp;</span><span class=n>InputTensorVector</span><span class=p>,</span> <span class=o>&amp;</span><span class=n>OutputTensorVector</span><span class=p>);</span>
<span class=k>if</span> <span class=p>(</span><span class=n>ret</span> <span class=o>!=</span> <span class=n>MI_SUCCESS</span><span class=p>)</span>
<span class=p>{</span>
    <span class=n>MI_IPU_DestroyCHN</span><span class=p>(</span><span class=n>u32ChannelID</span><span class=p>);</span>
    <span class=n>MI_IPU_DestroyDevice</span><span class=p>();</span>
    <span class=n>std</span><span class=o>::</span><span class=n>cerr</span> <span class=o>&lt;&lt;</span> <span class=s>&quot;IPU invoke failed!!&quot;</span> <span class=o>&lt;&lt;</span> <span class=n>std</span><span class=o>::</span><span class=n>endl</span><span class=p>;</span>
<span class=p>}</span>
</code></pre></div> <p>输入参数：</p> <div class=highlight><pre><span></span><code><span class=na>u32ChannelID：IPU通道的ID</span>

<span class=na>InputTensorVector：输入IPU Tensor数组结构体</span>

<span class=na>OutputTensorVector：输出IPU Tensor数组结构体</span>
</code></pre></div> <p>输出参数：</p> <p>MI_IPU API 错误码，具体参考《MI_IPU_API手册》</p> <hr> <h2 id=117-tensor>11.7. 释放输入输出Tensor<a class=headerlink href=#117-tensor title="Permanent link">&para;</a></h2> <p><div class=highlight><pre><span></span><code><span class=n>MI_IPU_PutInputTensors</span><span class=p>(</span><span class=n>u32ChannelID</span><span class=p>,</span> <span class=o>&amp;</span><span class=n>InputTensorVector</span><span class=p>);</span>
<span class=n>MI_IPU_PutOutputTensors</span><span class=p>(</span><span class=n>u32ChannelID</span><span class=p>,</span> <span class=o>&amp;</span><span class=n>OutputTensorVector</span><span class=p>);</span>
</code></pre></div> 输入参数：</p> <div class=highlight><pre><span></span><code><span class=na>u32ChannelID：IPU通道的ID</span>

<span class=na>InputTensorVector：输入IPU Tensor数组结构体</span>

<span class=na>OutputTensorVector：输出IPU Tensor数组结构体</span>
</code></pre></div> <p>输出参数：</p> <p>MI_IPU API 错误码，具体参考《MI_IPU_API手册》</p> <hr> <h2 id=118-ipu>11.8. 销毁IPU通道<a class=headerlink href=#118-ipu title="Permanent link">&para;</a></h2> <div class=highlight><pre><span></span><code><span class=n>MI_IPU_DestroyCHN</span><span class=p>(</span><span class=n>u32ChannelID</span><span class=p>);</span>
</code></pre></div> <p>输入参数：</p> <div class=highlight><pre><span></span><code><span class=na>s32Channel：创建IPU通道的ID</span>
</code></pre></div> <p>输出参数：</p> <p>MI_IPU API 错误码，具体参考《MI_IPU_API手册》</p> <hr> <h2 id=119-ipu>11.9. 销毁IPU设备<a class=headerlink href=#119-ipu title="Permanent link">&para;</a></h2> <div class=highlight><pre><span></span><code><span class=n>MI_IPU_DestroyDevice</span><span class=p>();</span>
</code></pre></div> <p>输入参数：</p> <div class=highlight><pre><span></span><code><span class=na>空</span>
</code></pre></div> <p>输出参数：MI_IPU API 错误码，具体参考《MI_IPU_API手册》</p> <hr> </article> </div> </div> </main> <footer class=md-footer> <nav class="md-footer__inner md-grid" aria-label=Footer> <a href=../IPU200M/DLA_SDK_Support.html class="md-footer__link md-footer__link--prev" rel=prev> <div class="md-footer__button md-icon"> <svg xmlns=http://www.w3.org/2000/svg viewbox="0 0 24 24"><path d="M20 11v2H8l5.5 5.5-1.42 1.42L4.16 12l7.92-7.92L13.5 5.5 8 11h12z"/></svg> </div> <div class=md-footer__title> <div class=md-ellipsis> <span class=md-footer__direction> Previous </span> 10. DLA SDK 支持 </div> </div> </a> <a href=../IPU200M/Preprocess.py_and_Input_Config.ini_Support.html class="md-footer__link md-footer__link--next" rel=next> <div class=md-footer__title> <div class=md-ellipsis> <span class=md-footer__direction> Next </span> 附录. 前处理和配置文件注意要点 </div> </div> <div class="md-footer__button md-icon"> <svg xmlns=http://www.w3.org/2000/svg viewbox="0 0 24 24"><path d="M4 11v2h12l-5.5 5.5 1.42 1.42L19.84 12l-7.92-7.92L10.5 5.5 16 11H4z"/></svg> </div> </a> </nav> <div class="md-footer-meta md-typeset"> <div class="md-footer-meta__inner md-grid"> <div class=md-footer-copyright> <div class=md-footer-copyright__highlight> Copyright&copy; 2021 SigmaStar Technology. All rights reserved. Security Level: Confidential A. </div> Made with <a href=https://squidfunk.github.io/mkdocs-material/ target=_blank rel=noopener> Material for MkDocs </a> </div> </div> </div> </footer> </div> <div class=md-dialog data-md-component=dialog> <div class="md-dialog__inner md-typeset"></div> </div> <script id=__config type=application/json>{"base": "../../..", "features": [], "translations": {"clipboard.copy": "Copy to clipboard", "clipboard.copied": "Copied to clipboard", "search.config.lang": "en", "search.config.pipeline": "trimmer, stopWordFilter", "search.config.separator": "[\\s\\-]+", "search.placeholder": "Search", "search.result.placeholder": "Type to start searching", "search.result.none": "No matching documents", "search.result.one": "1 matching document", "search.result.other": "# matching documents", "search.result.more.one": "1 more on this page", "search.result.more.other": "# more on this page", "search.result.term.missing": "Missing"}, "search": "../../../assets/javascripts/workers/search.fb4a9340.min.js", "version": null}</script> <script src=../../../assets/javascripts/bundle.a1c7c35e.min.js></script> <script src=../../../search/search_index.js></script> <script src=../../../javascripts/extra.js></script> <script src="https://cdnjs.cloudflare.com/ajax/libs/mathjax/2.7.0/MathJax.js?config=TeX-MML-AM_CHTML"></script> <script src=https://cdnjs.cloudflare.com/ajax/libs/raphael/2.2.7/raphael.min.js></script> <script src=https://cdnjs.cloudflare.com/ajax/libs/underscore.js/1.8.3/underscore-min.js></script> <script src=https://cdnjs.cloudflare.com/ajax/libs/js-sequence-diagrams/1.0.6/sequence-diagram-min.js></script> <script src=https://cdnjs.cloudflare.com/ajax/libs/flowchart/1.6.5/flowchart.min.js></script> <script src=https://unpkg.com/freezeframe/dist/freezeframe.min.js></script> <script src=https://unpkg.com/mermaid@7.1.0/dist/mermaid.min.js></script> <script src=../../../javascripts/umlconvert.js></script> </body> </html>