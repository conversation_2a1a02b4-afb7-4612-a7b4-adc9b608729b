<!doctype html><html lang=en class=no-js> <head><meta charset=utf-8><meta name=viewport content="width=device-width,initial-scale=1"><meta name=description content="Personal Documentation System Template"><meta name=author content="<PERSON> Cho<PERSON>"><link rel=icon href=../../../images/favicon.ico><meta name=generator content="mkdocs-1.1.2, mkdocs-material-7.0.6"><title>6. DumpDebug Tool - IPU SDK</title><link rel=stylesheet href=../../../assets/stylesheets/main.2c0c5eaf.min.css><link rel=stylesheet href=../../../assets/stylesheets/palette.7fa14f5b.min.css><meta name=theme-color content=#009485><link rel=preconnect href=https://fonts.gstatic.com crossorigin><link rel=stylesheet href="https://fonts.googleapis.com/css?family=Roboto:300,400,400i,700%7CRoboto+Mono&display=fallback"><style>:root{--md-text-font-family:"Roboto";--md-code-font-family:"Roboto Mono"}</style><link rel=stylesheet href=../../../stylesheets/extra.css></head> <body dir=ltr data-md-color-scheme data-md-color-primary=teal data-md-color-accent=teal> <input class=md-toggle data-md-toggle=drawer type=checkbox id=__drawer autocomplete=off> <input class=md-toggle data-md-toggle=search type=checkbox id=__search autocomplete=off> <label class=md-overlay for=__drawer></label> <div data-md-component=skip> <a href=#61 class=md-skip> Skip to content </a> </div> <div data-md-component=announce> </div> <header class=md-header data-md-component=header> <nav class="md-header__inner md-grid" aria-label=Header> <a href=../../.. title="IPU SDK" class="md-header__button md-logo" aria-label="IPU SDK" data-md-component=logo> <svg xmlns=http://www.w3.org/2000/svg viewbox="0 0 24 24"><path d="M10 20v-6h4v6h5v-8h3L12 3 2 12h3v8h5z"/></svg> </a> <label class="md-header__button md-icon" for=__drawer> <svg xmlns=http://www.w3.org/2000/svg viewbox="0 0 24 24"><path d="M3 6h18v2H3V6m0 5h18v2H3v-2m0 5h18v2H3v-2z"/></svg> </label> <div class=md-header__title data-md-component=header-title> <div class=md-header__ellipsis> <div class=md-header__topic> <span class=md-ellipsis> IPU SDK </span> </div> <div class=md-header__topic data-md-component=header-topic> <span class=md-ellipsis> 6. DumpDebug Tool </span> </div> </div> </div> <div class=md-header__options> </div> <label class="md-header__button md-icon" for=__search> <svg xmlns=http://www.w3.org/2000/svg viewbox="0 0 24 24"><path d="M9.5 3A6.5 6.5 0 0 1 16 9.5c0 1.61-.59 3.09-1.56 4.23l.27.27h.79l5 5-1.5 1.5-5-5v-.79l-.27-.27A6.516 6.516 0 0 1 9.5 16 6.5 6.5 0 0 1 3 9.5 6.5 6.5 0 0 1 9.5 3m0 2C7 5 5 7 5 9.5S7 14 9.5 14 14 12 14 9.5 12 5 9.5 5z"/></svg> </label> <div class=md-search data-md-component=search role=dialog> <label class=md-search__overlay for=__search></label> <div class=md-search__inner role=search> <form class=md-search__form name=search> <input type=text class=md-search__input name=query aria-label=Search placeholder=Search autocapitalize=off autocorrect=off autocomplete=off spellcheck=false data-md-component=search-query data-md-state=active required> <label class="md-search__icon md-icon" for=__search> <svg xmlns=http://www.w3.org/2000/svg viewbox="0 0 24 24"><path d="M9.5 3A6.5 6.5 0 0 1 16 9.5c0 1.61-.59 3.09-1.56 4.23l.27.27h.79l5 5-1.5 1.5-5-5v-.79l-.27-.27A6.516 6.516 0 0 1 9.5 16 6.5 6.5 0 0 1 3 9.5 6.5 6.5 0 0 1 9.5 3m0 2C7 5 5 7 5 9.5S7 14 9.5 14 14 12 14 9.5 12 5 9.5 5z"/></svg> <svg xmlns=http://www.w3.org/2000/svg viewbox="0 0 24 24"><path d="M20 11v2H8l5.5 5.5-1.42 1.42L4.16 12l7.92-7.92L13.5 5.5 8 11h12z"/></svg> </label> <button type=reset class="md-search__icon md-icon" aria-label=Clear tabindex=-1> <svg xmlns=http://www.w3.org/2000/svg viewbox="0 0 24 24"><path d="M19 6.41L17.59 5 12 10.59 6.41 5 5 6.41 10.59 12 5 17.59 6.41 19 12 13.41 17.59 19 19 17.59 13.41 12 19 6.41z"/></svg> </button> </form> <div class=md-search__output> <div class=md-search__scrollwrap data-md-scrollfix> <div class=md-search-result data-md-component=search-result> <div class=md-search-result__meta> Initializing search </div> <ol class=md-search-result__list></ol> </div> </div> </div> </div> </div> </nav> </header> <div class=md-container data-md-component=container> <main class=md-main data-md-component=main> <div class="md-main__inner md-grid"> <div class="md-sidebar md-sidebar--primary" data-md-component=sidebar data-md-type=navigation> <div class=md-sidebar__scrollwrap> <div class=md-sidebar__inner> <nav class="md-nav md-nav--primary" aria-label=Navigation data-md-level=0> <label class=md-nav__title for=__drawer> <a href=../../.. title="IPU SDK" class="md-nav__button md-logo" aria-label="IPU SDK" data-md-component=logo> <svg xmlns=http://www.w3.org/2000/svg viewbox="0 0 24 24"><path d="M10 20v-6h4v6h5v-8h3L12 3 2 12h3v8h5z"/></svg> </a> IPU SDK </label> <ul class=md-nav__list data-md-scrollfix> <li class=md-nav__item> <a href=../../../index.html class=md-nav__link> 主页 </a> </li> <li class="md-nav__item md-nav__item--nested"> <input class="md-nav__toggle md-toggle" data-md-toggle=__nav_2 type=checkbox id=__nav_2> <label class=md-nav__link for=__nav_2> SDK介绍 <span class="md-nav__icon md-icon"></span> </label> <nav class=md-nav aria-label=SDK介绍 data-md-level=1> <label class=md-nav__title for=__nav_2> <span class="md-nav__icon md-icon"></span> SDK介绍 </label> <ul class=md-nav__list data-md-scrollfix> <li class=md-nav__item> <a href=../../Introduction/Introduction.html class=md-nav__link> SDK框架介绍 </a> </li> <li class=md-nav__item> <a href=../../Introduction/Docker.html class=md-nav__link> Docker环境 </a> </li> </ul> </nav> </li> <li class="md-nav__item md-nav__item--active md-nav__item--nested"> <input class="md-nav__toggle md-toggle" data-md-toggle=__nav_3 type=checkbox id=__nav_3 checked> <label class=md-nav__link for=__nav_3> 用户手册 <span class="md-nav__icon md-icon"></span> </label> <nav class=md-nav aria-label=用户手册 data-md-level=1> <label class=md-nav__title for=__nav_3> <span class="md-nav__icon md-icon"></span> 用户手册 </label> <ul class=md-nav__list data-md-scrollfix> <li class=md-nav__item> <a href=Environment_Construction.html class=md-nav__link> 1. 快速开始 </a> </li> <li class=md-nav__item> <a href=../IPU200M/Convert.html class=md-nav__link> 2. Convert Tool </a> </li> <li class=md-nav__item> <a href=Calibrate.html class=md-nav__link> 3. Calibrator </a> </li> <li class=md-nav__item> <a href=Compile.html class=md-nav__link> 4. Compiler </a> </li> <li class=md-nav__item> <a href=../IPU200M/Simulate.html class=md-nav__link> 5. Simulator </a> </li> <li class="md-nav__item md-nav__item--active"> <input class="md-nav__toggle md-toggle" data-md-toggle=toc type=checkbox id=__toc> <label class="md-nav__link md-nav__link--active" for=__toc> 6. DumpDebug Tool <span class="md-nav__icon md-icon"></span> </label> <a href=DumpDebug_Tool.html class="md-nav__link md-nav__link--active"> 6. DumpDebug Tool </a> <nav class="md-nav md-nav--secondary" aria-label="Table of contents"> <label class=md-nav__title for=__toc> <span class="md-nav__icon md-icon"></span> Table of contents </label> <ul class=md-nav__list data-md-component=toc data-md-scrollfix> <li class=md-nav__item> <a href=#61 class=md-nav__link> 6.1. 主要功能 </a> </li> <li class=md-nav__item> <a href=#62 class=md-nav__link> 6.2. 使用步骤 </a> <nav class=md-nav aria-label="6.2. 使用步骤"> <ul class=md-nav__list> <li class=md-nav__item> <a href=#621-dump class=md-nav__link> 6.2.1 Dump网络模型中各层数据 </a> <nav class=md-nav aria-label="6.2.1 Dump网络模型中各层数据"> <ul class=md-nav__list> <li class=md-nav__item> <a href=#6211-dump-sigmastar class=md-nav__link> ******* Dump SigmaStar浮点或定点模型 </a> </li> <li class=md-nav__item> <a href=#6212-dump-caffe class=md-nav__link> ******* Dump Caffe原模型 </a> </li> <li class=md-nav__item> <a href=#6213-dump-onnx class=md-nav__link> ******* Dump Onnx原模型 </a> </li> </ul> </nav> </li> <li class=md-nav__item> <a href=#622-auto_dump_debugsh class=md-nav__link> 6.2.2 使用auto_dump_debug.sh脚本分析数据 </a> </li> <li class=md-nav__item> <a href=#6221-sigmastar class=md-nav__link> ******* 对比分析SigmaStar浮点与定点模型 </a> </li> <li class=md-nav__item> <a href=#6222-sigmastarcaffeonnx class=md-nav__link> 6.2.2.2 对比分析SigmaStar（浮点/定点）模型与原模型（Caffe/Onnx） </a> </li> </ul> </nav> </li> <li class=md-nav__item> <a href=#63-histogrampyhistogram class=md-nav__link> 6.3. 使用histogram.py绘制各层网络模型的Histogram图 </a> </li> <li class=md-nav__item> <a href=#64 class=md-nav__link> 6.4. 错误处理 </a> </li> <li class=md-nav__item> <a href=#65 class=md-nav__link> 6.5. 相关问题汇总 </a> </li> </ul> </nav> </li> <li class=md-nav__item> <a href=SigmaStar_Post_Processing_Module.html class=md-nav__link> 7. SigmaStar后处理模块 </a> </li> <li class=md-nav__item> <a href=Adding_A_New_Layer.html class=md-nav__link> 8. 如何添加新的Layer </a> </li> <li class=md-nav__item> <a href=../IPU200M/Special_Model_Conversion.html class=md-nav__link> 9. 特殊模型转换要点 </a> </li> <li class=md-nav__item> <a href=../IPU200M/DLA_SDK_Support.html class=md-nav__link> 10. DLA SDK 支持 </a> </li> <li class=md-nav__item> <a href=Running_Offline_Network_Model_On_Development_Board.html class=md-nav__link> 11. 在开发板上运行离线网络模型 </a> </li> <li class=md-nav__item> <a href=../IPU200M/Preprocess.py_and_Input_Config.ini_Support.html class=md-nav__link> 附录. 前处理和配置文件注意要点 </a> </li> </ul> </nav> </li> <li class="md-nav__item md-nav__item--nested"> <input class="md-nav__toggle md-toggle" data-md-toggle=__nav_4 type=checkbox id=__nav_4> <label class=md-nav__link for=__nav_4> FAQ <span class="md-nav__icon md-icon"></span> </label> <nav class=md-nav aria-label=FAQ data-md-level=1> <label class=md-nav__title for=__nav_4> <span class="md-nav__icon md-icon"></span> FAQ </label> <ul class=md-nav__list data-md-scrollfix> <li class=md-nav__item> <a href=../../FAQ/Common/Env_Setting.html class=md-nav__link> 环境设置问题 </a> </li> <li class=md-nav__item> <a href=../../FAQ/IPU200M/Model_Conversion.html class=md-nav__link> 模型转换问题 </a> </li> <li class=md-nav__item> <a href=../../FAQ/IPU200M/Development_Board.html class=md-nav__link> 板端使用问题 </a> </li> <li class=md-nav__item> <a href=../../FAQ/IPU200M/Other_Anomalies.html class=md-nav__link> 其他异常问题 </a> </li> </ul> </nav> </li> </ul> </nav> </div> </div> </div> <div class="md-sidebar md-sidebar--secondary" data-md-component=sidebar data-md-type=toc> <div class=md-sidebar__scrollwrap> <div class=md-sidebar__inner> <nav class="md-nav md-nav--secondary" aria-label="Table of contents"> <label class=md-nav__title for=__toc> <span class="md-nav__icon md-icon"></span> Table of contents </label> <ul class=md-nav__list data-md-component=toc data-md-scrollfix> <li class=md-nav__item> <a href=#61 class=md-nav__link> 6.1. 主要功能 </a> </li> <li class=md-nav__item> <a href=#62 class=md-nav__link> 6.2. 使用步骤 </a> <nav class=md-nav aria-label="6.2. 使用步骤"> <ul class=md-nav__list> <li class=md-nav__item> <a href=#621-dump class=md-nav__link> 6.2.1 Dump网络模型中各层数据 </a> <nav class=md-nav aria-label="6.2.1 Dump网络模型中各层数据"> <ul class=md-nav__list> <li class=md-nav__item> <a href=#6211-dump-sigmastar class=md-nav__link> ******* Dump SigmaStar浮点或定点模型 </a> </li> <li class=md-nav__item> <a href=#6212-dump-caffe class=md-nav__link> ******* Dump Caffe原模型 </a> </li> <li class=md-nav__item> <a href=#6213-dump-onnx class=md-nav__link> ******* Dump Onnx原模型 </a> </li> </ul> </nav> </li> <li class=md-nav__item> <a href=#622-auto_dump_debugsh class=md-nav__link> 6.2.2 使用auto_dump_debug.sh脚本分析数据 </a> </li> <li class=md-nav__item> <a href=#6221-sigmastar class=md-nav__link> ******* 对比分析SigmaStar浮点与定点模型 </a> </li> <li class=md-nav__item> <a href=#6222-sigmastarcaffeonnx class=md-nav__link> 6.2.2.2 对比分析SigmaStar（浮点/定点）模型与原模型（Caffe/Onnx） </a> </li> </ul> </nav> </li> <li class=md-nav__item> <a href=#63-histogrampyhistogram class=md-nav__link> 6.3. 使用histogram.py绘制各层网络模型的Histogram图 </a> </li> <li class=md-nav__item> <a href=#64 class=md-nav__link> 6.4. 错误处理 </a> </li> <li class=md-nav__item> <a href=#65 class=md-nav__link> 6.5. 相关问题汇总 </a> </li> </ul> </nav> </div> </div> </div> <div class=md-content data-md-component=content> <article class="md-content__inner md-typeset"> <h1>6. DumpDebug Tool</h1> <h2 id=61>6.1. 主要功能<a class=headerlink href=#61 title="Permanent link">&para;</a></h2> <p>DumpDebug Tool工具的位置在 <strong>SGS_IPU_SDK/DumpDebug/</strong> 。 该工具是主要功能如下： </p> <ul> <li>解析网络模型数据；</li> <li>对比不同阶段的网络模型数据；</li> <li>绘制各层网络模型的Histogram图。</li> </ul> <p>SigmaStar DLA SDK在转换网络时，Caffe、Onnx、TensorFlow的网络模型与SigmaStar浮点网络模型的结果完全一致，SigmaStar定点网络模型与SigmaStar离线网络模型的结果完全一致。</p> <p>有精度差别的只有在SigmaStar浮点模型转换为SigmaStar定点这个阶段，因此在发现SigmaStar定点网络的结果与原有浮点的结果相差较多时，使用DumpDebug Tool可以检查转换后定点网络的错误。</p> <p>DumpDebug Tool也可用于直接dump原模型Caffe/Onnx的每一层数据，以便与SigmaStar模型进行对比。</p> <hr> <h2 id=62>6.2. 使用步骤<a class=headerlink href=#62 title="Permanent link">&para;</a></h2> <h3 id=621-dump>6.2.1 Dump网络模型中各层数据<a class=headerlink href=#621-dump title="Permanent link">&para;</a></h3> <h4 id=6211-dump-sigmastar>******* Dump SigmaStar浮点或定点模型<a class=headerlink href=#6211-dump-sigmastar title="Permanent link">&para;</a></h4> <ul> <li> <p>在 <em>SGS_IPU_SDK/cfg</em> 文件夹中找到DebugConfig.txt文件，复制到当前运行路径下.</p> </li> <li> <p>打开DebugConfig.txt，修改文件内容： <div class=highlight><pre><span></span><code><span class=na>dumpTensor </span>
<span class=na>eliminateGarbage </span>
<span class=na>dequantFixed </span>
<span class=c1>#dumpasstring </span>
<span class=c1>#disableDomainFuseOps </span>
<span class=na>path</span><span class=o>=</span>
</code></pre></div></p> <p>DebugConfig.txt各参数作用:</p> <p><code>dumpTensor</code>: Dump网络模型各层数据总开关。</p> <p><code>eliminateGarbage</code>: Dump网络模型数据时去除无用数据（建议开启）。 </p> <p><code>dequantFixed</code>: 针对定点网络模型，将整形数据转换为浮点数据（建议开启）。</p> <p><code>dumpasstring</code>: Dump网络模型数据为字符串类型，关闭选项为二进制文件类型。（如果需要[6.2.2 使用auto_dump_debug.sh脚本分析数据]功能，务必关闭此选项）。</p> <p><code>disableDomainFuseOps</code>: 转换定点网络模型时，取消网络层融合（建议关闭）。</p> <p><code>path=</code>: 指定生成档案的输出全路径（path= 后面确保填写类似 <strong>/home/<USER>/strong> 的绝对路径。如果path= 后面没有内容或根本没有 path= 的话，则会默认输出到 <strong>$HOME</strong> 位置。另外，绝对路径或 <strong>/home/<USER>/strong> 不超过122字节）。 </p> </li> <li> <p>运行simulator对单张图片推演。 </p> </li> <li> <p>推演完成后，会在path=参数指定的目录生成sigma_outtensor_dump.bin，即为Dump网络模型中各层的数据。</p> </li> </ul> <p><strong>Note</strong> </p> <ul> <li>Dump完成后重命名sigma_outtensor_dump.bin文件，请勿修改文件后缀。新的Dump会复写sigma_outtensor_dump.bin文件。</li> <li>DebugConfig.txt文件中disableDomainFuseOps 选项只在使用calibrator将浮点网络模型转换成定点网络模型时有用。其作用是取消网络融合功能，关闭该选项时，定点网络模型和离线网络模型能够在转换时优化网络模型的算子，加速模型的运行，但是也会影响网络的层级结构，使得部分算子的输出不会被Dump到sigma_outtersor_dump.bin文件中。如果需要网络模型的每一层数据，可以打开disableDomainFuseOps选项，重新运行calibrator转换定点网络模型，此时转出的模型没有融合优化，可以输出每层的数据。</li> <li>对比浮点网络模型和定点网络模型，使用simulator中参数 <code>-t/--type</code> 和 <code>-m/--model</code> 指定不同阶段的模型，即可Dump出不同阶段的网络模型数据。</li> <li>离线模型不支持Dump Debug,离线模型网络的层级结构Domain已经融合，不能Dump网络模型中各层的数据。</li> <li>使用calibrator将浮点网络模型转换为定点网络模型时，会在SGS_IPU_SDK根目录下产生tensor_min_max.txt 文件，此文件记录了网络的每层输入和输出的最大与最小值，将在之后分析Dump数据时有用。</li> </ul> <h4 id=6212-dump-caffe>******* Dump Caffe原模型<a class=headerlink href=#6212-dump-caffe title="Permanent link">&para;</a></h4> <p>工具的位置在 <strong>SGS_IPU_SDK/DumpDebug/code/caffe_dump_data.py</strong>。</p> <p>caffe_dump_data.py脚本需要使用 <strong>python2</strong> 运行（Caffe仅支持python2），用于以字符串形式或者二进制形式dump出Caffe原模型每一层的数据。</p> <p>使用示例： <div class=highlight><pre><span></span><code>（1）caffe_dump_data.py中修改Caffe的运行路径，比如：

<span class=nv>caffe_root</span> <span class=o>=</span> <span class=s1>&#39;/home/<USER>/span>
sys.path.insert<span class=o>(</span><span class=m>0</span>,caffe_root+<span class=s1>&#39;/python&#39;</span><span class=o>)</span>

（2）运行脚本：

python2 caffe_dump_data.py <span class=se>\</span>
--model_file caffe_mobilenet_v2.prototxt <span class=se>\</span>
--weight_file caffe_mobilenet_v2.caffemodel <span class=se>\</span>
--i ./img.bmp <span class=se>\</span>
--dump_bin True <span class=se>\</span>
-n ./caffe_mobilenet_v2.py
</code></pre></div></p> <p>必选参数说明：</p> <p><code>--model_file</code>: Caffe原模型prototxt文件路径。</p> <p><code>--weight_file</code>: Caffe原模型caffemodel文件路径。</p> <p><code>-i</code>,<code>--image</code> : 图片文件或图片文件夹路径或 <strong>指定图片路径列表文件</strong> 。 </p> <p><code>--dump_bin</code> : 是否以二进制形式dump每一层结果。 <div class=highlight><pre><span></span><code>True:   Dump结果以二进制形式保存于当前运行目录下的
        ./dumpData/caffe_NHWC_outtensor_dump.bin（4维tensor排布格式为NHWC，与SigmaStar模型相同）  
False:  Dump结果以字符串形式保存于当前运行目录下的
        ./dumpData/caffe_NHWC_outtensor_dump.txt（4维tensor排布格式为NHWC，与SigmaStar模型相同）  
        ./dumpData/NHWC（每层输出单独生成文件，4维tensor排布格式为NHWC，与SigmaStar模型相同）  
        ./dumpData/NCHW（每层输出单独生成文件，4维tensor排布格式为NCHW，与原模型排布相同）  
</code></pre></div></p> <p><code>-n</code>, <code>--preprocess</code> : 前处理方法，请直接给定前处理python文件路径。需要确保前处理python文件中包含 <strong>get_image</strong> 函数，并且该函数带有 <strong>nchw</strong> 参数用来处理原模型4维输入的排布为NCHW（caffe_dump_data.py会直接调用get_image函数来生成模型input数据，并且强制nchw=True）。 前处理脚本函数示例： <div class=highlight><pre><span></span><code>def get_image<span class=o>(</span>img_path, <span class=nv>resizeH</span><span class=o>=</span><span class=m>224</span>, <span class=nv>resizeW</span><span class=o>=</span><span class=m>224</span>, <span class=nv>resizeC</span><span class=o>=</span><span class=m>3</span>, <span class=nv>norm</span><span class=o>=</span>True, <span class=nv>meanB</span><span class=o>=</span><span class=m>104</span>, <span class=nv>meanG</span><span class=o>=</span><span class=m>117</span>, <span class=nv>meanR</span><span class=o>=</span><span class=m>123</span>, <span class=nv>std</span><span class=o>=</span><span class=m>58</span>.82, <span class=nv>rgb</span><span class=o>=</span>False, <span class=nv>nchw</span><span class=o>=</span>False<span class=o>)</span>:
    ...
    ...
    <span class=k>if</span> nchw:
    <span class=c1># NCHW</span>
    <span class=nv>img_norm</span> <span class=o>=</span> np.transpose<span class=o>(</span>img_norm.reshape<span class=o>(</span>resizeH, resizeW, -1<span class=o>)</span>, <span class=nv>axes</span><span class=o>=(</span><span class=m>2</span>, <span class=m>0</span>, <span class=m>1</span><span class=o>))</span>

    <span class=k>return</span> np.expand_dims<span class=o>(</span>img_norm, <span class=m>0</span><span class=o>)</span>
</code></pre></div> <strong>Please Note:</strong> </p> <ul> <li>若模型为 <strong>多输入</strong> 时，<code>-n,--preprocess</code> 参数用法需要多个前处理方法，例如 <strong>-n preprocess1.py,preprocess2.py</strong> 或者 <strong>--preprocess preprocess1.py,preprocess2.py</strong></li> <li>若模型为 <strong>多输入</strong> 时，<code>-i/--image</code> 参数传入 <strong>指定图片路径列表文件</strong> 的形式。</li> </ul> <h4 id=6213-dump-onnx>******* Dump Onnx原模型<a class=headerlink href=#6213-dump-onnx title="Permanent link">&para;</a></h4> <p>工具的位置在 <strong>SGS_IPU_SDK/DumpDebug/code/onnx_dump_data.py</strong>。</p> <p>onnx_dump_data.py脚本需要使用 <strong>python3</strong> 运行（与IPU SDK环境相同），用于以字符串形式或者二进制形式dump出Onnx原模型每一层的数据。</p> <p>使用示例： <div class=highlight><pre><span></span><code>python3 onnx_dump_data.py <span class=se>\</span>
--model_file onnx_mobilenet_v2.onnx <span class=se>\</span>
--image ./img.bmp <span class=se>\</span>
--dump_bin True <span class=se>\</span>
-n onnx_mobilenet_v2.py
</code></pre></div></p> <p>必选参数说明：</p> <p><code>--model_file</code>: Onnx原模型文件路径。</p> <p><code>-i</code>,<code>--image</code> : 图片文件或图片文件夹路径或 <strong>指定图片路径列表文件</strong> 。 </p> <p><code>--dump_bin</code> : 是否以二进制形式dump每一层结果。 <div class=highlight><pre><span></span><code>True:   Dump结果以二进制形式保存于当前运行目录下的  
        ./dumpData/onnx_NHWC_outtensor_dump.bin（4维tensor排布格式为NHWC，与SigmaStar模型相同）  
False:  Dump结果以字符串形式保存于当前运行目录下的  
        ./dumpData/onnx_NHWC_outtensor_dump.txt（4维tensor排布格式为NHWC，与SigmaStar模型相同）  
        ./dumpData/NHWC（每层输出单独生成文件，4维tensor排布格式为NHWC，与SigmaStar模型相同）  
        ./dumpData/NCHW（每层输出单独生成文件，4维tensor排布格式为NCHW，与原模型排布相同）  
</code></pre></div></p> <p><code>-n</code>, <code>--preprocess</code> : 前处理方法，请直接给定前处理python文件路径。需要确保前处理python文件中包含 <strong>get_image</strong> 函数，并且该函数带有 <strong>nchw</strong> 参数用来处理原模型4维输入的排布为NCHW（onnx_dump_data.py会直接调用get_image函数来生成模型input数据，并且强制nchw=True）。 前处理脚本函数示例： <div class=highlight><pre><span></span><code>def get_image<span class=o>(</span>img_path, <span class=nv>resizeH</span><span class=o>=</span><span class=m>224</span>, <span class=nv>resizeW</span><span class=o>=</span><span class=m>224</span>, <span class=nv>resizeC</span><span class=o>=</span><span class=m>3</span>, <span class=nv>norm</span><span class=o>=</span>True, <span class=nv>meanB</span><span class=o>=</span><span class=m>104</span>, <span class=nv>meanG</span><span class=o>=</span><span class=m>117</span>, <span class=nv>meanR</span><span class=o>=</span><span class=m>123</span>, <span class=nv>std</span><span class=o>=</span><span class=m>58</span>.82, <span class=nv>rgb</span><span class=o>=</span>False, <span class=nv>nchw</span><span class=o>=</span>False<span class=o>)</span>:
    ...
    ...
    <span class=k>if</span> nchw:
    <span class=c1># NCHW</span>
    <span class=nv>img_norm</span> <span class=o>=</span> np.transpose<span class=o>(</span>img_norm.reshape<span class=o>(</span>resizeH, resizeW, -1<span class=o>)</span>, <span class=nv>axes</span><span class=o>=(</span><span class=m>2</span>, <span class=m>0</span>, <span class=m>1</span><span class=o>))</span>

    <span class=k>return</span> np.expand_dims<span class=o>(</span>img_norm, <span class=m>0</span><span class=o>)</span>
</code></pre></div> <strong>Please Note:</strong> </p> <ul> <li>若模型为 <strong>多输入</strong> 时，<code>-n,--preprocess</code> 参数用法需要多个前处理方法，例如 <strong>-n preprocess1.py,preprocess2.py</strong> 或者 <strong>--preprocess preprocess1.py,preprocess2.py</strong></li> <li>若模型为 <strong>多输入</strong> 时，<code>-i/--image</code> 参数传入 <strong>指定图片路径列表文件</strong> 的形式。</li> </ul> <hr> <h3 id=622-auto_dump_debugsh>6.2.2 使用auto_dump_debug.sh脚本分析数据<a class=headerlink href=#622-auto_dump_debugsh title="Permanent link">&para;</a></h3> <p>工具的位置在 <strong>SGS_IPU_SDK/DumpDebug/auto_dump_debug.sh</strong>。</p> <h3 id=6221-sigmastar>******* 对比分析SigmaStar浮点与定点模型<a class=headerlink href=#6221-sigmastar title="Permanent link">&para;</a></h3> <p>auto_dump_debug.sh脚本能够对比样本 bin（sample）和基准 bin（benchmark）的相同output tensor层的COS、MSE和RMSE，需使用[******* Dump SigmaStar浮点或定点模型]中Dump出浮点网络模型和定点网络模型的bin文件。 </p> <p>使用示例： <div class=highlight><pre><span></span><code>./auto_dump_debug.sh <span class=se>\</span>
/home/<USER>/SGS_IPU_SDK <span class=se>\</span>
/home/<USER>/sample.bin <span class=se>\</span>
/home/<USER>/benchmark.bin
</code></pre></div></p> <p>相关参数说明：</p> <p><code>Param1</code> : SGS_IPU_SDK 的路径，如果在当前位置，只需传目录夹名。</p> <p><code>Param2</code> : 需要对比的已经 dump出来的sample bin路径，此处应为定点网络模型Dump出的bin文件路径。 </p> <p><code>Param3</code> : 作为参考的已经 dump出来的 benchmark bin路径，此处应为浮点网络模型Dump出的bin文件路径。</p> <p>未开启disableDomainFuseOps选项时，完成分析后显示如下：（部分） <div class=highlight><pre><span></span><code>3.conv1_xx_xx_xx.xx.output0             OP_TYPE:CONV_2D     MSE:0.000046    COS:0.999898    RMSE:0.014310
4.pool1.xx.output0                      OP_TYPE:MAX_POOL_2D MSE:0.000057    COS:0.999931    RMSE:0.011018
7.res2a_branch1_xx_xx.xx.output0        OP_TYPE:CONV_2D     MSE:0.000076    COS:0.999886    RMSE:0.015652
11.res2a_branch2a_xx_xx_xx.xx.output0   OP_TYPE:CONV_2D     MSE:0.000027    COS:0.999652    RMSE:0.023612
16.res2a_xx.xx.output0                  OP_TYPE:RELU        MSE:0.000177    COS:0.999644    RMSE:0.025049
20.res2b_branch2a_xx_xx_xx.xx.output0   OP_TYPE:CONV_2D     MSE:0.000069    COS:0.999242    RMSE:0.039121
25.res2b_xx.xx.output0                  OP_TYPE:RELU        MSE:0.000430    COS:0.999452    RMSE:0.030551
28.res3a_branch1_xx_xx.xx.output0       OP_TYPE:CONV_2D     MSE:0.000165    COS:0.998975    RMSE:0.041644
32.res3a_branch2a_xx_xx_xx.xx.output0   OP_TYPE:CONV_2D     MSE:0.000139    COS:0.998842    RMSE:0.048416
37.res3a_xx.xx.output0                  OP_TYPE:RELU        MSE:0.000297    COS:0.999069    RMSE:0.041043
41.res3b_branch2a_xx_xx_xx.xx.output0   OP_TYPE:CONV_2D     MSE:0.000070    COS:0.998836    RMSE:0.051215
46.res3b_xx.xx.output0                  OP_TYPE:RELU        MSE:0.000366    COS:0.999035    RMSE:0.042863
49.res4a_branch1_xx_xx.xx.output0       OP_TYPE:CONV_2D     MSE:0.000026    COS:0.999282    RMSE:0.035575
53.res4a_branch2a_xx_xx_xx.xx.output0   OP_TYPE:CONV_2D     MSE:0.000108    COS:0.998950    RMSE:0.047213
58.res4a_xx.xx.output0                  OP_TYPE:RELU        MSE:0.000149    COS:0.999174    RMSE:0.040582
62.res4b_branch2a_xx_xx_xx.xx.output0   OP_TYPE:CONV_2D     MSE:0.000030    COS:0.999020    RMSE:0.046630
67.res4b_xx.xx.output0                  OP_TYPE:RELU        MSE:0.000168    COS:0.999073    RMSE:0.041229
70.res5a_branch1_xx_xx.xx.output0       OP_TYPE:CONV_2D     MSE:0.000305    COS:0.999276    RMSE:0.033715
74.res5a_branch2a_xx_xx_xx.xx.output0   OP_TYPE:CONV_2D     MSE:0.000032    COS:0.998723    RMSE:0.053469
79.res5a_xx.xx.output0                  OP_TYPE:RELU        MSE:0.000507    COS:0.998762    RMSE:0.050663
83.res5b_branch2a_xx_xx_xx.xx.output0   OP_TYPE:CONV_2D     MSE:0.000027    COS:0.998182    RMSE:0.064201
88.res5b_xx.xx.output0                  OP_TYPE:RELU        MSE:0.006022    COS:0.998665    RMSE:0.056931
89.pool5.xx.output0                     OP_TYPE:AVG_POOL_2D MSE:0.004321    COS:0.998488    RMSE:0.079694
90.fc1000_reshape#1_output#183.output0  OP_TYPE:ReshapeAliasMSE:0.004321    COS:0.998488    RMSE:0.079694
91.fc1000#185.xx.output0                OP_TYPE:CONV_2D     MSE:0.014197    COS:0.998901    RMSE:0.051447
92.fc1000.xx.output0                    OP_TYPE:ReshapeAliasMSE:0.014197    COS:0.998901    RMSE:0.051447
93.prob.xx.output0                      OP_TYPE:Fix2Float   MSE:0.000000    COS:1.000000    RMSE:0.000152
</code></pre></div></p> <p>开启disableDomainFuseOps选项时，完成分析后显示如下：（部分） <div class=highlight><pre><span></span><code>0.conv1.xx.output0                      OP_TYPE:CONV_2D     MSE:2.397930    COS:0.999887    RMSE:0.019312
1.conv1_xx.xx.output0                   OP_TYPE:MUL         MSE:0.000037    COS:0.999850    RMSE:0.020800
2.conv1_xx_xx.xx.output0                OP_TYPE:ADD         MSE:0.000037    COS:0.999936    RMSE:0.010667
3.conv1_xx_xx_xx.xx.output0             OP_TYPE:RELU        MSE:0.000052    COS:0.999885    RMSE:0.015123
4.pool1.xx.output0                      OP_TYPE:MAX_POOL_2D MSE:0.000063    COS:0.999924    RMSE:0.011603
5.res2a_branch1.xx.output0              OP_TYPE:CONV_2D     MSE:0.000096    COS:0.999952    RMSE:0.009368
6.res2a_branch1_xx.xx.output0           OP_TYPE:MUL         MSE:0.000080    COS:0.999956    RMSE:0.009144
7.res2a_branch1_xx_xx.xx.output0        OP_TYPE:ADD         MSE:0.000080    COS:0.999880    RMSE:0.016141
8.res2a_branch2a.xx.output0             OP_TYPE:CONV_2D     MSE:0.000349    COS:0.999951    RMSE:0.009727
9.res2a_branch2a_xx.xx.output0          OP_TYPE:MUL         MSE:0.000049    COS:0.999956    RMSE:0.009349
10.res2a_branch2a_xx_xx.xx.output0      OP_TYPE:ADD         MSE:0.000049    COS:0.999886    RMSE:0.016510
11.res2a_branch2a_xx_xx_xx.xx.output0   OP_TYPE:RELU        MSE:0.000028    COS:0.999641    RMSE:0.024211
12.res2a_branch2b.xx.output0            OP_TYPE:CONV_2D     MSE:0.000100    COS:0.999821    RMSE:0.018379
13.res2a_branch2b_xx.xx.output0         OP_TYPE:MUL         MSE:0.000118    COS:0.999832    RMSE:0.017982
14.res2a_branch2b_xx_xx.xx.output0      OP_TYPE:ADD         MSE:0.000118    COS:0.999670    RMSE:0.024984
15.res2a.xx.output0                     OP_TYPE:ADD         MSE:0.000241    COS:0.999771    RMSE:0.021801
16.res2a_xx.xx.output0                  OP_TYPE:RELU        MSE:0.000182    COS:0.999634    RMSE:0.025262
17.res2b_branch2a.xx.output0            OP_TYPE:CONV_2D     MSE:0.000525    COS:0.999709    RMSE:0.023127
18.res2b_branch2a_xx.xx.output0         OP_TYPE:MUL         MSE:0.000193    COS:0.999746    RMSE:0.022434
19.res2b_branch2a_xx_xx.xx.output0      OP_TYPE:ADD         MSE:0.000193    COS:0.999474    RMSE:0.032121
20.res2b_branch2a_xx_xx_xx.xx.output0   OP_TYPE:RELU        MSE:0.000071    COS:0.999219    RMSE:0.039747
21.res2b_branch2b.xx.output0            OP_TYPE:CONV_2D     MSE:0.000157    COS:0.999549    RMSE:0.030371
22.res2b_branch2b_xx.xx.output0         OP_TYPE:MUL         MSE:0.000272    COS:0.999587    RMSE:0.029537
23.res2b_branch2b_xx_xx.xx.output0      OP_TYPE:ADD         MSE:0.000272    COS:0.999151    RMSE:0.040304
24.res2b.xx.output0                     OP_TYPE:ADD         MSE:0.000534    COS:0.999425    RMSE:0.031402
25.res2b_xx.xx.output0                  OP_TYPE:RELU        MSE:0.000448    COS:0.999429    RMSE:0.031203
26.res3a_branch1.xx.output0             OP_TYPE:CONV_2D     MSE:0.000267    COS:0.999320    RMSE:0.034101
27.res3a_branch1_xx.xx.output0          OP_TYPE:MUL         MSE:0.000169    COS:0.999345    RMSE:0.034331
28.res3a_branch1_xx_xx.xx.output0       OP_TYPE:ADD         MSE:0.000169    COS:0.998949    RMSE:0.042434
29.res3a_branch2a.xx.output0            OP_TYPE:CONV_2D     MSE:0.001885    COS:0.999472    RMSE:0.030785
30.res3a_branch2a_xx.xx.output0         OP_TYPE:MUL         MSE:0.000427    COS:0.999486    RMSE:0.030586
31.res3a_branch2a_xx_xx.xx.output0      OP_TYPE:ADD         MSE:0.000427    COS:0.998909    RMSE:0.045188
32.res3a_branch2a_xx_xx_xx.xx.output0   OP_TYPE:RELU        MSE:0.000147    COS:0.998773    RMSE:0.049700
33.res3a_branch2b.xx.output0            OP_TYPE:CONV_2D     MSE:0.000331    COS:0.999425    RMSE:0.034038
34.res3a_branch2b_xx.xx.output0         OP_TYPE:MUL         MSE:0.000368    COS:0.999411    RMSE:0.034275
35.res3a_branch2b_xx_xx.xx.output0      OP_TYPE:ADD         MSE:0.000368    COS:0.998931    RMSE:0.044919
36.res3a.xx.output0                     OP_TYPE:ADD         MSE:0.000585    COS:0.999040    RMSE:0.042398
37.res3a_xx.xx.output0                  OP_TYPE:RELU        MSE:0.000301    COS:0.999058    RMSE:0.041479
38.res3b_branch2a.xx.output0            OP_TYPE:CONV_2D     MSE:0.000811    COS:0.999530    RMSE:0.029739
39.res3b_branch2a_xx.xx.output0         OP_TYPE:MUL         MSE:0.000288    COS:0.999580    RMSE:0.028879
40.res3b_branch2a_xx_xx.xx.output0      OP_TYPE:ADD         MSE:0.000288    COS:0.999383    RMSE:0.033874
41.res3b_branch2a_xx_xx_xx.xx.output0   OP_TYPE:RELU        MSE:0.000073    COS:0.998787    RMSE:0.052493
42.res3b_branch2b.xx.output0            OP_TYPE:CONV_2D     MSE:0.000136    COS:0.999469    RMSE:0.033442
43.res3b_branch2b_xx.xx.output0         OP_TYPE:MUL         MSE:0.000314    COS:0.999489    RMSE:0.033050
44.res3b_branch2b_xx_xx.xx.output0      OP_TYPE:ADD         MSE:0.000314    COS:0.999308    RMSE:0.036079
45.res3b.xx.output0                     OP_TYPE:ADD         MSE:0.000659    COS:0.998989    RMSE:0.042628
46.res3b_xx.xx.output0                  OP_TYPE:RELU        MSE:0.000375    COS:0.999011    RMSE:0.043575
47.res4a_branch1.xx.output0             OP_TYPE:CONV_2D     MSE:0.000098    COS:0.999264    RMSE:0.037842
48.res4a_branch1_xx.xx.output0          OP_TYPE:MUL         MSE:0.000027    COS:0.999298    RMSE:0.037615
49.res4a_branch1_xx_xx.xx.output0       OP_TYPE:ADD         MSE:0.000027    COS:0.999253    RMSE:0.036302
50.res4a_branch2a.xx.output0            OP_TYPE:CONV_2D     MSE:0.001011    COS:0.999543    RMSE:0.028507
51.res4a_branch2a_xx.xx.output0         OP_TYPE:MUL         MSE:0.000300    COS:0.999544    RMSE:0.028563
52.res4a_branch2a_xx_xx.xx.output0      OP_TYPE:ADD         MSE:0.000300    COS:0.999147    RMSE:0.040221
53.res4a_branch2a_xx_xx_xx.xx.output0   OP_TYPE:RELU        MSE:0.000107    COS:0.998961    RMSE:0.047238
54.res4a_branch2b.xx.output0            OP_TYPE:CONV_2D     MSE:0.000335    COS:0.999464    RMSE:0.032660
55.res4a_branch2b_xx.xx.output0         OP_TYPE:MUL         MSE:0.000255    COS:0.999453    RMSE:0.032788
56.res4a_branch2b_xx_xx.xx.output0      OP_TYPE:ADD         MSE:0.000255    COS:0.999234    RMSE:0.038888
57.res4a.xx.output0                     OP_TYPE:ADD         MSE:0.000312    COS:0.999298    RMSE:0.036873
58.res4a_xx.xx.output0                  OP_TYPE:RELU        MSE:0.000146    COS:0.999191    RMSE:0.040181
59.res4b_branch2a.xx.output0            OP_TYPE:CONV_2D     MSE:0.000369    COS:0.999748    RMSE:0.021992
60.res4b_branch2a_xx.xx.output0         OP_TYPE:MUL         MSE:0.000156    COS:0.999772    RMSE:0.021527
61.res4b_branch2a_xx_xx.xx.output0      OP_TYPE:ADD         MSE:0.000156    COS:0.999625    RMSE:0.026857
62.res4b_branch2a_xx_xx_xx.xx.output0   OP_TYPE:RELU        MSE:0.000028    COS:0.999073    RMSE:0.045494
63.res4b_branch2b.xx.output0            OP_TYPE:CONV_2D     MSE:0.000048    COS:0.999748    RMSE:0.022612
64.res4b_branch2b_xx.xx.output0         OP_TYPE:MUL         MSE:0.000191    COS:0.999751    RMSE:0.022354
65.res4b_branch2b_xx_xx.xx.output0      OP_TYPE:ADD         MSE:0.000191    COS:0.999678    RMSE:0.024581
66.res4b.xx.output0                     OP_TYPE:ADD         MSE:0.000337    COS:0.999448    RMSE:0.032108
67.res4b_xx.xx.output0                  OP_TYPE:RELU        MSE:0.000160    COS:0.999113    RMSE:0.040588
68.res5a_branch1.xx.output0             OP_TYPE:CONV_2D     MSE:0.000072    COS:0.999179    RMSE:0.038052
69.res5a_branch1_xx.xx.output0          OP_TYPE:MUL         MSE:0.000290    COS:0.999179    RMSE:0.038130
70.res5a_branch1_xx_xx.xx.output0       OP_TYPE:ADD         MSE:0.000290    COS:0.999311    RMSE:0.032880
71.res5a_branch2a.xx.output0            OP_TYPE:CONV_2D     MSE:0.000355    COS:0.999758    RMSE:0.020371
72.res5a_branch2a_xx.xx.output0         OP_TYPE:MUL         MSE:0.000133    COS:0.999750    RMSE:0.020561
73.res5a_branch2a_xx_xx.xx.output0      OP_TYPE:ADD         MSE:0.000133    COS:0.999613    RMSE:0.026392
74.res5a_branch2a_xx_xx_xx.xx.output0   OP_TYPE:RELU        MSE:0.000028    COS:0.998859    RMSE:0.051003
75.res5a_branch2b.xx.output0            OP_TYPE:CONV_2D     MSE:0.000063    COS:0.999512    RMSE:0.031809
76.res5a_branch2b_xx.xx.output0         OP_TYPE:MUL         MSE:0.001009    COS:0.999480    RMSE:0.032233
77.res5a_branch2b_xx_xx.xx.output0      OP_TYPE:ADD         MSE:0.001009    COS:0.999257    RMSE:0.039436
78.res5a.xx.output0                     OP_TYPE:ADD         MSE:0.001413    COS:0.999379    RMSE:0.034297
79.res5a_xx.xx.output0                  OP_TYPE:RELU        MSE:0.000457    COS:0.998876    RMSE:0.048274
80.res5b_branch2a.xx.output0            OP_TYPE:CONV_2D     MSE:0.001387    COS:0.999842    RMSE:0.015781
81.res5b_branch2a_xx.xx.output0         OP_TYPE:MUL         MSE:0.000239    COS:0.999839    RMSE:0.015798
82.res5b_branch2a_xx_xx.xx.output0      OP_TYPE:ADD         MSE:0.000239    COS:0.999639    RMSE:0.026762
83.res5b_branch2a_xx_xx_xx.xx.output0   OP_TYPE:RELU        MSE:0.000023    COS:0.998458    RMSE:0.058598
84.res5b_branch2b.xx.output0            OP_TYPE:CONV_2D     MSE:0.000041    COS:0.999061    RMSE:0.041137
85.res5b_branch2b_xx.xx.output0         OP_TYPE:MUL         MSE:0.007750    COS:0.999058    RMSE:0.041188
86.res5b_branch2b_xx_xx.xx.output0      OP_TYPE:ADD         MSE:0.007751    COS:0.998732    RMSE:0.052601
87.res5b.xx.output0                     OP_TYPE:ADD         MSE:0.008533    COS:0.998792    RMSE:0.052043
88.res5b_xx.xx.output0                  OP_TYPE:RELU        MSE:0.004860    COS:0.998924    RMSE:0.051718
89.pool5.xx.output0                     OP_TYPE:AVG_POOL_2D MSE:0.004061    COS:0.998601    RMSE:0.078395
90.fc1000_reshape#1_output#183.output0  OP_TYPE:ReshapeAliasMSE:0.004061    COS:0.998601    RMSE:0.078395
91.fc1000#185.xx.output0                OP_TYPE:CONV_2D     MSE:0.013363    COS:0.998935    RMSE:0.050327
92.fc1000.xx.output0                    OP_TYPE:ReshapeAliasMSE:0.013363    COS:0.998935    RMSE:0.050327
93.prob.xx.output0                      OP_TYPE:SOFTMAX     MSE:0.000000    COS:1.000000    RMSE:0.000152
</code></pre></div></p> <h3 id=6222-sigmastarcaffeonnx>6.2.2.2 对比分析SigmaStar（浮点/定点）模型与原模型（Caffe/Onnx）<a class=headerlink href=#6222-sigmastarcaffeonnx title="Permanent link">&para;</a></h3> <p>auto_dump_debug.sh脚本同样能够对比SigmaStar（浮点/定点）模型与原模型（Caffe/Onnx）相同output tensor层的COS、MSE和RMSE。打印格式与[******* 对比分析SigmaStar浮点与定点模型]相同。</p> <p>将[******* Dump SigmaStar浮点或定点模型]中Dump出的浮点网络模型或定点网络模型的bin文件作为样本 bin（sample），传入Param2，不得传入Param3。</p> <p>将[******* Dump Caffe原模型]或[******* Dump Onnx原模型]中Dump出的原模型bin文件作为基准 bin（benchmark），传入Param3，不得传入Param2。 </p> <p>与Caffe原模型对比： <div class=highlight><pre><span></span><code>./auto_dump_debug.sh <span class=se>\</span>
/home/<USER>/SGS_IPU_SDK <span class=se>\</span>
/home/<USER>/sigma_outtensor_dump.bin <span class=se>\</span>
/home/<USER>/caffe_NHWC_outtensor_dump.bin
</code></pre></div> 与Onnx原模型对比： <div class=highlight><pre><span></span><code>./auto_dump_debug.sh <span class=se>\</span>
/home/<USER>/SGS_IPU_SDK <span class=se>\</span>
/home/<USER>/sigma_outtensor_dump.bin <span class=se>\</span>
/home/<USER>/onnx_NHWC_outtensor_dump.bin
</code></pre></div></p> <p>相关参数说明：</p> <p><code>Param1</code> : SGS_IPU_SDK 的路径，如果在当前位置，只需传目录夹名。</p> <p><code>Param2</code> : 需要对比的已经 dump出来的sample bin路径，此处应为SigmaStar网络模型（浮点/定点）Dump出的bin文件路径。 </p> <p><code>Param3</code> : 作为参考的已经 dump出来的 benchmark bin路径，此处应为原模型（Caffe/Onnx）Dump出的bin文件路径。</p> <hr> <h2 id=63-histogrampyhistogram>6.3. 使用histogram.py绘制各层网络模型的Histogram图<a class=headerlink href=#63-histogrampyhistogram title="Permanent link">&para;</a></h2> <p>工具的位置在 <strong>SGS_IPU_SDK/DumpDebug/histogram.py</strong>。 该工具可对Dump出来的数据绘制各层的数据分布，使用该工具需Dump出的数据文件，以及使用calibrator 工具将浮点网络模型转换成定点网络模型时在运行目录下 <strong>log/tensor_min_max.txt</strong> 文件。 </p> <p>工具使用示例： <div class=highlight><pre><span></span><code>Python3 histogram.py sigma_outtensor_dump.bin tensor_min_max.txt
</code></pre></div></p> <p>运行中如下提示 <div class=highlight><pre><span></span><code>[===============================================&gt; ]97.61%
</code></pre></div></p> <p>绘制的Histogram图如下所示：</p> <p><img alt=Histogram.png src=mymedia/bda1edb5cc4a3628849707ed6922ed22.png> </p> <p>图中蓝色部分是该层网络数据的出现次数，左右两边的红色虚线是最小值和最大值。</p> <p><strong>Note</strong> </p> <ul> <li>工具在运行当前路径下，会创建Histograms文件夹，里面包含各网络层的数据Histogram图片。</li> <li>对不同Dump数据绘制网络模型的Histogram图时，应该重命名当前路径下的Histograms文件夹，或移动至其他路径，<em>histogram.py</em> 工具会在运行时删除当前路径下的Histograms文件夹。</li> </ul> <hr> <h2 id=64>6.4. 错误处理<a class=headerlink href=#64 title="Permanent link">&para;</a></h2> <hr> <p>当运行时发生SegmentFault错误时，程序中断运行，此时定位程序发生错误位置至关重要。请输入以下命令，协助提供相关信息。 </p> <p>修改生成core文件路径为当前目录，并设置core文件大小无上限： </p> <div class=highlight><pre><span></span><code>echo core &gt; /proc/sys/kernel/core_pattern 
ulimit -c unlimited 
</code></pre></div> <p>重新运行刚刚发生错误的转换网络命令。 </p> <p>生成core文件后，运行如下命令，会打印程序相关信息： </p> <div class=highlight><pre><span></span><code>~/SGS_IPU_SDK/DumpDebug/show_address.sh /path/to/SGS_IPU_SDK/bin/XXX /path/to/core   
</code></pre></div> <p>show_address.sh脚本需要两个参数，第一个参数为执行命令的bin的路径，第二个参数为core的路径。 </p> <p>屏幕会打印内存映射信息以及程序函数堆栈地址，请反馈此信息。 </p> <hr> <h2 id=65>6.5. 相关问题汇总<a class=headerlink href=#65 title="Permanent link">&para;</a></h2> <p>DumpDebug Tool提供了模型量化后精度下降问题的排查方法，可用于针对实际问题参考。 使用calibrator时，<code>--quant_level</code> 选择L2、L3、L4或L5时会根据统计信息自动配置卷积的量化方式，如果calibrator未能将下列情况的卷积修改成”INT16”卷积模式，请手动开启。 </p> <ol> <li>在将SigmaStar浮点网络模型转化为SigmaStar定点网络模型时，注意保存在运行目录下的 <strong>log/tensor_min_max.txt</strong> 文件，该文件记录了在转换过程中各层的最大和最小值。如果卷积输入层的最大值和最小值相差过大（一般认为差值大于30），需要在对应网络的input_config.ini文件中开启该层的”INT16”卷积模式，具体修改方法请参考[2.2. input config配置信息设置]配置信息。修改input_config.ini文件后需要重新从原始框架训练的模型转换。</li> <li>使用[6.2.2 使用auto_dump_debug.sh脚本分析数据]的auto_dump_debug.sh脚本对比数据后，如果RMSE值较大（一般认为大于0.5），可将该层前的卷积输入层开启 <em>INT16</em> 卷积模式。修改input_config.ini文件后需要重新从原始框架训练的模型转换。 </li> <li>使用[6.3. 使用histogram.py绘制各层网络模型的Histogram图]的histogram.py工具绘制Histogram图应使用SigmaStar浮点网络的Dump数据和对应的tensor_min_max.txt。如果图中的数据分布很集中，但是最大值和最小值范围较大（一般认为最大值和最小值的差值大于30），可以考虑对该层卷积的输入开启 <em>INT16</em> 卷积模式。修改input_config.ini文件后需要重新从原始框架训练的模型转换。</li> </ol> </article> </div> </div> </main> <footer class=md-footer> <nav class="md-footer__inner md-grid" aria-label=Footer> <a href=../IPU200M/Simulate.html class="md-footer__link md-footer__link--prev" rel=prev> <div class="md-footer__button md-icon"> <svg xmlns=http://www.w3.org/2000/svg viewbox="0 0 24 24"><path d="M20 11v2H8l5.5 5.5-1.42 1.42L4.16 12l7.92-7.92L13.5 5.5 8 11h12z"/></svg> </div> <div class=md-footer__title> <div class=md-ellipsis> <span class=md-footer__direction> Previous </span> 5. Simulator </div> </div> </a> <a href=SigmaStar_Post_Processing_Module.html class="md-footer__link md-footer__link--next" rel=next> <div class=md-footer__title> <div class=md-ellipsis> <span class=md-footer__direction> Next </span> 7. SigmaStar后处理模块 </div> </div> <div class="md-footer__button md-icon"> <svg xmlns=http://www.w3.org/2000/svg viewbox="0 0 24 24"><path d="M4 11v2h12l-5.5 5.5 1.42 1.42L19.84 12l-7.92-7.92L10.5 5.5 16 11H4z"/></svg> </div> </a> </nav> <div class="md-footer-meta md-typeset"> <div class="md-footer-meta__inner md-grid"> <div class=md-footer-copyright> <div class=md-footer-copyright__highlight> Copyright&copy; 2021 SigmaStar Technology. All rights reserved. Security Level: Confidential A. </div> Made with <a href=https://squidfunk.github.io/mkdocs-material/ target=_blank rel=noopener> Material for MkDocs </a> </div> </div> </div> </footer> </div> <div class=md-dialog data-md-component=dialog> <div class="md-dialog__inner md-typeset"></div> </div> <script id=__config type=application/json>{"base": "../../..", "features": [], "translations": {"clipboard.copy": "Copy to clipboard", "clipboard.copied": "Copied to clipboard", "search.config.lang": "en", "search.config.pipeline": "trimmer, stopWordFilter", "search.config.separator": "[\\s\\-]+", "search.placeholder": "Search", "search.result.placeholder": "Type to start searching", "search.result.none": "No matching documents", "search.result.one": "1 matching document", "search.result.other": "# matching documents", "search.result.more.one": "1 more on this page", "search.result.more.other": "# more on this page", "search.result.term.missing": "Missing"}, "search": "../../../assets/javascripts/workers/search.fb4a9340.min.js", "version": null}</script> <script src=../../../assets/javascripts/bundle.a1c7c35e.min.js></script> <script src=../../../search/search_index.js></script> <script src=../../../javascripts/extra.js></script> <script src="https://cdnjs.cloudflare.com/ajax/libs/mathjax/2.7.0/MathJax.js?config=TeX-MML-AM_CHTML"></script> <script src=https://cdnjs.cloudflare.com/ajax/libs/raphael/2.2.7/raphael.min.js></script> <script src=https://cdnjs.cloudflare.com/ajax/libs/underscore.js/1.8.3/underscore-min.js></script> <script src=https://cdnjs.cloudflare.com/ajax/libs/js-sequence-diagrams/1.0.6/sequence-diagram-min.js></script> <script src=https://cdnjs.cloudflare.com/ajax/libs/flowchart/1.6.5/flowchart.min.js></script> <script src=https://unpkg.com/freezeframe/dist/freezeframe.min.js></script> <script src=https://unpkg.com/mermaid@7.1.0/dist/mermaid.min.js></script> <script src=../../../javascripts/umlconvert.js></script> </body> </html>