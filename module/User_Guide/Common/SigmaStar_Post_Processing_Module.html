<!doctype html><html lang=en class=no-js> <head><meta charset=utf-8><meta name=viewport content="width=device-width,initial-scale=1"><meta name=description content="Personal Documentation System Template"><meta name=author content="<PERSON> Cho<PERSON>"><link rel=icon href=../../../images/favicon.ico><meta name=generator content="mkdocs-1.1.2, mkdocs-material-7.0.6"><title>7. SigmaStar后处理模块 - IPU SDK</title><link rel=stylesheet href=../../../assets/stylesheets/main.2c0c5eaf.min.css><link rel=stylesheet href=../../../assets/stylesheets/palette.7fa14f5b.min.css><meta name=theme-color content=#009485><link rel=preconnect href=https://fonts.gstatic.com crossorigin><link rel=stylesheet href="https://fonts.googleapis.com/css?family=Roboto:300,400,400i,700%7CRoboto+Mono&display=fallback"><style>:root{--md-text-font-family:"Roboto";--md-code-font-family:"Roboto Mono"}</style><link rel=stylesheet href=../../../stylesheets/extra.css></head> <body dir=ltr data-md-color-scheme data-md-color-primary=teal data-md-color-accent=teal> <input class=md-toggle data-md-toggle=drawer type=checkbox id=__drawer autocomplete=off> <input class=md-toggle data-md-toggle=search type=checkbox id=__search autocomplete=off> <label class=md-overlay for=__drawer></label> <div data-md-component=skip> <a href=#71 class=md-skip> Skip to content </a> </div> <div data-md-component=announce> </div> <header class=md-header data-md-component=header> <nav class="md-header__inner md-grid" aria-label=Header> <a href=../../.. title="IPU SDK" class="md-header__button md-logo" aria-label="IPU SDK" data-md-component=logo> <svg xmlns=http://www.w3.org/2000/svg viewbox="0 0 24 24"><path d="M10 20v-6h4v6h5v-8h3L12 3 2 12h3v8h5z"/></svg> </a> <label class="md-header__button md-icon" for=__drawer> <svg xmlns=http://www.w3.org/2000/svg viewbox="0 0 24 24"><path d="M3 6h18v2H3V6m0 5h18v2H3v-2m0 5h18v2H3v-2z"/></svg> </label> <div class=md-header__title data-md-component=header-title> <div class=md-header__ellipsis> <div class=md-header__topic> <span class=md-ellipsis> IPU SDK </span> </div> <div class=md-header__topic data-md-component=header-topic> <span class=md-ellipsis> 7. SigmaStar后处理模块 </span> </div> </div> </div> <div class=md-header__options> </div> <label class="md-header__button md-icon" for=__search> <svg xmlns=http://www.w3.org/2000/svg viewbox="0 0 24 24"><path d="M9.5 3A6.5 6.5 0 0 1 16 9.5c0 1.61-.59 3.09-1.56 4.23l.27.27h.79l5 5-1.5 1.5-5-5v-.79l-.27-.27A6.516 6.516 0 0 1 9.5 16 6.5 6.5 0 0 1 3 9.5 6.5 6.5 0 0 1 9.5 3m0 2C7 5 5 7 5 9.5S7 14 9.5 14 14 12 14 9.5 12 5 9.5 5z"/></svg> </label> <div class=md-search data-md-component=search role=dialog> <label class=md-search__overlay for=__search></label> <div class=md-search__inner role=search> <form class=md-search__form name=search> <input type=text class=md-search__input name=query aria-label=Search placeholder=Search autocapitalize=off autocorrect=off autocomplete=off spellcheck=false data-md-component=search-query data-md-state=active required> <label class="md-search__icon md-icon" for=__search> <svg xmlns=http://www.w3.org/2000/svg viewbox="0 0 24 24"><path d="M9.5 3A6.5 6.5 0 0 1 16 9.5c0 1.61-.59 3.09-1.56 4.23l.27.27h.79l5 5-1.5 1.5-5-5v-.79l-.27-.27A6.516 6.516 0 0 1 9.5 16 6.5 6.5 0 0 1 3 9.5 6.5 6.5 0 0 1 9.5 3m0 2C7 5 5 7 5 9.5S7 14 9.5 14 14 12 14 9.5 12 5 9.5 5z"/></svg> <svg xmlns=http://www.w3.org/2000/svg viewbox="0 0 24 24"><path d="M20 11v2H8l5.5 5.5-1.42 1.42L4.16 12l7.92-7.92L13.5 5.5 8 11h12z"/></svg> </label> <button type=reset class="md-search__icon md-icon" aria-label=Clear tabindex=-1> <svg xmlns=http://www.w3.org/2000/svg viewbox="0 0 24 24"><path d="M19 6.41L17.59 5 12 10.59 6.41 5 5 6.41 10.59 12 5 17.59 6.41 19 12 13.41 17.59 19 19 17.59 13.41 12 19 6.41z"/></svg> </button> </form> <div class=md-search__output> <div class=md-search__scrollwrap data-md-scrollfix> <div class=md-search-result data-md-component=search-result> <div class=md-search-result__meta> Initializing search </div> <ol class=md-search-result__list></ol> </div> </div> </div> </div> </div> </nav> </header> <div class=md-container data-md-component=container> <main class=md-main data-md-component=main> <div class="md-main__inner md-grid"> <div class="md-sidebar md-sidebar--primary" data-md-component=sidebar data-md-type=navigation> <div class=md-sidebar__scrollwrap> <div class=md-sidebar__inner> <nav class="md-nav md-nav--primary" aria-label=Navigation data-md-level=0> <label class=md-nav__title for=__drawer> <a href=../../.. title="IPU SDK" class="md-nav__button md-logo" aria-label="IPU SDK" data-md-component=logo> <svg xmlns=http://www.w3.org/2000/svg viewbox="0 0 24 24"><path d="M10 20v-6h4v6h5v-8h3L12 3 2 12h3v8h5z"/></svg> </a> IPU SDK </label> <ul class=md-nav__list data-md-scrollfix> <li class=md-nav__item> <a href=../../../index.html class=md-nav__link> 主页 </a> </li> <li class="md-nav__item md-nav__item--nested"> <input class="md-nav__toggle md-toggle" data-md-toggle=__nav_2 type=checkbox id=__nav_2> <label class=md-nav__link for=__nav_2> SDK介绍 <span class="md-nav__icon md-icon"></span> </label> <nav class=md-nav aria-label=SDK介绍 data-md-level=1> <label class=md-nav__title for=__nav_2> <span class="md-nav__icon md-icon"></span> SDK介绍 </label> <ul class=md-nav__list data-md-scrollfix> <li class=md-nav__item> <a href=../../Introduction/Introduction.html class=md-nav__link> SDK框架介绍 </a> </li> <li class=md-nav__item> <a href=../../Introduction/Docker.html class=md-nav__link> Docker环境 </a> </li> </ul> </nav> </li> <li class="md-nav__item md-nav__item--active md-nav__item--nested"> <input class="md-nav__toggle md-toggle" data-md-toggle=__nav_3 type=checkbox id=__nav_3 checked> <label class=md-nav__link for=__nav_3> 用户手册 <span class="md-nav__icon md-icon"></span> </label> <nav class=md-nav aria-label=用户手册 data-md-level=1> <label class=md-nav__title for=__nav_3> <span class="md-nav__icon md-icon"></span> 用户手册 </label> <ul class=md-nav__list data-md-scrollfix> <li class=md-nav__item> <a href=Environment_Construction.html class=md-nav__link> 1. 快速开始 </a> </li> <li class=md-nav__item> <a href=../IPU200M/Convert.html class=md-nav__link> 2. Convert Tool </a> </li> <li class=md-nav__item> <a href=Calibrate.html class=md-nav__link> 3. Calibrator </a> </li> <li class=md-nav__item> <a href=Compile.html class=md-nav__link> 4. Compiler </a> </li> <li class=md-nav__item> <a href=../IPU200M/Simulate.html class=md-nav__link> 5. Simulator </a> </li> <li class=md-nav__item> <a href=DumpDebug_Tool.html class=md-nav__link> 6. DumpDebug Tool </a> </li> <li class="md-nav__item md-nav__item--active"> <input class="md-nav__toggle md-toggle" data-md-toggle=toc type=checkbox id=__toc> <label class="md-nav__link md-nav__link--active" for=__toc> 7. SigmaStar后处理模块 <span class="md-nav__icon md-icon"></span> </label> <a href=SigmaStar_Post_Processing_Module.html class="md-nav__link md-nav__link--active"> 7. SigmaStar后处理模块 </a> <nav class="md-nav md-nav--secondary" aria-label="Table of contents"> <label class=md-nav__title for=__toc> <span class="md-nav__icon md-icon"></span> Table of contents </label> <ul class=md-nav__list data-md-component=toc data-md-scrollfix> <li class=md-nav__item> <a href=#71 class=md-nav__link> 7.1.模块介绍 </a> </li> <li class=md-nav__item> <a href=#72-bbox class=md-nav__link> 7.2. bbox坐标解码模块使用 </a> </li> <li class=md-nav__item> <a href=#73-sigmastar class=md-nav__link> 7.3. SigmaStar定制后处理算子 </a> <nav class=md-nav aria-label="7.3. SigmaStar定制后处理算子"> <ul class=md-nav__list> <li class=md-nav__item> <a href=#731-postprocess_unpack class=md-nav__link> 7.3.1. PostProcess_Unpack </a> </li> <li class=md-nav__item> <a href=#732-tflite_detection_nms class=md-nav__link> 7.3.2. TFLite_Detection_NMS </a> </li> </ul> </nav> </li> <li class=md-nav__item> <a href=#74-anchor class=md-nav__link> 7.4. 获取离线anchor数据 </a> </li> <li class=md-nav__item> <a href=#75 class=md-nav__link> 7.5. 举例使用 </a> <nav class=md-nav aria-label="7.5. 举例使用"> <ul class=md-nav__list> <li class=md-nav__item> <a href=#751 class=md-nav__link> 7.5.1. 参数配置 </a> </li> <li class=md-nav__item> <a href=#752-tensor class=md-nav__link> 7.5.2. 创建常量Tensor </a> </li> <li class=md-nav__item> <a href=#753 class=md-nav__link> 7.5.3. 创建一个算子 </a> </li> <li class=md-nav__item> <a href=#754 class=md-nav__link> 7.5.4. 创建客制化算子 </a> </li> </ul> </nav> </li> <li class=md-nav__item> <a href=#76-sgs_chalk class=md-nav__link> 7.6 构造模型工具sgs_chalk </a> <nav class=md-nav aria-label="7.6 构造模型工具sgs_chalk"> <ul class=md-nav__list> <li class=md-nav__item> <a href=#761-sgs_chalk class=md-nav__link> 7.6.1 sgs_chalk基本介绍 </a> </li> <li class=md-nav__item> <a href=#762-sgs_chalk class=md-nav__link> 7.6.2 sgs_chalk使用方法 </a> </li> <li class=md-nav__item> <a href=#763-sgs_chalkapi class=md-nav__link> 7.6.3 sgs_chalk模块API </a> </li> </ul> </nav> </li> </ul> </nav> </li> <li class=md-nav__item> <a href=Adding_A_New_Layer.html class=md-nav__link> 8. 如何添加新的Layer </a> </li> <li class=md-nav__item> <a href=../IPU200M/Special_Model_Conversion.html class=md-nav__link> 9. 特殊模型转换要点 </a> </li> <li class=md-nav__item> <a href=../IPU200M/DLA_SDK_Support.html class=md-nav__link> 10. DLA SDK 支持 </a> </li> <li class=md-nav__item> <a href=Running_Offline_Network_Model_On_Development_Board.html class=md-nav__link> 11. 在开发板上运行离线网络模型 </a> </li> <li class=md-nav__item> <a href=../IPU200M/Preprocess.py_and_Input_Config.ini_Support.html class=md-nav__link> 附录. 前处理和配置文件注意要点 </a> </li> </ul> </nav> </li> <li class="md-nav__item md-nav__item--nested"> <input class="md-nav__toggle md-toggle" data-md-toggle=__nav_4 type=checkbox id=__nav_4> <label class=md-nav__link for=__nav_4> FAQ <span class="md-nav__icon md-icon"></span> </label> <nav class=md-nav aria-label=FAQ data-md-level=1> <label class=md-nav__title for=__nav_4> <span class="md-nav__icon md-icon"></span> FAQ </label> <ul class=md-nav__list data-md-scrollfix> <li class=md-nav__item> <a href=../../FAQ/Common/Env_Setting.html class=md-nav__link> 环境设置问题 </a> </li> <li class=md-nav__item> <a href=../../FAQ/IPU200M/Model_Conversion.html class=md-nav__link> 模型转换问题 </a> </li> <li class=md-nav__item> <a href=../../FAQ/IPU200M/Development_Board.html class=md-nav__link> 板端使用问题 </a> </li> <li class=md-nav__item> <a href=../../FAQ/IPU200M/Other_Anomalies.html class=md-nav__link> 其他异常问题 </a> </li> </ul> </nav> </li> </ul> </nav> </div> </div> </div> <div class="md-sidebar md-sidebar--secondary" data-md-component=sidebar data-md-type=toc> <div class=md-sidebar__scrollwrap> <div class=md-sidebar__inner> <nav class="md-nav md-nav--secondary" aria-label="Table of contents"> <label class=md-nav__title for=__toc> <span class="md-nav__icon md-icon"></span> Table of contents </label> <ul class=md-nav__list data-md-component=toc data-md-scrollfix> <li class=md-nav__item> <a href=#71 class=md-nav__link> 7.1.模块介绍 </a> </li> <li class=md-nav__item> <a href=#72-bbox class=md-nav__link> 7.2. bbox坐标解码模块使用 </a> </li> <li class=md-nav__item> <a href=#73-sigmastar class=md-nav__link> 7.3. SigmaStar定制后处理算子 </a> <nav class=md-nav aria-label="7.3. SigmaStar定制后处理算子"> <ul class=md-nav__list> <li class=md-nav__item> <a href=#731-postprocess_unpack class=md-nav__link> 7.3.1. PostProcess_Unpack </a> </li> <li class=md-nav__item> <a href=#732-tflite_detection_nms class=md-nav__link> 7.3.2. TFLite_Detection_NMS </a> </li> </ul> </nav> </li> <li class=md-nav__item> <a href=#74-anchor class=md-nav__link> 7.4. 获取离线anchor数据 </a> </li> <li class=md-nav__item> <a href=#75 class=md-nav__link> 7.5. 举例使用 </a> <nav class=md-nav aria-label="7.5. 举例使用"> <ul class=md-nav__list> <li class=md-nav__item> <a href=#751 class=md-nav__link> 7.5.1. 参数配置 </a> </li> <li class=md-nav__item> <a href=#752-tensor class=md-nav__link> 7.5.2. 创建常量Tensor </a> </li> <li class=md-nav__item> <a href=#753 class=md-nav__link> 7.5.3. 创建一个算子 </a> </li> <li class=md-nav__item> <a href=#754 class=md-nav__link> 7.5.4. 创建客制化算子 </a> </li> </ul> </nav> </li> <li class=md-nav__item> <a href=#76-sgs_chalk class=md-nav__link> 7.6 构造模型工具sgs_chalk </a> <nav class=md-nav aria-label="7.6 构造模型工具sgs_chalk"> <ul class=md-nav__list> <li class=md-nav__item> <a href=#761-sgs_chalk class=md-nav__link> 7.6.1 sgs_chalk基本介绍 </a> </li> <li class=md-nav__item> <a href=#762-sgs_chalk class=md-nav__link> 7.6.2 sgs_chalk使用方法 </a> </li> <li class=md-nav__item> <a href=#763-sgs_chalkapi class=md-nav__link> 7.6.3 sgs_chalk模块API </a> </li> </ul> </nav> </li> </ul> </nav> </div> </div> </div> <div class=md-content data-md-component=content> <article class="md-content__inner md-typeset"> <h1>7. SigmaStar后处理模块</h1> <h2 id=71>7.1.模块介绍<a class=headerlink href=#71 title="Permanent link">&para;</a></h2> <p>SigmaStar后处理模块位置在<code>SGS_IPU_SDK/Scripts/postprocess</code>。 该模块主要以sgs_chalk生成一个检测网络后处理的通用生成方法。 使用该模块时，先根据后处理方法编写python后处理文件，生成独立的后处理模型文件，再在使用ConvertTool转换float模型的过程中加上--postprocess参数，传输编写好后处理文件的完整路径，最终生成的是将Backbone网络模型和后处理模型连接在一起的完整网络模型文件。另外可以通过--postprocess_input_config传入完整网络模型文件的ini文件，如果不传此参数，则自动生成的ini文件中默认quantizations和dequantizations都设置为True。 编写的python文件可参考<code>SGS_IPU_SDK/Scripts/postprocess/sgs_chalk_postprocess_method</code>文件夹下的示例。</p> <p>以下章节详细介绍封装好的后处理流程和自定义后处理流程的python文件编写，用以生成后处理网络模型文件。</p> <hr> <h2 id=72-bbox>7.2. bbox坐标解码模块使用<a class=headerlink href=#72-bbox title="Permanent link">&para;</a></h2> <p>为方便使用，Sigmastar分析了SSD、YOLOv1、YOLOv2、YOLOv3等网络的后处理，针对bbox坐标的提取已经抽象出一套解码流程，不同网络在结构上一样，不同点在部分的算子使用和anchor参数的传入。因此可以通过配置config字典变量，即可生成bbox坐标的后处理网络模型。bbox坐标解码网络如下图所示：</p> <p><img alt=box_process src=mymedia/********************************.jpg></p> <p>生成bbox坐标解码网络模型，可修改config字典变量，该变量参数意义如下表所示：</p> <table> <thead> <tr> <th>参数名</th> <th>参数类型</th> <th>描述信息</th> </tr> </thead> <tbody> <tr> <td>sh ape</td> <td>[ int]</td> <td>bbox tensor的形状，比如 [1,837]</td> </tr> <tr> <td>tx_func</td> <td>(tflite.BuiltinOperator, str)</td> <td>1. tflite.BuiltinOperator为tflite内置算子类型； 2. str为字符串<code>x_scale</code>或者<code>None</code>：当1中指定的算子为单口算子时str填<code>None</code>，如果为双口算子，这里填<code>x_scale</code>并在成员变量<code>x_scale</code>中指定其值。</td> </tr> <tr> <td>ty_func</td> <td>(tflite.BuiltinOperator, str)</td> <td>1. tflite.BuiltinOperator为tflite内置算子类型； 2. str为字符串<code>y_scale</code>或者<code>None</code>：当1中指定的算子为单口算子时str填<code>None</code>，如果为双口算子，这里填<code>y_scale</code>并在成员变量<code>y_scale</code>中指定其值。</td> </tr> <tr> <td>tw_func</td> <td>(tflite.BuiltinOperator, str)</td> <td>1. tflite.BuiltinOperator为tflite内置算子类型； 2. str为字符串<code>w_scale</code>或者<code>None</code>：当1中指定的算子为单口算子时str填<code>None</code>，如果为双口算子，这里填w_scale并在成员变量w_scale中指定其值。</td> </tr> <tr> <td>th_func</td> <td>(tflite.BuiltinOperator, str)</td> <td>1. tflite.BuiltinOperator为tflite内置算子类型； 2. str为字符串<code>h_scale</code>或者<code>None</code>：当1中指定的算子为单口算子时str填<code>None</code>，如果为双口算子，这里填<code>h_scale</code>并在成员变量<code>h_scale</code>中指定其值。</td> </tr> <tr> <td>x_scale</td> <td>float</td> <td>tx_func[1]为<code>x_scale</code>时指定的值</td> </tr> <tr> <td>y_scale</td> <td>float</td> <td>tx_func[1]为<code>y_scale</code>时指定的值</td> </tr> <tr> <td>w_scale</td> <td>float</td> <td>tx_func[1]为<code>w_scale</code>时指定的值</td> </tr> <tr> <td>h_scale</td> <td>float</td> <td>tx_func[1]为<code>h_scale</code>时指定的值</td> </tr> <tr> <td>anchor_selector</td> <td>str</td> <td><code>constant</code>或者为<code>None</code> 指定pw和ph是<code>constant</code>还是有pw_func和ph_func生成</td> </tr> <tr> <td>pw</td> <td>[ float ]</td> <td>如果anchor_selector为<code>constant</code>时pw指定为一个float列表</td> </tr> <tr> <td>ph</td> <td>[ float ]</td> <td>如果anchor_selector为<code>constant</code>时ph指定为一个float列表</td> </tr> <tr> <td>ppw</td> <td>[ float ]</td> <td>如果anchor_selector为<code>constant</code>时ppw指定为一个float列表</td> </tr> <tr> <td>pph</td> <td>[ float ]</td> <td>如果anchor_selector为<code>constant</code>时pph指定为一个float列表</td> </tr> <tr> <td>px</td> <td>[ float ]</td> <td>px指定为一个float列表</td> </tr> <tr> <td>py</td> <td>[ float ]</td> <td>py指定为一个float列表</td> </tr> <tr> <td>sx</td> <td>[ float ]</td> <td>sx指定为一个float列表</td> </tr> <tr> <td>sy</td> <td>[ float ]</td> <td>sy指定为一个float列表</td> </tr> <tr> <td>sw</td> <td>[ float ]</td> <td>sw指定为一个float列表</td> </tr> <tr> <td>sh</td> <td>[ float ]</td> <td>sh指定为一个float列表</td> </tr> </tbody> </table> <hr> <h2 id=73-sigmastar>7.3. SigmaStar定制后处理算子<a class=headerlink href=#73-sigmastar title="Permanent link">&para;</a></h2> <p>sgs_chalk提供了后处理算子的API，介绍如下：</p> <hr> <h3 id=731-postprocess_unpack>7.3.1. PostProcess_Unpack<a class=headerlink href=#731-postprocess_unpack title="Permanent link">&para;</a></h3> <p>PostProcess_Unpack算子目的是将Backbone网络的输出分离，支持最大分离出7个分支。 使用方法如下：</p> <p><div class=highlight><pre><span></span><code><span class=n>unpack_out_tensors1</span> <span class=o>=</span> <span class=p>[]</span>
    <span class=k>for</span> <span class=n>i</span> <span class=ow>in</span> <span class=nb>range</span><span class=p>(</span><span class=mi>7</span><span class=p>):</span>
        <span class=n>unpack_out_tensors1</span><span class=o>.</span><span class=n>append</span><span class=p>(</span><span class=s2>&quot;SGS_unpack&quot;</span><span class=o>+</span><span class=nb>str</span><span class=p>(</span><span class=n>i</span><span class=p>))</span>
<span class=n>output_list</span> <span class=o>=</span> <span class=n>sgs_chalk</span><span class=o>.</span><span class=n>PostProcess_Unpack</span><span class=p>(</span><span class=nb>input</span><span class=p>,</span><span class=n>mode</span><span class=o>=</span><span class=s1>&#39;YOLO&#39;</span><span class=p>,</span><span class=n>num_classes</span><span class=o>=</span><span class=mi>20</span><span class=p>,</span><span class=n>name</span><span class=o>=</span><span class=n>unpack_out_tensors1</span><span class=p>)</span>
</code></pre></div> 根据网络的不同，修改每行的第二个参数。如果该分支不需要，则对应的offset和lengh填0。</p> <p><code>input：</code>输入tensor</p> <p><code>mode：</code>输入字符串，只支持<code>YOLO</code>或者<code>SSD</code></p> <p><code>num_classes：</code>需要检测的类别数</p> <p><code>name：</code>输出tensor名字的list</p> <p>结合bbox坐标解码模块，PostProcess_Unpack有如下后处理网络示例：</p> <hr> <p>1.SSD模式下：分离bbox坐标</p> <p><img alt="Separating bbox coordinates" src=mymedia/********************************.png></p> <hr> <p>2.YOLO模式下：分离bbox坐标、confidence、scores、max_score</p> <p><img alt="Separating bbox coordinates, confidence, scores, and max_score" src=mymedia/911245f0f2010b64474ed87cd99a9917.png></p> <hr> <h3 id=732-tflite_detection_nms>7.3.2. TFLite_Detection_NMS<a class=headerlink href=#732-tflite_detection_nms title="Permanent link">&para;</a></h3> <p>TFLite_Detection_NMS算子将NMS操作组合成为一个算子，与PostProcess_Unpack算子配合，最大支持7个输入，输出为4个或5个。 使用方法如下：</p> <p><div class=highlight><pre><span></span><code><span class=n>out1</span> <span class=o>=</span> <span class=n>sgs_chalk</span><span class=o>.</span><span class=n>TFLite_Detection_NMS</span><span class=p>(</span><span class=n>x1</span><span class=p>,</span><span class=n>y1</span><span class=p>,</span><span class=n>x2</span><span class=p>,</span><span class=n>y2</span><span class=p>,</span><span class=n>confidence</span><span class=p>,</span><span class=n>score</span><span class=p>,</span><span class=n>max_score</span><span class=o>=</span><span class=kc>None</span><span class=p>,</span>
                                          <span class=n>mode</span><span class=o>=</span><span class=s1>&#39;YOLO&#39;</span><span class=p>,</span><span class=n>max_detections</span><span class=o>=</span><span class=mi>100</span><span class=p>,</span><span class=n>nms_score_threshold</span><span class=o>=</span><span class=mf>0.4</span><span class=p>,</span>
                                          <span class=n>nms_iou_threshold</span><span class=o>=</span><span class=mf>0.45</span><span class=p>,</span><span class=n>num_classes</span><span class=o>=</span><span class=mi>20</span><span class=p>,</span><span class=n>is_need_index</span><span class=o>=</span><span class=kc>False</span><span class=p>)</span>
</code></pre></div> 根据网络的不同，修改每行的第二个参数。如果该参数不需要，则对应参数填-1。</p> <p><code>x1：</code>对应PostProcess_Unpack算子分离出的x1 tensor。</p> <p><code>y1：</code>对应PostProcess_Unpack算子分离出的y1 tensor。</p> <p><code>x2：</code>对应PostProcess_Unpack算子分离出的x2 tensor。</p> <p><code>y2：</code>对应PostProcess_Unpack算子分离出的y2 tensor。</p> <p><code>confidence：</code>对应PostProcess_Unpack算子分离出的 bbox 置信度。</p> <p><code>score：</code>对应PostProcess_Unpack算子分离出的 bbox 得分。</p> <p><code>max_score：</code>对应PostProcess_Unpack算子分离出的 score最大值，只有<code>YOLO</code>模式下需要传入。</p> <p><code>mode：</code>输入字符串，只支持<code>YOLO</code>或者<code>SSD</code>。</p> <p><code>max_detections：</code>最大检测数。</p> <p><code>nms_score_threshold：</code>nms score阈值。</p> <p><code>nms_iou_threshold：</code>nms iou阈值。</p> <p><code>num_classes：</code>检测类别数。</p> <p><code>is_need_index：</code>是否输出index，默认为false。</p> <p><strong>Please Note:</strong></p> <ul> <li>TFLite_Detection_NMS算子SSD模式最大支持24576个bbox输入。</li> <li>TFLite_Detection_NMS算子YOLO模式先按照confidence取满足<code>nms_score_threshold</code>的前4096个框，再做IOU筛选。</li> <li>TFLite_Detection_NMS算子做多输出400个检测结果。</li> </ul> <hr> <h2 id=74-anchor>7.4. 获取离线anchor数据<a class=headerlink href=#74-anchor title="Permanent link">&para;</a></h2> <p>Caffe网络中如果PriorBox节点中数据是离线生成的，可以通过以下方法获取。 具体网络请参考SGS_Models/caffe/caffe_ssd_mobilenet_v1。 转换带PriorBox节点的backbone网络，使用Netron打开网络prototxt文件，如下图所示。</p> <p><img alt="Offline Anchor" src=mymedia/fabbd5e6de60eb398c4a2f0ccb4f4e69.png></p> <p>修改对应的input_config.ini文件和ConvertTool转换命令，有3个输出，生成backbone网络部分。 转换完成后，所有PriorBox节点生成为一个节点。使用Netron打开生成的backbone网络，先点击mbox_priorbox节点，再点击红框中的保存按钮，即可将anchor的数据保存为.npy文件。 在配置bbox坐标解码模块时，使用numpy.load读取.npy文件，配置好对应的变量。如果已有anchor数据，可以不用此方法，直接使用anchor数据。</p> <p><img alt="npy anchor data" src=mymedia/5227bc91472619d86d2a6b275cba8899.png></p> <p>后处理python文件在编写时，增加模型配置的输入为3个输入，增加mbox_priorbox的shape，具体代码详见</p> <p><code>SGS_IPU_SDK/Scripts/postprocess/sgs_chalk_postprocess_method/caffe_ssd_mobilenet_v1_postprocess.py</code></p> <div class=highlight><pre><span></span><code><span class=n>model_config</span> <span class=o>=</span> <span class=p>{</span><span class=s2>&quot;name&quot;</span><span class=p>:</span><span class=s2>&quot;caffe_ssd_mobilenet_v1&quot;</span><span class=p>,</span>
                <span class=s2>&quot;input&quot;</span> <span class=p>:</span> <span class=p>[</span><span class=s2>&quot;mbox_loc&quot;</span><span class=p>,</span><span class=s2>&quot;mbox_conf_softmax&quot;</span><span class=p>,</span><span class=s2>&quot;mbox_priorbox&quot;</span><span class=p>],</span>
                <span class=s2>&quot;input_shape&quot;</span> <span class=p>:</span> <span class=p>[[</span><span class=mi>1</span><span class=p>,</span><span class=mi>1917</span><span class=p>,</span><span class=mi>4</span><span class=p>],[</span><span class=mi>1</span><span class=p>,</span><span class=mi>1917</span><span class=p>,</span><span class=mi>21</span><span class=p>],[</span><span class=mi>1917</span><span class=p>,</span><span class=mi>4</span><span class=p>]],</span>
                <span class=s2>&quot;shape&quot;</span> <span class=p>:</span> <span class=p>[</span><span class=mi>1</span><span class=p>,</span><span class=mi>1917</span><span class=p>],</span>
                <span class=s2>&quot;out_shapes&quot;</span> <span class=p>:</span> <span class=p>[[</span><span class=mi>1</span><span class=p>,</span><span class=mi>10</span><span class=p>,</span><span class=mi>4</span><span class=p>],[</span><span class=mi>1</span><span class=p>,</span><span class=mi>10</span><span class=p>],[</span><span class=mi>1</span><span class=p>,</span><span class=mi>10</span><span class=p>],[</span><span class=mi>1</span><span class=p>]]}</span>
</code></pre></div> <p>生成后处理模型后，使用concat_net工具连接会自动清除mbox_priorbox节点。</p> <hr> <h2 id=75>7.5. 举例使用<a class=headerlink href=#75 title="Permanent link">&para;</a></h2> <p>以下示例以caffe_yolo_v2模型的后处理为例，具体代码详见</p> <p><code>SGS_IPU_SDK/Scripts/postprocess/sgs_chalk_postprocess_method/caffe_yolo_v2_postprocess.py</code></p> <h3 id=751>7.5.1. 参数配置<a class=headerlink href=#751 title="Permanent link">&para;</a></h3> <p>首先配置get_postprocess()函数中的model_config字典变量： <div class=highlight><pre><span></span><code><span class=n>model_config</span> <span class=o>=</span> <span class=p>{</span><span class=s2>&quot;name&quot;</span><span class=p>:</span><span class=s2>&quot;caffe_yolo_v2&quot;</span><span class=p>,</span>
          <span class=s2>&quot;input&quot;</span> <span class=p>:</span> <span class=p>[</span><span class=s1>&#39;conv23&#39;</span><span class=p>],</span>
          <span class=s2>&quot;input_shape&quot;</span> <span class=p>:</span> <span class=p>[</span><span class=mi>1</span><span class=p>,</span><span class=mi>13</span><span class=p>,</span><span class=mi>13</span><span class=p>,</span><span class=mi>125</span><span class=p>],</span>
          <span class=s2>&quot;shape&quot;</span> <span class=p>:</span> <span class=p>[</span><span class=mi>1</span><span class=p>,</span><span class=mi>845</span><span class=p>],</span>
          <span class=s2>&quot;out_shapes&quot;</span> <span class=p>:</span> <span class=p>[[</span><span class=mi>1</span><span class=p>,</span><span class=mi>100</span><span class=p>,</span><span class=mi>4</span><span class=p>],[</span><span class=mi>1</span><span class=p>,</span><span class=mi>100</span><span class=p>],[</span><span class=mi>1</span><span class=p>,</span><span class=mi>100</span><span class=p>],[</span><span class=mi>1</span><span class=p>]]}</span>
</code></pre></div> 其次配置config字典变量用于boxdecoder，根据bbox坐标解码的实际计算方法配置config的各个参数。</p> <p>配置参数： 需要用户配置的参数有box_num,side_x,side_y,num_classes,biases <div class=highlight><pre><span></span><code><span class=n>box_num</span> <span class=o>=</span> <span class=mi>5</span>
<span class=n>side_x</span> <span class=o>=</span> <span class=p>[</span><span class=mi>13</span><span class=p>]</span>
<span class=n>side_y</span> <span class=o>=</span> <span class=p>[</span><span class=mi>13</span><span class=p>]</span>
<span class=n>num_classes</span> <span class=o>=</span> <span class=mi>20</span>
<span class=n>num_anchors</span> <span class=o>=</span> <span class=mi>0</span>
<span class=k>for</span> <span class=n>i</span> <span class=ow>in</span> <span class=nb>range</span><span class=p>(</span><span class=nb>len</span><span class=p>(</span><span class=n>side_x</span><span class=p>)):</span>
    <span class=n>num_anchors</span> <span class=o>+=</span> <span class=n>box_num</span> <span class=o>*</span> <span class=n>side_x</span><span class=p>[</span><span class=n>i</span><span class=p>]</span> <span class=o>*</span> <span class=n>side_y</span><span class=p>[</span><span class=n>i</span><span class=p>]</span>
<span class=n>ppw</span> <span class=o>=</span> <span class=n>np</span><span class=o>.</span><span class=n>ones</span><span class=p>(</span><span class=n>num_anchors</span><span class=p>)</span>
<span class=n>pph</span> <span class=o>=</span> <span class=n>np</span><span class=o>.</span><span class=n>ones</span><span class=p>(</span><span class=n>num_anchors</span><span class=p>)</span>
<span class=k>for</span> <span class=n>k</span> <span class=ow>in</span> <span class=nb>range</span><span class=p>(</span><span class=nb>len</span><span class=p>(</span><span class=n>side_x</span><span class=p>)):</span>
    <span class=k>for</span> <span class=n>i</span> <span class=ow>in</span> <span class=nb>range</span><span class=p>(</span><span class=n>side_y</span><span class=p>[</span><span class=n>k</span><span class=p>]</span><span class=o>*</span><span class=n>side_x</span><span class=p>[</span><span class=n>k</span><span class=p>]):</span>
        <span class=n>tempX</span> <span class=o>=</span> <span class=n>i</span><span class=o>%</span><span class=n>side_y</span><span class=p>[</span><span class=n>k</span><span class=p>]</span>
        <span class=n>tempY</span> <span class=o>=</span> <span class=n>math</span><span class=o>.</span><span class=n>floor</span><span class=p>(</span><span class=n>i</span><span class=o>/</span><span class=n>side_y</span><span class=p>[</span><span class=n>k</span><span class=p>])</span>
        <span class=k>for</span> <span class=n>n</span> <span class=ow>in</span> <span class=nb>range</span><span class=p>(</span><span class=n>box_num</span><span class=p>):</span>
            <span class=n>px</span><span class=o>.</span><span class=n>append</span><span class=p>(</span><span class=n>tempX</span><span class=p>)</span>
            <span class=n>py</span><span class=o>.</span><span class=n>append</span><span class=p>(</span><span class=n>tempY</span><span class=p>)</span>
<span class=n>pw</span> <span class=o>=</span> <span class=n>np</span><span class=o>.</span><span class=n>ones</span><span class=p>(</span><span class=n>num_anchors</span><span class=p>)</span>
<span class=n>ph</span> <span class=o>=</span> <span class=n>np</span><span class=o>.</span><span class=n>ones</span><span class=p>(</span><span class=n>num_anchors</span><span class=p>)</span>

<span class=n>sx</span> <span class=o>=</span> <span class=n>np</span><span class=o>.</span><span class=n>ones</span><span class=p>(</span><span class=n>num_anchors</span><span class=p>)</span><span class=o>*</span><span class=p>(</span><span class=mf>1.0</span><span class=o>/</span><span class=n>side_y</span><span class=p>[</span><span class=mi>0</span><span class=p>])</span>
<span class=n>sy</span> <span class=o>=</span> <span class=n>np</span><span class=o>.</span><span class=n>ones</span><span class=p>(</span><span class=n>num_anchors</span><span class=p>)</span><span class=o>*</span><span class=p>(</span><span class=mf>1.0</span><span class=o>/</span><span class=n>side_x</span><span class=p>[</span><span class=mi>0</span><span class=p>])</span>

<span class=n>biases</span><span class=o>=</span> <span class=p>[[</span><span class=mf>1.3221</span><span class=p>,</span><span class=mf>1.73145</span><span class=p>],[</span><span class=mf>3.19275</span><span class=p>,</span><span class=mf>4.00944</span><span class=p>],[</span><span class=mf>5.05587</span><span class=p>,</span><span class=mf>8.09892</span><span class=p>],[</span><span class=mf>9.47112</span><span class=p>,</span><span class=mf>4.84053</span><span class=p>],[</span><span class=mf>11.2364</span><span class=p>,</span><span class=mf>10.0071</span><span class=p>]]</span>
<span class=n>sw</span> <span class=o>=</span> <span class=p>[</span><span class=n>x</span><span class=p>[</span><span class=mi>0</span><span class=p>]</span><span class=o>/</span><span class=p>(</span><span class=mi>2</span><span class=o>*</span><span class=n>side_x</span><span class=p>[</span><span class=mi>0</span><span class=p>])</span> <span class=k>for</span> <span class=n>x</span> <span class=ow>in</span> <span class=n>biases</span> <span class=p>]</span><span class=o>*</span><span class=p>(</span><span class=n>side_x</span><span class=p>[</span><span class=mi>0</span><span class=p>]</span><span class=o>*</span><span class=n>side_y</span><span class=p>[</span><span class=mi>0</span><span class=p>])</span>
<span class=n>sh</span> <span class=o>=</span> <span class=p>[</span><span class=n>x</span><span class=p>[</span><span class=mi>1</span><span class=p>]</span><span class=o>/</span><span class=p>(</span><span class=mi>2</span><span class=o>*</span><span class=n>side_y</span><span class=p>[</span><span class=mi>0</span><span class=p>])</span> <span class=k>for</span> <span class=n>x</span> <span class=ow>in</span> <span class=n>biases</span> <span class=p>]</span><span class=o>*</span><span class=p>(</span><span class=n>side_x</span><span class=p>[</span><span class=mi>0</span><span class=p>]</span><span class=o>*</span><span class=n>side_y</span><span class=p>[</span><span class=mi>0</span><span class=p>])</span>
</code></pre></div></p> <p>配置config字典变量：</p> <div class=highlight><pre><span></span><code><span class=n>config</span> <span class=o>=</span> <span class=p>{</span><span class=s2>&quot;shape&quot;</span> <span class=p>:</span> <span class=p>[</span><span class=mi>1</span><span class=p>,</span><span class=n>num_anchors</span><span class=p>],</span>
          <span class=s2>&quot;tx_func&quot;</span> <span class=p>:</span> <span class=p>(</span><span class=n>tflite</span><span class=o>.</span><span class=n>BuiltinOperator</span><span class=o>.</span><span class=n>BuiltinOperator</span><span class=p>()</span><span class=o>.</span><span class=n>LOGISTIC</span><span class=p>,</span><span class=kc>None</span><span class=p>),</span><span class=c1>#None or &#39;x_scale&#39;</span>
          <span class=s2>&quot;ty_func&quot;</span> <span class=p>:</span> <span class=p>(</span><span class=n>tflite</span><span class=o>.</span><span class=n>BuiltinOperator</span><span class=o>.</span><span class=n>BuiltinOperator</span><span class=p>()</span><span class=o>.</span><span class=n>LOGISTIC</span><span class=p>,</span><span class=kc>None</span><span class=p>),</span><span class=c1>#None or &#39;y_scale&#39;</span>
          <span class=s2>&quot;tw_func&quot;</span> <span class=p>:</span> <span class=p>(</span><span class=n>tflite</span><span class=o>.</span><span class=n>BuiltinOperator</span><span class=o>.</span><span class=n>BuiltinOperator</span><span class=p>()</span><span class=o>.</span><span class=n>RESHAPE</span><span class=p>,</span><span class=kc>None</span><span class=p>),</span><span class=c1>#None or &#39;w_scale&#39;</span>
          <span class=s2>&quot;th_func&quot;</span> <span class=p>:</span> <span class=p>(</span><span class=n>tflite</span><span class=o>.</span><span class=n>BuiltinOperator</span><span class=o>.</span><span class=n>BuiltinOperator</span><span class=p>()</span><span class=o>.</span><span class=n>RESHAPE</span><span class=p>,</span><span class=kc>None</span><span class=p>),</span><span class=c1>#None or &#39;h_scale&#39;</span>
          <span class=s2>&quot;x_scale&quot;</span> <span class=p>:</span> <span class=mf>0.1</span><span class=p>,</span>
          <span class=s2>&quot;y_scale&quot;</span> <span class=p>:</span> <span class=mf>0.1</span><span class=p>,</span>
          <span class=s2>&quot;w_scale&quot;</span> <span class=p>:</span> <span class=mi>1</span><span class=p>,</span>
          <span class=s2>&quot;h_scale&quot;</span> <span class=p>:</span> <span class=mi>1</span><span class=p>,</span>
          <span class=s2>&quot;anchor_selector&quot;</span> <span class=p>:</span> <span class=s2>&quot;constant&quot;</span><span class=p>,</span>
          <span class=s2>&quot;pw&quot;</span> <span class=p>:</span> <span class=n>pw</span><span class=p>,</span>
          <span class=s2>&quot;ph&quot;</span> <span class=p>:</span> <span class=n>ph</span><span class=p>,</span>
          <span class=s2>&quot;pw_func&quot;</span> <span class=p>:</span> <span class=p>(</span><span class=kc>None</span><span class=p>,</span><span class=kc>None</span><span class=p>),</span>
          <span class=s2>&quot;ph_func&quot;</span> <span class=p>:</span> <span class=p>(</span><span class=kc>None</span><span class=p>,</span><span class=kc>None</span><span class=p>),</span>
          <span class=s2>&quot;ppw&quot;</span> <span class=p>:</span> <span class=n>ppw</span><span class=p>,</span>
          <span class=s2>&quot;px&quot;</span> <span class=p>:</span> <span class=n>px</span><span class=p>,</span>
          <span class=s2>&quot;pph&quot;</span> <span class=p>:</span> <span class=n>pph</span><span class=p>,</span>
          <span class=s2>&quot;py&quot;</span> <span class=p>:</span> <span class=n>py</span><span class=p>,</span>
          <span class=s2>&quot;sx&quot;</span> <span class=p>:</span> <span class=n>sx</span><span class=p>,</span>
          <span class=s2>&quot;sy&quot;</span> <span class=p>:</span> <span class=n>sy</span><span class=p>,</span>
          <span class=s2>&quot;sw&quot;</span> <span class=p>:</span> <span class=n>sw</span><span class=p>,</span>
          <span class=s2>&quot;sh&quot;</span> <span class=p>:</span> <span class=n>sh</span>
          <span class=p>}</span>
</code></pre></div> <hr> <h3 id=752-tensor>7.5.2. 创建常量Tensor<a class=headerlink href=#752-tensor title="Permanent link">&para;</a></h3> <p><div class=highlight><pre><span></span><code><span class=n>constant1</span> <span class=o>=</span> <span class=n>sgs_chalk</span><span class=o>.</span><span class=n>Tensor</span><span class=p>(</span><span class=n>data</span><span class=o>=</span><span class=n>np</span><span class=o>.</span><span class=n>array</span><span class=p>([</span><span class=mi>1</span><span class=p>])</span><span class=o>.</span><span class=n>astype</span><span class=p>(</span><span class=n>np</span><span class=o>.</span><span class=n>float32</span><span class=p>),</span><span class=n>name</span> <span class=o>=</span> <span class=n>prefix</span> <span class=o>+</span> <span class=s2>&quot;mul_constant&quot;</span><span class=p>)</span>
</code></pre></div> 调用sgs_chalk的创建Tensor API,传入data和name即可</p> <hr> <h3 id=753>7.5.3. 创建一个算子<a class=headerlink href=#753 title="Permanent link">&para;</a></h3> <p>创建一个双口Mul算子：</p> <p><div class=highlight><pre><span></span><code><span class=n>SGS_score1</span> <span class=o>=</span> <span class=n>sgs_chalk</span><span class=o>.</span><span class=n>Mul</span><span class=p>(</span><span class=n>confidence_tensor</span><span class=p>,</span><span class=n>SGS_score0</span><span class=p>,</span><span class=n>name</span><span class=o>=</span><span class=s2>&quot;SGS_score1&quot;</span><span class=p>)</span>
</code></pre></div> 调用创建sgs_chalk的创建Mul算子API，传入输入tensor，输出tensor的name即可</p> <p>创建一个Reshape算子，需要创建常量Tensor：</p> <p><div class=highlight><pre><span></span><code><span class=n>reshape_tensor</span> <span class=o>=</span> <span class=n>sgs_chalk</span><span class=o>.</span><span class=n>Reshape</span><span class=p>(</span><span class=n>in0</span><span class=p>,[</span><span class=mi>1</span><span class=p>,</span><span class=mi>845</span><span class=p>,</span><span class=mi>25</span><span class=p>],</span><span class=n>name</span> <span class=o>=</span> <span class=s2>&quot;reshape_tensor&quot;</span><span class=p>)</span>
</code></pre></div> 调用sgs_chalk的创建Reshape算子API，传入输入tensor,shape,输出tensor的name即可</p> <hr> <h3 id=754>7.5.4. 创建客制化算子<a class=headerlink href=#754 title="Permanent link">&para;</a></h3> <p>见7.4节</p> <hr> <p>并且最终要返回创建好的后处理模型的文件名用于在ConvertTool中生成最终的float模型。</p> <h2 id=76-sgs_chalk>7.6 构造模型工具sgs_chalk<a class=headerlink href=#76-sgs_chalk title="Permanent link">&para;</a></h2> <h3 id=761-sgs_chalk>7.6.1 sgs_chalk基本介绍<a class=headerlink href=#761-sgs_chalk title="Permanent link">&para;</a></h3> <p>sgs_chalk是sigmastar自主研发的一款能够快速构建模型的工具，使用方法类似于开源深度学习框架pytorch和tensorflow，将sigmastar支持的算子封装成API，通过用户调用API构建脚本，生成最原始的debug.sim，并且通过设置相关参数，能够一步生成到float.sim和fixed.sim。下面会介绍具体的使用方法。</p> <h3 id=762-sgs_chalk>7.6.2 sgs_chalk使用方法<a class=headerlink href=#762-sgs_chalk title="Permanent link">&para;</a></h3> <p>(1) 使用前首先需要在脚本开头添加以下语句</p> <p><div class=highlight><pre><span></span><code><span class=kn>from</span> <span class=nn>calibrator_custom</span> <span class=kn>import</span> <span class=n>sgs_chalk</span>
</code></pre></div> (2) 构建tensor</p> <p>构建tensor调用的接口为<code>sgs_chalk.Tensor</code>,下面对传入参数进行介绍：</p> <p><code>data</code>: 传入tensor的具体数据，需要是np.array格式 </p> <p><code>shape</code>: 传入tensor的shape</p> <p><code>dtype</code>: 传入tensor的data的数据类型,不传的话默认是’ float32’,支持传入的数据类型为<code>uint8</code>/<code>int16</code>/<code>float32</code>/<code>int32</code>/<code>int64</code>/<code>complex64</code></p> <p><code>name</code>: 传入tensor的name,如果不传的话会默认以prefix_数字命名，并且内部有检查机制，遇到重名tensor会进行重新命名</p> <p><code>prefix</code>: 设置默认tensor命名的前缀</p> <p>在sgs_chalk中，构建tensor主要分为以下两种,variable tensor会在构建operator时返回，一般无需用户创建</p> <p>创建input tensor,调用sgs_chalk.Input接口</p> <div class=highlight><pre><span></span><code><span class=n>in0</span> <span class=o>=</span> <span class=n>sgs_chalk</span><span class=o>.</span><span class=n>Input</span><span class=p>((</span><span class=mi>1</span><span class=p>,</span><span class=mi>28</span><span class=p>,</span><span class=mi>28</span><span class=p>,</span><span class=mi>1</span><span class=p>),</span><span class=n>name</span><span class=o>=</span><span class=s1>&#39;input&#39;</span><span class=p>)</span>
</code></pre></div> <p>创建const tensor示例 </p> <p><div class=highlight><pre><span></span><code><span class=n>weight1</span> <span class=o>=</span> <span class=n>np</span><span class=o>.</span><span class=n>load</span><span class=p>(</span><span class=s2>&quot;mnist_gen_weights/conv2d_4_kernel.npy&quot;</span><span class=p>)</span>
<span class=n>weight1_tensor</span> <span class=o>=</span> <span class=n>sgs_chalk</span><span class=o>.</span><span class=n>Tensor</span><span class=p>(</span><span class=n>data</span><span class=o>=</span><span class=n>weight1</span><span class=p>,</span><span class=n>name</span><span class=o>=</span><span class=s1>&#39;conv2d_4/kernel&#39;</span><span class=p>)</span>
</code></pre></div> (3)构建operator 构建operator的接口是根据每个算子来的，创建每个算子需要传入的参数也不相同，会返回output tensor。具体可以参考1.4.3节，下面举几个简单的例子：</p> <p>创建add <div class=highlight><pre><span></span><code><span class=n>in0</span> <span class=o>=</span> <span class=n>sgs_chalk</span><span class=o>.</span><span class=n>Input</span><span class=p>((</span><span class=mi>28</span><span class=p>,</span><span class=mi>512</span><span class=p>),</span><span class=n>name</span><span class=o>=</span><span class=s1>&#39;input0&#39;</span><span class=p>)</span>
<span class=n>in1</span> <span class=o>=</span> <span class=n>sgs_chalk</span><span class=o>.</span><span class=n>Input</span><span class=p>((</span><span class=mi>28</span><span class=p>,</span><span class=mi>512</span><span class=p>),</span><span class=n>name</span><span class=o>=</span><span class=s1>&#39;input1&#39;</span><span class=p>)</span>
<span class=n>out0</span> <span class=o>=</span> <span class=n>sgs_chalk</span><span class=o>.</span><span class=n>Add</span><span class=p>(</span><span class=n>in0</span><span class=p>,</span><span class=n>in1</span><span class=p>,</span><span class=n>name</span><span class=o>=</span><span class=s1>&#39;output&#39;</span><span class=p>)</span>
</code></pre></div></p> <p>创建conv2d <div class=highlight><pre><span></span><code><span class=n>in0</span> <span class=o>=</span> <span class=n>sgs_chalk</span><span class=o>.</span><span class=n>Input</span><span class=p>((</span><span class=mi>1</span><span class=p>,</span><span class=mi>28</span><span class=p>,</span><span class=mi>28</span><span class=p>,</span><span class=mi>4</span><span class=p>),</span><span class=n>name</span><span class=o>=</span><span class=s1>&#39;input0&#39;</span><span class=p>)</span>
<span class=n>in1</span> <span class=o>=</span> <span class=n>sgs_chalk</span><span class=o>.</span><span class=n>Input</span><span class=p>((</span><span class=mi>8</span><span class=p>,</span><span class=mi>3</span><span class=p>,</span><span class=mi>3</span><span class=p>,</span><span class=mi>4</span><span class=p>),</span><span class=n>name</span><span class=o>=</span><span class=s1>&#39;input1&#39;</span><span class=p>)</span>
<span class=n>out0</span> <span class=o>=</span> <span class=n>sgs_chalk</span><span class=o>.</span><span class=n>Conv2d</span><span class=p>(</span><span class=n>in0</span><span class=p>,</span><span class=n>in1</span><span class=p>,</span><span class=n>name</span><span class=o>=</span><span class=s1>&#39;output&#39;</span><span class=p>)</span>
</code></pre></div></p> <p>(4)构建model 构建model调用的接口为sgs_chalk.Model,下面对传入参数进行介绍： input_tensors: 传入输入tensor名称，集合为list形式 output_tensors: 传入输出tensor名称，集合为list形式 <div class=highlight><pre><span></span><code><span class=n>model</span> <span class=o>=</span> <span class=n>sgs_chalk</span><span class=o>.</span><span class=n>Model</span><span class=p>([</span><span class=n>in0</span><span class=p>],[</span><span class=n>out0</span><span class=p>])</span>
</code></pre></div></p> <p>(4)导出模型 导出模型应该在构建模型之后，调用接口如下： <div class=highlight><pre><span></span><code><span class=n>model</span><span class=o>.</span><span class=n>save</span><span class=p>(</span><span class=n>model_path</span><span class=p>,</span> <span class=n>input_config</span><span class=o>=</span><span class=kc>None</span><span class=p>,</span> <span class=n>convert_fixed</span><span class=o>=</span><span class=kc>False</span><span class=p>,</span> <span class=n>inputs</span><span class=o>=</span><span class=kc>None</span><span class=p>)</span> 
</code></pre></div> 该API有以下几种用法：</p> <ul> <li> <p>仅保存debug模型： <div class=highlight><pre><span></span><code><span class=n>model</span><span class=o>.</span><span class=n>save</span><span class=p>(</span><span class=s1>&#39;test.sim&#39;</span><span class=p>)</span>    
</code></pre></div> 会生成debug_test.sim模型</p> </li> <li> <p>会保存debug和float模型，需要配置input_config.ini <div class=highlight><pre><span></span><code><span class=n>model</span><span class=o>.</span><span class=n>save</span><span class=p>(</span><span class=s1>&#39;test.sim&#39;</span><span class=p>,</span><span class=n>input_config</span><span class=o>=</span><span class=s1>&#39;./input_config.ini&#39;</span><span class=p>)</span> 
</code></pre></div> 会生成Debug_test.sim和test.sim</p> </li> <li> <p>保存fixed模型，convert_fixed=True，inputs=None： <div class=highlight><pre><span></span><code><span class=n>model</span><span class=o>.</span><span class=n>save</span><span class=p>(</span><span class=s1>&#39;test.sim&#39;</span><span class=p>,</span> <span class=n>input_config</span><span class=o>=</span><span class=s1>&#39;./input_config.ini&#39;</span><span class=p>,</span> <span class=n>convert_fixed</span><span class=o>=</span><span class=kc>True</span><span class=p>)</span> 
</code></pre></div> 会生成Debug_test.sim、test.sim和test_fixed.sim，这样的转换是基于默认量化导入的min/max转换的，因此有时候不能满足模型转换的精度需求。</p> </li> <li> <p>如果添加inputs参数，将会自动生成模型前处理文件，并按照inputs的内容即用户传入的图片等数据走sigmastar内部统计量化流程生成fixed.sim模型： <div class=highlight><pre><span></span><code><span class=n>model</span><span class=o>.</span><span class=n>save</span><span class=p>(</span><span class=s1>&#39;test.sim&#39;</span><span class=p>,</span> <span class=n>input_config</span><span class=o>=</span><span class=s1>&#39;./input_config.ini&#39;</span><span class=p>,</span> <span class=n>convert_fixed</span><span class=o>=</span><span class=kc>True</span><span class=p>,</span> <span class=n>inputs</span><span class=o>=</span><span class=s1>&#39;0.jpg&#39;</span><span class=p>)</span> 
</code></pre></div> 会读入inputs参数，根据input_config.ini的配置自动生成模型前处理脚本，量化生成fixed.sim模型</p> </li> </ul> <p>inputs参数有如下几种用法：</p> <ul> <li> <p>inputs=/path/to/img_dir 或 inputs=/path/to/img.jpg 如果input_config.ini中有模型输入的training_input_formats为RGB / BGR / GRAY，可以选用此用模式。</p> </li> <li> <p>inputs=/path/to/npy_dir 或 inputs=/path/to/data.npy 如果input_config.ini中模型输入均为RAWDATA_S16_NHWC / RAWDATA_F32_NHWC / RAWDATA_COMPLEX64，可以选用此用模式。需要注意的是输入数据必须为numpy的npy格式</p> </li> <li> <p>inputs=/path/to/img.txt  input_config.ini中有多个输入时，可以选用此用模式</p> </li> <li> <p>inputs='RAWDATA' 如果input_config.ini中所有模型输入均为RAWDATA_S16_NHWC / RAWDATA_F32_NHWC / RAWDATA_COMPLEX64，可以选用此用模式。‘RAWDATA’模式提前配置好模型输入的min/max，保存模型是会生成基于模型输入配置的min/max输入文件和前处理，并转换生成fixed模型。此模式仅被用来测试构造出的模型能否按照sigmastar内部统计的量化流程生成到fix模型。</p> </li> </ul> <h3 id=763-sgs_chalkapi>7.6.3 sgs_chalk模块API<a class=headerlink href=#763-sgs_chalkapi title="Permanent link">&para;</a></h3> <ul> <li>Abs:</li> </ul> <p>Computes the absolute value of a tensor.</p> <p>Given a tensor of integer or floating-point values, this operation returns a tensor of the same type, where each element contains the absolute value of the corresponding element in the input.</p> <p>Args: <div class=highlight><pre><span></span><code>x: A `Tensor`
</code></pre></div></p> <p>Returns: <div class=highlight><pre><span></span><code>A `Tensor` of same shape and type as `x`.
</code></pre></div> Examples:</p> <details> <summary>case0</summary> <div class=highlight><pre><span></span><code><span class=kn>from</span> <span class=nn>calibrator_custom</span> <span class=kn>import</span> <span class=n>sgs_chalk</span>
<span class=kn>import</span> <span class=nn>numpy</span> <span class=k>as</span> <span class=nn>np</span>
<span class=n>in0</span> <span class=o>=</span> <span class=n>sgs_chalk</span><span class=o>.</span><span class=n>Input</span><span class=p>((</span><span class=mi>28</span><span class=p>,</span><span class=mi>512</span><span class=p>),</span><span class=n>name</span><span class=o>=</span><span class=s1>&#39;input0&#39;</span><span class=p>)</span>
<span class=n>out0</span> <span class=o>=</span> <span class=n>sgs_chalk</span><span class=o>.</span><span class=n>Abs</span><span class=p>(</span><span class=n>in0</span><span class=p>,</span><span class=n>name</span><span class=o>=</span><span class=s1>&#39;output&#39;</span><span class=p>)</span>
<span class=n>model</span> <span class=o>=</span> <span class=n>sgs_chalk</span><span class=o>.</span><span class=n>Model</span><span class=p>([</span><span class=n>in0</span><span class=p>],[</span><span class=n>out0</span><span class=p>])</span>
<span class=n>model</span><span class=o>.</span><span class=n>save</span><span class=p>(</span><span class=s1>&#39;test.sim&#39;</span><span class=p>,</span><span class=n>input_config</span><span class=o>=</span><span class=s1>&#39;./input_config.ini&#39;</span><span class=p>,</span><span class=n>convert_fixed</span><span class=o>=</span><span class=kc>True</span><span class=p>)</span>
</code></pre></div> </details> <ul> <li>Add:</li> </ul> <p>Returns x + y element-wise.</p> <p>Args: <div class=highlight><pre><span></span><code>x: A `Tensor`. Must be Variable Tensor.
y: A `Tensor` or `numpy.ndarray`. Must have the same type as `x`, can be Variable or Const Tensor.
   Support inner most dimension broadcasting.
name: A name for the output tensor (optional).
</code></pre></div></p> <p>Returns: <div class=highlight><pre><span></span><code>A Variable `Tensor`. Has the same shape as `x`.
</code></pre></div></p> <p>Examples:</p> <details> <summary>case0</summary> <div class=highlight><pre><span></span><code><span class=kn>from</span> <span class=nn>calibrator_custom</span> <span class=kn>import</span> <span class=n>sgs_chalk</span>
<span class=kn>import</span> <span class=nn>numpy</span> <span class=k>as</span> <span class=nn>np</span>
<span class=n>in0</span> <span class=o>=</span> <span class=n>sgs_chalk</span><span class=o>.</span><span class=n>Input</span><span class=p>((</span><span class=mi>28</span><span class=p>,</span><span class=mi>512</span><span class=p>),</span><span class=n>name</span><span class=o>=</span><span class=s1>&#39;input0&#39;</span><span class=p>)</span>
<span class=n>in1</span> <span class=o>=</span> <span class=n>sgs_chalk</span><span class=o>.</span><span class=n>Input</span><span class=p>((</span><span class=mi>28</span><span class=p>,</span><span class=mi>512</span><span class=p>),</span><span class=n>name</span><span class=o>=</span><span class=s1>&#39;input1&#39;</span><span class=p>)</span>
<span class=n>out0</span> <span class=o>=</span> <span class=n>sgs_chalk</span><span class=o>.</span><span class=n>Add</span><span class=p>(</span><span class=n>in0</span><span class=p>,</span><span class=n>in1</span><span class=p>,</span><span class=n>name</span><span class=o>=</span><span class=s1>&#39;output&#39;</span><span class=p>)</span>
<span class=n>model</span> <span class=o>=</span> <span class=n>sgs_chalk</span><span class=o>.</span><span class=n>Model</span><span class=p>([</span><span class=n>in0</span><span class=p>,</span><span class=n>in1</span><span class=p>],</span><span class=n>out0</span><span class=p>)</span>
<span class=n>model</span><span class=o>.</span><span class=n>save</span><span class=p>(</span><span class=s1>&#39;test.sim&#39;</span><span class=p>)</span>
</code></pre></div> </details> <details> <summary>case1</summary> <div class=highlight><pre><span></span><code><span class=kn>from</span> <span class=nn>calibrator_custom</span> <span class=kn>import</span> <span class=n>sgs_chalk</span>
<span class=kn>import</span> <span class=nn>numpy</span> <span class=k>as</span> <span class=nn>np</span>
<span class=n>in0</span> <span class=o>=</span> <span class=n>sgs_chalk</span><span class=o>.</span><span class=n>Input</span><span class=p>((</span><span class=mi>28</span><span class=p>,</span><span class=mi>512</span><span class=p>),</span><span class=n>name</span><span class=o>=</span><span class=s1>&#39;input0&#39;</span><span class=p>)</span>
<span class=n>out0</span> <span class=o>=</span> <span class=n>sgs_chalk</span><span class=o>.</span><span class=n>Add</span><span class=p>(</span><span class=n>in0</span><span class=p>,</span><span class=mf>5.0</span><span class=p>,</span><span class=n>name</span><span class=o>=</span><span class=s1>&#39;output&#39;</span><span class=p>)</span>
<span class=n>model</span> <span class=o>=</span> <span class=n>sgs_chalk</span><span class=o>.</span><span class=n>Model</span><span class=p>([</span><span class=n>in0</span><span class=p>],</span><span class=n>out0</span><span class=p>)</span>
<span class=n>model</span><span class=o>.</span><span class=n>save</span><span class=p>(</span><span class=s1>&#39;test.sim&#39;</span><span class=p>)</span>
</code></pre></div> </details> <ul> <li>Argmax:</li> </ul> <p>Returns the indices of the maximum values along an axis.</p> <p>Args: <div class=highlight><pre><span></span><code>x: A `Tensor`. Must be Variable Tensor.
axis: int
keep_dim: bool
name: A name for the output tensor (optional).
</code></pre></div></p> <p>Returns: <div class=highlight><pre><span></span><code>A Variable `Tensor`. It has the same shape as `x.shape`
    with the dimension along `axis` removed.
</code></pre></div></p> <p>Examples:</p> <details> <summary>case0</summary> <div class=highlight><pre><span></span><code><span class=kn>from</span> <span class=nn>calibrator_custom</span> <span class=kn>import</span> <span class=n>sgs_chalk</span>
<span class=kn>import</span> <span class=nn>numpy</span> <span class=k>as</span> <span class=nn>np</span>
<span class=n>in0</span> <span class=o>=</span> <span class=n>sgs_chalk</span><span class=o>.</span><span class=n>Input</span><span class=p>((</span><span class=mi>1</span><span class=p>,</span> <span class=mi>80</span><span class=p>,</span> <span class=mi>512</span><span class=p>),</span><span class=n>name</span><span class=o>=</span><span class=s1>&#39;input0&#39;</span><span class=p>)</span>
<span class=n>out0</span> <span class=o>=</span> <span class=n>sgs_chalk</span><span class=o>.</span><span class=n>Argmax</span><span class=p>(</span><span class=n>in0</span><span class=p>,</span> <span class=mi>1</span><span class=p>,</span> <span class=n>name</span><span class=o>=</span><span class=s1>&#39;output&#39;</span><span class=p>)</span>
<span class=n>model</span><span class=o>.</span><span class=n>save</span><span class=p>(</span><span class=s1>&#39;test.sim&#39;</span><span class=p>)</span>
</code></pre></div> </details> <ul> <li>Atan2:</li> </ul> <p>Element-wise arctangent of <code>input0</code> and <code>input1</code> with consideration of the quadrant. Returns a new tensor with the signed angles in radians between (-pi, pi)</p> <p>Args: <div class=highlight><pre><span></span><code>input:0 A `Tensor`. Must be Variable Tensor.
input1: A `Tensor`. Must be Variable Tensor.
name: A name for the model Output tensor (optional).
</code></pre></div></p> <p>Returns: <div class=highlight><pre><span></span><code>A Variable `Tensor`. Has the same shape as input.
</code></pre></div></p> <p>Examples:</p> <details> <summary>case0</summary> <div class=highlight><pre><span></span><code><span class=kn>from</span> <span class=nn>calibrator_custom</span> <span class=kn>import</span> <span class=n>sgs_chalk</span>
<span class=n>input0</span> <span class=o>=</span> <span class=n>sgs_chalk</span><span class=o>.</span><span class=n>Input</span><span class=p>((</span><span class=mi>1</span><span class=p>,</span> <span class=mi>22535</span><span class=p>),</span> <span class=n>name</span><span class=o>=</span><span class=s1>&#39;input0&#39;</span><span class=p>)</span>
<span class=n>input1</span> <span class=o>=</span> <span class=n>sgs_chalk</span><span class=o>.</span><span class=n>Input</span><span class=p>((</span><span class=mi>1</span><span class=p>,</span> <span class=mi>22535</span><span class=p>),</span> <span class=n>name</span><span class=o>=</span><span class=s1>&#39;input1&#39;</span><span class=p>)</span>
<span class=n>out</span> <span class=o>=</span> <span class=n>sgs_chalk</span><span class=o>.</span><span class=n>Atan2</span><span class=p>(</span><span class=n>input0</span><span class=p>,</span> <span class=n>input1</span><span class=p>,</span> <span class=n>name</span><span class=o>=</span><span class=s1>&#39;output&#39;</span><span class=p>)</span>
<span class=n>model</span> <span class=o>=</span> <span class=n>sgs_chalk</span><span class=o>.</span><span class=n>Model</span><span class=p>([</span><span class=n>input0</span><span class=p>,</span> <span class=n>input1</span><span class=p>],</span> <span class=n>out</span><span class=p>)</span>
<span class=n>model</span><span class=o>.</span><span class=n>save</span><span class=p>(</span><span class=s1>&#39;atan2.sim&#39;</span><span class=p>,</span> <span class=n>input_config</span><span class=o>=</span><span class=s1>&#39;./input_config.ini&#39;</span><span class=p>,</span> <span class=n>convert_fixed</span><span class=o>=</span><span class=kc>True</span><span class=p>)</span>
</code></pre></div> </details> <ul> <li>AveragePool2d:</li> </ul> <p>Performs the average pooling on the input. Each entry in <code>output</code> is the mean of the corresponding size <code>ksize</code> window in <code>value</code>.</p> <p>Args: <div class=highlight><pre><span></span><code>x: A 4-D `Tensor` of shape `[batch, height, width, channels]`
ksize: A list of `ints` that has length `2`. The size of
the window for each dimension of the input tensor.
strides: An int or list of `ints` that has length `2`. The
stride of the sliding window for each dimension of the input tensor.
padding: a tuple (paddingTop, paddingBottom, paddingLeft, paddingRight).
padding_type: str, only support [SAME, VALID, CAFFE, ONNXINSIDE, ONNXOUTSIDE], default is CAFFE.
 SAME or VALID padding_type will not use padding value.
name: Optional name for the operation.
</code></pre></div></p> <p>Returns: <div class=highlight><pre><span></span><code>The average pooled output tensor.
</code></pre></div></p> <p>Examples:</p> <details> <summary>case0</summary> <div class=highlight><pre><span></span><code><span class=kn>from</span> <span class=nn>calibrator_custom</span> <span class=kn>import</span> <span class=n>sgs_chalk</span>
<span class=kn>import</span> <span class=nn>numpy</span> <span class=k>as</span> <span class=nn>np</span>
<span class=n>in0</span> <span class=o>=</span> <span class=n>sgs_chalk</span><span class=o>.</span><span class=n>Input</span><span class=p>((</span><span class=mi>1</span><span class=p>,</span><span class=mi>28</span><span class=p>,</span><span class=mi>28</span><span class=p>,</span><span class=mi>4</span><span class=p>),</span><span class=n>name</span><span class=o>=</span><span class=s1>&#39;input0&#39;</span><span class=p>)</span>
<span class=n>out0</span> <span class=o>=</span> <span class=n>sgs_chalk</span><span class=o>.</span><span class=n>Average_Pool_2D</span><span class=p>(</span><span class=n>in0</span><span class=p>,[</span><span class=mi>2</span><span class=p>,</span><span class=mi>2</span><span class=p>],</span><span class=n>name</span><span class=o>=</span><span class=s1>&#39;output&#39;</span><span class=p>)</span>
<span class=n>model</span> <span class=o>=</span> <span class=n>sgs_chalk</span><span class=o>.</span><span class=n>Model</span><span class=p>([</span><span class=n>in0</span><span class=p>],</span><span class=n>out0</span><span class=p>)</span>
<span class=n>model</span><span class=o>.</span><span class=n>save</span><span class=p>(</span><span class=s1>&#39;test.sim&#39;</span><span class=p>)</span>
</code></pre></div> </details> <details> <summary>case1</summary> <div class=highlight><pre><span></span><code><span class=kn>from</span> <span class=nn>calibrator_custom</span> <span class=kn>import</span> <span class=n>sgs_chalk</span>
<span class=kn>import</span> <span class=nn>numpy</span> <span class=k>as</span> <span class=nn>np</span>
<span class=n>in0</span> <span class=o>=</span> <span class=n>sgs_chalk</span><span class=o>.</span><span class=n>Input</span><span class=p>((</span><span class=mi>1</span><span class=p>,</span><span class=mi>112</span><span class=p>,</span><span class=mi>112</span><span class=p>,</span><span class=mi>3</span><span class=p>),</span><span class=n>name</span><span class=o>=</span><span class=s1>&#39;input0&#39;</span><span class=p>)</span>
<span class=n>out0</span> <span class=o>=</span> <span class=n>sgs_chalk</span><span class=o>.</span><span class=n>AveragePool2d</span><span class=p>(</span><span class=n>in0</span><span class=p>,[</span><span class=mi>2</span><span class=p>,</span><span class=mi>2</span><span class=p>],</span><span class=n>name</span><span class=o>=</span><span class=s1>&#39;output&#39;</span><span class=p>)</span>
<span class=n>model</span> <span class=o>=</span> <span class=n>sgs_chalk</span><span class=o>.</span><span class=n>Model</span><span class=p>([</span><span class=n>in0</span><span class=p>],</span><span class=n>out0</span><span class=p>)</span>
<span class=n>model</span><span class=o>.</span><span class=n>save</span><span class=p>(</span><span class=s1>&#39;test.sim&#39;</span><span class=p>,</span><span class=n>input_config</span><span class=o>=</span><span class=s1>&#39;./input_config.ini&#39;</span><span class=p>,</span><span class=n>convert_fixed</span><span class=o>=</span><span class=kc>True</span><span class=p>)</span>
</code></pre></div> </details> <ul> <li>BoxDecoder:</li> </ul> <p>sigmastar postprocess BoxDecoder</p> <p>Args: <div class=highlight><pre><span></span><code>unpacked_box: a list of tensors which are unpacked
</code></pre></div></p> <p>Return: <div class=highlight><pre><span></span><code>a list of tensors decoded
</code></pre></div></p> <p>Examples:</p> <details> <summary>case0</summary> <div class=highlight><pre><span></span><code><span class=kn>from</span> <span class=nn>calibrator_custom</span> <span class=kn>import</span> <span class=n>sgs_chalk</span>
<span class=kn>import</span> <span class=nn>numpy</span> <span class=k>as</span> <span class=nn>np</span>
<span class=n>box_num</span> <span class=o>=</span> <span class=mi>9</span>
<span class=n>side_x</span> <span class=o>=</span> <span class=mi>19</span>
<span class=n>side_y</span> <span class=o>=</span> <span class=mi>19</span>
<span class=n>ppw</span> <span class=o>=</span> <span class=n>anchor</span><span class=o>.</span><span class=n>ones</span><span class=p>(</span><span class=mi>3249</span><span class=p>)</span>
<span class=n>px</span> <span class=o>=</span> <span class=n>anchor</span><span class=o>.</span><span class=n>index_div_linear</span><span class=p>(</span><span class=mi>1</span><span class=p>,</span><span class=mi>1</span><span class=p>,</span><span class=mi>0</span><span class=p>,</span><span class=n>box_num</span> <span class=p>,</span><span class=n>side_x</span><span class=p>,</span><span class=n>side_y</span><span class=p>)</span>
<span class=n>pph</span> <span class=o>=</span> <span class=n>anchor</span><span class=o>.</span><span class=n>ones</span><span class=p>(</span><span class=mi>3249</span><span class=p>)</span>
<span class=n>py</span> <span class=o>=</span> <span class=n>anchor</span><span class=o>.</span><span class=n>index_div_linear</span><span class=p>(</span><span class=mi>1</span><span class=p>,</span><span class=mi>1</span><span class=p>,</span><span class=mi>0</span><span class=p>,</span><span class=n>side_x</span><span class=o>*</span><span class=n>box_num</span><span class=p>,</span><span class=n>side_y</span><span class=p>,</span><span class=mi>1</span><span class=p>)</span>
<span class=n>pw</span> <span class=o>=</span> <span class=n>anchor</span><span class=o>.</span><span class=n>ones</span><span class=p>(</span><span class=mi>3249</span><span class=p>)</span>
<span class=n>ph</span> <span class=o>=</span> <span class=n>anchor</span><span class=o>.</span><span class=n>ones</span><span class=p>(</span><span class=mi>3249</span><span class=p>)</span>

<span class=n>sx</span> <span class=o>=</span> <span class=n>anchor</span><span class=o>.</span><span class=n>ns</span><span class=p>(</span><span class=mi>3249</span><span class=p>,</span><span class=mf>1.0</span><span class=o>/</span><span class=mi>19</span><span class=p>)</span>
<span class=n>sy</span> <span class=o>=</span> <span class=n>anchor</span><span class=o>.</span><span class=n>ns</span><span class=p>(</span><span class=mi>3249</span><span class=p>,</span><span class=mf>1.0</span><span class=o>/</span><span class=mi>19</span><span class=p>)</span>

<span class=n>biases</span><span class=o>=</span> <span class=p>[[</span><span class=mf>9.5</span><span class=p>,</span><span class=mf>9.5</span><span class=p>],[</span><span class=mf>9.5</span><span class=p>,</span><span class=mf>9.5</span><span class=p>],[</span><span class=mf>9.5</span><span class=p>,</span><span class=mf>9.5</span><span class=p>],[</span><span class=mf>9.5</span><span class=p>,</span><span class=mf>9.5</span><span class=p>],[</span><span class=mf>9.5</span><span class=p>,</span><span class=mf>9.5</span><span class=p>],[</span><span class=mf>9.5</span><span class=p>,</span><span class=mf>9.5</span><span class=p>],[</span><span class=mf>9.5</span><span class=p>,</span><span class=mf>9.5</span><span class=p>],[</span><span class=mf>9.5</span><span class=p>,</span><span class=mf>9.5</span><span class=p>],[</span><span class=mf>9.5</span><span class=p>,</span><span class=mf>9.5</span><span class=p>]]</span>
<span class=n>sw</span> <span class=o>=</span> <span class=p>[</span><span class=n>x</span><span class=p>[</span><span class=mi>0</span><span class=p>]</span><span class=o>/</span><span class=p>(</span><span class=mi>2</span><span class=o>*</span><span class=mi>19</span><span class=p>)</span> <span class=k>for</span> <span class=n>x</span> <span class=ow>in</span> <span class=n>biases</span> <span class=p>]</span><span class=o>*</span><span class=p>(</span><span class=mi>19</span><span class=o>*</span><span class=mi>19</span><span class=p>)</span>
<span class=n>sh</span> <span class=o>=</span> <span class=p>[</span><span class=n>x</span><span class=p>[</span><span class=mi>1</span><span class=p>]</span><span class=o>/</span><span class=p>(</span><span class=mi>2</span><span class=o>*</span><span class=mi>19</span><span class=p>)</span> <span class=k>for</span> <span class=n>x</span> <span class=ow>in</span> <span class=n>biases</span> <span class=p>]</span><span class=o>*</span><span class=p>(</span><span class=mi>19</span><span class=o>*</span><span class=mi>19</span><span class=p>)</span>
<span class=n>config</span> <span class=o>=</span> <span class=p>{</span><span class=s2>&quot;shape&quot;</span> <span class=p>:</span> <span class=p>[</span><span class=mi>1</span><span class=p>,</span><span class=mi>3249</span><span class=p>],</span>
      <span class=s2>&quot;tx_func&quot;</span> <span class=p>:</span> <span class=p>(</span><span class=n>tflite</span><span class=o>.</span><span class=n>BuiltinOperator</span><span class=o>.</span><span class=n>BuiltinOperator</span><span class=p>()</span><span class=o>.</span><span class=n>LOGISTIC</span><span class=p>,</span><span class=kc>None</span><span class=p>),</span><span class=c1>#None or &#39;x_scale&#39;</span>
      <span class=s2>&quot;ty_func&quot;</span> <span class=p>:</span> <span class=p>(</span><span class=n>tflite</span><span class=o>.</span><span class=n>BuiltinOperator</span><span class=o>.</span><span class=n>BuiltinOperator</span><span class=p>()</span><span class=o>.</span><span class=n>LOGISTIC</span><span class=p>,</span><span class=kc>None</span><span class=p>),</span><span class=c1>#None or &#39;y_scale&#39;</span>
      <span class=s2>&quot;tw_func&quot;</span> <span class=p>:</span> <span class=p>(</span><span class=n>tflite</span><span class=o>.</span><span class=n>BuiltinOperator</span><span class=o>.</span><span class=n>BuiltinOperator</span><span class=p>()</span><span class=o>.</span><span class=n>RESHAPE</span><span class=p>,</span><span class=kc>None</span><span class=p>),</span><span class=c1>#None or &#39;w_scale&#39;</span>
      <span class=s2>&quot;th_func&quot;</span> <span class=p>:</span> <span class=p>(</span><span class=n>tflite</span><span class=o>.</span><span class=n>BuiltinOperator</span><span class=o>.</span><span class=n>BuiltinOperator</span><span class=p>()</span><span class=o>.</span><span class=n>RESHAPE</span><span class=p>,</span><span class=kc>None</span><span class=p>),</span><span class=c1>#None or &#39;h_scale&#39;</span>
      <span class=s2>&quot;x_scale&quot;</span> <span class=p>:</span> <span class=mf>0.1</span><span class=p>,</span>
      <span class=s2>&quot;y_scale&quot;</span> <span class=p>:</span> <span class=mf>0.1</span><span class=p>,</span>
      <span class=s2>&quot;w_scale&quot;</span> <span class=p>:</span> <span class=mi>1</span><span class=p>,</span>
      <span class=s2>&quot;h_scale&quot;</span> <span class=p>:</span> <span class=mi>1</span><span class=p>,</span>
      <span class=s2>&quot;anchor_selector&quot;</span> <span class=p>:</span> <span class=s2>&quot;constant&quot;</span><span class=p>,</span>
      <span class=s2>&quot;pw&quot;</span> <span class=p>:</span> <span class=n>pw</span><span class=p>,</span>
      <span class=s2>&quot;ph&quot;</span> <span class=p>:</span> <span class=n>ph</span><span class=p>,</span>
      <span class=s2>&quot;pw_func&quot;</span> <span class=p>:</span> <span class=p>(</span><span class=kc>None</span><span class=p>,</span><span class=kc>None</span><span class=p>),</span>
      <span class=s2>&quot;ph_func&quot;</span> <span class=p>:</span> <span class=p>(</span><span class=kc>None</span><span class=p>,</span><span class=kc>None</span><span class=p>),</span>
      <span class=s2>&quot;ppw&quot;</span> <span class=p>:</span> <span class=n>ppw</span><span class=p>,</span>
      <span class=s2>&quot;px&quot;</span> <span class=p>:</span> <span class=n>px</span><span class=p>,</span>
      <span class=s2>&quot;pph&quot;</span> <span class=p>:</span> <span class=n>pph</span><span class=p>,</span>
      <span class=s2>&quot;py&quot;</span> <span class=p>:</span> <span class=n>py</span><span class=p>,</span>
      <span class=s2>&quot;sx&quot;</span> <span class=p>:</span> <span class=n>sx</span><span class=p>,</span>
      <span class=s2>&quot;sy&quot;</span> <span class=p>:</span> <span class=n>sy</span><span class=p>,</span>
      <span class=s2>&quot;sw&quot;</span> <span class=p>:</span> <span class=n>sw</span><span class=p>,</span>
      <span class=s2>&quot;sh&quot;</span> <span class=p>:</span> <span class=n>sh</span>
      <span class=p>}</span>
<span class=n>in0</span> <span class=o>=</span> <span class=n>sgs_chalk</span><span class=o>.</span><span class=n>Input</span><span class=p>(</span><span class=n>model_config</span><span class=p>[</span><span class=s2>&quot;input_shape&quot;</span><span class=p>][</span><span class=mi>0</span><span class=p>],</span> <span class=n>name</span> <span class=o>=</span> <span class=n>model_config</span><span class=p>[</span><span class=s2>&quot;input&quot;</span><span class=p>][</span><span class=mi>0</span><span class=p>])</span>
<span class=n>unpack_out_tensors1</span> <span class=o>=</span> <span class=p>[]</span>
<span class=k>for</span> <span class=n>i</span> <span class=ow>in</span> <span class=nb>range</span><span class=p>(</span><span class=mi>7</span><span class=p>):</span>
    <span class=n>unpack_out_tensors1</span><span class=o>.</span><span class=n>append</span><span class=p>(</span><span class=s2>&quot;SGS_unpack1_&quot;</span><span class=o>+</span><span class=nb>str</span><span class=p>(</span><span class=n>i</span><span class=p>))</span>
<span class=n>output_list</span> <span class=o>=</span> <span class=n>sgs_chalk</span><span class=o>.</span><span class=n>PostProcess_Unpack</span><span class=p>(</span><span class=n>in0</span><span class=p>,</span><span class=n>name</span><span class=o>=</span><span class=n>unpack_out_tensors1</span><span class=p>)</span>

<span class=n>bosdecoder_output_list</span> <span class=o>=</span> <span class=n>sgs_chalk</span><span class=o>.</span><span class=n>BoxDecoder</span><span class=p>(</span><span class=n>config</span><span class=p>,</span><span class=n>output_list</span><span class=p>)</span>
<span class=n>model</span> <span class=o>=</span> <span class=n>sgs_chalk</span><span class=o>.</span><span class=n>Model</span><span class=p>([</span><span class=n>in0</span><span class=p>],</span><span class=n>bosdecoder_output_list</span><span class=p>)</span>
<span class=n>model</span><span class=o>.</span><span class=n>save</span><span class=p>(</span><span class=s1>&#39;test.sim&#39;</span><span class=p>,</span><span class=n>input_config</span><span class=o>=</span><span class=s1>&#39;./input_config.ini&#39;</span><span class=p>,</span><span class=n>convert_fixed</span><span class=o>=</span><span class=kc>True</span><span class=p>)</span>
</code></pre></div> </details> <ul> <li>BoxDecoder2:</li> </ul> <p>sigmastar postprocess BoxDecoder2</p> <p>Args: <div class=highlight><pre><span></span><code>unpacked_box: a list of tensors which are unpacked
</code></pre></div></p> <p>Return: <div class=highlight><pre><span></span><code>:return:a list of tensors decoded
</code></pre></div></p> <p>Examples:</p> <details> <summary>case0</summary> <div class=highlight><pre><span></span><code><span class=kn>from</span> <span class=nn>calibrator_custom</span> <span class=kn>import</span> <span class=n>sgs_chalk</span>
<span class=kn>import</span> <span class=nn>numpy</span> <span class=k>as</span> <span class=nn>np</span>
<span class=n>box_num</span> <span class=o>=</span> <span class=mi>9</span>
<span class=n>side_x</span> <span class=o>=</span> <span class=mi>19</span>
<span class=n>side_y</span> <span class=o>=</span> <span class=mi>19</span>
<span class=n>ppw</span> <span class=o>=</span> <span class=n>anchor</span><span class=o>.</span><span class=n>ones</span><span class=p>(</span><span class=mi>3249</span><span class=p>)</span>
<span class=n>px</span> <span class=o>=</span> <span class=n>anchor</span><span class=o>.</span><span class=n>index_div_linear</span><span class=p>(</span><span class=mi>1</span><span class=p>,</span><span class=mi>1</span><span class=p>,</span><span class=mi>0</span><span class=p>,</span><span class=n>box_num</span> <span class=p>,</span><span class=n>side_x</span><span class=p>,</span><span class=n>side_y</span><span class=p>)</span>
<span class=n>pph</span> <span class=o>=</span> <span class=n>anchor</span><span class=o>.</span><span class=n>ones</span><span class=p>(</span><span class=mi>3249</span><span class=p>)</span>
<span class=n>py</span> <span class=o>=</span> <span class=n>anchor</span><span class=o>.</span><span class=n>index_div_linear</span><span class=p>(</span><span class=mi>1</span><span class=p>,</span><span class=mi>1</span><span class=p>,</span><span class=mi>0</span><span class=p>,</span><span class=n>side_x</span><span class=o>*</span><span class=n>box_num</span><span class=p>,</span><span class=n>side_y</span><span class=p>,</span><span class=mi>1</span><span class=p>)</span>
<span class=n>pw</span> <span class=o>=</span> <span class=n>anchor</span><span class=o>.</span><span class=n>ones</span><span class=p>(</span><span class=mi>3249</span><span class=p>)</span>
<span class=n>ph</span> <span class=o>=</span> <span class=n>anchor</span><span class=o>.</span><span class=n>ones</span><span class=p>(</span><span class=mi>3249</span><span class=p>)</span>

<span class=n>sx</span> <span class=o>=</span> <span class=n>anchor</span><span class=o>.</span><span class=n>ns</span><span class=p>(</span><span class=mi>3249</span><span class=p>,</span><span class=mf>1.0</span><span class=o>/</span><span class=mi>19</span><span class=p>)</span>
<span class=n>sy</span> <span class=o>=</span> <span class=n>anchor</span><span class=o>.</span><span class=n>ns</span><span class=p>(</span><span class=mi>3249</span><span class=p>,</span><span class=mf>1.0</span><span class=o>/</span><span class=mi>19</span><span class=p>)</span>

<span class=n>biases</span><span class=o>=</span> <span class=p>[[</span><span class=mf>9.5</span><span class=p>,</span><span class=mf>9.5</span><span class=p>],[</span><span class=mf>9.5</span><span class=p>,</span><span class=mf>9.5</span><span class=p>],[</span><span class=mf>9.5</span><span class=p>,</span><span class=mf>9.5</span><span class=p>],[</span><span class=mf>9.5</span><span class=p>,</span><span class=mf>9.5</span><span class=p>],[</span><span class=mf>9.5</span><span class=p>,</span><span class=mf>9.5</span><span class=p>],[</span><span class=mf>9.5</span><span class=p>,</span><span class=mf>9.5</span><span class=p>],[</span><span class=mf>9.5</span><span class=p>,</span><span class=mf>9.5</span><span class=p>],[</span><span class=mf>9.5</span><span class=p>,</span><span class=mf>9.5</span><span class=p>],[</span><span class=mf>9.5</span><span class=p>,</span><span class=mf>9.5</span><span class=p>]]</span>
<span class=n>sw</span> <span class=o>=</span> <span class=p>[</span><span class=n>x</span><span class=p>[</span><span class=mi>0</span><span class=p>]</span><span class=o>/</span><span class=p>(</span><span class=mi>2</span><span class=o>*</span><span class=mi>19</span><span class=p>)</span> <span class=k>for</span> <span class=n>x</span> <span class=ow>in</span> <span class=n>biases</span> <span class=p>]</span><span class=o>*</span><span class=p>(</span><span class=mi>19</span><span class=o>*</span><span class=mi>19</span><span class=p>)</span>
<span class=n>sh</span> <span class=o>=</span> <span class=p>[</span><span class=n>x</span><span class=p>[</span><span class=mi>1</span><span class=p>]</span><span class=o>/</span><span class=p>(</span><span class=mi>2</span><span class=o>*</span><span class=mi>19</span><span class=p>)</span> <span class=k>for</span> <span class=n>x</span> <span class=ow>in</span> <span class=n>biases</span> <span class=p>]</span><span class=o>*</span><span class=p>(</span><span class=mi>19</span><span class=o>*</span><span class=mi>19</span><span class=p>)</span>
<span class=n>config</span> <span class=o>=</span> <span class=p>{</span><span class=s2>&quot;shape&quot;</span> <span class=p>:</span> <span class=p>[</span><span class=mi>1</span><span class=p>,</span><span class=mi>3249</span><span class=p>],</span>
    <span class=s2>&quot;tx_func&quot;</span> <span class=p>:</span> <span class=p>(</span><span class=n>tflite</span><span class=o>.</span><span class=n>BuiltinOperator</span><span class=o>.</span><span class=n>BuiltinOperator</span><span class=p>()</span><span class=o>.</span><span class=n>LOGISTIC</span><span class=p>,</span><span class=kc>None</span><span class=p>),</span><span class=c1>#None or &#39;x_scale&#39;</span>
    <span class=s2>&quot;ty_func&quot;</span> <span class=p>:</span> <span class=p>(</span><span class=n>tflite</span><span class=o>.</span><span class=n>BuiltinOperator</span><span class=o>.</span><span class=n>BuiltinOperator</span><span class=p>()</span><span class=o>.</span><span class=n>LOGISTIC</span><span class=p>,</span><span class=kc>None</span><span class=p>),</span><span class=c1>#None or &#39;y_scale&#39;</span>
    <span class=s2>&quot;tw_func&quot;</span> <span class=p>:</span> <span class=p>(</span><span class=n>tflite</span><span class=o>.</span><span class=n>BuiltinOperator</span><span class=o>.</span><span class=n>BuiltinOperator</span><span class=p>()</span><span class=o>.</span><span class=n>RESHAPE</span><span class=p>,</span><span class=kc>None</span><span class=p>),</span><span class=c1>#None or &#39;w_scale&#39;</span>
    <span class=s2>&quot;th_func&quot;</span> <span class=p>:</span> <span class=p>(</span><span class=n>tflite</span><span class=o>.</span><span class=n>BuiltinOperator</span><span class=o>.</span><span class=n>BuiltinOperator</span><span class=p>()</span><span class=o>.</span><span class=n>RESHAPE</span><span class=p>,</span><span class=kc>None</span><span class=p>),</span><span class=c1>#None or &#39;h_scale&#39;</span>
    <span class=s2>&quot;x_scale&quot;</span> <span class=p>:</span> <span class=mf>0.1</span><span class=p>,</span>
    <span class=s2>&quot;y_scale&quot;</span> <span class=p>:</span> <span class=mf>0.1</span><span class=p>,</span>
    <span class=s2>&quot;w_scale&quot;</span> <span class=p>:</span> <span class=mi>1</span><span class=p>,</span>
    <span class=s2>&quot;h_scale&quot;</span> <span class=p>:</span> <span class=mi>1</span><span class=p>,</span>
    <span class=s2>&quot;anchor_selector&quot;</span> <span class=p>:</span> <span class=s2>&quot;constant&quot;</span><span class=p>,</span>
    <span class=s2>&quot;pw&quot;</span> <span class=p>:</span> <span class=n>pw</span><span class=p>,</span>
    <span class=s2>&quot;ph&quot;</span> <span class=p>:</span> <span class=n>ph</span><span class=p>,</span>
    <span class=s2>&quot;pw_func&quot;</span> <span class=p>:</span> <span class=p>(</span><span class=kc>None</span><span class=p>,</span><span class=kc>None</span><span class=p>),</span>
    <span class=s2>&quot;ph_func&quot;</span> <span class=p>:</span> <span class=p>(</span><span class=kc>None</span><span class=p>,</span><span class=kc>None</span><span class=p>),</span>
    <span class=s2>&quot;ppw&quot;</span> <span class=p>:</span> <span class=n>ppw</span><span class=p>,</span>
    <span class=s2>&quot;px&quot;</span> <span class=p>:</span> <span class=n>px</span><span class=p>,</span>
    <span class=s2>&quot;pph&quot;</span> <span class=p>:</span> <span class=n>pph</span><span class=p>,</span>
    <span class=s2>&quot;py&quot;</span> <span class=p>:</span> <span class=n>py</span><span class=p>,</span>
    <span class=s2>&quot;sx&quot;</span> <span class=p>:</span> <span class=n>sx</span><span class=p>,</span>
    <span class=s2>&quot;sy&quot;</span> <span class=p>:</span> <span class=n>sy</span><span class=p>,</span>
    <span class=s2>&quot;sw&quot;</span> <span class=p>:</span> <span class=n>sw</span><span class=p>,</span>
    <span class=s2>&quot;sh&quot;</span> <span class=p>:</span> <span class=n>sh</span>
    <span class=p>}</span>
<span class=n>in0</span> <span class=o>=</span> <span class=n>sgs_chalk</span><span class=o>.</span><span class=n>Input</span><span class=p>(</span><span class=n>model_config</span><span class=p>[</span><span class=s2>&quot;input_shape&quot;</span><span class=p>][</span><span class=mi>0</span><span class=p>],</span> <span class=n>name</span> <span class=o>=</span> <span class=n>model_config</span><span class=p>[</span><span class=s2>&quot;input&quot;</span><span class=p>][</span><span class=mi>0</span><span class=p>])</span>
<span class=n>unpack_out_tensors1</span> <span class=o>=</span> <span class=p>[]</span>
<span class=k>for</span> <span class=n>i</span> <span class=ow>in</span> <span class=nb>range</span><span class=p>(</span><span class=mi>7</span><span class=p>):</span>
    <span class=n>unpack_out_tensors1</span><span class=o>.</span><span class=n>append</span><span class=p>(</span><span class=s2>&quot;SGS_unpack1_&quot;</span><span class=o>+</span><span class=nb>str</span><span class=p>(</span><span class=n>i</span><span class=p>))</span>
<span class=n>output_list</span> <span class=o>=</span> <span class=n>sgs_chalk</span><span class=o>.</span><span class=n>PostProcess_Unpack</span><span class=p>(</span><span class=n>in0</span><span class=p>,</span><span class=n>name</span><span class=o>=</span><span class=n>unpack_out_tensors1</span><span class=p>)</span>

<span class=n>bosdecoder_output_list</span> <span class=o>=</span> <span class=n>sgs_chalk</span><span class=o>.</span><span class=n>BoxDecoder</span><span class=p>(</span><span class=n>config</span><span class=p>,</span><span class=n>output_list</span><span class=p>)</span>
<span class=n>model</span> <span class=o>=</span> <span class=n>sgs_chalk</span><span class=o>.</span><span class=n>Model</span><span class=p>([</span><span class=n>in0</span><span class=p>],</span><span class=n>bosdecoder_output_list</span><span class=p>)</span>
<span class=c1>#model.save(&#39;test.sim&#39;)</span>
<span class=n>model</span><span class=o>.</span><span class=n>save</span><span class=p>(</span><span class=s1>&#39;test.sim&#39;</span><span class=p>,</span><span class=n>input_config</span><span class=o>=</span><span class=s1>&#39;./input_config.ini&#39;</span><span class=p>,</span><span class=n>convert_fixed</span><span class=o>=</span><span class=kc>True</span><span class=p>)</span>
</code></pre></div> </details> <ul> <li>Clip:</li> </ul> <p>Clips tensor values to a specified min and max.</p> <p>Given a tensor <code>x</code>, this operation returns a tensor of the same type and shape as <code>x</code> with its values clipped to <code>min_value</code> and <code>max_value</code>. Any values less than <code>min_value</code> are set to <code>min_value</code>. Any values greater than <code>max_value</code> are set to <code>max_value</code>.</p> <p>Args: <div class=highlight><pre><span></span><code>x: A `Tensor`. Must be Variable Tensor.
min_value: A value.
max_value: A value.
name: A name for the output tensor (optional).
</code></pre></div></p> <p>Returns: <div class=highlight><pre><span></span><code>A Variable `Tensor`. has the same shape as x
</code></pre></div></p> <p>Examples:</p> <details> <summary>case0</summary> <div class=highlight><pre><span></span><code><span class=kn>from</span> <span class=nn>calibrator_custom</span> <span class=kn>import</span> <span class=n>sgs_chalk</span>
<span class=kn>import</span> <span class=nn>numpy</span> <span class=k>as</span> <span class=nn>np</span>
<span class=nb>input</span> <span class=o>=</span> <span class=n>sgs_chalk</span><span class=o>.</span><span class=n>Input</span><span class=p>((</span><span class=mi>1</span><span class=p>,</span><span class=mi>36</span><span class=p>,</span><span class=mi>2048</span><span class=p>),</span> <span class=n>name</span><span class=o>=</span><span class=s1>&#39;input&#39;</span><span class=p>)</span>
<span class=n>output</span> <span class=o>=</span> <span class=n>sgs_chalk</span><span class=o>.</span><span class=n>Clip</span><span class=p>(</span><span class=nb>input</span><span class=p>,</span><span class=mi>0</span><span class=p>,</span><span class=mi>1</span><span class=p>,</span> <span class=n>name</span><span class=o>=</span><span class=s1>&#39;output&#39;</span><span class=p>)</span>
<span class=n>model</span> <span class=o>=</span> <span class=n>sgs_chalk</span><span class=o>.</span><span class=n>Model</span><span class=p>([</span><span class=nb>input</span><span class=p>,],</span> <span class=n>output</span><span class=p>)</span>
<span class=n>model</span><span class=o>.</span><span class=n>save</span><span class=p>(</span><span class=s1>&#39;test.sim&#39;</span><span class=p>,</span> <span class=n>input_config</span><span class=o>=</span><span class=s1>&#39;./input_config.ini&#39;</span><span class=p>,</span> <span class=n>convert_fixed</span><span class=o>=</span><span class=kc>True</span><span class=p>)</span>
</code></pre></div> </details> <ul> <li>Concatenation:</li> </ul> <p>Concatenates the list of tensors <code>values</code> along dimension <code>axis</code>. If <code>values[i].shape = [D0, D1, ... Daxis(i), ...Dn]</code>, the concatenated result has shape [D0, D1, ... Raxis, ...Dn] where Raxis = sum(Daxis(i)) That is, the data from the input tensors is joined along the <code>axis</code> dimension. The number of dimensions of the input tensors must match, and all dimensions except <code>axis</code> must be equal.</p> <p>Args: <div class=highlight><pre><span></span><code>values: A list of `Tensor` objects or a single `Tensor`.
axis: 0-D `int32` `Tensor`.  Dimension along which to concatenate. Must be
    in the range `[-rank(values), rank(values))`. As in Python, indexing for
    axis is 0-based. Positive axis in the rage of `[0, rank(values))` refers
    to `axis`-th dimension. And negative axis refers to `axis +
    rank(values)`-th dimension.
name: A name for the output tensor (optional).
</code></pre></div></p> <p>Returns: <div class=highlight><pre><span></span><code>A Variable `Tensor`.
</code></pre></div></p> <p>Examples:</p> <details> <summary>case0</summary> <div class=highlight><pre><span></span><code><span class=kn>from</span> <span class=nn>calibrator_custom</span> <span class=kn>import</span> <span class=n>sgs_chalk</span>
<span class=kn>import</span> <span class=nn>numpy</span> <span class=k>as</span> <span class=nn>np</span>
<span class=n>in0</span> <span class=o>=</span> <span class=n>sgs_chalk</span><span class=o>.</span><span class=n>Input</span><span class=p>((</span><span class=mi>28</span><span class=p>,</span><span class=mi>512</span><span class=p>,</span><span class=mi>1</span><span class=p>),</span><span class=n>name</span><span class=o>=</span><span class=s1>&#39;input0&#39;</span><span class=p>)</span>
<span class=n>in1</span> <span class=o>=</span> <span class=n>sgs_chalk</span><span class=o>.</span><span class=n>Input</span><span class=p>((</span><span class=mi>28</span><span class=p>,</span><span class=mi>512</span><span class=p>,</span><span class=mi>2</span><span class=p>),</span><span class=n>name</span><span class=o>=</span><span class=s1>&#39;input1&#39;</span><span class=p>)</span>
<span class=n>in2</span> <span class=o>=</span> <span class=n>sgs_chalk</span><span class=o>.</span><span class=n>Input</span><span class=p>((</span><span class=mi>28</span><span class=p>,</span><span class=mi>512</span><span class=p>,</span><span class=mi>3</span><span class=p>),</span><span class=n>name</span><span class=o>=</span><span class=s1>&#39;input2&#39;</span><span class=p>)</span>
<span class=n>x</span> <span class=o>=</span> <span class=p>[</span><span class=n>in0</span><span class=p>,</span> <span class=n>in1</span><span class=p>,</span> <span class=n>in2</span><span class=p>]</span>
<span class=n>out0</span> <span class=o>=</span> <span class=n>sgs_chalk</span><span class=o>.</span><span class=n>Concatenation</span><span class=p>(</span><span class=n>x</span><span class=p>,</span><span class=n>axis</span><span class=o>=</span><span class=mi>2</span><span class=p>,</span><span class=n>name</span><span class=o>=</span><span class=s1>&#39;output&#39;</span><span class=p>)</span>
<span class=n>model</span> <span class=o>=</span> <span class=n>sgs_chalk</span><span class=o>.</span><span class=n>Model</span><span class=p>(</span><span class=n>x</span><span class=p>,</span><span class=n>out0</span><span class=p>)</span>
<span class=n>model</span><span class=o>.</span><span class=n>save</span><span class=p>(</span><span class=s1>&#39;test.sim&#39;</span><span class=p>)</span>
</code></pre></div> </details> <details> <summary>case1</summary> <div class=highlight><pre><span></span><code><span class=kn>from</span> <span class=nn>calibrator_custom</span> <span class=kn>import</span> <span class=n>sgs_chalk</span>
<span class=kn>import</span> <span class=nn>numpy</span> <span class=k>as</span> <span class=nn>np</span>
<span class=n>in0</span> <span class=o>=</span> <span class=n>sgs_chalk</span><span class=o>.</span><span class=n>Input</span><span class=p>((</span><span class=mi>28</span><span class=p>,</span><span class=mi>1</span><span class=p>,</span><span class=mi>512</span><span class=p>),</span><span class=n>name</span><span class=o>=</span><span class=s1>&#39;input0&#39;</span><span class=p>)</span>
<span class=n>in1</span> <span class=o>=</span> <span class=n>sgs_chalk</span><span class=o>.</span><span class=n>Input</span><span class=p>((</span><span class=mi>28</span><span class=p>,</span><span class=mi>2</span><span class=p>,</span><span class=mi>512</span><span class=p>),</span><span class=n>name</span><span class=o>=</span><span class=s1>&#39;input1&#39;</span><span class=p>)</span>
<span class=n>in2</span> <span class=o>=</span> <span class=n>sgs_chalk</span><span class=o>.</span><span class=n>Input</span><span class=p>((</span><span class=mi>28</span><span class=p>,</span><span class=mi>3</span><span class=p>,</span><span class=mi>512</span><span class=p>),</span><span class=n>name</span><span class=o>=</span><span class=s1>&#39;input2&#39;</span><span class=p>)</span>
<span class=n>x</span> <span class=o>=</span> <span class=p>[</span><span class=n>in0</span><span class=p>,</span> <span class=n>in1</span><span class=p>,</span> <span class=n>in2</span><span class=p>]</span>
<span class=n>out0</span> <span class=o>=</span> <span class=n>sgs_chalk</span><span class=o>.</span><span class=n>Concatenation</span><span class=p>(</span><span class=n>x</span><span class=p>,</span><span class=n>axis</span><span class=o>=</span><span class=mi>1</span><span class=p>,</span><span class=n>name</span><span class=o>=</span><span class=s1>&#39;output&#39;</span><span class=p>)</span>
<span class=n>model</span> <span class=o>=</span> <span class=n>sgs_chalk</span><span class=o>.</span><span class=n>Model</span><span class=p>(</span><span class=n>x</span><span class=p>,</span><span class=n>out0</span><span class=p>)</span>
<span class=n>model</span><span class=o>.</span><span class=n>save</span><span class=p>(</span><span class=s1>&#39;test.sim&#39;</span><span class=p>)</span>
</code></pre></div> </details> <ul> <li>Conv2d:</li> </ul> <p>Conv2d</p> <p>Args: <div class=highlight><pre><span></span><code>x : input tensor of shape(1, iH, iW,in_channels)
weight: filters of shape(out_channels, kH, kW, in_channels)
bias: optional bias tensor of shape(out_channels).Default:None
stride: the stride of the convolving kernel. Can be a single number or a tuple(sH,sW).Default: 1
padding: a tuple (paddingTop, paddingBottom, paddingLeft, paddingRight) or a str `SAME`/ `VALID`.
dilation: the spacing between kernel elements. Can be a single number or a tuple(dH,dW).Default: 1
Activation: only support NONE/RELU/RELU_N1_TO_1/RELU6
</code></pre></div></p> <p>Examples:</p> <details> <summary>case0</summary> <div class=highlight><pre><span></span><code><span class=kn>from</span> <span class=nn>calibrator_custom</span> <span class=kn>import</span> <span class=n>sgs_chalk</span>
<span class=kn>import</span> <span class=nn>numpy</span> <span class=k>as</span> <span class=nn>np</span>
<span class=n>in0</span> <span class=o>=</span> <span class=n>sgs_chalk</span><span class=o>.</span><span class=n>Input</span><span class=p>((</span><span class=mi>1</span><span class=p>,</span><span class=mi>28</span><span class=p>,</span><span class=mi>28</span><span class=p>,</span><span class=mi>4</span><span class=p>),</span><span class=n>name</span><span class=o>=</span><span class=s1>&#39;input0&#39;</span><span class=p>)</span>
<span class=n>in1</span> <span class=o>=</span> <span class=n>sgs_chalk</span><span class=o>.</span><span class=n>Input</span><span class=p>((</span><span class=mi>8</span><span class=p>,</span><span class=mi>3</span><span class=p>,</span><span class=mi>3</span><span class=p>,</span><span class=mi>4</span><span class=p>),</span><span class=n>name</span><span class=o>=</span><span class=s1>&#39;input1&#39;</span><span class=p>)</span>
<span class=n>out0</span> <span class=o>=</span> <span class=n>sgs_chalk</span><span class=o>.</span><span class=n>Conv2d</span><span class=p>(</span><span class=n>in0</span><span class=p>,</span><span class=n>in1</span><span class=p>,</span><span class=n>name</span><span class=o>=</span><span class=s1>&#39;output&#39;</span><span class=p>)</span>
<span class=n>model</span> <span class=o>=</span> <span class=n>sgs_chalk</span><span class=o>.</span><span class=n>Model</span><span class=p>([</span><span class=n>in0</span><span class=p>,</span><span class=n>in1</span><span class=p>],</span><span class=n>out0</span><span class=p>)</span>
<span class=n>model</span><span class=o>.</span><span class=n>save</span><span class=p>(</span><span class=s1>&#39;test.sim&#39;</span><span class=p>)</span>
</code></pre></div> </details> <details> <summary>case1</summary> <div class=highlight><pre><span></span><code><span class=kn>from</span> <span class=nn>calibrator_custom</span> <span class=kn>import</span> <span class=n>sgs_chalk</span>
<span class=kn>import</span> <span class=nn>numpy</span> <span class=k>as</span> <span class=nn>np</span>
<span class=n>input1</span> <span class=o>=</span> <span class=n>np</span><span class=o>.</span><span class=n>zeros</span><span class=p>((</span><span class=mi>8</span><span class=p>,</span><span class=mi>3</span><span class=p>,</span><span class=mi>3</span><span class=p>,</span><span class=mi>4</span><span class=p>),</span> <span class=n>dtype</span><span class=o>=</span><span class=n>np</span><span class=o>.</span><span class=n>float32</span><span class=p>)</span>
<span class=n>in0</span> <span class=o>=</span> <span class=n>sgs_chalk</span><span class=o>.</span><span class=n>Input</span><span class=p>((</span><span class=mi>1</span><span class=p>,</span><span class=mi>28</span><span class=p>,</span><span class=mi>28</span><span class=p>,</span><span class=mi>4</span><span class=p>),</span><span class=n>name</span><span class=o>=</span><span class=s1>&#39;input0&#39;</span><span class=p>)</span>
<span class=n>out0</span> <span class=o>=</span> <span class=n>sgs_chalk</span><span class=o>.</span><span class=n>Conv2d</span><span class=p>(</span><span class=n>in0</span><span class=p>,</span><span class=n>input1</span><span class=p>,</span> <span class=n>Activation</span> <span class=o>=</span> <span class=s1>&#39;RELU&#39;</span><span class=p>,</span><span class=n>name</span><span class=o>=</span><span class=s1>&#39;output&#39;</span><span class=p>)</span>
<span class=n>model</span> <span class=o>=</span> <span class=n>sgs_chalk</span><span class=o>.</span><span class=n>Model</span><span class=p>([</span><span class=n>in0</span><span class=p>],</span><span class=n>out0</span><span class=p>)</span>
<span class=n>model</span><span class=o>.</span><span class=n>save</span><span class=p>(</span><span class=s1>&#39;test.sim&#39;</span><span class=p>)</span>
</code></pre></div> </details> <ul> <li>Cos:</li> </ul> <p>Returns Cos(x) element-wise.</p> <p>Args: <div class=highlight><pre><span></span><code>x: A `Tensor`. Must be Variable Tensor.

Returns:
```text
A Variable `Tensor`. Has the same shape as `x`.
</code></pre></div></p> <p>Examples:</p> <details> <summary>case0</summary> <div class=highlight><pre><span></span><code><span class=kn>from</span> <span class=nn>calibrator_custom</span> <span class=kn>import</span> <span class=n>sgs_chalk</span>
<span class=kn>import</span> <span class=nn>numpy</span> <span class=k>as</span> <span class=nn>np</span>
<span class=n>in0</span> <span class=o>=</span> <span class=n>sgs_chalk</span><span class=o>.</span><span class=n>Input</span><span class=p>((</span><span class=mi>28</span><span class=p>,</span><span class=mi>512</span><span class=p>),</span><span class=n>name</span><span class=o>=</span><span class=s1>&#39;input0&#39;</span><span class=p>)</span>
<span class=n>out0</span> <span class=o>=</span> <span class=n>sgs_chalk</span><span class=o>.</span><span class=n>Sin</span><span class=p>(</span><span class=n>in0</span><span class=p>,</span><span class=n>name</span><span class=o>=</span><span class=s1>&#39;output&#39;</span><span class=p>)</span>
<span class=n>model</span> <span class=o>=</span> <span class=n>sgs_chalk</span><span class=o>.</span><span class=n>Model</span><span class=p>([</span><span class=n>in0</span><span class=p>],</span><span class=n>out0</span><span class=p>)</span>
<span class=n>model</span><span class=o>.</span><span class=n>save</span><span class=p>(</span><span class=s1>&#39;test.sim&#39;</span><span class=p>)</span>
</code></pre></div> </details> <ul> <li>DepthWiseConv2d:</li> </ul> <p>DepthWiseConv2D</p> <p>Args: <div class=highlight><pre><span></span><code>x : input tensor of shape(1, iH, iW,in_channels)
weight: filters of shape(1, kH, kW, out_channels) in_channels = out_channels
bias: optional bias tensor of shape(out_channels).Default:None
stride: the stride of the convolving kernel. Can be a single number or a tuple(sH,sW).Default: 1
padding: a tuple (paddingTop, paddingBottom, paddingLeft, paddingRight) or a str `SAME`/ `VALID`.
dilation: the spacing between kernel elements. Can be a single number or a tuple(dH,dW).Default: 1
Activation: only support NONE/RELU/RELU_N1_TO_1/RELU6
</code></pre></div></p> <p>Examples:</p> <details> <summary>case0</summary> <div class=highlight><pre><span></span><code><span class=kn>from</span> <span class=nn>calibrator_custom</span> <span class=kn>import</span> <span class=n>sgs_chalk</span>
<span class=kn>import</span> <span class=nn>numpy</span> <span class=k>as</span> <span class=nn>np</span>
<span class=n>in0</span> <span class=o>=</span> <span class=n>sgs_chalk</span><span class=o>.</span><span class=n>Input</span><span class=p>((</span><span class=mi>1</span><span class=p>,</span><span class=mi>28</span><span class=p>,</span><span class=mi>28</span><span class=p>,</span><span class=mi>4</span><span class=p>),</span><span class=n>name</span><span class=o>=</span><span class=s1>&#39;input0&#39;</span><span class=p>)</span>
<span class=n>in1</span> <span class=o>=</span> <span class=n>sgs_chalk</span><span class=o>.</span><span class=n>Input</span><span class=p>((</span><span class=mi>1</span><span class=p>,</span><span class=mi>3</span><span class=p>,</span><span class=mi>3</span><span class=p>,</span><span class=mi>4</span><span class=p>),</span><span class=n>name</span><span class=o>=</span><span class=s1>&#39;input1&#39;</span><span class=p>)</span>
<span class=n>out0</span> <span class=o>=</span> <span class=n>sgs_chalk</span><span class=o>.</span><span class=n>DepthWiseConv2D</span><span class=p>(</span><span class=n>in0</span><span class=p>,</span><span class=n>in1</span><span class=p>,</span><span class=n>name</span><span class=o>=</span><span class=s1>&#39;output&#39;</span><span class=p>)</span>
<span class=n>model</span> <span class=o>=</span> <span class=n>sgs_chalk</span><span class=o>.</span><span class=n>Model</span><span class=p>([</span><span class=n>in0</span><span class=p>,</span><span class=n>in1</span><span class=p>],</span><span class=n>out0</span><span class=p>)</span>
<span class=n>model</span><span class=o>.</span><span class=n>save</span><span class=p>(</span><span class=s1>&#39;test.sim&#39;</span><span class=p>)</span>
</code></pre></div> </details> <details> <summary>case1</summary> <div class=highlight><pre><span></span><code><span class=kn>from</span> <span class=nn>calibrator_custom</span> <span class=kn>import</span> <span class=n>sgs_chalk</span>
<span class=kn>import</span> <span class=nn>numpy</span> <span class=k>as</span> <span class=nn>np</span>
<span class=n>input1</span> <span class=o>=</span> <span class=n>np</span><span class=o>.</span><span class=n>zeros</span><span class=p>((</span><span class=mi>1</span><span class=p>,</span><span class=mi>3</span><span class=p>,</span><span class=mi>3</span><span class=p>,</span><span class=mi>4</span><span class=p>),</span> <span class=n>dtype</span><span class=o>=</span><span class=n>np</span><span class=o>.</span><span class=n>float32</span><span class=p>)</span>
<span class=n>in0</span> <span class=o>=</span> <span class=n>sgs_chalk</span><span class=o>.</span><span class=n>Input</span><span class=p>((</span><span class=mi>1</span><span class=p>,</span><span class=mi>28</span><span class=p>,</span><span class=mi>28</span><span class=p>,</span><span class=mi>4</span><span class=p>),</span><span class=n>name</span><span class=o>=</span><span class=s1>&#39;input0&#39;</span><span class=p>)</span>
<span class=n>out0</span> <span class=o>=</span> <span class=n>sgs_chalk</span><span class=o>.</span><span class=n>DepthWiseConv2D</span><span class=p>(</span><span class=n>in0</span><span class=p>,</span><span class=n>input1</span><span class=p>,</span><span class=n>bias</span> <span class=o>=</span> <span class=kc>None</span><span class=p>,</span> <span class=n>Activation</span> <span class=o>=</span> <span class=s1>&#39;RELU&#39;</span><span class=p>,</span><span class=n>name</span><span class=o>=</span><span class=s1>&#39;output&#39;</span><span class=p>)</span>
<span class=n>model</span> <span class=o>=</span> <span class=n>sgs_chalk</span><span class=o>.</span><span class=n>Model</span><span class=p>([</span><span class=n>in0</span><span class=p>],</span><span class=n>out0</span><span class=p>)</span>
<span class=n>model</span><span class=o>.</span><span class=n>save</span><span class=p>(</span><span class=s1>&#39;test.sim&#39;</span><span class=p>)</span>
</code></pre></div> </details> <ul> <li>Dilation:</li> </ul> <p>Dilation a tensor.</p> <p>Args: <div class=highlight><pre><span></span><code>tensor: A `Tensor`.
name: A name for the output tensor (optional).
</code></pre></div></p> <p>Returns: <div class=highlight><pre><span></span><code>A `Tensor`. Has the same type as `tensor`.
</code></pre></div></p> <p>Examples:</p> <details> <summary>case0</summary> <div class=highlight><pre><span></span><code><span class=kn>from</span> <span class=nn>calibrator_custom</span> <span class=kn>import</span> <span class=n>sgs_chalk</span>
<span class=kn>import</span> <span class=nn>numpy</span> <span class=k>as</span> <span class=nn>np</span>
<span class=n>in0</span> <span class=o>=</span> <span class=n>sgs_chalk</span><span class=o>.</span><span class=n>Input</span><span class=p>((</span><span class=mi>28</span><span class=p>,</span><span class=mi>512</span><span class=p>),</span><span class=n>name</span><span class=o>=</span><span class=s1>&#39;input0&#39;</span><span class=p>)</span>
<span class=n>out0</span> <span class=o>=</span> <span class=n>sgs_chalk</span><span class=o>.</span><span class=n>Dilation</span><span class=p>(</span><span class=n>in0</span><span class=p>,[</span><span class=mi>2</span><span class=p>,</span><span class=mi>1</span><span class=p>,</span><span class=mi>0</span><span class=p>,</span><span class=mi>0</span><span class=p>],[</span><span class=mi>2</span><span class=p>,</span><span class=mi>0</span><span class=p>],</span><span class=n>name</span><span class=o>=</span><span class=s1>&#39;output&#39;</span><span class=p>)</span>
<span class=n>model</span> <span class=o>=</span> <span class=n>sgs_chalk</span><span class=o>.</span><span class=n>Model</span><span class=p>([</span><span class=n>in0</span><span class=p>],</span><span class=n>out0</span><span class=p>)</span>
<span class=n>model</span><span class=o>.</span><span class=n>save</span><span class=p>(</span><span class=s1>&#39;test.sim&#39;</span><span class=p>)</span>
</code></pre></div> </details> <ul> <li>Elu:</li> </ul> <p>Args: <div class=highlight><pre><span></span><code>x: A `Tensor`. Must be Variable Tensor.
name: A name for the model Input tensor (optional).
</code></pre></div></p> <p>Returns: <div class=highlight><pre><span></span><code>A Variable `Tensor`. Has the same shape as settings.
</code></pre></div></p> <p>Examples:</p> <details> <summary>case0</summary> <div class=highlight><pre><span></span><code><span class=kn>from</span> <span class=nn>calibrator_custom</span> <span class=kn>import</span> <span class=n>sgs_chalk</span>
<span class=kn>import</span> <span class=nn>numpy</span> <span class=k>as</span> <span class=nn>np</span>
<span class=n>in0</span> <span class=o>=</span> <span class=n>sgs_chalk</span><span class=o>.</span><span class=n>Input</span><span class=p>((</span><span class=mi>28</span><span class=p>,</span><span class=mi>512</span><span class=p>),</span><span class=n>name</span><span class=o>=</span><span class=s1>&#39;input0&#39;</span><span class=p>)</span>
<span class=n>out0</span> <span class=o>=</span> <span class=n>sgs_chalk</span><span class=o>.</span><span class=n>Elu</span><span class=p>(</span><span class=n>in0</span><span class=p>,</span><span class=n>name</span><span class=o>=</span><span class=s1>&#39;output&#39;</span><span class=p>)</span>
<span class=n>model</span> <span class=o>=</span> <span class=n>sgs_chalk</span><span class=o>.</span><span class=n>Model</span><span class=p>([</span><span class=n>in0</span><span class=p>],</span><span class=n>out0</span><span class=p>)</span>
<span class=n>model</span><span class=o>.</span><span class=n>save</span><span class=p>(</span><span class=s1>&#39;test.sim&#39;</span><span class=p>,</span><span class=n>input_config</span><span class=o>=</span><span class=s1>&#39;./input_config.ini&#39;</span><span class=p>,</span><span class=n>convert_fixed</span><span class=o>=</span><span class=kc>True</span><span class=p>)</span>
</code></pre></div> </details> <ul> <li>Equal:</li> </ul> <p>Returns the truth value of (x == y) element-wise.</p> <p>Args: <div class=highlight><pre><span></span><code>x: A `Tensor`. Must be Variable Tensor.
y: A `Tensor` or `numpy.ndarray`. Must have the same type as `x`, can be Variable or Const Tensor.
   Support inner most dimension broadcasting.
name: A name for the output tensor (optional).
</code></pre></div></p> <p>Returns: <div class=highlight><pre><span></span><code>A Variable `Tensor`. Has the same shape as `x`.Tensor` of type `bool
</code></pre></div></p> <p>Examples:</p> <details> <summary>case0</summary> <div class=highlight><pre><span></span><code><span class=kn>from</span> <span class=nn>calibrator_custom</span> <span class=kn>import</span> <span class=n>sgs_chalk</span>
<span class=kn>import</span> <span class=nn>numpy</span> <span class=k>as</span> <span class=nn>np</span>
<span class=n>in0</span> <span class=o>=</span> <span class=n>sgs_chalk</span><span class=o>.</span><span class=n>Input</span><span class=p>((</span><span class=mi>28</span><span class=p>,</span><span class=mi>512</span><span class=p>),</span><span class=n>name</span><span class=o>=</span><span class=s1>&#39;input0&#39;</span><span class=p>)</span>
<span class=n>in1</span> <span class=o>=</span> <span class=n>sgs_chalk</span><span class=o>.</span><span class=n>Input</span><span class=p>((</span><span class=mi>28</span><span class=p>,</span><span class=mi>512</span><span class=p>),</span><span class=n>name</span><span class=o>=</span><span class=s1>&#39;input1&#39;</span><span class=p>)</span>
<span class=n>out0</span> <span class=o>=</span> <span class=n>sgs_chalk</span><span class=o>.</span><span class=n>Equal</span><span class=p>(</span><span class=n>in0</span><span class=p>,</span><span class=n>in1</span><span class=p>,</span><span class=n>name</span><span class=o>=</span><span class=s1>&#39;output&#39;</span><span class=p>)</span>
<span class=n>model</span> <span class=o>=</span> <span class=n>sgs_chalk</span><span class=o>.</span><span class=n>Model</span><span class=p>([</span><span class=n>in0</span><span class=p>,</span><span class=n>in1</span><span class=p>],</span><span class=n>out0</span><span class=p>)</span>
<span class=n>model</span><span class=o>.</span><span class=n>save</span><span class=p>(</span><span class=s1>&#39;test.sim&#39;</span><span class=p>)</span>
</code></pre></div> </details> <details> <summary>case1</summary> <div class=highlight><pre><span></span><code><span class=kn>from</span> <span class=nn>calibrator_custom</span> <span class=kn>import</span> <span class=n>sgs_chalk</span>
<span class=kn>import</span> <span class=nn>numpy</span> <span class=k>as</span> <span class=nn>np</span>
<span class=n>in0</span> <span class=o>=</span> <span class=n>sgs_chalk</span><span class=o>.</span><span class=n>Input</span><span class=p>((</span><span class=mi>28</span><span class=p>,</span><span class=mi>512</span><span class=p>),</span><span class=n>name</span><span class=o>=</span><span class=s1>&#39;input0&#39;</span><span class=p>)</span>
<span class=n>out0</span> <span class=o>=</span> <span class=n>sgs_chalk</span><span class=o>.</span><span class=n>Equal</span><span class=p>(</span><span class=n>in0</span><span class=p>,</span><span class=mf>5.0</span><span class=p>,</span><span class=n>name</span><span class=o>=</span><span class=s1>&#39;output&#39;</span><span class=p>)</span>
<span class=n>model</span> <span class=o>=</span> <span class=n>sgs_chalk</span><span class=o>.</span><span class=n>Model</span><span class=p>([</span><span class=n>in0</span><span class=p>],</span><span class=n>out0</span><span class=p>)</span>
<span class=n>model</span><span class=o>.</span><span class=n>save</span><span class=p>(</span><span class=s1>&#39;test.sim&#39;</span><span class=p>)</span>
</code></pre></div> </details> <details> <summary>case2</summary> <div class=highlight><pre><span></span><code><span class=kn>from</span> <span class=nn>calibrator_custom</span> <span class=kn>import</span> <span class=n>sgs_chalk</span>
<span class=kn>import</span> <span class=nn>numpy</span> <span class=k>as</span> <span class=nn>np</span>
<span class=n>input1</span> <span class=o>=</span> <span class=n>np</span><span class=o>.</span><span class=n>zeros</span><span class=p>((</span><span class=mi>28</span><span class=p>,</span><span class=mi>512</span><span class=p>),</span> <span class=n>dtype</span><span class=o>=</span><span class=n>np</span><span class=o>.</span><span class=n>float32</span><span class=p>)</span>
<span class=n>in0</span> <span class=o>=</span> <span class=n>sgs_chalk</span><span class=o>.</span><span class=n>Input</span><span class=p>((</span><span class=mi>28</span><span class=p>,</span><span class=mi>512</span><span class=p>),</span><span class=n>name</span><span class=o>=</span><span class=s1>&#39;input0&#39;</span><span class=p>)</span>
<span class=n>out0</span> <span class=o>=</span> <span class=n>sgs_chalk</span><span class=o>.</span><span class=n>Equal</span><span class=p>(</span><span class=n>in0</span><span class=p>,</span><span class=n>input1</span><span class=p>,</span><span class=n>name</span><span class=o>=</span><span class=s1>&#39;output&#39;</span><span class=p>)</span>
<span class=n>model</span> <span class=o>=</span> <span class=n>sgs_chalk</span><span class=o>.</span><span class=n>Model</span><span class=p>([</span><span class=n>in0</span><span class=p>],</span><span class=n>out0</span><span class=p>)</span>
<span class=n>model</span><span class=o>.</span><span class=n>save</span><span class=p>(</span><span class=s1>&#39;test.sim&#39;</span><span class=p>)</span>
</code></pre></div> </details> <ul> <li>Exp:</li> </ul> <p>Computes exponential of x element-wise. (y = e^x).</p> <p>Args: <div class=highlight><pre><span></span><code>x: A `Tensor`. Must be Variable Tensor.
name: A name for the output tensor (optional).
</code></pre></div></p> <p>Returns: <div class=highlight><pre><span></span><code>A Variable `Tensor`. Has the same shape as `x`.
</code></pre></div></p> <p>Examples:</p> <details> <summary>case0</summary> <div class=highlight><pre><span></span><code><span class=kn>from</span> <span class=nn>calibrator_custom</span> <span class=kn>import</span> <span class=n>sgs_chalk</span>
<span class=kn>import</span> <span class=nn>numpy</span> <span class=k>as</span> <span class=nn>np</span>
<span class=n>in0</span> <span class=o>=</span> <span class=n>sgs_chalk</span><span class=o>.</span><span class=n>Input</span><span class=p>((</span><span class=mi>28</span><span class=p>,</span><span class=mi>512</span><span class=p>),</span><span class=n>name</span><span class=o>=</span><span class=s1>&#39;input0&#39;</span><span class=p>)</span>
<span class=n>out0</span> <span class=o>=</span> <span class=n>sgs_chalk</span><span class=o>.</span><span class=n>Exp</span><span class=p>(</span><span class=n>in0</span><span class=p>,</span><span class=n>name</span><span class=o>=</span><span class=s1>&#39;output&#39;</span><span class=p>)</span>
<span class=n>model</span> <span class=o>=</span> <span class=n>sgs_chalk</span><span class=o>.</span><span class=n>Model</span><span class=p>([</span><span class=n>in0</span><span class=p>],</span><span class=n>out0</span><span class=p>)</span>
<span class=n>model</span><span class=o>.</span><span class=n>save</span><span class=p>(</span><span class=s1>&#39;test.sim&#39;</span><span class=p>)</span>
</code></pre></div> </details> <ul> <li>FloorDiv:</li> </ul> <p>Returns x / y element-wise.</p> <p>Args: <div class=highlight><pre><span></span><code>x: A `Tensor`. Must be Variable Tensor.
y: A `Tensor` or `numpy.ndarray`. Must have the same type as `x`, can be Variable or Const Tensor.
   Support inner most dimension broadcasting.
name: A name for the output tensor (optional).
</code></pre></div></p> <p>Returns: <div class=highlight><pre><span></span><code>A Variable `Tensor`. Has the same shape as `x`.
</code></pre></div></p> <p>Examples:</p> <details> <summary>case0</summary> <div class=highlight><pre><span></span><code><span class=kn>from</span> <span class=nn>calibrator_custom</span> <span class=kn>import</span> <span class=n>sgs_chalk</span>
<span class=kn>import</span> <span class=nn>numpy</span> <span class=k>as</span> <span class=nn>np</span>
<span class=n>in0</span> <span class=o>=</span> <span class=n>sgs_chalk</span><span class=o>.</span><span class=n>Input</span><span class=p>((</span><span class=mi>28</span><span class=p>,</span><span class=mi>512</span><span class=p>),</span><span class=n>name</span><span class=o>=</span><span class=s1>&#39;input0&#39;</span><span class=p>)</span>
<span class=n>in1</span> <span class=o>=</span> <span class=n>sgs_chalk</span><span class=o>.</span><span class=n>Input</span><span class=p>((</span><span class=mi>28</span><span class=p>,</span><span class=mi>512</span><span class=p>),</span><span class=n>name</span><span class=o>=</span><span class=s1>&#39;input1&#39;</span><span class=p>)</span>
<span class=n>out0</span> <span class=o>=</span> <span class=n>sgs_chalk</span><span class=o>.</span><span class=n>FloorDiv</span><span class=p>(</span><span class=n>in0</span><span class=p>,</span><span class=n>in1</span><span class=p>,</span><span class=n>name</span><span class=o>=</span><span class=s1>&#39;output&#39;</span><span class=p>)</span>
<span class=n>model</span> <span class=o>=</span> <span class=n>sgs_chalk</span><span class=o>.</span><span class=n>Model</span><span class=p>([</span><span class=n>in0</span><span class=p>,</span><span class=n>in1</span><span class=p>],</span><span class=n>out0</span><span class=p>)</span>
<span class=n>model</span><span class=o>.</span><span class=n>save</span><span class=p>(</span><span class=s1>&#39;test.sim&#39;</span><span class=p>)</span>
</code></pre></div> </details> <details> <summary>case1</summary> <div class=highlight><pre><span></span><code><span class=kn>from</span> <span class=nn>calibrator_custom</span> <span class=kn>import</span> <span class=n>sgs_chalk</span>
<span class=kn>import</span> <span class=nn>numpy</span> <span class=k>as</span> <span class=nn>np</span>
<span class=n>in0</span> <span class=o>=</span> <span class=n>sgs_chalk</span><span class=o>.</span><span class=n>Input</span><span class=p>((</span><span class=mi>28</span><span class=p>,</span><span class=mi>512</span><span class=p>),</span><span class=n>name</span><span class=o>=</span><span class=s1>&#39;input0&#39;</span><span class=p>)</span>
<span class=n>out0</span> <span class=o>=</span> <span class=n>sgs_chalk</span><span class=o>.</span><span class=n>FloorDiv</span><span class=p>(</span><span class=n>in0</span><span class=p>,</span><span class=mf>5.0</span><span class=p>,</span><span class=n>name</span><span class=o>=</span><span class=s1>&#39;output&#39;</span><span class=p>)</span>
<span class=n>model</span> <span class=o>=</span> <span class=n>sgs_chalk</span><span class=o>.</span><span class=n>Model</span><span class=p>([</span><span class=n>in0</span><span class=p>],</span><span class=n>out0</span><span class=p>)</span>
<span class=n>model</span><span class=o>.</span><span class=n>save</span><span class=p>(</span><span class=s1>&#39;test.sim&#39;</span><span class=p>)</span>
</code></pre></div> </details> <details> <summary>case2</summary> <div class=highlight><pre><span></span><code><span class=kn>from</span> <span class=nn>calibrator_custom</span> <span class=kn>import</span> <span class=n>sgs_chalk</span>
<span class=kn>import</span> <span class=nn>numpy</span> <span class=k>as</span> <span class=nn>np</span>
<span class=n>input1</span> <span class=o>=</span> <span class=n>np</span><span class=o>.</span><span class=n>ones</span><span class=p>((</span><span class=mi>512</span><span class=p>),</span> <span class=n>dtype</span><span class=o>=</span><span class=n>np</span><span class=o>.</span><span class=n>float32</span><span class=p>)</span>
<span class=n>in0</span> <span class=o>=</span> <span class=n>sgs_chalk</span><span class=o>.</span><span class=n>Input</span><span class=p>((</span><span class=mi>28</span><span class=p>,</span><span class=mi>512</span><span class=p>),</span><span class=n>name</span><span class=o>=</span><span class=s1>&#39;input0&#39;</span><span class=p>)</span>
<span class=n>out0</span> <span class=o>=</span> <span class=n>sgs_chalk</span><span class=o>.</span><span class=n>FloorDiv</span><span class=p>(</span><span class=n>in0</span><span class=p>,</span><span class=n>input1</span><span class=p>,</span><span class=n>name</span><span class=o>=</span><span class=s1>&#39;output&#39;</span><span class=p>)</span>
<span class=n>model</span> <span class=o>=</span> <span class=n>sgs_chalk</span><span class=o>.</span><span class=n>Model</span><span class=p>([</span><span class=n>in0</span><span class=p>],</span><span class=n>out0</span><span class=p>)</span>
<span class=n>model</span><span class=o>.</span><span class=n>save</span><span class=p>(</span><span class=s1>&#39;test.sim&#39;</span><span class=p>,</span><span class=n>input_config</span><span class=o>=</span><span class=s1>&#39;./input_config.ini&#39;</span><span class=p>,</span><span class=n>convert_fixed</span><span class=o>=</span><span class=kc>True</span><span class=p>)</span>
</code></pre></div> </details> <ul> <li>Fullyconnected:</li> </ul> <p><code>fully_connected</code> creates a variable called <code>weights</code>, representing a fully connected weight matrix, which is multiplied by the <code>inputs</code> to produce a <code>Tensor</code> of hidden units.</p> <p>Args: <div class=highlight><pre><span></span><code>x : input tensor of shape 2 dims
weight: filters of shape 2 dims
bias: optional bias tensor of shape(out_channels). Default:None
Activation: only support NONE/RELU/RELU_N1_TO_1/RELU6
</code></pre></div></p> <p>Examples:</p> <details> <summary>case0</summary> <div class=highlight><pre><span></span><code><span class=kn>from</span> <span class=nn>calibrator_custom</span> <span class=kn>import</span> <span class=n>sgs_chalk</span>
<span class=kn>import</span> <span class=nn>numpy</span> <span class=k>as</span> <span class=nn>np</span>
<span class=n>input1</span> <span class=o>=</span> <span class=n>np</span><span class=o>.</span><span class=n>zeros</span><span class=p>((</span><span class=mi>1000</span><span class=p>,</span><span class=mi>112</span><span class=p>),</span> <span class=n>dtype</span><span class=o>=</span><span class=n>np</span><span class=o>.</span><span class=n>float32</span><span class=p>)</span>
<span class=n>in0</span> <span class=o>=</span> <span class=n>sgs_chalk</span><span class=o>.</span><span class=n>Input</span><span class=p>((</span><span class=mi>3</span><span class=p>,</span><span class=mi>112</span><span class=p>),</span><span class=n>name</span><span class=o>=</span><span class=s1>&#39;input0&#39;</span><span class=p>)</span>
<span class=n>out0</span> <span class=o>=</span> <span class=n>sgs_chalk</span><span class=o>.</span><span class=n>Fullyconnected</span><span class=p>(</span><span class=n>in0</span><span class=p>,</span><span class=n>input1</span><span class=p>,</span><span class=n>name</span><span class=o>=</span><span class=s1>&#39;output&#39;</span><span class=p>)</span>
<span class=n>model</span> <span class=o>=</span> <span class=n>sgs_chalk</span><span class=o>.</span><span class=n>Model</span><span class=p>([</span><span class=n>in0</span><span class=p>],</span><span class=n>out0</span><span class=p>)</span>
<span class=n>model</span><span class=o>.</span><span class=n>save</span><span class=p>(</span><span class=s1>&#39;test.sim&#39;</span><span class=p>)</span>
</code></pre></div> </details> <details> <summary>case1</summary> <div class=highlight><pre><span></span><code><span class=kn>from</span> <span class=nn>calibrator_custom</span> <span class=kn>import</span> <span class=n>sgs_chalk</span>
<span class=kn>import</span> <span class=nn>numpy</span> <span class=k>as</span> <span class=nn>np</span>
<span class=n>input1</span> <span class=o>=</span> <span class=n>np</span><span class=o>.</span><span class=n>zeros</span><span class=p>((</span><span class=mi>1000</span><span class=p>,</span><span class=mi>112</span><span class=p>),</span> <span class=n>dtype</span><span class=o>=</span><span class=n>np</span><span class=o>.</span><span class=n>float32</span><span class=p>)</span>
<span class=n>in0</span> <span class=o>=</span> <span class=n>sgs_chalk</span><span class=o>.</span><span class=n>Input</span><span class=p>((</span><span class=mi>3</span><span class=p>,</span><span class=mi>112</span><span class=p>),</span><span class=n>name</span><span class=o>=</span><span class=s1>&#39;input0&#39;</span><span class=p>)</span>
<span class=n>out0</span> <span class=o>=</span> <span class=n>sgs_chalk</span><span class=o>.</span><span class=n>Fullyconnected</span><span class=p>(</span><span class=n>in0</span><span class=p>,</span><span class=n>input1</span><span class=p>,</span><span class=n>bias</span> <span class=o>=</span> <span class=kc>None</span><span class=p>,</span> <span class=n>Activation</span> <span class=o>=</span> <span class=s1>&#39;RELU&#39;</span><span class=p>,</span><span class=n>name</span><span class=o>=</span><span class=s1>&#39;output&#39;</span><span class=p>)</span>
<span class=n>model</span> <span class=o>=</span> <span class=n>sgs_chalk</span><span class=o>.</span><span class=n>Model</span><span class=p>([</span><span class=n>in0</span><span class=p>],</span><span class=n>out0</span><span class=p>)</span>
<span class=n>model</span><span class=o>.</span><span class=n>save</span><span class=p>(</span><span class=s1>&#39;test.sim&#39;</span><span class=p>)</span>
</code></pre></div> </details> <ul> <li>Gather:</li> </ul> <p>Gather slices from <code>x</code> axis <code>axis</code> according to <code>indices</code>.</p> <p><code>indices</code> must be an integer tensor of any dimension (usually 0-D or 1-D). Produces an output tensor with shape <code>params.shape[:axis] + indices.shape + params.shape[axis + 1:]</code></p> <p>Args: <div class=highlight><pre><span></span><code>x: A variable `Tensor`. The tensor from which to gather values.
indices: A `Tensor`.  Must be in range `[0, params.shape[axis])`.
axis: A `Tensor`. The axis in `params` to gather `indices` from. Defaults to the first
      dimension.
name: A name for the output tensor (optional).
</code></pre></div></p> <p>Returns: <div class=highlight><pre><span></span><code>A `Tensor`. Has the same type as `value`.
</code></pre></div></p> <p>Examples:</p> <details> <summary>case0</summary> <div class=highlight><pre><span></span><code><span class=kn>from</span> <span class=nn>calibrator_custom</span> <span class=kn>import</span> <span class=n>sgs_chalk</span>
<span class=kn>import</span> <span class=nn>numpy</span> <span class=k>as</span> <span class=nn>np</span>
<span class=n>in0</span> <span class=o>=</span> <span class=n>sgs_chalk</span><span class=o>.</span><span class=n>Input</span><span class=p>((</span><span class=mi>28</span><span class=p>,</span><span class=mi>512</span><span class=p>,</span><span class=mi>4</span><span class=p>),</span><span class=n>name</span><span class=o>=</span><span class=s1>&#39;input0&#39;</span><span class=p>)</span>
<span class=n>out0</span> <span class=o>=</span> <span class=n>sgs_chalk</span><span class=o>.</span><span class=n>Gather</span><span class=p>(</span><span class=n>in0</span><span class=p>,[</span><span class=mi>0</span><span class=p>,</span><span class=mi>2</span><span class=p>],</span><span class=n>name</span><span class=o>=</span><span class=s1>&#39;output&#39;</span><span class=p>)</span>
<span class=n>model</span> <span class=o>=</span> <span class=n>sgs_chalk</span><span class=o>.</span><span class=n>Model</span><span class=p>([</span><span class=n>in0</span><span class=p>],</span><span class=n>out0</span><span class=p>)</span>
<span class=n>model</span><span class=o>.</span><span class=n>save</span><span class=p>(</span><span class=s1>&#39;test.sim&#39;</span><span class=p>)</span>
</code></pre></div> </details> <details> <summary>case1</summary> <div class=highlight><pre><span></span><code><span class=kn>from</span> <span class=nn>calibrator_custom</span> <span class=kn>import</span> <span class=n>sgs_chalk</span>
<span class=kn>import</span> <span class=nn>numpy</span> <span class=k>as</span> <span class=nn>np</span>
<span class=n>in0</span> <span class=o>=</span> <span class=n>sgs_chalk</span><span class=o>.</span><span class=n>Input</span><span class=p>((</span><span class=mi>28</span><span class=p>,</span><span class=mi>512</span><span class=p>,</span><span class=mi>4</span><span class=p>),</span><span class=n>name</span><span class=o>=</span><span class=s1>&#39;input0&#39;</span><span class=p>)</span>
<span class=n>out0</span> <span class=o>=</span> <span class=n>sgs_chalk</span><span class=o>.</span><span class=n>Gather</span><span class=p>(</span><span class=n>in0</span><span class=p>,[[</span><span class=mi>0</span><span class=p>,</span><span class=mi>1</span><span class=p>],[</span><span class=mi>0</span><span class=p>,</span><span class=mi>1</span><span class=p>]],</span><span class=n>axis</span><span class=o>=</span><span class=mi>1</span><span class=p>,</span><span class=n>name</span><span class=o>=</span><span class=s1>&#39;output&#39;</span><span class=p>)</span>
<span class=n>model</span> <span class=o>=</span> <span class=n>sgs_chalk</span><span class=o>.</span><span class=n>Model</span><span class=p>([</span><span class=n>in0</span><span class=p>],</span><span class=n>out0</span><span class=p>)</span>
<span class=n>model</span><span class=o>.</span><span class=n>save</span><span class=p>(</span><span class=s1>&#39;test.sim&#39;</span><span class=p>)</span>
</code></pre></div> </details> <ul> <li>Greater:</li> </ul> <p>greater(x, y, name=None) Returns the truth value of (x &gt; y) element-wise.</p> <div class=highlight><pre><span></span><code>Args:
x: A `Tensor`.
y: A `Tensor`. Must have the same type as `x`.
name: A name for the output tensor (optional).
</code></pre></div> <p>Returns: <div class=highlight><pre><span></span><code>A `Tensor` of type `bool`.
</code></pre></div></p> <p>Examples:</p> <details> <summary>case0</summary> <div class=highlight><pre><span></span><code><span class=kn>from</span> <span class=nn>calibrator_custom</span> <span class=kn>import</span> <span class=n>sgs_chalk</span>
<span class=kn>import</span> <span class=nn>numpy</span> <span class=k>as</span> <span class=nn>np</span>
<span class=n>in0</span> <span class=o>=</span> <span class=n>sgs_chalk</span><span class=o>.</span><span class=n>Input</span><span class=p>((</span><span class=mi>28</span><span class=p>,</span><span class=mi>512</span><span class=p>),</span><span class=n>name</span><span class=o>=</span><span class=s1>&#39;input0&#39;</span><span class=p>)</span>
<span class=n>in1</span> <span class=o>=</span> <span class=n>sgs_chalk</span><span class=o>.</span><span class=n>Input</span><span class=p>((</span><span class=mi>28</span><span class=p>,</span><span class=mi>512</span><span class=p>),</span><span class=n>name</span><span class=o>=</span><span class=s1>&#39;input1&#39;</span><span class=p>)</span>
<span class=n>out0</span> <span class=o>=</span> <span class=n>sgs_chalk</span><span class=o>.</span><span class=n>Greater</span><span class=p>(</span><span class=n>in0</span><span class=p>,</span><span class=n>in1</span><span class=p>,</span><span class=n>name</span><span class=o>=</span><span class=s1>&#39;output&#39;</span><span class=p>)</span>
<span class=n>model</span> <span class=o>=</span> <span class=n>sgs_chalk</span><span class=o>.</span><span class=n>Model</span><span class=p>([</span><span class=n>in0</span><span class=p>,</span><span class=n>in1</span><span class=p>],</span><span class=n>out0</span><span class=p>)</span>
<span class=n>model</span><span class=o>.</span><span class=n>save</span><span class=p>(</span><span class=s1>&#39;test.sim&#39;</span><span class=p>)</span>
</code></pre></div> </details> <details> <summary>case1</summary> <div class=highlight><pre><span></span><code><span class=kn>from</span> <span class=nn>calibrator_custom</span> <span class=kn>import</span> <span class=n>sgs_chalk</span>
<span class=kn>import</span> <span class=nn>numpy</span> <span class=k>as</span> <span class=nn>np</span>
<span class=n>in0</span> <span class=o>=</span> <span class=n>sgs_chalk</span><span class=o>.</span><span class=n>Input</span><span class=p>((</span><span class=mi>28</span><span class=p>,</span><span class=mi>512</span><span class=p>),</span><span class=n>name</span><span class=o>=</span><span class=s1>&#39;input0&#39;</span><span class=p>)</span>
<span class=n>out0</span> <span class=o>=</span> <span class=n>sgs_chalk</span><span class=o>.</span><span class=n>Greater</span><span class=p>(</span><span class=n>in0</span><span class=p>,</span><span class=mf>5.0</span><span class=p>,</span><span class=n>name</span><span class=o>=</span><span class=s1>&#39;output&#39;</span><span class=p>)</span>
<span class=n>model</span> <span class=o>=</span> <span class=n>sgs_chalk</span><span class=o>.</span><span class=n>Model</span><span class=p>([</span><span class=n>in0</span><span class=p>],</span><span class=n>out0</span><span class=p>)</span>
<span class=n>model</span><span class=o>.</span><span class=n>save</span><span class=p>(</span><span class=s1>&#39;test.sim&#39;</span><span class=p>)</span>
</code></pre></div> </details> <details> <summary>case2</summary> <div class=highlight><pre><span></span><code><span class=kn>from</span> <span class=nn>calibrator_custom</span> <span class=kn>import</span> <span class=n>sgs_chalk</span>
<span class=kn>import</span> <span class=nn>numpy</span> <span class=k>as</span> <span class=nn>np</span>
<span class=n>input1</span> <span class=o>=</span> <span class=n>np</span><span class=o>.</span><span class=n>zeros</span><span class=p>((</span><span class=mi>28</span><span class=p>,</span><span class=mi>512</span><span class=p>),</span> <span class=n>dtype</span><span class=o>=</span><span class=n>np</span><span class=o>.</span><span class=n>float32</span><span class=p>)</span>
<span class=n>in0</span> <span class=o>=</span> <span class=n>sgs_chalk</span><span class=o>.</span><span class=n>Input</span><span class=p>((</span><span class=mi>28</span><span class=p>,</span><span class=mi>512</span><span class=p>),</span><span class=n>name</span><span class=o>=</span><span class=s1>&#39;input0&#39;</span><span class=p>)</span>
<span class=n>out0</span> <span class=o>=</span> <span class=n>sgs_chalk</span><span class=o>.</span><span class=n>Greater</span><span class=p>(</span><span class=n>in0</span><span class=p>,</span><span class=n>input1</span><span class=p>,</span><span class=n>name</span><span class=o>=</span><span class=s1>&#39;output&#39;</span><span class=p>)</span>
<span class=n>model</span> <span class=o>=</span> <span class=n>sgs_chalk</span><span class=o>.</span><span class=n>Model</span><span class=p>([</span><span class=n>in0</span><span class=p>],</span><span class=n>out0</span><span class=p>)</span>
<span class=n>model</span><span class=o>.</span><span class=n>save</span><span class=p>(</span><span class=s1>&#39;test.sim&#39;</span><span class=p>)</span>
</code></pre></div> </details> <ul> <li>GreaterEqual:</li> </ul> <p>Returns the truth value of (x &gt;= y) element-wise.</p> <p>Args: <div class=highlight><pre><span></span><code>x: A `Tensor`. Must be Variable Tensor.
y: A `Tensor` or `numpy.ndarray`. Must have the same type as `x`, can be Variable or Const Tensor.
   Support inner most dimension broadcasting.
name: A name for the output tensor (optional).
</code></pre></div></p> <p>Returns: <div class=highlight><pre><span></span><code>A Variable `Tensor`. Has the same shape as `x`.Tensor` of type `bool
</code></pre></div></p> <p>Examples:</p> <details> <summary>case0</summary> <div class=highlight><pre><span></span><code><span class=kn>from</span> <span class=nn>calibrator_custom</span> <span class=kn>import</span> <span class=n>sgs_chalk</span>
<span class=kn>import</span> <span class=nn>numpy</span> <span class=k>as</span> <span class=nn>np</span>
<span class=n>in0</span> <span class=o>=</span> <span class=n>sgs_chalk</span><span class=o>.</span><span class=n>Input</span><span class=p>((</span><span class=mi>28</span><span class=p>,</span><span class=mi>512</span><span class=p>),</span><span class=n>name</span><span class=o>=</span><span class=s1>&#39;input0&#39;</span><span class=p>)</span>
<span class=n>in1</span> <span class=o>=</span> <span class=n>sgs_chalk</span><span class=o>.</span><span class=n>Input</span><span class=p>((</span><span class=mi>28</span><span class=p>,</span><span class=mi>512</span><span class=p>),</span><span class=n>name</span><span class=o>=</span><span class=s1>&#39;input1&#39;</span><span class=p>)</span>
<span class=n>out0</span> <span class=o>=</span> <span class=n>sgs_chalk</span><span class=o>.</span><span class=n>GreaterEqual</span><span class=p>(</span><span class=n>in0</span><span class=p>,</span><span class=n>in1</span><span class=p>,</span><span class=n>name</span><span class=o>=</span><span class=s1>&#39;output&#39;</span><span class=p>)</span>
<span class=n>model</span> <span class=o>=</span> <span class=n>sgs_chalk</span><span class=o>.</span><span class=n>Model</span><span class=p>([</span><span class=n>in0</span><span class=p>,</span><span class=n>in1</span><span class=p>],</span><span class=n>out0</span><span class=p>)</span>
<span class=n>model</span><span class=o>.</span><span class=n>save</span><span class=p>(</span><span class=s1>&#39;test.sim&#39;</span><span class=p>)</span>
</code></pre></div> </details> <details> <summary>case1</summary> <div class=highlight><pre><span></span><code><span class=kn>from</span> <span class=nn>calibrator_custom</span> <span class=kn>import</span> <span class=n>sgs_chalk</span>
<span class=kn>import</span> <span class=nn>numpy</span> <span class=k>as</span> <span class=nn>np</span>
<span class=n>in0</span> <span class=o>=</span> <span class=n>sgs_chalk</span><span class=o>.</span><span class=n>Input</span><span class=p>((</span><span class=mi>28</span><span class=p>,</span><span class=mi>512</span><span class=p>),</span><span class=n>name</span><span class=o>=</span><span class=s1>&#39;input0&#39;</span><span class=p>)</span>
<span class=n>out0</span> <span class=o>=</span> <span class=n>sgs_chalk</span><span class=o>.</span><span class=n>GreaterEqual</span><span class=p>(</span><span class=n>in0</span><span class=p>,</span><span class=mf>5.0</span><span class=p>,</span><span class=n>name</span><span class=o>=</span><span class=s1>&#39;output&#39;</span><span class=p>)</span>
<span class=n>model</span> <span class=o>=</span> <span class=n>sgs_chalk</span><span class=o>.</span><span class=n>Model</span><span class=p>([</span><span class=n>in0</span><span class=p>],</span><span class=n>out0</span><span class=p>)</span>
<span class=n>model</span><span class=o>.</span><span class=n>save</span><span class=p>(</span><span class=s1>&#39;test.sim&#39;</span><span class=p>)</span>
</code></pre></div> </details> <details> <summary>case2</summary> <div class=highlight><pre><span></span><code><span class=kn>from</span> <span class=nn>calibrator_custom</span> <span class=kn>import</span> <span class=n>sgs_chalk</span>
<span class=kn>import</span> <span class=nn>numpy</span> <span class=k>as</span> <span class=nn>np</span>
<span class=n>input1</span> <span class=o>=</span> <span class=n>np</span><span class=o>.</span><span class=n>zeros</span><span class=p>((</span><span class=mi>28</span><span class=p>,</span><span class=mi>512</span><span class=p>),</span> <span class=n>dtype</span><span class=o>=</span><span class=n>np</span><span class=o>.</span><span class=n>float32</span><span class=p>)</span>
<span class=n>in0</span> <span class=o>=</span> <span class=n>sgs_chalk</span><span class=o>.</span><span class=n>Input</span><span class=p>((</span><span class=mi>28</span><span class=p>,</span><span class=mi>512</span><span class=p>),</span><span class=n>name</span><span class=o>=</span><span class=s1>&#39;input0&#39;</span><span class=p>)</span>
<span class=n>out0</span> <span class=o>=</span> <span class=n>sgs_chalk</span><span class=o>.</span><span class=n>GreaterEqual</span><span class=p>(</span><span class=n>in0</span><span class=p>,</span><span class=n>input1</span><span class=p>,</span><span class=n>name</span><span class=o>=</span><span class=s1>&#39;output&#39;</span><span class=p>)</span>
<span class=n>model</span> <span class=o>=</span> <span class=n>sgs_chalk</span><span class=o>.</span><span class=n>Model</span><span class=p>([</span><span class=n>in0</span><span class=p>],</span><span class=n>out0</span><span class=p>)</span>
<span class=n>model</span><span class=o>.</span><span class=n>save</span><span class=p>(</span><span class=s1>&#39;test.sim&#39;</span><span class=p>)</span>
</code></pre></div> </details> <ul> <li>Input:</li> </ul> <p>As an entry point into a graph.</p> <p>Args: <div class=highlight><pre><span></span><code>shape: `Tuple`.
name: A name for the model Input tensor (optional).
</code></pre></div></p> <p>Returns: <div class=highlight><pre><span></span><code>A Variable `Tensor`. Has the same shape as settings.
</code></pre></div></p> <p>Examples:</p> <details> <summary>case0</summary> <div class=highlight><pre><span></span><code><span class=kn>from</span> <span class=nn>calibrator_custom</span> <span class=kn>import</span> <span class=n>sgs_chalk</span>
<span class=kn>import</span> <span class=nn>numpy</span> <span class=k>as</span> <span class=nn>np</span>
<span class=n>in0</span> <span class=o>=</span> <span class=n>sgs_chalk</span><span class=o>.</span><span class=n>Input</span><span class=p>((</span><span class=mi>28</span><span class=p>,</span><span class=mi>512</span><span class=p>),</span><span class=n>name</span><span class=o>=</span><span class=s1>&#39;input0&#39;</span><span class=p>)</span>
</code></pre></div> </details> <details> <summary>case1</summary> <div class=highlight><pre><span></span><code><span class=kn>from</span> <span class=nn>calibrator_custom</span> <span class=kn>import</span> <span class=n>sgs_chalk</span>
<span class=kn>import</span> <span class=nn>numpy</span> <span class=k>as</span> <span class=nn>np</span>
<span class=n>in0</span> <span class=o>=</span> <span class=n>sgs_chalk</span><span class=o>.</span><span class=n>Input</span><span class=p>((</span><span class=mi>28</span><span class=p>,</span><span class=mi>512</span><span class=p>),</span><span class=n>datatype</span><span class=o>=</span><span class=s1>&#39;complex64&#39;</span><span class=p>,</span> <span class=n>name</span><span class=o>=</span><span class=s1>&#39;input0&#39;</span><span class=p>)</span>
</code></pre></div> </details> <ul> <li>LSTM:</li> </ul> <p>Applies a multi-layer long short-term memory (LSTM) RNN to an input sequence.</p> <p>Args: <div class=highlight><pre><span></span><code>x: A `Tensor`,shape is (L,1,N,input_size)  N=batch size  L=sequence length
h0:tensor of shape(1,1,1,hidden_size),containing the initial hidden state for each element in the input sequence.
c0:tensor of shape(1,1,1,hidden_size),containing the initial cell state for each element in the input sequence.
hidden_size : The number of features in the hidden state h
cn_output: whether output cn ,default false.
W: A `Tensor`, shape is [1,4*hidden_size,input_size]
R: A `Tensor`, shape is [1,4*hidden_size,hidden_size]
B: A `Tensor`, shape is [1,8*hidden_size]
</code></pre></div></p> <p>Returns: <div class=highlight><pre><span></span><code>A `Tensor` list,usually concatenate op should be add after LSTM
</code></pre></div></p> <p>Examples:</p> <details> <summary>case0</summary> <div class=highlight><pre><span></span><code><span class=kn>from</span> <span class=nn>calibrator_custom</span> <span class=kn>import</span> <span class=n>sgs_chalk</span>
<span class=kn>import</span> <span class=nn>numpy</span> <span class=k>as</span> <span class=nn>np</span>

<span class=n>W_1</span> <span class=o>=</span> <span class=n>np</span><span class=o>.</span><span class=n>ones</span><span class=p>((</span><span class=mi>1</span><span class=p>,</span><span class=mi>128</span><span class=p>,</span><span class=mi>48</span><span class=p>))</span>
<span class=n>R_1</span> <span class=o>=</span> <span class=n>np</span><span class=o>.</span><span class=n>ones</span><span class=p>((</span><span class=mi>1</span><span class=p>,</span><span class=mi>128</span><span class=p>,</span><span class=mi>32</span><span class=p>))</span>
<span class=n>B_1</span> <span class=o>=</span> <span class=n>np</span><span class=o>.</span><span class=n>ones</span><span class=p>((</span><span class=mi>1</span><span class=p>,</span><span class=mi>256</span><span class=p>))</span>

<span class=n>W_2</span> <span class=o>=</span> <span class=n>np</span><span class=o>.</span><span class=n>ones</span><span class=p>((</span><span class=mi>1</span><span class=p>,</span><span class=mi>64</span><span class=p>,</span><span class=mi>32</span><span class=p>))</span>
<span class=n>R_2</span> <span class=o>=</span> <span class=n>np</span><span class=o>.</span><span class=n>ones</span><span class=p>((</span><span class=mi>1</span><span class=p>,</span><span class=mi>64</span><span class=p>,</span><span class=mi>16</span><span class=p>))</span>
<span class=n>B_2</span> <span class=o>=</span> <span class=n>np</span><span class=o>.</span><span class=n>ones</span><span class=p>((</span><span class=mi>1</span><span class=p>,</span><span class=mi>128</span><span class=p>))</span>

<span class=n>in0</span> <span class=o>=</span> <span class=n>sgs_chalk</span><span class=o>.</span><span class=n>Input</span><span class=p>((</span><span class=mi>2</span><span class=p>,</span><span class=mi>1</span><span class=p>,</span><span class=mi>1</span><span class=p>,</span><span class=mi>48</span><span class=p>),</span><span class=n>name</span><span class=o>=</span><span class=s1>&#39;input0&#39;</span><span class=p>)</span>
<span class=n>input1</span> <span class=o>=</span> <span class=n>np</span><span class=o>.</span><span class=n>zeros</span><span class=p>((</span><span class=mi>1</span><span class=p>,</span><span class=mi>1</span><span class=p>,</span><span class=mi>1</span><span class=p>,</span><span class=mi>32</span><span class=p>),</span> <span class=n>dtype</span><span class=o>=</span><span class=n>np</span><span class=o>.</span><span class=n>float32</span><span class=p>)</span>
<span class=n>input2</span> <span class=o>=</span> <span class=n>np</span><span class=o>.</span><span class=n>zeros</span><span class=p>((</span><span class=mi>1</span><span class=p>,</span><span class=mi>1</span><span class=p>,</span><span class=mi>1</span><span class=p>,</span><span class=mi>32</span><span class=p>),</span> <span class=n>dtype</span><span class=o>=</span><span class=n>np</span><span class=o>.</span><span class=n>float32</span><span class=p>)</span>
<span class=n>input3</span> <span class=o>=</span> <span class=n>np</span><span class=o>.</span><span class=n>zeros</span><span class=p>((</span><span class=mi>1</span><span class=p>,</span><span class=mi>1</span><span class=p>,</span><span class=mi>1</span><span class=p>,</span><span class=mi>16</span><span class=p>),</span> <span class=n>dtype</span><span class=o>=</span><span class=n>np</span><span class=o>.</span><span class=n>float32</span><span class=p>)</span>
<span class=n>input4</span> <span class=o>=</span> <span class=n>np</span><span class=o>.</span><span class=n>zeros</span><span class=p>((</span><span class=mi>1</span><span class=p>,</span><span class=mi>1</span><span class=p>,</span><span class=mi>1</span><span class=p>,</span><span class=mi>16</span><span class=p>),</span> <span class=n>dtype</span><span class=o>=</span><span class=n>np</span><span class=o>.</span><span class=n>float32</span><span class=p>)</span>
<span class=n>output_tensor_list</span> <span class=o>=</span> <span class=n>sgs_chalk</span><span class=o>.</span><span class=n>LSTM</span><span class=p>(</span><span class=n>in0</span><span class=p>,</span><span class=n>input1</span><span class=p>,</span><span class=n>input2</span><span class=p>,</span><span class=n>hidden_size</span><span class=o>=</span><span class=mi>32</span><span class=p>,</span><span class=n>W</span><span class=o>=</span><span class=n>W_1</span><span class=p>,</span><span class=n>R</span><span class=o>=</span><span class=n>R_1</span><span class=p>,</span><span class=n>B</span><span class=o>=</span><span class=n>B_1</span><span class=p>,</span><span class=n>name</span><span class=o>=</span><span class=p>[</span><span class=s1>&#39;lstm_output0&#39;</span><span class=p>,</span><span class=s1>&#39;lstm_output1&#39;</span><span class=p>])</span>
<span class=n>out_1</span> <span class=o>=</span> <span class=n>sgs_chalk</span><span class=o>.</span><span class=n>Concatenation</span><span class=p>([</span><span class=n>output_tensor_list</span><span class=p>[</span><span class=mi>0</span><span class=p>],</span><span class=n>output_tensor_list</span><span class=p>[</span><span class=mi>1</span><span class=p>]],</span><span class=n>axis</span><span class=o>=</span><span class=mi>0</span><span class=p>,</span><span class=n>name</span><span class=o>=</span><span class=s2>&quot;output0&quot;</span><span class=p>)</span>

<span class=n>output_tensor_list_1</span> <span class=o>=</span> <span class=n>sgs_chalk</span><span class=o>.</span><span class=n>LSTM</span><span class=p>(</span><span class=n>out_1</span><span class=p>,</span><span class=n>input3</span><span class=p>,</span><span class=n>input4</span><span class=p>,</span><span class=n>hidden_size</span><span class=o>=</span><span class=mi>16</span><span class=p>,</span><span class=n>W</span><span class=o>=</span><span class=n>W_2</span><span class=p>,</span><span class=n>R</span><span class=o>=</span><span class=n>R_2</span><span class=p>,</span><span class=n>B</span><span class=o>=</span><span class=n>B_2</span><span class=p>,</span><span class=n>name</span><span class=o>=</span><span class=p>[</span><span class=s1>&#39;lstm1_output0&#39;</span><span class=p>,</span><span class=s1>&#39;lstm1_output1&#39;</span><span class=p>])</span>
<span class=n>out_2</span> <span class=o>=</span> <span class=n>sgs_chalk</span><span class=o>.</span><span class=n>Concatenation</span><span class=p>(</span><span class=n>output_tensor_list_1</span><span class=p>,</span><span class=n>axis</span><span class=o>=</span><span class=mi>0</span><span class=p>,</span><span class=n>name</span><span class=o>=</span><span class=s2>&quot;output0_0&quot;</span><span class=p>)</span>
<span class=n>model</span> <span class=o>=</span> <span class=n>sgs_chalk</span><span class=o>.</span><span class=n>Model</span><span class=p>([</span><span class=n>in0</span><span class=p>],</span><span class=n>out_2</span><span class=p>)</span>
<span class=n>model</span><span class=o>.</span><span class=n>save</span><span class=p>(</span><span class=s1>&#39;test.sim&#39;</span><span class=p>,</span><span class=n>input_config</span><span class=o>=</span><span class=s1>&#39;./input_config.ini&#39;</span><span class=p>,</span> <span class=n>convert_fixed</span><span class=o>=</span><span class=kc>True</span><span class=p>,</span> <span class=n>inputs</span><span class=o>=</span><span class=s1>&#39;RAWDATA&#39;</span><span class=p>)</span>
</code></pre></div> </details> <details> <summary>case1</summary> <div class=highlight><pre><span></span><code><span class=kn>from</span> <span class=nn>calibrator_custom</span> <span class=kn>import</span> <span class=n>sgs_chalk</span>
<span class=kn>import</span> <span class=nn>numpy</span> <span class=k>as</span> <span class=nn>np</span>

<span class=n>W_1</span> <span class=o>=</span> <span class=n>np</span><span class=o>.</span><span class=n>ones</span><span class=p>((</span><span class=mi>1</span><span class=p>,</span><span class=mi>128</span><span class=p>,</span><span class=mi>48</span><span class=p>))</span>
<span class=n>R_1</span> <span class=o>=</span> <span class=n>np</span><span class=o>.</span><span class=n>ones</span><span class=p>((</span><span class=mi>1</span><span class=p>,</span><span class=mi>128</span><span class=p>,</span><span class=mi>32</span><span class=p>))</span>
<span class=n>B_1</span> <span class=o>=</span> <span class=n>np</span><span class=o>.</span><span class=n>ones</span><span class=p>((</span><span class=mi>1</span><span class=p>,</span><span class=mi>256</span><span class=p>))</span>

<span class=n>in0</span> <span class=o>=</span> <span class=n>sgs_chalk</span><span class=o>.</span><span class=n>Input</span><span class=p>((</span><span class=mi>2</span><span class=p>,</span><span class=mi>1</span><span class=p>,</span><span class=mi>1</span><span class=p>,</span><span class=mi>48</span><span class=p>),</span><span class=n>name</span><span class=o>=</span><span class=s1>&#39;input0&#39;</span><span class=p>)</span>
<span class=n>input1</span> <span class=o>=</span> <span class=n>np</span><span class=o>.</span><span class=n>zeros</span><span class=p>((</span><span class=mi>1</span><span class=p>,</span><span class=mi>1</span><span class=p>,</span><span class=mi>1</span><span class=p>,</span><span class=mi>32</span><span class=p>),</span> <span class=n>dtype</span><span class=o>=</span><span class=n>np</span><span class=o>.</span><span class=n>float32</span><span class=p>)</span>
<span class=n>input2</span> <span class=o>=</span> <span class=n>np</span><span class=o>.</span><span class=n>zeros</span><span class=p>((</span><span class=mi>1</span><span class=p>,</span><span class=mi>1</span><span class=p>,</span><span class=mi>1</span><span class=p>,</span><span class=mi>32</span><span class=p>),</span> <span class=n>dtype</span><span class=o>=</span><span class=n>np</span><span class=o>.</span><span class=n>float32</span><span class=p>)</span>
<span class=n>output_tensor_list</span><span class=o>=</span> <span class=n>sgs_chalk</span><span class=o>.</span><span class=n>LSTM</span><span class=p>(</span><span class=n>in0</span><span class=p>,</span><span class=n>input1</span><span class=p>,</span><span class=n>input2</span><span class=p>,</span><span class=n>hidden_size</span><span class=o>=</span><span class=mi>32</span><span class=p>,</span><span class=n>cn_output</span><span class=o>=</span><span class=kc>True</span><span class=p>,</span><span class=n>W</span><span class=o>=</span><span class=n>W_1</span><span class=p>,</span><span class=n>R</span><span class=o>=</span><span class=n>R_1</span><span class=p>,</span><span class=n>B</span><span class=o>=</span><span class=n>B_1</span><span class=p>,</span><span class=n>name</span><span class=o>=</span><span class=p>[</span><span class=s1>&#39;lstm_output0&#39;</span><span class=p>,</span><span class=s1>&#39;lstm_output1&#39;</span><span class=p>])</span>
<span class=n>out_1</span> <span class=o>=</span> <span class=n>sgs_chalk</span><span class=o>.</span><span class=n>Concatenation</span><span class=p>([</span><span class=n>output_tensor_list</span><span class=p>[</span><span class=mi>0</span><span class=p>],</span><span class=n>output_tensor_list</span><span class=p>[</span><span class=mi>1</span><span class=p>]],</span><span class=n>axis</span><span class=o>=</span><span class=mi>0</span><span class=p>,</span><span class=n>name</span><span class=o>=</span><span class=s2>&quot;output0&quot;</span><span class=p>)</span>
<span class=n>model</span> <span class=o>=</span> <span class=n>sgs_chalk</span><span class=o>.</span><span class=n>Model</span><span class=p>([</span><span class=n>in0</span><span class=p>],[</span><span class=n>out_1</span><span class=p>,</span><span class=n>output_tensor_list</span><span class=p>[</span><span class=mi>2</span><span class=p>]])</span>
<span class=n>model</span><span class=o>.</span><span class=n>save</span><span class=p>(</span><span class=s1>&#39;test.sim&#39;</span><span class=p>,</span><span class=n>input_config</span><span class=o>=</span><span class=s1>&#39;./input_config.ini&#39;</span><span class=p>,</span> <span class=n>convert_fixed</span><span class=o>=</span><span class=kc>True</span><span class=p>,</span> <span class=n>inputs</span><span class=o>=</span><span class=s1>&#39;RAWDATA&#39;</span><span class=p>)</span>
</code></pre></div> </details> <ul> <li>LeakyRelu:</li> </ul> <p>Compute the Leaky ReLU activation function.</p> <p>Args: <div class=highlight><pre><span></span><code>tensor: A `Tensor`.
alpha: Slope of the activation function at x &lt; 0.
name: A name for the output tensor (optional).
</code></pre></div></p> <p>Returns: <div class=highlight><pre><span></span><code>A `Tensor`. Has the same type as `tensor`.
</code></pre></div></p> <p>Examples:</p> <details> <summary>case0</summary> <div class=highlight><pre><span></span><code><span class=kn>from</span> <span class=nn>calibrator_custom</span> <span class=kn>import</span> <span class=n>sgs_chalk</span>
<span class=kn>import</span> <span class=nn>numpy</span> <span class=k>as</span> <span class=nn>np</span>
<span class=n>in0</span> <span class=o>=</span> <span class=n>sgs_chalk</span><span class=o>.</span><span class=n>Input</span><span class=p>((</span><span class=mi>28</span><span class=p>,</span><span class=mi>512</span><span class=p>),</span><span class=n>name</span><span class=o>=</span><span class=s1>&#39;input0&#39;</span><span class=p>)</span>
<span class=n>out0</span> <span class=o>=</span> <span class=n>sgs_chalk</span><span class=o>.</span><span class=n>LeakyRelu</span><span class=p>(</span><span class=n>in0</span><span class=p>,</span><span class=n>name</span><span class=o>=</span><span class=s1>&#39;output&#39;</span><span class=p>)</span>
<span class=n>model</span> <span class=o>=</span> <span class=n>sgs_chalk</span><span class=o>.</span><span class=n>Model</span><span class=p>([</span><span class=n>in0</span><span class=p>],</span><span class=n>out0</span><span class=p>)</span>
<span class=n>model</span><span class=o>.</span><span class=n>save</span><span class=p>(</span><span class=s1>&#39;test.sim&#39;</span><span class=p>)</span>
</code></pre></div> </details> <ul> <li>Less:</li> </ul> <p>greater(x, y, name=None) Returns the truth value of (x &lt; y) element-wise.</p> <p>Args: <div class=highlight><pre><span></span><code>x: A `Tensor`.
y: A `Tensor`. Must have the same type as `x`.
name: A name for the output tensor (optional).
</code></pre></div></p> <p>Returns: <div class=highlight><pre><span></span><code>A `Tensor` of type `bool`.
</code></pre></div></p> <p>Examples:</p> <details> <summary>case0</summary> <div class=highlight><pre><span></span><code><span class=kn>from</span> <span class=nn>calibrator_custom</span> <span class=kn>import</span> <span class=n>sgs_chalk</span>
<span class=kn>import</span> <span class=nn>numpy</span> <span class=k>as</span> <span class=nn>np</span>
<span class=n>in0</span> <span class=o>=</span> <span class=n>sgs_chalk</span><span class=o>.</span><span class=n>Input</span><span class=p>((</span><span class=mi>28</span><span class=p>,</span><span class=mi>512</span><span class=p>),</span><span class=n>name</span><span class=o>=</span><span class=s1>&#39;input0&#39;</span><span class=p>)</span>
<span class=n>in1</span> <span class=o>=</span> <span class=n>sgs_chalk</span><span class=o>.</span><span class=n>Input</span><span class=p>((</span><span class=mi>28</span><span class=p>,</span><span class=mi>512</span><span class=p>),</span><span class=n>name</span><span class=o>=</span><span class=s1>&#39;input1&#39;</span><span class=p>)</span>
<span class=n>out0</span> <span class=o>=</span> <span class=n>sgs_chalk</span><span class=o>.</span><span class=n>Less</span><span class=p>(</span><span class=n>in0</span><span class=p>,</span><span class=n>in1</span><span class=p>,</span><span class=n>name</span><span class=o>=</span><span class=s1>&#39;output&#39;</span><span class=p>)</span>
<span class=n>model</span> <span class=o>=</span> <span class=n>sgs_chalk</span><span class=o>.</span><span class=n>Model</span><span class=p>([</span><span class=n>in0</span><span class=p>,</span><span class=n>in1</span><span class=p>],</span><span class=n>out0</span><span class=p>)</span>
<span class=n>model</span><span class=o>.</span><span class=n>save</span><span class=p>(</span><span class=s1>&#39;test.sim&#39;</span><span class=p>)</span>
</code></pre></div> </details> <details> <summary>case1</summary> <div class=highlight><pre><span></span><code><span class=kn>from</span> <span class=nn>calibrator_custom</span> <span class=kn>import</span> <span class=n>sgs_chalk</span>
<span class=kn>import</span> <span class=nn>numpy</span> <span class=k>as</span> <span class=nn>np</span>
<span class=n>in0</span> <span class=o>=</span> <span class=n>sgs_chalk</span><span class=o>.</span><span class=n>Input</span><span class=p>((</span><span class=mi>28</span><span class=p>,</span><span class=mi>512</span><span class=p>),</span><span class=n>name</span><span class=o>=</span><span class=s1>&#39;input0&#39;</span><span class=p>)</span>
<span class=n>out0</span> <span class=o>=</span> <span class=n>sgs_chalk</span><span class=o>.</span><span class=n>Less</span><span class=p>(</span><span class=n>in0</span><span class=p>,</span><span class=mf>5.0</span><span class=p>,</span><span class=n>name</span><span class=o>=</span><span class=s1>&#39;output&#39;</span><span class=p>)</span>
<span class=n>model</span> <span class=o>=</span> <span class=n>sgs_chalk</span><span class=o>.</span><span class=n>Model</span><span class=p>([</span><span class=n>in0</span><span class=p>],</span><span class=n>out0</span><span class=p>)</span>
<span class=n>model</span><span class=o>.</span><span class=n>save</span><span class=p>(</span><span class=s1>&#39;test.sim&#39;</span><span class=p>)</span>
</code></pre></div> </details> <details> <summary>case2</summary> <div class=highlight><pre><span></span><code><span class=kn>from</span> <span class=nn>calibrator_custom</span> <span class=kn>import</span> <span class=n>sgs_chalk</span>
<span class=kn>import</span> <span class=nn>numpy</span> <span class=k>as</span> <span class=nn>np</span>
<span class=n>input1</span> <span class=o>=</span> <span class=n>np</span><span class=o>.</span><span class=n>zeros</span><span class=p>((</span><span class=mi>28</span><span class=p>,</span><span class=mi>512</span><span class=p>),</span> <span class=n>dtype</span><span class=o>=</span><span class=n>np</span><span class=o>.</span><span class=n>float32</span><span class=p>)</span>
<span class=n>in0</span> <span class=o>=</span> <span class=n>sgs_chalk</span><span class=o>.</span><span class=n>Input</span><span class=p>((</span><span class=mi>28</span><span class=p>,</span><span class=mi>512</span><span class=p>),</span><span class=n>name</span><span class=o>=</span><span class=s1>&#39;input0&#39;</span><span class=p>)</span>
<span class=n>out0</span> <span class=o>=</span> <span class=n>sgs_chalk</span><span class=o>.</span><span class=n>Less</span><span class=p>(</span><span class=n>in0</span><span class=p>,</span><span class=n>input1</span><span class=p>,</span><span class=n>name</span><span class=o>=</span><span class=s1>&#39;output&#39;</span><span class=p>)</span>
<span class=n>model</span> <span class=o>=</span> <span class=n>sgs_chalk</span><span class=o>.</span><span class=n>Model</span><span class=p>([</span><span class=n>in0</span><span class=p>],</span><span class=n>out0</span><span class=p>)</span>
<span class=n>model</span><span class=o>.</span><span class=n>save</span><span class=p>(</span><span class=s1>&#39;test.sim&#39;</span><span class=p>)</span>
</code></pre></div> </details> <ul> <li>Log:</li> </ul> <p>Args: <div class=highlight><pre><span></span><code>tensor: A `Tensor`.
name: A name for the output tensor (optional).
</code></pre></div></p> <p>Returns: <div class=highlight><pre><span></span><code>A `Tensor`. Has the same type as `tensor`.
</code></pre></div></p> <p>Examples:</p> <details> <summary>case0</summary> <div class=highlight><pre><span></span><code><span class=kn>from</span> <span class=nn>calibrator_custom</span> <span class=kn>import</span> <span class=n>sgs_chalk</span>
<span class=kn>import</span> <span class=nn>numpy</span> <span class=k>as</span> <span class=nn>np</span>
<span class=n>in0</span> <span class=o>=</span> <span class=n>sgs_chalk</span><span class=o>.</span><span class=n>Input</span><span class=p>((</span><span class=mi>28</span><span class=p>,</span><span class=mi>512</span><span class=p>),</span><span class=n>name</span><span class=o>=</span><span class=s1>&#39;input0&#39;</span><span class=p>)</span>
<span class=n>out0</span> <span class=o>=</span> <span class=n>sgs_chalk</span><span class=o>.</span><span class=n>Log</span><span class=p>(</span><span class=n>in0</span><span class=p>,</span><span class=n>name</span><span class=o>=</span><span class=s1>&#39;output&#39;</span><span class=p>)</span>
<span class=n>model</span> <span class=o>=</span> <span class=n>sgs_chalk</span><span class=o>.</span><span class=n>Model</span><span class=p>([</span><span class=n>in0</span><span class=p>],</span><span class=n>out0</span><span class=p>)</span>
<span class=n>model</span><span class=o>.</span><span class=n>save</span><span class=p>(</span><span class=s1>&#39;test.sim&#39;</span><span class=p>)</span>
</code></pre></div> </details> <ul> <li>LogicalAnd:</li> </ul> <p>Returns the truth value of x AND y element-wise.</p> <p>Args: <div class=highlight><pre><span></span><code>x: A `Tensor`. Must be Variable Tensor.
y: A `Tensor` or `numpy.ndarray`. Must have the same type as `x`, can be Variable or Const Tensor.
   Support inner most dimension broadcasting.
name: A name for the output tensor (optional).sss
</code></pre></div></p> <p>Returns: <div class=highlight><pre><span></span><code>A Variable `Tensor`. Has the same shape as `x`.Tensor` of type `bool
</code></pre></div></p> <p>Examples:</p> <details> <summary>case0</summary> <div class=highlight><pre><span></span><code><span class=kn>from</span> <span class=nn>calibrator_custom</span> <span class=kn>import</span> <span class=n>sgs_chalk</span>
<span class=kn>import</span> <span class=nn>numpy</span> <span class=k>as</span> <span class=nn>np</span>
<span class=n>in0</span> <span class=o>=</span> <span class=n>sgs_chalk</span><span class=o>.</span><span class=n>Input</span><span class=p>((</span><span class=mi>28</span><span class=p>,</span><span class=mi>512</span><span class=p>),</span><span class=n>name</span><span class=o>=</span><span class=s1>&#39;input0&#39;</span><span class=p>)</span>
<span class=n>in1</span> <span class=o>=</span> <span class=n>sgs_chalk</span><span class=o>.</span><span class=n>Input</span><span class=p>((</span><span class=mi>28</span><span class=p>,</span><span class=mi>512</span><span class=p>),</span><span class=n>name</span><span class=o>=</span><span class=s1>&#39;input1&#39;</span><span class=p>)</span>
<span class=n>out0</span> <span class=o>=</span> <span class=n>sgs_chalk</span><span class=o>.</span><span class=n>LogicalAnd</span><span class=p>(</span><span class=n>in0</span><span class=p>,</span><span class=n>in1</span><span class=p>,</span><span class=n>name</span><span class=o>=</span><span class=s1>&#39;output&#39;</span><span class=p>)</span>
<span class=n>model</span> <span class=o>=</span> <span class=n>sgs_chalk</span><span class=o>.</span><span class=n>Model</span><span class=p>([</span><span class=n>in0</span><span class=p>,</span><span class=n>in1</span><span class=p>],</span><span class=n>out0</span><span class=p>)</span>
<span class=n>model</span><span class=o>.</span><span class=n>save</span><span class=p>(</span><span class=s1>&#39;test.sim&#39;</span><span class=p>)</span>
</code></pre></div> </details> <details> <summary>case1</summary> <div class=highlight><pre><span></span><code><span class=kn>from</span> <span class=nn>calibrator_custom</span> <span class=kn>import</span> <span class=n>sgs_chalk</span>
<span class=kn>import</span> <span class=nn>numpy</span> <span class=k>as</span> <span class=nn>np</span>
<span class=n>in0</span> <span class=o>=</span> <span class=n>sgs_chalk</span><span class=o>.</span><span class=n>Input</span><span class=p>((</span><span class=mi>28</span><span class=p>,</span><span class=mi>512</span><span class=p>),</span><span class=n>name</span><span class=o>=</span><span class=s1>&#39;input0&#39;</span><span class=p>)</span>
<span class=n>out0</span> <span class=o>=</span> <span class=n>sgs_chalk</span><span class=o>.</span><span class=n>LogicalAnd</span><span class=p>(</span><span class=n>in0</span><span class=p>,</span><span class=mf>1.0</span><span class=p>,</span><span class=n>name</span><span class=o>=</span><span class=s1>&#39;output&#39;</span><span class=p>)</span>
<span class=n>model</span> <span class=o>=</span> <span class=n>sgs_chalk</span><span class=o>.</span><span class=n>Model</span><span class=p>([</span><span class=n>in0</span><span class=p>],</span><span class=n>out0</span><span class=p>)</span>
<span class=n>model</span><span class=o>.</span><span class=n>save</span><span class=p>(</span><span class=s1>&#39;test.sim&#39;</span><span class=p>)</span>
</code></pre></div> </details> <details> <summary>case2</summary> <div class=highlight><pre><span></span><code><span class=kn>from</span> <span class=nn>calibrator_custom</span> <span class=kn>import</span> <span class=n>sgs_chalk</span>
<span class=kn>import</span> <span class=nn>numpy</span> <span class=k>as</span> <span class=nn>np</span>
<span class=n>input1</span> <span class=o>=</span> <span class=n>np</span><span class=o>.</span><span class=n>zeros</span><span class=p>((</span><span class=mi>28</span><span class=p>,</span><span class=mi>512</span><span class=p>),</span> <span class=n>dtype</span><span class=o>=</span><span class=n>np</span><span class=o>.</span><span class=n>float32</span><span class=p>)</span>
<span class=n>in0</span> <span class=o>=</span> <span class=n>sgs_chalk</span><span class=o>.</span><span class=n>Input</span><span class=p>((</span><span class=mi>28</span><span class=p>,</span><span class=mi>512</span><span class=p>),</span><span class=n>name</span><span class=o>=</span><span class=s1>&#39;input0&#39;</span><span class=p>)</span>
<span class=n>out0</span> <span class=o>=</span> <span class=n>sgs_chalk</span><span class=o>.</span><span class=n>LogicalAnd</span><span class=p>(</span><span class=n>in0</span><span class=p>,</span><span class=n>input1</span><span class=p>,</span><span class=n>name</span><span class=o>=</span><span class=s1>&#39;output&#39;</span><span class=p>)</span>
<span class=n>model</span> <span class=o>=</span> <span class=n>sgs_chalk</span><span class=o>.</span><span class=n>Model</span><span class=p>([</span><span class=n>in0</span><span class=p>],</span><span class=n>out0</span><span class=p>)</span>
<span class=n>model</span><span class=o>.</span><span class=n>save</span><span class=p>(</span><span class=s1>&#39;test.sim&#39;</span><span class=p>)</span>
</code></pre></div> </details> <ul> <li>LogicalNot:</li> </ul> <p>Returns the truth value of NOT x element-wise.</p> <p>Args: <div class=highlight><pre><span></span><code>x: A `Tensor`. Must be Variable Tensor.
name: A name for the output tensor (optional).
</code></pre></div></p> <p>Returns: <div class=highlight><pre><span></span><code>A Variable `Tensor`. Has the same shape as `x`.Tensor` of type `bool
</code></pre></div></p> <p>Examples:</p> <details> <summary>case0</summary> <div class=highlight><pre><span></span><code><span class=kn>from</span> <span class=nn>calibrator_custom</span> <span class=kn>import</span> <span class=n>sgs_chalk</span>
<span class=kn>import</span> <span class=nn>numpy</span> <span class=k>as</span> <span class=nn>np</span>
<span class=n>in0</span> <span class=o>=</span> <span class=n>sgs_chalk</span><span class=o>.</span><span class=n>Input</span><span class=p>((</span><span class=mi>28</span><span class=p>,</span><span class=mi>512</span><span class=p>),</span><span class=n>name</span><span class=o>=</span><span class=s1>&#39;input0&#39;</span><span class=p>)</span>
<span class=n>out0</span> <span class=o>=</span> <span class=n>sgs_chalk</span><span class=o>.</span><span class=n>LogicalNot</span><span class=p>(</span><span class=n>in0</span><span class=p>,</span><span class=n>name</span><span class=o>=</span><span class=s1>&#39;output&#39;</span><span class=p>)</span>
<span class=n>model</span> <span class=o>=</span> <span class=n>sgs_chalk</span><span class=o>.</span><span class=n>Model</span><span class=p>(</span><span class=n>in0</span><span class=p>,</span><span class=n>out0</span><span class=p>)</span>
<span class=n>model</span><span class=o>.</span><span class=n>save</span><span class=p>(</span><span class=s1>&#39;test.sim&#39;</span><span class=p>)</span>
</code></pre></div> </details> <ul> <li>Logistic:</li> </ul> <p><code>output = 1 / (1 + exp(-x))</code></p> <p>Args: <div class=highlight><pre><span></span><code>tensor: A `Tensor`.
name: A name for the output tensor (optional).
</code></pre></div></p> <p>Returns: <div class=highlight><pre><span></span><code>A `Tensor`. Has the same type as `tensor`.
</code></pre></div></p> <p>Examples:</p> <details> <summary>case0</summary> <div class=highlight><pre><span></span><code><span class=kn>from</span> <span class=nn>calibrator_custom</span> <span class=kn>import</span> <span class=n>sgs_chalk</span>
<span class=kn>import</span> <span class=nn>numpy</span> <span class=k>as</span> <span class=nn>np</span>
<span class=n>in0</span> <span class=o>=</span> <span class=n>sgs_chalk</span><span class=o>.</span><span class=n>Input</span><span class=p>((</span><span class=mi>28</span><span class=p>,</span><span class=mi>512</span><span class=p>),</span><span class=n>name</span><span class=o>=</span><span class=s1>&#39;input0&#39;</span><span class=p>)</span>
<span class=n>out0</span> <span class=o>=</span> <span class=n>sgs_chalk</span><span class=o>.</span><span class=n>Logistic</span><span class=p>(</span><span class=n>in0</span><span class=p>,</span><span class=n>name</span><span class=o>=</span><span class=s1>&#39;output&#39;</span><span class=p>)</span>
<span class=n>model</span> <span class=o>=</span> <span class=n>sgs_chalk</span><span class=o>.</span><span class=n>Model</span><span class=p>([</span><span class=n>in0</span><span class=p>],</span><span class=n>out0</span><span class=p>)</span>
<span class=n>model</span><span class=o>.</span><span class=n>save</span><span class=p>(</span><span class=s1>&#39;test.sim&#39;</span><span class=p>)</span>
</code></pre></div> </details> <ul> <li>MaxPool2d:</li> </ul> <p>Performs the max pooling on the input. Each entry in <code>output</code> is the max of the corresponding size <code>ksize</code> window in <code>value</code>.</p> <p>Args: <div class=highlight><pre><span></span><code>x: A 4-D `Tensor` of shape `[batch, height, width, channels]`
ksize: A list of `ints` that has length `2`. The size of
the window for each dimension of the input tensor.
strides: An int or list of `ints` that has length `2`. The
stride of the sliding window for each dimension of the input tensor.
padding: a tuple (paddingTop, paddingBottom, paddingLeft, paddingRight) or a str `SAME`/ `VALID`.
name: Optional name for the operation.
</code></pre></div></p> <p>Returns: <div class=highlight><pre><span></span><code>The max pooled output tensor
</code></pre></div></p> <p>Examples:</p> <details> <summary>case0</summary> <div class=highlight><pre><span></span><code><span class=kn>from</span> <span class=nn>calibrator_custom</span> <span class=kn>import</span> <span class=n>sgs_chalk</span>
<span class=kn>import</span> <span class=nn>numpy</span> <span class=k>as</span> <span class=nn>np</span>
<span class=n>in0</span> <span class=o>=</span> <span class=n>sgs_chalk</span><span class=o>.</span><span class=n>Input</span><span class=p>((</span><span class=mi>1</span><span class=p>,</span><span class=mi>112</span><span class=p>,</span><span class=mi>112</span><span class=p>,</span><span class=mi>3</span><span class=p>),</span><span class=n>name</span><span class=o>=</span><span class=s1>&#39;input0&#39;</span><span class=p>)</span>
<span class=n>out0</span> <span class=o>=</span> <span class=n>sgs_chalk</span><span class=o>.</span><span class=n>MaxPool2d</span><span class=p>(</span><span class=n>in0</span><span class=p>,</span> <span class=p>(</span><span class=mi>2</span><span class=p>,</span><span class=mi>2</span><span class=p>),</span> <span class=n>name</span><span class=o>=</span><span class=s1>&#39;output&#39;</span><span class=p>)</span>
<span class=n>model</span> <span class=o>=</span> <span class=n>sgs_chalk</span><span class=o>.</span><span class=n>Model</span><span class=p>([</span><span class=n>in0</span><span class=p>],</span><span class=n>out0</span><span class=p>)</span>
<span class=n>model</span><span class=o>.</span><span class=n>save</span><span class=p>(</span><span class=s1>&#39;test.sim&#39;</span><span class=p>,</span><span class=n>input_config</span><span class=o>=</span><span class=s1>&#39;./input_config.ini&#39;</span><span class=p>,</span><span class=n>convert_fixed</span><span class=o>=</span><span class=kc>True</span><span class=p>)</span>
</code></pre></div> </details> <ul> <li>Maximum:</li> </ul> <p>Returns the max of x and y (i.e. x &gt; y ? x : y) element-wise.</p> <p>Args: <div class=highlight><pre><span></span><code>x: A `Tensor`. Must be Variable Tensor.
y: A `Tensor` or `numpy.ndarray`. Must have the same type as `x`, can be Variable or Const Tensor.
   Support inner most dimension broadcasting.
name: A name for the output tensor (optional).
</code></pre></div></p> <p>Returns: <div class=highlight><pre><span></span><code>A Variable `Tensor`. Has the same shape as `x`.Tensor` of type `bool
</code></pre></div></p> <p>Examples:</p> <details> <summary>case0</summary> <div class=highlight><pre><span></span><code><span class=kn>from</span> <span class=nn>calibrator_custom</span> <span class=kn>import</span> <span class=n>sgs_chalk</span>
<span class=kn>import</span> <span class=nn>numpy</span> <span class=k>as</span> <span class=nn>np</span>
<span class=n>in0</span> <span class=o>=</span> <span class=n>sgs_chalk</span><span class=o>.</span><span class=n>Input</span><span class=p>((</span><span class=mi>28</span><span class=p>,</span><span class=mi>512</span><span class=p>),</span><span class=n>name</span><span class=o>=</span><span class=s1>&#39;input0&#39;</span><span class=p>)</span>
<span class=n>in1</span> <span class=o>=</span> <span class=n>sgs_chalk</span><span class=o>.</span><span class=n>Input</span><span class=p>((</span><span class=mi>28</span><span class=p>,</span><span class=mi>512</span><span class=p>),</span><span class=n>name</span><span class=o>=</span><span class=s1>&#39;input1&#39;</span><span class=p>)</span>
<span class=n>out0</span> <span class=o>=</span> <span class=n>sgs_chalk</span><span class=o>.</span><span class=n>Maximum</span><span class=p>(</span><span class=n>in0</span><span class=p>,</span><span class=n>in1</span><span class=p>,</span><span class=n>name</span><span class=o>=</span><span class=s1>&#39;output&#39;</span><span class=p>)</span>
<span class=n>model</span> <span class=o>=</span> <span class=n>sgs_chalk</span><span class=o>.</span><span class=n>Model</span><span class=p>([</span><span class=n>in0</span><span class=p>,</span><span class=n>in1</span><span class=p>],</span><span class=n>out0</span><span class=p>)</span>
<span class=n>model</span><span class=o>.</span><span class=n>save</span><span class=p>(</span><span class=s1>&#39;test.sim&#39;</span><span class=p>)</span>
</code></pre></div> </details> <details> <summary>case1</summary> <div class=highlight><pre><span></span><code><span class=kn>from</span> <span class=nn>calibrator_custom</span> <span class=kn>import</span> <span class=n>sgs_chalk</span>
<span class=kn>import</span> <span class=nn>numpy</span> <span class=k>as</span> <span class=nn>np</span>
<span class=n>in0</span> <span class=o>=</span> <span class=n>sgs_chalk</span><span class=o>.</span><span class=n>Input</span><span class=p>((</span><span class=mi>28</span><span class=p>,</span><span class=mi>512</span><span class=p>),</span><span class=n>name</span><span class=o>=</span><span class=s1>&#39;input0&#39;</span><span class=p>)</span>
<span class=n>out0</span> <span class=o>=</span> <span class=n>sgs_chalk</span><span class=o>.</span><span class=n>Maximum</span><span class=p>(</span><span class=n>in0</span><span class=p>,</span><span class=mf>1.0</span><span class=p>,</span><span class=n>name</span><span class=o>=</span><span class=s1>&#39;output&#39;</span><span class=p>)</span>
<span class=n>model</span> <span class=o>=</span> <span class=n>sgs_chalk</span><span class=o>.</span><span class=n>Model</span><span class=p>([</span><span class=n>in0</span><span class=p>],</span><span class=n>out0</span><span class=p>)</span>
<span class=n>model</span><span class=o>.</span><span class=n>save</span><span class=p>(</span><span class=s1>&#39;test.sim&#39;</span><span class=p>)</span>
</code></pre></div> </details> <details> <summary>case2</summary> <div class=highlight><pre><span></span><code><span class=kn>from</span> <span class=nn>calibrator_custom</span> <span class=kn>import</span> <span class=n>sgs_chalk</span>
<span class=kn>import</span> <span class=nn>numpy</span> <span class=k>as</span> <span class=nn>np</span>
<span class=n>input1</span> <span class=o>=</span> <span class=n>np</span><span class=o>.</span><span class=n>zeros</span><span class=p>((</span><span class=mi>28</span><span class=p>,</span><span class=mi>512</span><span class=p>),</span> <span class=n>dtype</span><span class=o>=</span><span class=n>np</span><span class=o>.</span><span class=n>float32</span><span class=p>)</span>
<span class=n>in0</span> <span class=o>=</span> <span class=n>sgs_chalk</span><span class=o>.</span><span class=n>Input</span><span class=p>((</span><span class=mi>28</span><span class=p>,</span><span class=mi>512</span><span class=p>),</span><span class=n>name</span><span class=o>=</span><span class=s1>&#39;input0&#39;</span><span class=p>)</span>
<span class=n>out0</span> <span class=o>=</span> <span class=n>sgs_chalk</span><span class=o>.</span><span class=n>Maximum</span><span class=p>(</span><span class=n>in0</span><span class=p>,</span><span class=n>input1</span><span class=p>,</span><span class=n>name</span><span class=o>=</span><span class=s1>&#39;output&#39;</span><span class=p>)</span>
<span class=n>model</span> <span class=o>=</span> <span class=n>sgs_chalk</span><span class=o>.</span><span class=n>Model</span><span class=p>([</span><span class=n>in0</span><span class=p>],</span><span class=n>out0</span><span class=p>)</span>
<span class=n>model</span><span class=o>.</span><span class=n>save</span><span class=p>(</span><span class=s1>&#39;test.sim&#39;</span><span class=p>)</span>
</code></pre></div> </details> <ul> <li>Mean:</li> </ul> <p>Computes the mean of elements across dimensions of a tensor.</p> <p>Reduces <code>input_tensor</code> along the dimensions given in <code>axis</code>.</p> <p>Args: <div class=highlight><pre><span></span><code>tensor: A `Tensor`.
axis: The dimensions to reduce.
keep_dim:whether keep dim or not
name: A name for the output tensor (optional).
</code></pre></div></p> <p>Returns: <div class=highlight><pre><span></span><code>A `Tensor`. The reduced tensor.
</code></pre></div></p> <p>Examples:</p> <details> <summary>case0</summary> <div class=highlight><pre><span></span><code><span class=kn>from</span> <span class=nn>calibrator_custom</span> <span class=kn>import</span> <span class=n>sgs_chalk</span>
<span class=kn>import</span> <span class=nn>numpy</span> <span class=k>as</span> <span class=nn>np</span>
<span class=n>in0</span> <span class=o>=</span> <span class=n>sgs_chalk</span><span class=o>.</span><span class=n>Input</span><span class=p>((</span><span class=mi>1</span><span class=p>,</span><span class=mi>28</span><span class=p>,</span><span class=mi>28</span><span class=p>,</span><span class=mi>512</span><span class=p>),</span><span class=n>name</span><span class=o>=</span><span class=s1>&#39;input0&#39;</span><span class=p>)</span>
<span class=n>out0</span> <span class=o>=</span> <span class=n>sgs_chalk</span><span class=o>.</span><span class=n>Mean</span><span class=p>(</span><span class=n>in0</span><span class=p>,[</span><span class=mi>1</span><span class=p>,</span><span class=mi>2</span><span class=p>],</span><span class=n>name</span><span class=o>=</span><span class=s1>&#39;output&#39;</span><span class=p>)</span>
<span class=n>model</span> <span class=o>=</span> <span class=n>sgs_chalk</span><span class=o>.</span><span class=n>Model</span><span class=p>([</span><span class=n>in0</span><span class=p>],</span><span class=n>out0</span><span class=p>)</span>
<span class=n>model</span><span class=o>.</span><span class=n>save</span><span class=p>(</span><span class=s1>&#39;test.sim&#39;</span><span class=p>)</span>
</code></pre></div> </details> <ul> <li>Minimum:</li> </ul> <p>Returns the min of x and y (i.e. x &lt; y ? x : y) element-wise.</p> <p>Args: <div class=highlight><pre><span></span><code>x: A `Tensor`. Must be Variable Tensor.
y: A `Tensor` or `numpy.ndarray`. Must have the same type as `x`, can be Variable or Const Tensor.
   Support inner most dimension broadcasting.
name: A name for the output tensor (optional).
</code></pre></div></p> <p>Returns: <div class=highlight><pre><span></span><code>A Variable `Tensor`. Has the same shape as `x`.Tensor` of type `bool
</code></pre></div></p> <p>Examples:</p> <details> <summary>case0</summary> <div class=highlight><pre><span></span><code><span class=kn>from</span> <span class=nn>calibrator_custom</span> <span class=kn>import</span> <span class=n>sgs_chalk</span>
<span class=kn>import</span> <span class=nn>numpy</span> <span class=k>as</span> <span class=nn>np</span>
<span class=n>in0</span> <span class=o>=</span> <span class=n>sgs_chalk</span><span class=o>.</span><span class=n>Input</span><span class=p>((</span><span class=mi>28</span><span class=p>,</span><span class=mi>512</span><span class=p>),</span><span class=n>name</span><span class=o>=</span><span class=s1>&#39;input0&#39;</span><span class=p>)</span>
<span class=n>in1</span> <span class=o>=</span> <span class=n>sgs_chalk</span><span class=o>.</span><span class=n>Input</span><span class=p>((</span><span class=mi>28</span><span class=p>,</span><span class=mi>512</span><span class=p>),</span><span class=n>name</span><span class=o>=</span><span class=s1>&#39;input1&#39;</span><span class=p>)</span>
<span class=n>out0</span> <span class=o>=</span> <span class=n>sgs_chalk</span><span class=o>.</span><span class=n>Minimum</span><span class=p>(</span><span class=n>in0</span><span class=p>,</span><span class=n>in1</span><span class=p>,</span><span class=n>name</span><span class=o>=</span><span class=s1>&#39;output&#39;</span><span class=p>)</span>
<span class=n>model</span> <span class=o>=</span> <span class=n>sgs_chalk</span><span class=o>.</span><span class=n>Model</span><span class=p>([</span><span class=n>in0</span><span class=p>,</span><span class=n>in1</span><span class=p>],</span><span class=n>out0</span><span class=p>)</span>
<span class=n>model</span><span class=o>.</span><span class=n>save</span><span class=p>(</span><span class=s1>&#39;test.sim&#39;</span><span class=p>)</span>
</code></pre></div> </details> <details> <summary>case1</summary> <div class=highlight><pre><span></span><code><span class=kn>from</span> <span class=nn>calibrator_custom</span> <span class=kn>import</span> <span class=n>sgs_chalk</span>
<span class=kn>import</span> <span class=nn>numpy</span> <span class=k>as</span> <span class=nn>np</span>
<span class=n>in0</span> <span class=o>=</span> <span class=n>sgs_chalk</span><span class=o>.</span><span class=n>Input</span><span class=p>((</span><span class=mi>28</span><span class=p>,</span><span class=mi>512</span><span class=p>),</span><span class=n>name</span><span class=o>=</span><span class=s1>&#39;input0&#39;</span><span class=p>)</span>
<span class=n>out0</span> <span class=o>=</span> <span class=n>sgs_chalk</span><span class=o>.</span><span class=n>Minimum</span><span class=p>(</span><span class=n>in0</span><span class=p>,</span><span class=mf>1.0</span><span class=p>,</span><span class=n>name</span><span class=o>=</span><span class=s1>&#39;output&#39;</span><span class=p>)</span>
<span class=n>model</span> <span class=o>=</span> <span class=n>sgs_chalk</span><span class=o>.</span><span class=n>Model</span><span class=p>([</span><span class=n>in0</span><span class=p>],</span><span class=n>out0</span><span class=p>)</span>
<span class=n>model</span><span class=o>.</span><span class=n>save</span><span class=p>(</span><span class=s1>&#39;test.sim&#39;</span><span class=p>)</span>
</code></pre></div> </details> <details> <summary>case2</summary> <div class=highlight><pre><span></span><code><span class=kn>from</span> <span class=nn>calibrator_custom</span> <span class=kn>import</span> <span class=n>sgs_chalk</span>
<span class=kn>import</span> <span class=nn>numpy</span> <span class=k>as</span> <span class=nn>np</span>
<span class=n>input1</span> <span class=o>=</span> <span class=n>np</span><span class=o>.</span><span class=n>zeros</span><span class=p>((</span><span class=mi>28</span><span class=p>,</span><span class=mi>512</span><span class=p>),</span> <span class=n>dtype</span><span class=o>=</span><span class=n>np</span><span class=o>.</span><span class=n>float32</span><span class=p>)</span>
<span class=n>in0</span> <span class=o>=</span> <span class=n>sgs_chalk</span><span class=o>.</span><span class=n>Input</span><span class=p>((</span><span class=mi>28</span><span class=p>,</span><span class=mi>512</span><span class=p>),</span><span class=n>name</span><span class=o>=</span><span class=s1>&#39;input0&#39;</span><span class=p>)</span>
<span class=n>out0</span> <span class=o>=</span> <span class=n>sgs_chalk</span><span class=o>.</span><span class=n>Minimum</span><span class=p>(</span><span class=n>in0</span><span class=p>,</span><span class=n>input1</span><span class=p>,</span><span class=n>name</span><span class=o>=</span><span class=s1>&#39;output&#39;</span><span class=p>)</span>
<span class=n>model</span> <span class=o>=</span> <span class=n>sgs_chalk</span><span class=o>.</span><span class=n>Model</span><span class=p>([</span><span class=n>in0</span><span class=p>],</span><span class=n>out0</span><span class=p>)</span>
<span class=n>model</span><span class=o>.</span><span class=n>save</span><span class=p>(</span><span class=s1>&#39;test.sim&#39;</span><span class=p>)</span>
</code></pre></div> </details> <ul> <li>Mul:</li> </ul> <p>Returns x * y element-wise.</p> <p>Args: <div class=highlight><pre><span></span><code>x: A `Tensor`. Must be Variable Tensor.
y: A `Tensor` or `numpy.ndarray`. Must have the same type as `x`, can be Variable or Const Tensor.
   Support inner most dimension broadcasting.
name: A name for the output tensor (optional).
</code></pre></div></p> <p>Returns: <div class=highlight><pre><span></span><code>A Variable `Tensor`. Has the same shape as `x`.
</code></pre></div></p> <p>Examples:</p> <details> <summary>case0</summary> <div class=highlight><pre><span></span><code><span class=kn>from</span> <span class=nn>calibrator_custom</span> <span class=kn>import</span> <span class=n>sgs_chalk</span>
<span class=kn>import</span> <span class=nn>numpy</span> <span class=k>as</span> <span class=nn>np</span>
<span class=n>in0</span> <span class=o>=</span> <span class=n>sgs_chalk</span><span class=o>.</span><span class=n>Input</span><span class=p>((</span><span class=mi>28</span><span class=p>,</span><span class=mi>512</span><span class=p>),</span><span class=n>name</span><span class=o>=</span><span class=s1>&#39;input0&#39;</span><span class=p>)</span>
<span class=n>in1</span> <span class=o>=</span> <span class=n>sgs_chalk</span><span class=o>.</span><span class=n>Input</span><span class=p>((</span><span class=mi>28</span><span class=p>,</span><span class=mi>512</span><span class=p>),</span><span class=n>name</span><span class=o>=</span><span class=s1>&#39;input1&#39;</span><span class=p>)</span>
<span class=n>out0</span> <span class=o>=</span> <span class=n>sgs_chalk</span><span class=o>.</span><span class=n>Mul</span><span class=p>(</span><span class=n>in0</span><span class=p>,</span><span class=n>in1</span><span class=p>,</span><span class=n>name</span><span class=o>=</span><span class=s1>&#39;output&#39;</span><span class=p>)</span>
<span class=n>model</span> <span class=o>=</span> <span class=n>sgs_chalk</span><span class=o>.</span><span class=n>Model</span><span class=p>([</span><span class=n>in0</span><span class=p>,</span><span class=n>in1</span><span class=p>],</span><span class=n>out0</span><span class=p>)</span>
<span class=n>model</span><span class=o>.</span><span class=n>save</span><span class=p>(</span><span class=s1>&#39;test.sim&#39;</span><span class=p>)</span>


<span class=o>&lt;/</span><span class=n>details</span><span class=o>&gt;</span>

<span class=o>&lt;</span><span class=n>details</span><span class=o>&gt;</span>
<span class=o>&lt;</span><span class=n>summary</span><span class=o>&gt;</span><span class=n>case1</span><span class=o>&lt;/</span><span class=n>summary</span><span class=o>&gt;</span>

<span class=err>```</span><span class=n>python</span>
<span class=kn>from</span> <span class=nn>calibrator_custom</span> <span class=kn>import</span> <span class=n>sgs_chalk</span>
<span class=kn>import</span> <span class=nn>numpy</span> <span class=k>as</span> <span class=nn>np</span>
<span class=n>in0</span> <span class=o>=</span> <span class=n>sgs_chalk</span><span class=o>.</span><span class=n>Input</span><span class=p>((</span><span class=mi>28</span><span class=p>,</span><span class=mi>512</span><span class=p>),</span><span class=n>name</span><span class=o>=</span><span class=s1>&#39;input0&#39;</span><span class=p>)</span>
<span class=n>out0</span> <span class=o>=</span> <span class=n>sgs_chalk</span><span class=o>.</span><span class=n>Mul</span><span class=p>(</span><span class=n>in0</span><span class=p>,</span><span class=mf>5.0</span><span class=p>,</span><span class=n>name</span><span class=o>=</span><span class=s1>&#39;output&#39;</span><span class=p>)</span>
<span class=n>model</span> <span class=o>=</span> <span class=n>sgs_chalk</span><span class=o>.</span><span class=n>Model</span><span class=p>([</span><span class=n>in0</span><span class=p>],</span><span class=n>out0</span><span class=p>)</span>
<span class=n>model</span><span class=o>.</span><span class=n>save</span><span class=p>(</span><span class=s1>&#39;test.sim&#39;</span><span class=p>)</span>
</code></pre></div> </details> <ul> <li>Negative:</li> </ul> <p>Computes the negative value of a tensor.</p> <p>Given a tensor of integer or floating-point values, this operation returns a tensor of the same type, where each element contains the absolute value of the corresponding element in the input.</p> <p>Args: <div class=highlight><pre><span></span><code>x: A `Tensor`
</code></pre></div></p> <p>Returns: <div class=highlight><pre><span></span><code>A `Tensor` of same shape and type as `x`.
</code></pre></div></p> <p>Examples:</p> <details> <summary>case0</summary> <div class=highlight><pre><span></span><code><span class=kn>from</span> <span class=nn>calibrator_custom</span> <span class=kn>import</span> <span class=n>sgs_chalk</span>
<span class=kn>import</span> <span class=nn>numpy</span> <span class=k>as</span> <span class=nn>np</span>
<span class=n>in0</span> <span class=o>=</span> <span class=n>sgs_chalk</span><span class=o>.</span><span class=n>Input</span><span class=p>((</span><span class=mi>28</span><span class=p>,</span><span class=mi>512</span><span class=p>),</span><span class=n>name</span><span class=o>=</span><span class=s1>&#39;input0&#39;</span><span class=p>)</span>
<span class=n>out0</span> <span class=o>=</span> <span class=n>sgs_chalk</span><span class=o>.</span><span class=n>Negative</span><span class=p>(</span><span class=n>in0</span><span class=p>,</span><span class=n>name</span><span class=o>=</span><span class=s1>&#39;output&#39;</span><span class=p>)</span>
<span class=n>model</span> <span class=o>=</span> <span class=n>sgs_chalk</span><span class=o>.</span><span class=n>Model</span><span class=p>([</span><span class=n>in0</span><span class=p>],[</span><span class=n>out0</span><span class=p>])</span>
<span class=n>model</span><span class=o>.</span><span class=n>save</span><span class=p>(</span><span class=s1>&#39;test.sim&#39;</span><span class=p>,</span><span class=n>input_config</span><span class=o>=</span><span class=s1>&#39;./input_config.ini&#39;</span><span class=p>,</span><span class=n>convert_fixed</span><span class=o>=</span><span class=kc>True</span><span class=p>)</span>
</code></pre></div> </details> <ul> <li>NotEqual:</li> </ul> <p>Returns the truth value of (x != y) element-wise.</p> <p>Args: <div class=highlight><pre><span></span><code>x: A `Tensor`. Must be Variable Tensor.
y: A `Tensor` or `numpy.ndarray`. Must have the same type as `x`, can be Variable or Const Tensor.
   Support inner most dimension broadcasting.
name: A name for the output tensor (optional).
</code></pre></div></p> <p>Returns: <div class=highlight><pre><span></span><code>A Variable `Tensor`. Has the same shape as `x`.Tensor` of type `bool
</code></pre></div></p> <p>Examples:</p> <details> <summary>case0</summary> <div class=highlight><pre><span></span><code><span class=kn>from</span> <span class=nn>calibrator_custom</span> <span class=kn>import</span> <span class=n>sgs_chalk</span>
<span class=kn>import</span> <span class=nn>numpy</span> <span class=k>as</span> <span class=nn>np</span>
<span class=n>in0</span> <span class=o>=</span> <span class=n>sgs_chalk</span><span class=o>.</span><span class=n>Input</span><span class=p>((</span><span class=mi>28</span><span class=p>,</span><span class=mi>512</span><span class=p>),</span><span class=n>name</span><span class=o>=</span><span class=s1>&#39;input0&#39;</span><span class=p>)</span>
<span class=n>in1</span> <span class=o>=</span> <span class=n>sgs_chalk</span><span class=o>.</span><span class=n>Input</span><span class=p>((</span><span class=mi>28</span><span class=p>,</span><span class=mi>512</span><span class=p>),</span><span class=n>name</span><span class=o>=</span><span class=s1>&#39;input1&#39;</span><span class=p>)</span>
<span class=n>out0</span> <span class=o>=</span> <span class=n>sgs_chalk</span><span class=o>.</span><span class=n>NotEqual</span><span class=p>(</span><span class=n>in0</span><span class=p>,</span><span class=n>in1</span><span class=p>,</span><span class=n>name</span><span class=o>=</span><span class=s1>&#39;output&#39;</span><span class=p>)</span>
<span class=n>model</span> <span class=o>=</span> <span class=n>sgs_chalk</span><span class=o>.</span><span class=n>Model</span><span class=p>([</span><span class=n>in0</span><span class=p>,</span><span class=n>in1</span><span class=p>],</span><span class=n>out0</span><span class=p>)</span>
<span class=n>model</span><span class=o>.</span><span class=n>save</span><span class=p>(</span><span class=s1>&#39;test.sim&#39;</span><span class=p>)</span>
</code></pre></div> </details> <details> <summary>case1</summary> <div class=highlight><pre><span></span><code><span class=kn>from</span> <span class=nn>calibrator_custom</span> <span class=kn>import</span> <span class=n>sgs_chalk</span>
<span class=kn>import</span> <span class=nn>numpy</span> <span class=k>as</span> <span class=nn>np</span>
<span class=n>in0</span> <span class=o>=</span> <span class=n>sgs_chalk</span><span class=o>.</span><span class=n>Input</span><span class=p>((</span><span class=mi>28</span><span class=p>,</span><span class=mi>512</span><span class=p>),</span><span class=n>name</span><span class=o>=</span><span class=s1>&#39;input0&#39;</span><span class=p>)</span>
<span class=n>out0</span> <span class=o>=</span> <span class=n>sgs_chalk</span><span class=o>.</span><span class=n>NotEqual</span><span class=p>(</span><span class=n>in0</span><span class=p>,</span><span class=mf>5.0</span><span class=p>,</span><span class=n>name</span><span class=o>=</span><span class=s1>&#39;output&#39;</span><span class=p>)</span>
<span class=n>model</span> <span class=o>=</span> <span class=n>sgs_chalk</span><span class=o>.</span><span class=n>Model</span><span class=p>([</span><span class=n>in0</span><span class=p>],</span><span class=n>out0</span><span class=p>)</span>
<span class=n>model</span><span class=o>.</span><span class=n>save</span><span class=p>(</span><span class=s1>&#39;test.sim&#39;</span><span class=p>)</span>
</code></pre></div> </details> <details> <summary>case2</summary> <div class=highlight><pre><span></span><code><span class=kn>from</span> <span class=nn>calibrator_custom</span> <span class=kn>import</span> <span class=n>sgs_chalk</span>
<span class=kn>import</span> <span class=nn>numpy</span> <span class=k>as</span> <span class=nn>np</span>
<span class=n>input1</span> <span class=o>=</span> <span class=n>np</span><span class=o>.</span><span class=n>zeros</span><span class=p>((</span><span class=mi>28</span><span class=p>,</span><span class=mi>512</span><span class=p>),</span> <span class=n>dtype</span><span class=o>=</span><span class=n>np</span><span class=o>.</span><span class=n>float32</span><span class=p>)</span>
<span class=n>in0</span> <span class=o>=</span> <span class=n>sgs_chalk</span><span class=o>.</span><span class=n>Input</span><span class=p>((</span><span class=mi>28</span><span class=p>,</span><span class=mi>512</span><span class=p>),</span><span class=n>name</span><span class=o>=</span><span class=s1>&#39;input0&#39;</span><span class=p>)</span>
<span class=n>out0</span> <span class=o>=</span> <span class=n>sgs_chalk</span><span class=o>.</span><span class=n>NotEqual</span><span class=p>(</span><span class=n>in0</span><span class=p>,</span><span class=n>input1</span><span class=p>,</span><span class=n>name</span><span class=o>=</span><span class=s1>&#39;output&#39;</span><span class=p>)</span>
<span class=n>model</span> <span class=o>=</span> <span class=n>sgs_chalk</span><span class=o>.</span><span class=n>Model</span><span class=p>([</span><span class=n>in0</span><span class=p>],</span><span class=n>out0</span><span class=p>)</span>
<span class=n>model</span><span class=o>.</span><span class=n>save</span><span class=p>(</span><span class=s1>&#39;test.sim&#39;</span><span class=p>)</span>
</code></pre></div> </details> <ul> <li>PSRoiPooling:</li> </ul> <p>RoiPooling</p> <p>Args: <div class=highlight><pre><span></span><code>x: A `Tensor`. Must be Variable Tensor.
RoiInfo: must be two dim, and the  second dim is 5.
kernel_size : output kernel size.
spatial_scale ; A value
name: A name for the output tensor (optional).
</code></pre></div></p> <p>Returns: <div class=highlight><pre><span></span><code>A Variable `Tensor`. shape is [RoiInfo.shape[0],kernel_size[0],kernel_size[1],x.shape[3]]
</code></pre></div></p> <p>Examples:</p> <details> <summary>case0</summary> <div class=highlight><pre><span></span><code><span class=kn>from</span> <span class=nn>calibrator_custom</span> <span class=kn>import</span> <span class=n>sgs_chalk</span>
<span class=kn>import</span> <span class=nn>numpy</span> <span class=k>as</span> <span class=nn>np</span>
<span class=nb>input</span> <span class=o>=</span> <span class=n>sgs_chalk</span><span class=o>.</span><span class=n>Input</span><span class=p>((</span><span class=mi>1</span><span class=p>,</span><span class=mi>49</span><span class=p>,</span><span class=mi>14</span><span class=p>,</span><span class=mi>14</span><span class=p>,</span><span class=mi>21</span><span class=p>),</span> <span class=n>name</span><span class=o>=</span><span class=s1>&#39;input&#39;</span><span class=p>)</span>
<span class=n>RoiInfo</span>  <span class=o>=</span> <span class=n>sgs_chalk</span><span class=o>.</span><span class=n>Input</span><span class=p>((</span><span class=mi>300</span><span class=p>,</span><span class=mi>5</span><span class=p>),</span> <span class=n>name</span><span class=o>=</span><span class=s1>&#39;input0&#39;</span><span class=p>)</span>
<span class=n>output</span> <span class=o>=</span> <span class=n>sgs_chalk</span><span class=o>.</span><span class=n>PSRoiPooling</span><span class=p>(</span><span class=nb>input</span><span class=p>,</span><span class=n>RoiInfo</span><span class=p>,[</span><span class=mi>7</span><span class=p>,</span><span class=mi>7</span><span class=p>],</span> <span class=mf>0.0625</span><span class=p>,</span><span class=n>name</span><span class=o>=</span><span class=s1>&#39;output&#39;</span><span class=p>)</span>
<span class=n>model</span> <span class=o>=</span> <span class=n>sgs_chalk</span><span class=o>.</span><span class=n>Model</span><span class=p>([</span><span class=nb>input</span><span class=p>,</span><span class=n>RoiInfo</span><span class=p>],</span> <span class=n>output</span><span class=p>)</span>
<span class=c1>#model.save(&#39;test.sim&#39;, input_config=&#39;./input_config.ini&#39;, convert_fixed=True)</span>
<span class=n>model</span><span class=o>.</span><span class=n>save</span><span class=p>(</span><span class=s1>&#39;test.sim&#39;</span><span class=p>,</span> <span class=n>input_config</span><span class=o>=</span><span class=s1>&#39;./input_config.ini&#39;</span><span class=p>,</span> <span class=n>convert_fixed</span><span class=o>=</span><span class=kc>True</span><span class=p>,</span><span class=n>inputs</span><span class=o>=</span><span class=s1>&#39;RAWDATA&#39;</span><span class=p>)</span>
</code></pre></div> </details> <ul> <li>Pack:</li> </ul> <p>Stacks a list of rank-<code>R</code> tensors into one rank-<code>(R+1)</code> tensor.</p> <p>Packs the list of tensors in <code>values</code> into a tensor with rank one higher than each tensor in <code>values</code>, by packing them along the <code>axis</code> dimension. Given a list of length <code>N</code> of tensors of shape <code>(A, B, C)</code>;</p> <p>if <code>axis == 0</code> then the <code>output</code> tensor will have the shape <code>(N, A, B, C)</code>. if <code>axis == 1</code> then the <code>output</code> tensor will have the shape <code>(A, N, B, C)</code>. Etc.</p> <p>Args: <div class=highlight><pre><span></span><code>x: A list of `Tensor` objects with the same shape and type. Must be Variable Tensor.
axis: An `int`. The axis to stack along. Defaults to the first dimension.
      Negative values wrap around, so the valid range is `[-(R+1), R+1)`.
name: A name for the output tensor (optional).
</code></pre></div></p> <p>Returns: <div class=highlight><pre><span></span><code>A Variable `Tensor`.
</code></pre></div></p> <p>Examples:</p> <details> <summary>case0</summary> <div class=highlight><pre><span></span><code><span class=kn>from</span> <span class=nn>calibrator_custom</span> <span class=kn>import</span> <span class=n>sgs_chalk</span>
<span class=kn>import</span> <span class=nn>numpy</span> <span class=k>as</span> <span class=nn>np</span>
<span class=n>in0</span> <span class=o>=</span> <span class=n>sgs_chalk</span><span class=o>.</span><span class=n>Input</span><span class=p>((</span><span class=mi>28</span><span class=p>,</span><span class=mi>512</span><span class=p>,</span><span class=mi>1</span><span class=p>),</span><span class=n>name</span><span class=o>=</span><span class=s1>&#39;input0&#39;</span><span class=p>)</span>
<span class=n>in1</span> <span class=o>=</span> <span class=n>sgs_chalk</span><span class=o>.</span><span class=n>Input</span><span class=p>((</span><span class=mi>28</span><span class=p>,</span><span class=mi>512</span><span class=p>,</span><span class=mi>1</span><span class=p>),</span><span class=n>name</span><span class=o>=</span><span class=s1>&#39;input1&#39;</span><span class=p>)</span>
<span class=n>in2</span> <span class=o>=</span> <span class=n>sgs_chalk</span><span class=o>.</span><span class=n>Input</span><span class=p>((</span><span class=mi>28</span><span class=p>,</span><span class=mi>512</span><span class=p>,</span><span class=mi>1</span><span class=p>),</span><span class=n>name</span><span class=o>=</span><span class=s1>&#39;input2&#39;</span><span class=p>)</span>
<span class=n>x</span> <span class=o>=</span> <span class=p>[</span><span class=n>in0</span><span class=p>,</span> <span class=n>in1</span><span class=p>,</span> <span class=n>in2</span><span class=p>]</span>
<span class=n>out0</span> <span class=o>=</span> <span class=n>sgs_chalk</span><span class=o>.</span><span class=n>Pack</span><span class=p>(</span><span class=n>x</span><span class=p>,</span><span class=n>axis</span><span class=o>=</span><span class=mi>2</span><span class=p>,</span><span class=n>name</span><span class=o>=</span><span class=s1>&#39;output&#39;</span><span class=p>)</span>
<span class=n>model</span> <span class=o>=</span> <span class=n>sgs_chalk</span><span class=o>.</span><span class=n>Model</span><span class=p>(</span><span class=n>x</span><span class=p>,</span><span class=n>out0</span><span class=p>)</span>
<span class=n>model</span><span class=o>.</span><span class=n>save</span><span class=p>(</span><span class=s1>&#39;test.sim&#39;</span><span class=p>)</span>
</code></pre></div> </details> <ul> <li>Pad:</li> </ul> <p>Pads a tensor.</p> <p>This operation pads a <code>tensor</code> according to the <code>paddings</code> you specify. <code>paddings</code> is an integer tensor with shape <code>[n, 2]</code>, where n is the rank of <code>tensor</code>. For each dimension D of <code>input</code>, <code>paddings[D, 0]</code> indicates how many values to add before the contents of <code>tensor</code> in that dimension, and <code>paddings[D, 1]</code> indicates how many values to add after the contents of <code>tensor</code> in that dimension.</p> <p>The padded size of each dimension D of the output is: <code>paddings[D, 0] + tensor.dim_size(D) + paddings[D, 1]</code></p> <p>Args: <div class=highlight><pre><span></span><code>tensor: A `Tensor`.
paddings: A `Tensor` of type `int32`.
name: A name for the output tensor (optional).
</code></pre></div></p> <p>Returns: <div class=highlight><pre><span></span><code>A `Tensor`. Has the same type as `tensor`.
</code></pre></div></p> <p>Examples:</p> <details> <summary>case0</summary> <div class=highlight><pre><span></span><code><span class=kn>from</span> <span class=nn>calibrator_custom</span> <span class=kn>import</span> <span class=n>sgs_chalk</span>
<span class=kn>import</span> <span class=nn>numpy</span> <span class=k>as</span> <span class=nn>np</span>
<span class=n>in0</span> <span class=o>=</span> <span class=n>sgs_chalk</span><span class=o>.</span><span class=n>Input</span><span class=p>((</span><span class=mi>28</span><span class=p>,</span><span class=mi>512</span><span class=p>),</span><span class=n>name</span><span class=o>=</span><span class=s1>&#39;input0&#39;</span><span class=p>)</span>
<span class=n>out0</span> <span class=o>=</span> <span class=n>sgs_chalk</span><span class=o>.</span><span class=n>Pad</span><span class=p>(</span><span class=n>in0</span><span class=p>,[[</span><span class=mi>0</span><span class=p>,</span><span class=mi>0</span><span class=p>],[</span><span class=mi>10</span><span class=p>,</span><span class=mi>10</span><span class=p>]],</span><span class=n>name</span><span class=o>=</span><span class=s1>&#39;output&#39;</span><span class=p>)</span>
<span class=n>model</span> <span class=o>=</span> <span class=n>sgs_chalk</span><span class=o>.</span><span class=n>Model</span><span class=p>([</span><span class=n>in0</span><span class=p>],</span><span class=n>out0</span><span class=p>)</span>
<span class=n>model</span><span class=o>.</span><span class=n>save</span><span class=p>(</span><span class=s1>&#39;test.sim&#39;</span><span class=p>)</span>
</code></pre></div> </details> <details> <summary>case1</summary> <div class=highlight><pre><span></span><code><span class=kn>from</span> <span class=nn>calibrator_custom</span> <span class=kn>import</span> <span class=n>sgs_chalk</span>
<span class=kn>import</span> <span class=nn>numpy</span> <span class=k>as</span> <span class=nn>np</span>
<span class=n>in0</span> <span class=o>=</span> <span class=n>sgs_chalk</span><span class=o>.</span><span class=n>Input</span><span class=p>((</span><span class=mi>28</span><span class=p>,</span><span class=mi>512</span><span class=p>,</span><span class=mi>512</span><span class=p>,</span><span class=mi>4</span><span class=p>),</span><span class=n>name</span><span class=o>=</span><span class=s1>&#39;input0&#39;</span><span class=p>)</span>
<span class=n>out0</span> <span class=o>=</span> <span class=n>sgs_chalk</span><span class=o>.</span><span class=n>Pad</span><span class=p>(</span><span class=n>in0</span><span class=p>,[[</span><span class=mi>1</span><span class=p>,</span><span class=mi>1</span><span class=p>],[</span><span class=mi>2</span><span class=p>,</span><span class=mi>2</span><span class=p>],[</span><span class=mi>3</span><span class=p>,</span><span class=mi>3</span><span class=p>],[</span><span class=mi>4</span><span class=p>,</span><span class=mi>4</span><span class=p>]],</span><span class=n>name</span><span class=o>=</span><span class=s1>&#39;output&#39;</span><span class=p>)</span>
<span class=n>model</span> <span class=o>=</span> <span class=n>sgs_chalk</span><span class=o>.</span><span class=n>Model</span><span class=p>([</span><span class=n>in0</span><span class=p>],</span><span class=n>out0</span><span class=p>)</span>
<span class=n>model</span><span class=o>.</span><span class=n>save</span><span class=p>(</span><span class=s1>&#39;test.sim&#39;</span><span class=p>)</span>
</code></pre></div> </details> <ul> <li>PostProcess_Max:</li> </ul> <p>sigmastar postprocess max</p> <p>Args: <div class=highlight><pre><span></span><code>num_classes: number of classes
is_skip_background: please note background must be the first one
</code></pre></div></p> <p>Examples:</p> <details> <summary>case0</summary> <div class=highlight><pre><span></span><code><span class=kn>from</span> <span class=nn>calibrator_custom</span> <span class=kn>import</span> <span class=n>sgs_chalk</span>
<span class=kn>import</span> <span class=nn>numpy</span> <span class=k>as</span> <span class=nn>np</span>
<span class=n>mbox_conf_softmax</span> <span class=o>=</span> <span class=n>sgs_chalk</span><span class=o>.</span><span class=n>Tensor</span><span class=p>(</span><span class=n>shape</span><span class=o>=</span><span class=n>model_config</span><span class=p>[</span><span class=s2>&quot;input_shape&quot;</span><span class=p>][</span><span class=mi>1</span><span class=p>],</span><span class=n>name</span> <span class=o>=</span> <span class=n>model_config</span><span class=p>[</span><span class=s2>&quot;input&quot;</span><span class=p>][</span><span class=mi>1</span><span class=p>])</span>
<span class=n>cus_options</span> <span class=o>=</span> <span class=p>[(</span><span class=sa>b</span><span class=s2>&quot;scores_lengh&quot;</span><span class=p>,</span><span class=mi>21</span><span class=p>,</span><span class=s2>&quot;int&quot;</span><span class=p>),</span>
            <span class=p>(</span><span class=sa>b</span><span class=s2>&quot;skip&quot;</span><span class=p>,</span><span class=mi>1</span><span class=p>,</span><span class=s2>&quot;int&quot;</span><span class=p>)]</span>
<span class=n>postprocess_max_output_list</span> <span class=o>=</span> <span class=n>sgs_chalk</span><span class=o>.</span><span class=n>PostProcess_Max</span><span class=p>(</span><span class=n>mbox_conf_softmax</span><span class=p>,</span><span class=n>num_classes</span><span class=o>=</span><span class=mi>21</span><span class=p>,</span><span class=n>skip</span><span class=o>=</span><span class=mi>1</span><span class=p>)</span>
<span class=n>model</span> <span class=o>=</span> <span class=n>sgs_chalk</span><span class=o>.</span><span class=n>Model</span><span class=p>([</span><span class=n>mbox_conf_softmax</span><span class=p>],</span><span class=n>postprocess_max_output_list</span><span class=p>)</span>
<span class=n>model</span><span class=o>.</span><span class=n>save</span><span class=p>(</span><span class=s1>&#39;test.sim&#39;</span><span class=p>,</span><span class=n>input_config</span><span class=o>=</span><span class=s1>&#39;./input_config.ini&#39;</span><span class=p>,</span><span class=n>convert_fixed</span><span class=o>=</span><span class=kc>True</span><span class=p>)</span>
</code></pre></div> </details> <ul> <li>PostProcess_Unpack:</li> </ul> <p>sigmastar postprocess unpack</p> <p>Args: <div class=highlight><pre><span></span><code>num_classes: number of classes
</code></pre></div></p> <p>Examples:</p> <details> <summary>case0</summary> <div class=highlight><pre><span></span><code><span class=kn>from</span> <span class=nn>calibrator_custom</span> <span class=kn>import</span> <span class=n>sgs_chalk</span>
<span class=kn>import</span> <span class=nn>numpy</span> <span class=k>as</span> <span class=nn>np</span>
<span class=n>in0</span> <span class=o>=</span> <span class=n>sgs_chalk</span><span class=o>.</span><span class=n>Input</span><span class=p>((</span><span class=mi>1</span><span class=p>,</span><span class=mi>3249</span><span class=p>,</span><span class=mi>6</span><span class=p>),</span> <span class=n>name</span> <span class=o>=</span> <span class=s1>&#39;x&#39;</span><span class=p>)</span>
<span class=n>unpack_out_tensors1</span> <span class=o>=</span> <span class=p>[]</span>
<span class=k>for</span> <span class=n>i</span> <span class=ow>in</span> <span class=nb>range</span><span class=p>(</span><span class=mi>4</span><span class=p>):</span>
    <span class=n>unpack_out_tensors1</span><span class=o>.</span><span class=n>append</span><span class=p>(</span><span class=s2>&quot;SGS_unpack1_&quot;</span><span class=o>+</span><span class=nb>str</span><span class=p>(</span><span class=n>i</span><span class=p>))</span>
<span class=n>output_list</span> <span class=o>=</span> <span class=n>sgs_chalk</span><span class=o>.</span><span class=n>PostProcess_Unpack</span><span class=p>(</span><span class=n>in0</span><span class=p>,</span><span class=n>scores_lengh</span><span class=o>=</span><span class=mi>80</span><span class=p>,</span><span class=n>name</span><span class=o>=</span><span class=n>unpack_out_tensors1</span><span class=p>)</span>
<span class=n>model</span> <span class=o>=</span> <span class=n>sgs_chalk</span><span class=o>.</span><span class=n>Model</span><span class=p>([</span><span class=n>in0</span><span class=p>],</span><span class=n>output_list</span><span class=p>)</span>
<span class=c1>#model.save(&#39;test.sim&#39;)</span>
<span class=n>model</span><span class=o>.</span><span class=n>save</span><span class=p>(</span><span class=s1>&#39;test.sim&#39;</span><span class=p>,</span><span class=n>input_config</span><span class=o>=</span><span class=s1>&#39;./input_config.ini&#39;</span><span class=p>,</span><span class=n>convert_fixed</span><span class=o>=</span><span class=kc>True</span><span class=p>)</span>
</code></pre></div> </details> <ul> <li>Prelu:</li> </ul> <p>Parametric Rectified Linear Unit.</p> <p>It follows: f(x) = alpha * x for x &lt; 0, f(x) = x for x &gt;= 0, where <code>alpha</code> is a learned array with the same shape as x.</p> <p>Args: <div class=highlight><pre><span></span><code>tensor: A `Tensor`.
slope: Slope of the activation function at x &lt; 0.
name: A name for the output tensor (optional).
</code></pre></div></p> <p>Returns: <div class=highlight><pre><span></span><code>A `Tensor`. Has the same type as `tensor`.
</code></pre></div></p> <p>Examples:</p> <details> <summary>case0</summary> <div class=highlight><pre><span></span><code><span class=kn>from</span> <span class=nn>calibrator_custom</span> <span class=kn>import</span> <span class=n>sgs_chalk</span>
<span class=kn>import</span> <span class=nn>numpy</span> <span class=k>as</span> <span class=nn>np</span>
<span class=n>in0</span> <span class=o>=</span> <span class=n>sgs_chalk</span><span class=o>.</span><span class=n>Input</span><span class=p>((</span><span class=mi>28</span><span class=p>,</span><span class=mi>512</span><span class=p>),</span><span class=n>name</span><span class=o>=</span><span class=s1>&#39;input0&#39;</span><span class=p>)</span>
<span class=n>out0</span> <span class=o>=</span> <span class=n>sgs_chalk</span><span class=o>.</span><span class=n>Prelu</span><span class=p>(</span><span class=n>in0</span><span class=p>,</span><span class=mi>5</span><span class=p>,</span><span class=n>name</span><span class=o>=</span><span class=s1>&#39;output&#39;</span><span class=p>)</span>
<span class=n>model</span> <span class=o>=</span> <span class=n>sgs_chalk</span><span class=o>.</span><span class=n>Model</span><span class=p>([</span><span class=n>in0</span><span class=p>],</span><span class=n>out0</span><span class=p>)</span>
<span class=n>model</span><span class=o>.</span><span class=n>save</span><span class=p>(</span><span class=s1>&#39;test.sim&#39;</span><span class=p>)</span>
</code></pre></div> </details> <details> <summary>case1</summary> <div class=highlight><pre><span></span><code><span class=kn>from</span> <span class=nn>calibrator_custom</span> <span class=kn>import</span> <span class=n>sgs_chalk</span>
<span class=kn>import</span> <span class=nn>numpy</span> <span class=k>as</span> <span class=nn>np</span>
<span class=n>input1</span> <span class=o>=</span> <span class=n>np</span><span class=o>.</span><span class=n>zeros</span><span class=p>((</span><span class=mi>512</span><span class=p>,),</span> <span class=n>dtype</span><span class=o>=</span><span class=n>np</span><span class=o>.</span><span class=n>float32</span><span class=p>)</span>
<span class=n>in0</span> <span class=o>=</span> <span class=n>sgs_chalk</span><span class=o>.</span><span class=n>Input</span><span class=p>((</span><span class=mi>3</span><span class=p>,</span><span class=mi>28</span><span class=p>,</span><span class=mi>512</span><span class=p>),</span><span class=n>name</span><span class=o>=</span><span class=s1>&#39;input0&#39;</span><span class=p>)</span>
<span class=n>out0</span> <span class=o>=</span> <span class=n>sgs_chalk</span><span class=o>.</span><span class=n>Prelu</span><span class=p>(</span><span class=n>in0</span><span class=p>,</span><span class=mi>5</span><span class=p>,</span><span class=n>name</span><span class=o>=</span><span class=s1>&#39;output&#39;</span><span class=p>)</span>
<span class=n>model</span> <span class=o>=</span> <span class=n>sgs_chalk</span><span class=o>.</span><span class=n>Model</span><span class=p>([</span><span class=n>in0</span><span class=p>],</span><span class=n>out0</span><span class=p>)</span>
<span class=n>model</span><span class=o>.</span><span class=n>save</span><span class=p>(</span><span class=s1>&#39;test.sim&#39;</span><span class=p>)</span>
</code></pre></div> </details> <ul> <li>RSqrt:</li> </ul> <p>Computes square root of x element-wise. I.e., (y = rsqrt{x} ).</p> <p>Args: <div class=highlight><pre><span></span><code>x: A `Tensor`
</code></pre></div></p> <p>Returns: <div class=highlight><pre><span></span><code>A `Tensor` of same shape and type as `x`.
</code></pre></div></p> <p>Examples:</p> <details> <summary>case0</summary> <div class=highlight><pre><span></span><code><span class=kn>from</span> <span class=nn>calibrator_custom</span> <span class=kn>import</span> <span class=n>sgs_chalk</span>
<span class=kn>import</span> <span class=nn>numpy</span> <span class=k>as</span> <span class=nn>np</span>
<span class=n>in0</span> <span class=o>=</span> <span class=n>sgs_chalk</span><span class=o>.</span><span class=n>Input</span><span class=p>((</span><span class=mi>28</span><span class=p>,</span><span class=mi>512</span><span class=p>),</span><span class=n>name</span><span class=o>=</span><span class=s1>&#39;input0&#39;</span><span class=p>)</span>
<span class=n>out0</span> <span class=o>=</span> <span class=n>sgs_chalk</span><span class=o>.</span><span class=n>RSqrt</span><span class=p>(</span><span class=n>in0</span><span class=p>,</span><span class=n>name</span><span class=o>=</span><span class=s1>&#39;output&#39;</span><span class=p>)</span>
<span class=n>model</span> <span class=o>=</span> <span class=n>sgs_chalk</span><span class=o>.</span><span class=n>Model</span><span class=p>([</span><span class=n>in0</span><span class=p>],[</span><span class=n>out0</span><span class=p>])</span>
<span class=n>model</span><span class=o>.</span><span class=n>save</span><span class=p>(</span><span class=s1>&#39;test.sim&#39;</span><span class=p>,</span><span class=n>input_config</span><span class=o>=</span><span class=s1>&#39;./input_config.ini&#39;</span><span class=p>,</span><span class=n>convert_fixed</span><span class=o>=</span><span class=kc>True</span><span class=p>)</span>
</code></pre></div> </details> <ul> <li>Reciprocal:</li> </ul> <p>Computes the Reciprocal value of a tensor.</p> <p>Given a tensor of integer or floating-point values, this operation returns a tensor of the same type, where each element contains the absolute value of the corresponding element in the input.</p> <p>Args: <div class=highlight><pre><span></span><code>x: A `Tensor`
</code></pre></div></p> <p>Returns: <div class=highlight><pre><span></span><code>A `Tensor` of same shape and type as `x`.
</code></pre></div></p> <p>Examples:</p> <details> <summary>case0</summary> <div class=highlight><pre><span></span><code><span class=kn>from</span> <span class=nn>calibrator_custom</span> <span class=kn>import</span> <span class=n>sgs_chalk</span>
<span class=kn>import</span> <span class=nn>numpy</span> <span class=k>as</span> <span class=nn>np</span>
<span class=n>in0</span> <span class=o>=</span> <span class=n>sgs_chalk</span><span class=o>.</span><span class=n>Input</span><span class=p>((</span><span class=mi>28</span><span class=p>,</span><span class=mi>512</span><span class=p>),</span><span class=n>name</span><span class=o>=</span><span class=s1>&#39;input0&#39;</span><span class=p>)</span>
<span class=n>out0</span> <span class=o>=</span> <span class=n>sgs_chalk</span><span class=o>.</span><span class=n>Reciprocal</span><span class=p>(</span><span class=n>in0</span><span class=p>,</span><span class=n>name</span><span class=o>=</span><span class=s1>&#39;output&#39;</span><span class=p>)</span>
<span class=n>model</span> <span class=o>=</span> <span class=n>sgs_chalk</span><span class=o>.</span><span class=n>Model</span><span class=p>([</span><span class=n>in0</span><span class=p>],[</span><span class=n>out0</span><span class=p>])</span>
<span class=n>model</span><span class=o>.</span><span class=n>save</span><span class=p>(</span><span class=s1>&#39;test.sim&#39;</span><span class=p>,</span><span class=n>input_config</span><span class=o>=</span><span class=s1>&#39;./input_config.ini&#39;</span><span class=p>,</span><span class=n>convert_fixed</span><span class=o>=</span><span class=kc>True</span><span class=p>)</span>
</code></pre></div> </details> <ul> <li>ReduceMax:</li> </ul> <p>Computes the maxmum of elements across dimensions of a tensor.</p> <p>Reduces <code>input_tensor</code> along the dimensions given in <code>axis</code>. Unless <code>keepdims</code> is true, the rank of the tensor is reduced by 1 for each entry in <code>axis</code>. If <code>keepdims</code> is true, the reduced dimensions are retained with length 1.</p> <p>Args: <div class=highlight><pre><span></span><code>input:A `Tensor` or &#39;Array&#39;. Must be Variable Tensor if it&#39;s a Tensor.
asix: A `Tensor` or &#39;Array&#39;. Must be Const Tensor if it&#39;s a Tensor.
keepdims: A one dimension array or list.
name: A name for the model output tensor (optional).
</code></pre></div></p> <p>Returns: <div class=highlight><pre><span></span><code>A Variable `Tensor`. Has the same shape as input.
</code></pre></div></p> <p>Examples:</p> <details> <summary>case0</summary> <div class=highlight><pre><span></span><code><span class=kn>from</span> <span class=nn>calibrator_custom</span> <span class=kn>import</span> <span class=n>sgs_chalk</span>
<span class=kn>import</span> <span class=nn>numpy</span> <span class=k>as</span> <span class=nn>np</span>
<span class=n>in0</span> <span class=o>=</span> <span class=n>sgs_chalk</span><span class=o>.</span><span class=n>Input</span><span class=p>((</span><span class=mi>1</span><span class=p>,</span><span class=mi>28</span><span class=p>,</span><span class=mi>512</span><span class=p>,</span><span class=mi>33</span><span class=p>),</span><span class=n>name</span><span class=o>=</span><span class=s1>&#39;input0&#39;</span><span class=p>)</span>
<span class=n>axis</span> <span class=o>=</span> <span class=n>np</span><span class=o>.</span><span class=n>array</span><span class=p>([</span><span class=o>-</span><span class=mi>2</span><span class=p>,</span><span class=mi>1</span><span class=p>],</span><span class=n>dtype</span><span class=o>=</span><span class=n>np</span><span class=o>.</span><span class=n>int32</span><span class=p>)</span>
<span class=n>out0</span> <span class=o>=</span> <span class=n>sgs_chalk</span><span class=o>.</span><span class=n>ReduceMax</span><span class=p>(</span><span class=n>in0</span><span class=p>,</span> <span class=n>axis</span><span class=p>,</span> <span class=n>keepdims</span><span class=o>=</span><span class=mi>1</span><span class=p>,</span> <span class=n>name</span><span class=o>=</span><span class=s1>&#39;output&#39;</span><span class=p>)</span>
<span class=n>model</span> <span class=o>=</span> <span class=n>sgs_chalk</span><span class=o>.</span><span class=n>Model</span><span class=p>([</span><span class=n>in0</span><span class=p>],</span><span class=n>out0</span><span class=p>)</span>
<span class=n>model</span><span class=o>.</span><span class=n>save</span><span class=p>(</span><span class=s1>&#39;reducemax.sim&#39;</span><span class=p>,</span><span class=n>input_config</span><span class=o>=</span><span class=s1>&#39;./input_config.ini&#39;</span><span class=p>,</span> <span class=n>convert_fixed</span><span class=o>=</span><span class=kc>True</span><span class=p>,</span> <span class=n>inputs</span><span class=o>=</span><span class=s1>&#39;RAWDATA&#39;</span><span class=p>)</span>
</code></pre></div> </details> <ul> <li>ReduceMin:</li> </ul> <p>Computes the minimum of elements across dimensions of a tensor.</p> <p>Reduces <code>input_tensor</code> along the dimensions given in <code>axis</code>. Unless <code>keepdims</code> is true, the rank of the tensor is reduced by 1 for each entry in <code>axis</code>. If <code>keepdims</code> is true, the reduced dimensions are retained with length 1.</p> <p>Args: <div class=highlight><pre><span></span><code>input: A `Tensor` or &#39;Array&#39;. Must be Variable Tensor if it&#39;s a Tensor.
asix: A `Tensor` or &#39;Array&#39;. Must be Const Tensor if it&#39;s a Tensor.
keepdims: A one dimension array or list.
name: A name for the model output tensor (optional).
</code></pre></div></p> <p>Returns: <div class=highlight><pre><span></span><code>A Variable `Tensor`. Has the same shape as input.
</code></pre></div></p> <p>Examples:</p> <details> <summary>case0</summary> <div class=highlight><pre><span></span><code><span class=kn>from</span> <span class=nn>calibrator_custom</span> <span class=kn>import</span> <span class=n>sgs_chalk</span>
<span class=kn>import</span> <span class=nn>numpy</span> <span class=k>as</span> <span class=nn>np</span>
<span class=n>in0</span> <span class=o>=</span> <span class=n>sgs_chalk</span><span class=o>.</span><span class=n>Input</span><span class=p>((</span><span class=mi>1</span><span class=p>,</span><span class=mi>28</span><span class=p>,</span><span class=mi>512</span><span class=p>,</span><span class=mi>33</span><span class=p>),</span><span class=n>name</span><span class=o>=</span><span class=s1>&#39;input0&#39;</span><span class=p>)</span>
<span class=n>axis</span> <span class=o>=</span> <span class=n>np</span><span class=o>.</span><span class=n>array</span><span class=p>([</span><span class=o>-</span><span class=mi>2</span><span class=p>,</span><span class=mi>1</span><span class=p>],</span><span class=n>dtype</span><span class=o>=</span><span class=n>np</span><span class=o>.</span><span class=n>int32</span><span class=p>)</span>
<span class=n>out0</span> <span class=o>=</span> <span class=n>sgs_chalk</span><span class=o>.</span><span class=n>ReduceMin</span><span class=p>(</span><span class=n>in0</span><span class=p>,</span> <span class=n>axis</span><span class=p>,</span> <span class=n>keepdims</span><span class=o>=</span><span class=mi>1</span><span class=p>,</span> <span class=n>name</span><span class=o>=</span><span class=s1>&#39;output&#39;</span><span class=p>)</span>
<span class=n>model</span> <span class=o>=</span> <span class=n>sgs_chalk</span><span class=o>.</span><span class=n>Model</span><span class=p>([</span><span class=n>in0</span><span class=p>],</span><span class=n>out0</span><span class=p>)</span>
<span class=n>model</span><span class=o>.</span><span class=n>save</span><span class=p>(</span><span class=s1>&#39;reducemin.sim&#39;</span><span class=p>,</span><span class=n>input_config</span><span class=o>=</span><span class=s1>&#39;./input_config.ini&#39;</span><span class=p>,</span> <span class=n>convert_fixed</span><span class=o>=</span><span class=kc>True</span><span class=p>,</span> <span class=n>inputs</span><span class=o>=</span><span class=s1>&#39;RAWDATA&#39;</span><span class=p>)</span>
</code></pre></div> </details> <ul> <li>Relu:</li> </ul> <p>Computes rectified linear: <code>max(x, 0)</code>.</p> <p>Args: <div class=highlight><pre><span></span><code>tensor: A `Tensor`.
name: A name for the output tensor (optional).
</code></pre></div></p> <p>Returns: <div class=highlight><pre><span></span><code>A `Tensor`. Has the same type as `tensor`.
</code></pre></div></p> <p>Examples:</p> <details> <summary>case0</summary> <div class=highlight><pre><span></span><code><span class=kn>from</span> <span class=nn>calibrator_custom</span> <span class=kn>import</span> <span class=n>sgs_chalk</span>
<span class=kn>import</span> <span class=nn>numpy</span> <span class=k>as</span> <span class=nn>np</span>
<span class=n>in0</span> <span class=o>=</span> <span class=n>sgs_chalk</span><span class=o>.</span><span class=n>Input</span><span class=p>((</span><span class=mi>28</span><span class=p>,</span><span class=mi>512</span><span class=p>),</span><span class=n>name</span><span class=o>=</span><span class=s1>&#39;input0&#39;</span><span class=p>)</span>
<span class=n>out0</span> <span class=o>=</span> <span class=n>sgs_chalk</span><span class=o>.</span><span class=n>Relu</span><span class=p>(</span><span class=n>in0</span><span class=p>,</span><span class=n>name</span><span class=o>=</span><span class=s1>&#39;output&#39;</span><span class=p>)</span>
<span class=n>model</span> <span class=o>=</span> <span class=n>sgs_chalk</span><span class=o>.</span><span class=n>Model</span><span class=p>([</span><span class=n>in0</span><span class=p>],</span><span class=n>out0</span><span class=p>)</span>
<span class=n>model</span><span class=o>.</span><span class=n>save</span><span class=p>(</span><span class=s1>&#39;test.sim&#39;</span><span class=p>)</span>
</code></pre></div> </details> <ul> <li>Relu6:</li> </ul> <p>Computes Rectified Linear 6: `min(max(features, 0), 6)</p> <p>Args: <div class=highlight><pre><span></span><code>tensor: A `Tensor`.
name: A name for the output tensor (optional).
</code></pre></div></p> <p>Returns: <div class=highlight><pre><span></span><code>A `Tensor`. Has the same type as `tensor`.
</code></pre></div></p> <p>Examples:</p> <details> <summary>case0</summary> <div class=highlight><pre><span></span><code><span class=kn>from</span> <span class=nn>calibrator_custom</span> <span class=kn>import</span> <span class=n>sgs_chalk</span>
<span class=kn>import</span> <span class=nn>numpy</span> <span class=k>as</span> <span class=nn>np</span>
<span class=n>in0</span> <span class=o>=</span> <span class=n>sgs_chalk</span><span class=o>.</span><span class=n>Input</span><span class=p>((</span><span class=mi>28</span><span class=p>,</span><span class=mi>512</span><span class=p>),</span><span class=n>name</span><span class=o>=</span><span class=s1>&#39;input0&#39;</span><span class=p>)</span>
<span class=n>out0</span> <span class=o>=</span> <span class=n>sgs_chalk</span><span class=o>.</span><span class=n>Relu6</span><span class=p>(</span><span class=n>in0</span><span class=p>,</span><span class=n>name</span><span class=o>=</span><span class=s1>&#39;output&#39;</span><span class=p>)</span>
<span class=n>model</span> <span class=o>=</span> <span class=n>sgs_chalk</span><span class=o>.</span><span class=n>Model</span><span class=p>([</span><span class=n>in0</span><span class=p>],</span><span class=n>out0</span><span class=p>)</span>
<span class=n>model</span><span class=o>.</span><span class=n>save</span><span class=p>(</span><span class=s1>&#39;test.sim&#39;</span><span class=p>)</span>
</code></pre></div> </details> <ul> <li>Relu_N1_TO_1:</li> </ul> <p>Args: <div class=highlight><pre><span></span><code>tensor: A `Tensor`.
name: A name for the output tensor (optional).
</code></pre></div></p> <p>Returns: <div class=highlight><pre><span></span><code>A `Tensor`. Has the same type as `tensor`.
</code></pre></div></p> <p>Examples:</p> <details> <summary>case0</summary> <div class=highlight><pre><span></span><code><span class=kn>from</span> <span class=nn>calibrator_custom</span> <span class=kn>import</span> <span class=n>sgs_chalk</span>
<span class=kn>import</span> <span class=nn>numpy</span> <span class=k>as</span> <span class=nn>np</span>
<span class=n>in0</span> <span class=o>=</span> <span class=n>sgs_chalk</span><span class=o>.</span><span class=n>Input</span><span class=p>((</span><span class=mi>28</span><span class=p>,</span><span class=mi>512</span><span class=p>),</span><span class=n>name</span><span class=o>=</span><span class=s1>&#39;input0&#39;</span><span class=p>)</span>
<span class=n>out0</span> <span class=o>=</span> <span class=n>sgs_chalk</span><span class=o>.</span><span class=n>Relu_N1_TO_1</span><span class=p>(</span><span class=n>in0</span><span class=p>,</span><span class=n>name</span><span class=o>=</span><span class=s1>&#39;output&#39;</span><span class=p>)</span>
<span class=n>model</span> <span class=o>=</span> <span class=n>sgs_chalk</span><span class=o>.</span><span class=n>Model</span><span class=p>([</span><span class=n>in0</span><span class=p>],</span><span class=n>out0</span><span class=p>)</span>
<span class=n>model</span><span class=o>.</span><span class=n>save</span><span class=p>(</span><span class=s1>&#39;test.sim&#39;</span><span class=p>)</span>
</code></pre></div> </details> <ul> <li>Reshape:</li> </ul> <p>As an entry point into a graph. reshape(a, newshape, order='C') Gives a new shape to an array without changing its data.</p> <p>Examples:</p> <details> <summary>case0</summary> <div class=highlight><pre><span></span><code><span class=kn>from</span> <span class=nn>calibrator_custom</span> <span class=kn>import</span> <span class=n>sgs_chalk</span>
<span class=kn>import</span> <span class=nn>numpy</span> <span class=k>as</span> <span class=nn>np</span>
<span class=n>in0</span> <span class=o>=</span> <span class=n>sgs_chalk</span><span class=o>.</span><span class=n>Input</span><span class=p>((</span><span class=mi>28</span><span class=p>,</span><span class=mi>512</span><span class=p>),</span><span class=n>name</span><span class=o>=</span><span class=s1>&#39;input0&#39;</span><span class=p>)</span>
<span class=n>out0</span> <span class=o>=</span> <span class=n>sgs_chalk</span><span class=o>.</span><span class=n>Reshape</span><span class=p>(</span><span class=n>in0</span><span class=p>,(</span><span class=mi>28</span><span class=p>,</span><span class=mi>256</span><span class=p>,</span><span class=mi>2</span><span class=p>),</span><span class=n>name</span><span class=o>=</span><span class=s1>&#39;output&#39;</span><span class=p>)</span>
<span class=n>model</span> <span class=o>=</span> <span class=n>sgs_chalk</span><span class=o>.</span><span class=n>Model</span><span class=p>([</span><span class=n>in0</span><span class=p>],</span><span class=n>out0</span><span class=p>)</span>
<span class=n>model</span><span class=o>.</span><span class=n>save</span><span class=p>(</span><span class=s1>&#39;test.sim&#39;</span><span class=p>)</span>
</code></pre></div> </details> <details> <summary>case1</summary> <div class=highlight><pre><span></span><code><span class=kn>from</span> <span class=nn>calibrator_custom</span> <span class=kn>import</span> <span class=n>sgs_chalk</span>
<span class=kn>import</span> <span class=nn>numpy</span> <span class=k>as</span> <span class=nn>np</span>
<span class=n>in0</span> <span class=o>=</span> <span class=n>sgs_chalk</span><span class=o>.</span><span class=n>Input</span><span class=p>((</span><span class=mi>28</span><span class=p>,</span><span class=mi>512</span><span class=p>),</span><span class=n>name</span><span class=o>=</span><span class=s1>&#39;input0&#39;</span><span class=p>)</span>
<span class=n>out0</span> <span class=o>=</span> <span class=n>sgs_chalk</span><span class=o>.</span><span class=n>Reshape</span><span class=p>(</span><span class=n>in0</span><span class=p>,(</span><span class=mi>28</span><span class=p>,</span><span class=mi>256</span><span class=p>,</span><span class=o>-</span><span class=mi>1</span><span class=p>),</span><span class=n>name</span><span class=o>=</span><span class=s1>&#39;output&#39;</span><span class=p>)</span>
<span class=n>model</span> <span class=o>=</span> <span class=n>sgs_chalk</span><span class=o>.</span><span class=n>Model</span><span class=p>([</span><span class=n>in0</span><span class=p>],</span><span class=n>out0</span><span class=p>)</span>
<span class=n>model</span><span class=o>.</span><span class=n>save</span><span class=p>(</span><span class=s1>&#39;test.sim&#39;</span><span class=p>)</span>
</code></pre></div> </details> <ul> <li>RoiPooling:</li> </ul> <p>RoiPooling</p> <p>Args: <div class=highlight><pre><span></span><code>x: A `Tensor`. Must be Variable Tensor.
RoiInfo: must be two dim, and the  second dim is 5.
kernel_size : output kernel size.
spatial_scale ; A value
name: A name for the output tensor (optional).
</code></pre></div></p> <p>Returns: <div class=highlight><pre><span></span><code>A Variable `Tensor`. shape is [RoiInfo.shape[0],kernel_size[0],kernel_size[1],x.shape[3]]
</code></pre></div></p> <p>Examples:</p> <details> <summary>case0</summary> <div class=highlight><pre><span></span><code><span class=kn>from</span> <span class=nn>calibrator_custom</span> <span class=kn>import</span> <span class=n>sgs_chalk</span>
<span class=kn>import</span> <span class=nn>numpy</span> <span class=k>as</span> <span class=nn>np</span>
<span class=nb>input</span> <span class=o>=</span> <span class=n>sgs_chalk</span><span class=o>.</span><span class=n>Input</span><span class=p>((</span><span class=mi>1</span><span class=p>,</span><span class=mi>25</span><span class=p>,</span><span class=mi>32</span><span class=p>,</span><span class=mi>256</span><span class=p>),</span> <span class=n>name</span><span class=o>=</span><span class=s1>&#39;input&#39;</span><span class=p>)</span>
<span class=n>RoiInfo</span>  <span class=o>=</span> <span class=n>sgs_chalk</span><span class=o>.</span><span class=n>Input</span><span class=p>((</span><span class=mi>304</span><span class=p>,</span><span class=mi>5</span><span class=p>),</span> <span class=n>name</span><span class=o>=</span><span class=s1>&#39;input0&#39;</span><span class=p>)</span>
<span class=n>output</span> <span class=o>=</span> <span class=n>sgs_chalk</span><span class=o>.</span><span class=n>RoiPooling</span><span class=p>(</span><span class=nb>input</span><span class=p>,</span><span class=n>RoiInfo</span><span class=p>,[</span><span class=mi>6</span><span class=p>,</span><span class=mi>6</span><span class=p>],</span> <span class=mf>0.0625</span><span class=p>,</span><span class=n>name</span><span class=o>=</span><span class=s1>&#39;output&#39;</span><span class=p>)</span>
<span class=n>model</span> <span class=o>=</span> <span class=n>sgs_chalk</span><span class=o>.</span><span class=n>Model</span><span class=p>([</span><span class=nb>input</span><span class=p>,</span><span class=n>RoiInfo</span><span class=p>],</span> <span class=n>output</span><span class=p>)</span>
<span class=c1>#model.save(&#39;test.sim&#39;, input_config=&#39;./input_config.ini&#39;, convert_fixed=True)</span>
<span class=n>model</span><span class=o>.</span><span class=n>save</span><span class=p>(</span><span class=s1>&#39;test.sim&#39;</span><span class=p>,</span> <span class=n>input_config</span><span class=o>=</span><span class=s1>&#39;./input_config.ini&#39;</span><span class=p>,</span> <span class=n>convert_fixed</span><span class=o>=</span><span class=kc>True</span><span class=p>,</span><span class=n>inputs</span><span class=o>=</span><span class=s1>&#39;RAWDATA&#39;</span><span class=p>)</span>
</code></pre></div> </details> <ul> <li>Round:</li> </ul> <p>Rounds the values of a tensor to the nearest integer, element-wise.</p> <p>Args: <div class=highlight><pre><span></span><code>x: A `Tensor`
</code></pre></div></p> <p>Returns: <div class=highlight><pre><span></span><code>A `Tensor` of same shape and type as `x`.
</code></pre></div></p> <p>Examples:</p> <details> <summary>case0</summary> <div class=highlight><pre><span></span><code><span class=kn>from</span> <span class=nn>calibrator_custom</span> <span class=kn>import</span> <span class=n>sgs_chalk</span>
<span class=kn>import</span> <span class=nn>numpy</span> <span class=k>as</span> <span class=nn>np</span>
<span class=n>in0</span> <span class=o>=</span> <span class=n>sgs_chalk</span><span class=o>.</span><span class=n>Input</span><span class=p>((</span><span class=mi>28</span><span class=p>,</span><span class=mi>512</span><span class=p>),</span><span class=n>name</span><span class=o>=</span><span class=s1>&#39;input0&#39;</span><span class=p>)</span>
<span class=n>out0</span> <span class=o>=</span> <span class=n>sgs_chalk</span><span class=o>.</span><span class=n>Round</span><span class=p>(</span><span class=n>in0</span><span class=p>,</span><span class=n>name</span><span class=o>=</span><span class=s1>&#39;output&#39;</span><span class=p>)</span>
<span class=n>model</span> <span class=o>=</span> <span class=n>sgs_chalk</span><span class=o>.</span><span class=n>Model</span><span class=p>([</span><span class=n>in0</span><span class=p>],[</span><span class=n>out0</span><span class=p>])</span>
<span class=n>model</span><span class=o>.</span><span class=n>save</span><span class=p>(</span><span class=s1>&#39;test.sim&#39;</span><span class=p>,</span><span class=n>input_config</span><span class=o>=</span><span class=s1>&#39;./input_config.ini&#39;</span><span class=p>,</span><span class=n>convert_fixed</span><span class=o>=</span><span class=kc>True</span><span class=p>)</span>
</code></pre></div> </details> <ul> <li>Select:</li> </ul> <p>where(condition, x=None, y=None, name=None)</p> <p>Return the elements, either from <code>x</code> or <code>y</code>, depending on the <code>condition</code>. (deprecated) If both <code>x</code> and <code>y</code> are None, then this operation returns the coordinates of true elements of <code>condition</code>. The coordinates are returned in a 2-D tensor where the first dimension (rows) represents the number of true elements, and the second dimension (columns) represents the coordinates of the true elements. Keep in mind, the shape of the output tensor can vary depending on how many true values there are in input. Indices are output in row-major order.</p> <p>If both non-None, <code>x</code> and <code>y</code> must have the same shape. The <code>condition</code> tensor must be a scalar if <code>x</code> and <code>y</code> are scalar. If <code>x</code> and <code>y</code> are vectors of higher rank, then <code>condition</code> must be either a vector with size matching the first dimension of <code>x</code>, or must have the same shape as <code>x</code>.</p> <p>The <code>condition</code> tensor acts as a mask that chooses, based on the value at each element, whether the corresponding element / row in the output should be taken from <code>x</code> (if true) or <code>y</code> (if false).</p> <p>If <code>condition</code> is a vector and <code>x</code> and <code>y</code> are higher rank matrices, then it chooses which row (outer dimension) to copy from <code>x</code> and <code>y</code>. If <code>condition</code> has the same shape as <code>x</code> and <code>y</code>, then it chooses which element to copy from <code>x</code> and <code>y</code>.</p> <p>Args: <div class=highlight><pre><span></span><code>condition: A `Tensor` of type `float32`
x: A Tensor which may have the same shape as `condition`. If `condition` is
rank 1, `x` may have higher rank, but its first dimension must match the
size of `condition`.
y: A `tensor` with the same shape and type as `x`.
name: A name of the operation (optional)
</code></pre></div></p> <p>Returns: <div class=highlight><pre><span></span><code>A `Tensor` with the same type and shape as `x`, `y` if they are non-None.
Otherwise, a `Tensor` with shape `(num_true, rank(condition))`.
</code></pre></div></p> <p>Examples:</p> <details> <summary>case0</summary> <div class=highlight><pre><span></span><code><span class=kn>from</span> <span class=nn>calibrator_custom</span> <span class=kn>import</span> <span class=n>sgs_chalk</span>
<span class=kn>import</span> <span class=nn>numpy</span> <span class=k>as</span> <span class=nn>np</span>
<span class=n>in0</span> <span class=o>=</span> <span class=n>sgs_chalk</span><span class=o>.</span><span class=n>Input</span><span class=p>((</span><span class=mi>28</span><span class=p>,</span><span class=mi>512</span><span class=p>),</span><span class=n>name</span><span class=o>=</span><span class=s1>&#39;input0&#39;</span><span class=p>)</span>
<span class=n>in1</span> <span class=o>=</span> <span class=n>sgs_chalk</span><span class=o>.</span><span class=n>Input</span><span class=p>((</span><span class=mi>28</span><span class=p>,</span><span class=mi>512</span><span class=p>),</span><span class=n>name</span><span class=o>=</span><span class=s1>&#39;input1&#39;</span><span class=p>)</span>
<span class=n>in2</span> <span class=o>=</span> <span class=n>sgs_chalk</span><span class=o>.</span><span class=n>Input</span><span class=p>((</span><span class=mi>28</span><span class=p>,</span><span class=mi>512</span><span class=p>),</span><span class=n>name</span><span class=o>=</span><span class=s1>&#39;input2&#39;</span><span class=p>)</span>
<span class=n>out0</span> <span class=o>=</span> <span class=n>sgs_chalk</span><span class=o>.</span><span class=n>Select</span><span class=p>(</span><span class=n>in0</span><span class=p>,</span><span class=n>in1</span><span class=p>,</span><span class=n>in2</span><span class=p>,</span><span class=n>name</span><span class=o>=</span><span class=s1>&#39;output&#39;</span><span class=p>)</span>
<span class=n>model</span> <span class=o>=</span> <span class=n>sgs_chalk</span><span class=o>.</span><span class=n>Model</span><span class=p>([</span><span class=n>in0</span><span class=p>,</span><span class=n>in1</span><span class=p>,</span><span class=n>in2</span><span class=p>],</span><span class=n>out0</span><span class=p>)</span>
<span class=n>model</span><span class=o>.</span><span class=n>save</span><span class=p>(</span><span class=s1>&#39;test.sim&#39;</span><span class=p>)</span>
</code></pre></div> </details> <details> <summary>case1</summary> <div class=highlight><pre><span></span><code><span class=kn>from</span> <span class=nn>calibrator_custom</span> <span class=kn>import</span> <span class=n>sgs_chalk</span>
<span class=kn>import</span> <span class=nn>numpy</span> <span class=k>as</span> <span class=nn>np</span>
<span class=n>in0</span> <span class=o>=</span> <span class=n>sgs_chalk</span><span class=o>.</span><span class=n>Input</span><span class=p>((</span><span class=mi>28</span><span class=p>,</span><span class=mi>512</span><span class=p>),</span><span class=n>name</span><span class=o>=</span><span class=s1>&#39;input0&#39;</span><span class=p>)</span>
<span class=n>in1</span> <span class=o>=</span> <span class=n>sgs_chalk</span><span class=o>.</span><span class=n>Input</span><span class=p>((</span><span class=mi>28</span><span class=p>,</span><span class=mi>512</span><span class=p>),</span><span class=n>name</span><span class=o>=</span><span class=s1>&#39;input1&#39;</span><span class=p>)</span>
<span class=n>out0</span> <span class=o>=</span> <span class=n>sgs_chalk</span><span class=o>.</span><span class=n>Select</span><span class=p>(</span><span class=n>in0</span><span class=p>,</span><span class=mf>5.0</span><span class=p>,</span><span class=n>in1</span><span class=p>,</span><span class=n>name</span><span class=o>=</span><span class=s1>&#39;output&#39;</span><span class=p>)</span>
<span class=n>model</span> <span class=o>=</span> <span class=n>sgs_chalk</span><span class=o>.</span><span class=n>Model</span><span class=p>([</span><span class=n>in0</span><span class=p>,</span><span class=n>in1</span><span class=p>],</span><span class=n>out0</span><span class=p>)</span>
<span class=n>model</span><span class=o>.</span><span class=n>save</span><span class=p>(</span><span class=s1>&#39;test.sim&#39;</span><span class=p>)</span>
</code></pre></div> </details> <details> <summary>case2</summary> <div class=highlight><pre><span></span><code><span class=kn>from</span> <span class=nn>calibrator_custom</span> <span class=kn>import</span> <span class=n>sgs_chalk</span>
<span class=kn>import</span> <span class=nn>numpy</span> <span class=k>as</span> <span class=nn>np</span>
<span class=n>input1</span> <span class=o>=</span> <span class=n>np</span><span class=o>.</span><span class=n>zeros</span><span class=p>((</span><span class=mi>28</span><span class=p>,</span><span class=mi>512</span><span class=p>),</span> <span class=n>dtype</span><span class=o>=</span><span class=n>np</span><span class=o>.</span><span class=n>float32</span><span class=p>)</span>
<span class=n>in0</span> <span class=o>=</span> <span class=n>sgs_chalk</span><span class=o>.</span><span class=n>Input</span><span class=p>((</span><span class=mi>28</span><span class=p>,</span><span class=mi>512</span><span class=p>),</span><span class=n>name</span><span class=o>=</span><span class=s1>&#39;input0&#39;</span><span class=p>)</span>
<span class=n>in1</span> <span class=o>=</span> <span class=n>sgs_chalk</span><span class=o>.</span><span class=n>Input</span><span class=p>((</span><span class=mi>28</span><span class=p>,</span><span class=mi>512</span><span class=p>),</span><span class=n>name</span><span class=o>=</span><span class=s1>&#39;input1&#39;</span><span class=p>)</span>
<span class=n>out0</span> <span class=o>=</span> <span class=n>sgs_chalk</span><span class=o>.</span><span class=n>Select</span><span class=p>(</span><span class=n>input1</span><span class=p>,</span><span class=n>in0</span><span class=p>,</span><span class=n>in1</span><span class=p>,</span><span class=n>name</span><span class=o>=</span><span class=s1>&#39;output&#39;</span><span class=p>)</span>
<span class=n>model</span> <span class=o>=</span> <span class=n>sgs_chalk</span><span class=o>.</span><span class=n>Model</span><span class=p>([</span><span class=n>in0</span><span class=p>,</span><span class=n>in1</span><span class=p>],</span><span class=n>out0</span><span class=p>)</span>
<span class=n>model</span><span class=o>.</span><span class=n>save</span><span class=p>(</span><span class=s1>&#39;test.sim&#39;</span><span class=p>)</span>
</code></pre></div> </details> <ul> <li>Shape:</li> </ul> <p>Computes the shape of a tensor.</p> <p>Given a tensor of integer or floating-point values, this operation returns a tensor of the same type, where each element contains the absolute value of the corresponding element in the input.</p> <p>Args: <div class=highlight><pre><span></span><code>x: A `Tensor`
dtype: Output data type. Only support int32 or int64
</code></pre></div></p> <p>Returns: <div class=highlight><pre><span></span><code>A `Tensor` of same shape and type as `x`.
</code></pre></div></p> <p>Examples:</p> <details> <summary>case0</summary> <div class=highlight><pre><span></span><code><span class=kn>from</span> <span class=nn>calibrator_custom</span> <span class=kn>import</span> <span class=n>sgs_chalk</span>
<span class=kn>import</span> <span class=nn>numpy</span> <span class=k>as</span> <span class=nn>np</span>
<span class=n>in0</span> <span class=o>=</span> <span class=n>sgs_chalk</span><span class=o>.</span><span class=n>Input</span><span class=p>((</span><span class=mi>28</span><span class=p>,</span><span class=mi>512</span><span class=p>),</span><span class=n>name</span><span class=o>=</span><span class=s1>&#39;input0&#39;</span><span class=p>)</span>
<span class=n>out0</span> <span class=o>=</span> <span class=n>sgs_chalk</span><span class=o>.</span><span class=n>Shape</span><span class=p>(</span><span class=n>in0</span><span class=p>,</span><span class=n>name</span><span class=o>=</span><span class=s1>&#39;output&#39;</span><span class=p>)</span>
<span class=n>model</span> <span class=o>=</span> <span class=n>sgs_chalk</span><span class=o>.</span><span class=n>Model</span><span class=p>([</span><span class=n>in0</span><span class=p>],[</span><span class=n>out0</span><span class=p>])</span>
<span class=n>model</span><span class=o>.</span><span class=n>save</span><span class=p>(</span><span class=s1>&#39;test.sim&#39;</span><span class=p>,</span><span class=n>input_config</span><span class=o>=</span><span class=s1>&#39;./input_config.ini&#39;</span><span class=p>,</span><span class=n>convert_fixed</span><span class=o>=</span><span class=kc>True</span><span class=p>)</span>
</code></pre></div> </details> <ul> <li>Sin:</li> </ul> <p>Returns sin(x) element-wise.</p> <p>Args: <div class=highlight><pre><span></span><code>x: A `Tensor`. Must be Variable Tensor.

Returns:
```text
A Variable `Tensor`. Has the same shape as `x`.
</code></pre></div></p> <p>Examples:</p> <details> <summary>case0</summary> <div class=highlight><pre><span></span><code><span class=kn>from</span> <span class=nn>calibrator_custom</span> <span class=kn>import</span> <span class=n>sgs_chalk</span>
<span class=kn>import</span> <span class=nn>numpy</span> <span class=k>as</span> <span class=nn>np</span>
<span class=n>in0</span> <span class=o>=</span> <span class=n>sgs_chalk</span><span class=o>.</span><span class=n>Input</span><span class=p>((</span><span class=mi>28</span><span class=p>,</span><span class=mi>512</span><span class=p>),</span><span class=n>name</span><span class=o>=</span><span class=s1>&#39;input0&#39;</span><span class=p>)</span>
<span class=n>out0</span> <span class=o>=</span> <span class=n>sgs_chalk</span><span class=o>.</span><span class=n>Sin</span><span class=p>(</span><span class=n>in0</span><span class=p>,</span><span class=n>name</span><span class=o>=</span><span class=s1>&#39;output&#39;</span><span class=p>)</span>
<span class=n>model</span> <span class=o>=</span> <span class=n>sgs_chalk</span><span class=o>.</span><span class=n>Model</span><span class=p>([</span><span class=n>in0</span><span class=p>],</span><span class=n>out0</span><span class=p>)</span>
<span class=n>model</span><span class=o>.</span><span class=n>save</span><span class=p>(</span><span class=s1>&#39;test.sim&#39;</span><span class=p>)</span>
</code></pre></div> </details> <ul> <li>Slice:</li> </ul> <p>Extracts a slice from a tensor.</p> <p>This operation extracts a slice of size <code>size</code> from a tensor <code>input</code> starting at the location specified by <code>begin</code>. The slice <code>size</code> is represented as a tensor shape, where <code>size[i]</code> is the number of elements of the 'i'th dimension of <code>input</code> that you want to slice. The starting location (<code>begin</code>) for the slice is represented as an offset in each dimension of <code>input</code>. In other words, <code>begin[i]</code> is the offset into the 'i'th dimension of <code>input</code> that you want to slice from.</p> <p>Args: <div class=highlight><pre><span></span><code>x: A `Tensor`. Must be Variable Tensor.
begin: const Tensor
size: const Tensor
name: A name for the output tensor (optional).
</code></pre></div></p> <p>Returns: <div class=highlight><pre><span></span><code>A Variable `Tensor`. Has the same shape as `size`.
</code></pre></div></p> <p>Examples:</p> <details> <summary>case0</summary> <div class=highlight><pre><span></span><code><span class=kn>from</span> <span class=nn>calibrator_custom</span> <span class=kn>import</span> <span class=n>sgs_chalk</span>
<span class=kn>import</span> <span class=nn>numpy</span> <span class=k>as</span> <span class=nn>np</span>
<span class=n>in0</span> <span class=o>=</span> <span class=n>sgs_chalk</span><span class=o>.</span><span class=n>Input</span><span class=p>((</span><span class=mi>28</span><span class=p>,</span><span class=mi>512</span><span class=p>),</span><span class=n>name</span><span class=o>=</span><span class=s1>&#39;input0&#39;</span><span class=p>)</span>
<span class=n>out0</span> <span class=o>=</span> <span class=n>sgs_chalk</span><span class=o>.</span><span class=n>Slice</span><span class=p>(</span><span class=n>in0</span><span class=p>,[</span><span class=mi>0</span><span class=p>,</span><span class=mi>0</span><span class=p>],[</span><span class=mi>28</span><span class=p>,</span><span class=mi>512</span><span class=p>],</span><span class=n>name</span><span class=o>=</span><span class=s1>&#39;output&#39;</span><span class=p>)</span>
<span class=n>model</span> <span class=o>=</span> <span class=n>sgs_chalk</span><span class=o>.</span><span class=n>Model</span><span class=p>([</span><span class=n>in0</span><span class=p>],</span><span class=n>out0</span><span class=p>)</span>
<span class=n>model</span><span class=o>.</span><span class=n>save</span><span class=p>(</span><span class=s1>&#39;test.sim&#39;</span><span class=p>)</span>
</code></pre></div> </details> <details> <summary>case1</summary> <div class=highlight><pre><span></span><code><span class=kn>from</span> <span class=nn>calibrator_custom</span> <span class=kn>import</span> <span class=n>sgs_chalk</span>
<span class=kn>import</span> <span class=nn>numpy</span> <span class=k>as</span> <span class=nn>np</span>
<span class=n>in0</span> <span class=o>=</span> <span class=n>sgs_chalk</span><span class=o>.</span><span class=n>Input</span><span class=p>((</span><span class=mi>28</span><span class=p>,</span><span class=mi>512</span><span class=p>),</span><span class=n>name</span><span class=o>=</span><span class=s1>&#39;input0&#39;</span><span class=p>)</span>
<span class=n>out0</span> <span class=o>=</span> <span class=n>sgs_chalk</span><span class=o>.</span><span class=n>Slice</span><span class=p>(</span><span class=n>in0</span><span class=p>,[</span><span class=mi>0</span><span class=p>,</span><span class=mi>0</span><span class=p>],[</span><span class=mi>14</span><span class=p>,</span><span class=mi>510</span><span class=p>],</span><span class=n>name</span><span class=o>=</span><span class=s1>&#39;output&#39;</span><span class=p>)</span>
<span class=n>model</span> <span class=o>=</span> <span class=n>sgs_chalk</span><span class=o>.</span><span class=n>Model</span><span class=p>([</span><span class=n>in0</span><span class=p>],</span><span class=n>out0</span><span class=p>)</span>
<span class=n>model</span><span class=o>.</span><span class=n>save</span><span class=p>(</span><span class=s1>&#39;test.sim&#39;</span><span class=p>)</span>
</code></pre></div> </details> <ul> <li>Softmax:</li> </ul> <p>Computes softmax activations.</p> <p>Args: <div class=highlight><pre><span></span><code>tensor: A `Tensor`.
name: A name for the output tensor (optional).
</code></pre></div></p> <p>Returns: <div class=highlight><pre><span></span><code>A `Tensor`. Has the same type as `tensor`.
</code></pre></div></p> <p>Examples:</p> <details> <summary>case0</summary> <div class=highlight><pre><span></span><code><span class=kn>from</span> <span class=nn>calibrator_custom</span> <span class=kn>import</span> <span class=n>sgs_chalk</span>
<span class=kn>import</span> <span class=nn>numpy</span> <span class=k>as</span> <span class=nn>np</span>
<span class=n>in0</span> <span class=o>=</span> <span class=n>sgs_chalk</span><span class=o>.</span><span class=n>Input</span><span class=p>((</span><span class=mi>28</span><span class=p>,</span><span class=mi>512</span><span class=p>),</span><span class=n>name</span><span class=o>=</span><span class=s1>&#39;input0&#39;</span><span class=p>)</span>
<span class=n>out0</span> <span class=o>=</span> <span class=n>sgs_chalk</span><span class=o>.</span><span class=n>Softmax</span><span class=p>(</span><span class=n>in0</span><span class=p>,</span><span class=n>name</span><span class=o>=</span><span class=s1>&#39;output&#39;</span><span class=p>)</span>
<span class=n>model</span> <span class=o>=</span> <span class=n>sgs_chalk</span><span class=o>.</span><span class=n>Model</span><span class=p>([</span><span class=n>in0</span><span class=p>],</span><span class=n>out0</span><span class=p>)</span>
<span class=n>model</span><span class=o>.</span><span class=n>save</span><span class=p>(</span><span class=s1>&#39;test.sim&#39;</span><span class=p>)</span>
</code></pre></div> </details> <ul> <li>Split:</li> </ul> <p>Splits a tensor into sub tensors.</p> <p>NumSplits is an integer, then <code>value</code> is split along dimension <code>axis</code> into NumSplits smaller tensors. This requires that NumSplits evenly divides <code>value.shape[axis]</code>.</p> <p>Args: <div class=highlight><pre><span></span><code>input: A `Tensor`. 1-D or higher.
NumSplits: an integer indicating the number of splits along split_dim
axis: An integer or scalar `int32` `Tensor`. The dimension along which to
      split. Must be in the range `[-rank(value), rank(value))`. Defaults to 0.
name: A name for the output tensor (optional).
</code></pre></div></p> <p>Returns: <div class=highlight><pre><span></span><code>returns `num_or_size_splits` `Tensor` objects;
</code></pre></div></p> <p>Examples:</p> <details> <summary>case0</summary> <div class=highlight><pre><span></span><code><span class=kn>from</span> <span class=nn>calibrator_custom</span> <span class=kn>import</span> <span class=n>sgs_chalk</span>
<span class=kn>import</span> <span class=nn>numpy</span> <span class=k>as</span> <span class=nn>np</span>
<span class=n>in0</span> <span class=o>=</span> <span class=n>sgs_chalk</span><span class=o>.</span><span class=n>Input</span><span class=p>((</span><span class=mi>28</span><span class=p>,</span><span class=mi>4</span><span class=p>,</span><span class=mi>4</span><span class=p>),</span><span class=n>name</span><span class=o>=</span><span class=s1>&#39;input0&#39;</span><span class=p>)</span>
<span class=n>output_list</span> <span class=o>=</span> <span class=n>sgs_chalk</span><span class=o>.</span><span class=n>Split</span><span class=p>(</span><span class=n>in0</span><span class=p>,</span><span class=n>NumSplits</span><span class=o>=</span><span class=mi>2</span><span class=p>,</span> <span class=n>axis</span><span class=o>=</span><span class=mi>2</span><span class=p>,</span><span class=n>name</span><span class=o>=</span><span class=p>[</span><span class=s1>&#39;output0&#39;</span><span class=p>,</span><span class=s1>&#39;output1&#39;</span><span class=p>])</span>
<span class=n>model</span> <span class=o>=</span> <span class=n>sgs_chalk</span><span class=o>.</span><span class=n>Model</span><span class=p>([</span><span class=n>in0</span><span class=p>],</span><span class=n>output_list</span><span class=p>)</span>
<span class=n>model</span><span class=o>.</span><span class=n>save</span><span class=p>(</span><span class=s1>&#39;test.sim&#39;</span><span class=p>,</span><span class=n>input_config</span><span class=o>=</span><span class=s1>&#39;./input_config.ini&#39;</span><span class=p>,</span><span class=n>convert_fixed</span><span class=o>=</span><span class=kc>True</span><span class=p>)</span>
</code></pre></div> </details> <ul> <li>Split_V:</li> </ul> <p>Splits a tensor into sub tensors.</p> <p>Sizesplits is a 1-D Tensor (or list), and value is split into len(size_splits) elements. The shape of the i-th element has the same size as the <code>value</code> except along dimension <code>axis</code> where the size is size_splits[i].</p> <p>Args: <div class=highlight><pre><span></span><code>input: A `Tensor`. 1-D or higher.
SizeSplits: Python list containing the sizes of each output tensor along split_dim.
            the sum of sizes along the split dimension must match that of x
axis: An integer or scalar `int32` `Tensor`. The dimension along which to
      split. Must be in the range `[-rank(value), rank(value))`. Defaults to 0.
name: A name for the output tensor (optional).
</code></pre></div></p> <p>Returns: <div class=highlight><pre><span></span><code>returns `size_splits` `Tensor` objects;
</code></pre></div></p> <p>Examples:</p> <details> <summary>case0</summary> <div class=highlight><pre><span></span><code><span class=kn>from</span> <span class=nn>calibrator_custom</span> <span class=kn>import</span> <span class=n>sgs_chalk</span>
<span class=kn>import</span> <span class=nn>numpy</span> <span class=k>as</span> <span class=nn>np</span>
<span class=n>in0</span> <span class=o>=</span> <span class=n>sgs_chalk</span><span class=o>.</span><span class=n>Input</span><span class=p>((</span><span class=mi>28</span><span class=p>,</span><span class=mi>4</span><span class=p>,</span><span class=mi>4</span><span class=p>),</span><span class=n>name</span><span class=o>=</span><span class=s1>&#39;input0&#39;</span><span class=p>)</span>
<span class=n>output_list</span> <span class=o>=</span> <span class=n>sgs_chalk</span><span class=o>.</span><span class=n>Split_V</span><span class=p>(</span><span class=n>in0</span><span class=p>,</span><span class=n>SizeSplits</span><span class=o>=</span><span class=p>[</span><span class=mi>1</span><span class=p>,</span><span class=mi>2</span><span class=p>,</span><span class=mi>1</span><span class=p>],</span> <span class=n>axis</span><span class=o>=</span><span class=mi>2</span><span class=p>,</span><span class=n>name</span><span class=o>=</span><span class=p>[</span><span class=s1>&#39;output0&#39;</span><span class=p>,</span><span class=s1>&#39;output1&#39;</span><span class=p>,</span><span class=s1>&#39;output2&#39;</span><span class=p>])</span>
<span class=n>model</span> <span class=o>=</span> <span class=n>sgs_chalk</span><span class=o>.</span><span class=n>Model</span><span class=p>([</span><span class=n>in0</span><span class=p>],</span><span class=n>output_list</span><span class=p>)</span>
<span class=n>model</span><span class=o>.</span><span class=n>save</span><span class=p>(</span><span class=s1>&#39;test.sim&#39;</span><span class=p>,</span><span class=n>input_config</span><span class=o>=</span><span class=s1>&#39;./input_config.ini&#39;</span><span class=p>,</span><span class=n>convert_fixed</span><span class=o>=</span><span class=kc>True</span><span class=p>)</span>
</code></pre></div> </details> <details> <summary>case1</summary> <div class=highlight><pre><span></span><code><span class=kn>from</span> <span class=nn>calibrator_custom</span> <span class=kn>import</span> <span class=n>sgs_chalk</span>
<span class=kn>import</span> <span class=nn>numpy</span> <span class=k>as</span> <span class=nn>np</span>
<span class=n>in0</span> <span class=o>=</span> <span class=n>sgs_chalk</span><span class=o>.</span><span class=n>Input</span><span class=p>((</span><span class=mi>28</span><span class=p>,</span><span class=mi>4</span><span class=p>,</span><span class=mi>3</span><span class=p>),</span><span class=n>name</span><span class=o>=</span><span class=s1>&#39;input0&#39;</span><span class=p>)</span>
<span class=n>output_list</span> <span class=o>=</span> <span class=n>sgs_chalk</span><span class=o>.</span><span class=n>Split_V</span><span class=p>(</span><span class=n>in0</span><span class=p>,</span><span class=n>SizeSplits</span><span class=o>=</span><span class=p>[</span><span class=mi>2</span><span class=p>,</span><span class=mi>2</span><span class=p>],</span> <span class=n>axis</span><span class=o>=</span><span class=mi>1</span><span class=p>,</span><span class=n>name</span><span class=o>=</span><span class=p>[</span><span class=s1>&#39;output0&#39;</span><span class=p>,</span><span class=s1>&#39;output1&#39;</span><span class=p>])</span>
<span class=n>model</span> <span class=o>=</span> <span class=n>sgs_chalk</span><span class=o>.</span><span class=n>Model</span><span class=p>([</span><span class=n>in0</span><span class=p>],</span><span class=n>output_list</span><span class=p>)</span>
<span class=n>model</span><span class=o>.</span><span class=n>save</span><span class=p>(</span><span class=s1>&#39;test.sim&#39;</span><span class=p>,</span><span class=n>input_config</span><span class=o>=</span><span class=s1>&#39;./input_config.ini&#39;</span><span class=p>,</span><span class=n>convert_fixed</span><span class=o>=</span><span class=kc>True</span><span class=p>)</span>
</code></pre></div> </details> <ul> <li>Sqrt:</li> </ul> <p>Computes square root of x element-wise. I.e., (y = sqrt{x} = x^{&frac12;}).</p> <p>Args: <div class=highlight><pre><span></span><code>x: A `Tensor`
</code></pre></div></p> <p>Returns: <div class=highlight><pre><span></span><code>A `Tensor` of same shape and type as `x`.
</code></pre></div></p> <p>Examples:</p> <details> <summary>case0</summary> <div class=highlight><pre><span></span><code><span class=kn>from</span> <span class=nn>calibrator_custom</span> <span class=kn>import</span> <span class=n>sgs_chalk</span>
<span class=kn>import</span> <span class=nn>numpy</span> <span class=k>as</span> <span class=nn>np</span>
<span class=n>in0</span> <span class=o>=</span> <span class=n>sgs_chalk</span><span class=o>.</span><span class=n>Input</span><span class=p>((</span><span class=mi>28</span><span class=p>,</span><span class=mi>512</span><span class=p>),</span><span class=n>name</span><span class=o>=</span><span class=s1>&#39;input0&#39;</span><span class=p>)</span>
<span class=n>out0</span> <span class=o>=</span> <span class=n>sgs_chalk</span><span class=o>.</span><span class=n>Sqrt</span><span class=p>(</span><span class=n>in0</span><span class=p>,</span><span class=n>name</span><span class=o>=</span><span class=s1>&#39;output&#39;</span><span class=p>)</span>
<span class=n>model</span> <span class=o>=</span> <span class=n>sgs_chalk</span><span class=o>.</span><span class=n>Model</span><span class=p>([</span><span class=n>in0</span><span class=p>],[</span><span class=n>out0</span><span class=p>])</span>
<span class=n>model</span><span class=o>.</span><span class=n>save</span><span class=p>(</span><span class=s1>&#39;test.sim&#39;</span><span class=p>,</span><span class=n>input_config</span><span class=o>=</span><span class=s1>&#39;./input_config.ini&#39;</span><span class=p>,</span><span class=n>convert_fixed</span><span class=o>=</span><span class=kc>True</span><span class=p>)</span>
</code></pre></div> </details> <ul> <li>Square:</li> </ul> <p>Args: <div class=highlight><pre><span></span><code>input: A `Tensor`. Must be Variable Tensor.
name: A name for the model output tensor (optional).
</code></pre></div></p> <p>Returns: <div class=highlight><pre><span></span><code>A Variable `Tensor`. Has the same shape as input.
</code></pre></div></p> <p>Examples:</p> <details> <summary>case0</summary> <div class=highlight><pre><span></span><code><span class=kn>from</span> <span class=nn>calibrator_custom</span> <span class=kn>import</span> <span class=n>sgs_chalk</span>
<span class=kn>import</span> <span class=nn>numpy</span> <span class=k>as</span> <span class=nn>np</span>
<span class=nb>input</span> <span class=o>=</span> <span class=n>sgs_chalk</span><span class=o>.</span><span class=n>Input</span><span class=p>((</span><span class=mi>14</span><span class=p>,</span><span class=mi>3</span><span class=p>,</span><span class=mi>41</span><span class=p>,</span><span class=mi>2</span><span class=p>,</span><span class=mi>1</span><span class=p>,</span><span class=mi>2</span><span class=p>,</span><span class=mi>3</span><span class=p>,</span><span class=mi>5</span><span class=p>),</span> <span class=n>name</span><span class=o>=</span><span class=s1>&#39;input&#39;</span><span class=p>)</span>
<span class=n>square_out</span> <span class=o>=</span> <span class=n>sgs_chalk</span><span class=o>.</span><span class=n>Square</span><span class=p>(</span><span class=nb>input</span><span class=p>,</span> <span class=n>name</span><span class=o>=</span><span class=s1>&#39;softplus_out&#39;</span><span class=p>)</span>
<span class=n>model</span> <span class=o>=</span> <span class=n>sgs_chalk</span><span class=o>.</span><span class=n>Model</span><span class=p>([</span><span class=nb>input</span><span class=p>,],</span> <span class=n>square_out</span><span class=p>)</span>
<span class=n>model</span><span class=o>.</span><span class=n>save</span><span class=p>(</span><span class=s1>&#39;test.sim&#39;</span><span class=p>,</span> <span class=n>input_config</span><span class=o>=</span><span class=s1>&#39;./input_config.ini&#39;</span><span class=p>,</span> <span class=n>convert_fixed</span><span class=o>=</span><span class=kc>True</span><span class=p>)</span>
</code></pre></div> </details> <ul> <li>StridedSlice:</li> </ul> <p>Extracts a strided slice of a tensor (generalized python array indexing).</p> <p>Roughly speaking, this op extracts a slice of size <code>(end-begin)/stride</code> from the given <code>input_</code> tensor. Starting at the location specified by <code>begin</code> the slice continues by adding <code>stride</code> to the index until all dimensions are not less than <code>end</code>. Note that a stride can be negative, which causes a reverse slice.</p> <p>Given a Python slice <code>input[spec0, spec1, ..., specn]</code>, this function will be called as follows.</p> <p><code>begin</code>, <code>end</code>, and <code>strides</code> will be vectors of length n. n in general is not equal to the rank of the <code>input_</code> tensor.</p> <p>Args: <div class=highlight><pre><span></span><code>x: A `Tensor`. Must be Variable Tensor.
begin: const Tensor
size: const Tensor
begin_mask: An `int32` mask.
end_mask: An `int32` mask.
ellipsis_mask: An `int32` mask.
new_axis_mask: An `int32` mask.
shrink_axis_mask: An `int32` mask.
name: A name for the output tensor (optional).
</code></pre></div></p> <p>Returns: <div class=highlight><pre><span></span><code>A Variable `Tensor`. Has the same shape as `x`. (end-begin)/stride
</code></pre></div></p> <p>Examples:</p> <details> <summary>case0</summary> <div class=highlight><pre><span></span><code><span class=kn>from</span> <span class=nn>calibrator_custom</span> <span class=kn>import</span> <span class=n>sgs_chalk</span>
<span class=kn>import</span> <span class=nn>numpy</span> <span class=k>as</span> <span class=nn>np</span>
<span class=n>in0</span> <span class=o>=</span> <span class=n>sgs_chalk</span><span class=o>.</span><span class=n>Input</span><span class=p>((</span><span class=mi>28</span><span class=p>,</span><span class=mi>512</span><span class=p>,</span><span class=mi>4</span><span class=p>),</span><span class=n>name</span><span class=o>=</span><span class=s1>&#39;input0&#39;</span><span class=p>)</span>
<span class=n>out0</span> <span class=o>=</span> <span class=n>sgs_chalk</span><span class=o>.</span><span class=n>StridedSlice</span><span class=p>(</span><span class=n>in0</span><span class=p>,[</span><span class=mi>1</span><span class=p>,</span><span class=mi>0</span><span class=p>,</span><span class=mi>0</span><span class=p>],[</span><span class=mi>28</span><span class=p>,</span><span class=mi>512</span><span class=p>,</span><span class=mi>4</span><span class=p>],[</span><span class=mi>1</span><span class=p>,</span><span class=mi>2</span><span class=p>,</span><span class=mi>1</span><span class=p>],</span><span class=n>name</span><span class=o>=</span><span class=s1>&#39;output&#39;</span><span class=p>)</span>
<span class=n>model</span> <span class=o>=</span> <span class=n>sgs_chalk</span><span class=o>.</span><span class=n>Model</span><span class=p>([</span><span class=n>in0</span><span class=p>],</span><span class=n>out0</span><span class=p>)</span>
<span class=n>model</span><span class=o>.</span><span class=n>save</span><span class=p>(</span><span class=s1>&#39;test.sim&#39;</span><span class=p>)</span>
</code></pre></div> </details> <ul> <li>Sub:</li> </ul> <p>Returns x - y element-wise.</p> <p>Args: <div class=highlight><pre><span></span><code>x: A `Tensor`. Must be Variable Tensor.
y: A `Tensor` or `numpy.ndarray`. Must have the same type as `x`, can be Variable or Const Tensor.
   Support inner most dimension broadcasting.
name: A name for the output tensor (optional).
</code></pre></div></p> <p>Returns: <div class=highlight><pre><span></span><code>A Variable `Tensor`. Has the same shape as `x`.
</code></pre></div></p> <p>Examples:</p> <details> <summary>case0</summary> <div class=highlight><pre><span></span><code><span class=kn>from</span> <span class=nn>calibrator_custom</span> <span class=kn>import</span> <span class=n>sgs_chalk</span>
<span class=kn>import</span> <span class=nn>numpy</span> <span class=k>as</span> <span class=nn>np</span>
<span class=n>in0</span> <span class=o>=</span> <span class=n>sgs_chalk</span><span class=o>.</span><span class=n>Input</span><span class=p>((</span><span class=mi>28</span><span class=p>,</span><span class=mi>512</span><span class=p>),</span><span class=n>name</span><span class=o>=</span><span class=s1>&#39;input0&#39;</span><span class=p>)</span>
<span class=n>in1</span> <span class=o>=</span> <span class=n>sgs_chalk</span><span class=o>.</span><span class=n>Input</span><span class=p>((</span><span class=mi>28</span><span class=p>,</span><span class=mi>512</span><span class=p>),</span><span class=n>name</span><span class=o>=</span><span class=s1>&#39;input1&#39;</span><span class=p>)</span>
<span class=n>out0</span> <span class=o>=</span> <span class=n>sgs_chalk</span><span class=o>.</span><span class=n>Sub</span><span class=p>(</span><span class=n>in0</span><span class=p>,</span><span class=n>in1</span><span class=p>,</span><span class=n>name</span><span class=o>=</span><span class=s1>&#39;output&#39;</span><span class=p>)</span>
<span class=n>model</span> <span class=o>=</span> <span class=n>sgs_chalk</span><span class=o>.</span><span class=n>Model</span><span class=p>([</span><span class=n>in0</span><span class=p>,</span><span class=n>in1</span><span class=p>],</span><span class=n>out0</span><span class=p>)</span>
<span class=n>model</span><span class=o>.</span><span class=n>save</span><span class=p>(</span><span class=s1>&#39;test.sim&#39;</span><span class=p>)</span>
</code></pre></div> </details> <details> <summary>case1</summary> <div class=highlight><pre><span></span><code><span class=kn>from</span> <span class=nn>calibrator_custom</span> <span class=kn>import</span> <span class=n>sgs_chalk</span>
<span class=kn>import</span> <span class=nn>numpy</span> <span class=k>as</span> <span class=nn>np</span>
<span class=n>in0</span> <span class=o>=</span> <span class=n>sgs_chalk</span><span class=o>.</span><span class=n>Input</span><span class=p>((</span><span class=mi>28</span><span class=p>,</span><span class=mi>512</span><span class=p>),</span><span class=n>name</span><span class=o>=</span><span class=s1>&#39;input0&#39;</span><span class=p>)</span>
<span class=n>out0</span> <span class=o>=</span> <span class=n>sgs_chalk</span><span class=o>.</span><span class=n>Sub</span><span class=p>(</span><span class=n>in0</span><span class=p>,</span><span class=mf>5.0</span><span class=p>,</span><span class=n>name</span><span class=o>=</span><span class=s1>&#39;output&#39;</span><span class=p>)</span>
<span class=n>model</span> <span class=o>=</span> <span class=n>sgs_chalk</span><span class=o>.</span><span class=n>Model</span><span class=p>([</span><span class=n>in0</span><span class=p>],</span><span class=n>out0</span><span class=p>)</span>
<span class=n>model</span><span class=o>.</span><span class=n>save</span><span class=p>(</span><span class=s1>&#39;test.sim&#39;</span><span class=p>)</span>
</code></pre></div> </details> <ul> <li>Sum:</li> </ul> <p>Computes the sum of elements across dimensions of a tensor.</p> <p>Reduces <code>input_tensor</code> along the dimensions given in <code>axis</code>.</p> <p>Args: <div class=highlight><pre><span></span><code>tensor: A `Tensor`.
axis: The dimensions to reduce.
keep_dim:whether keep dim or not
name: A name for the output tensor (optional).
</code></pre></div></p> <p>Returns: <div class=highlight><pre><span></span><code>A `Tensor`. The reduced tensor.
</code></pre></div></p> <p>Examples:</p> <details> <summary>case0</summary> <div class=highlight><pre><span></span><code><span class=kn>from</span> <span class=nn>calibrator_custom</span> <span class=kn>import</span> <span class=n>sgs_chalk</span>
<span class=kn>import</span> <span class=nn>numpy</span> <span class=k>as</span> <span class=nn>np</span>
<span class=n>in0</span> <span class=o>=</span> <span class=n>sgs_chalk</span><span class=o>.</span><span class=n>Input</span><span class=p>((</span><span class=mi>1</span><span class=p>,</span><span class=mi>28</span><span class=p>,</span><span class=mi>28</span><span class=p>,</span><span class=mi>512</span><span class=p>),</span><span class=n>name</span><span class=o>=</span><span class=s1>&#39;input0&#39;</span><span class=p>)</span>
<span class=n>out0</span> <span class=o>=</span> <span class=n>sgs_chalk</span><span class=o>.</span><span class=n>Sum</span><span class=p>(</span><span class=n>in0</span><span class=p>,[</span><span class=mi>0</span><span class=p>,</span><span class=mi>1</span><span class=p>,</span><span class=mi>2</span><span class=p>,</span><span class=mi>3</span><span class=p>],</span><span class=n>name</span><span class=o>=</span><span class=s1>&#39;output&#39;</span><span class=p>)</span>
<span class=n>model</span> <span class=o>=</span> <span class=n>sgs_chalk</span><span class=o>.</span><span class=n>Model</span><span class=p>([</span><span class=n>in0</span><span class=p>],</span><span class=n>out0</span><span class=p>)</span>
<span class=n>model</span><span class=o>.</span><span class=n>save</span><span class=p>(</span><span class=s1>&#39;test.sim&#39;</span><span class=p>)</span>
</code></pre></div> </details> <details> <summary>case1</summary> <div class=highlight><pre><span></span><code><span class=kn>from</span> <span class=nn>calibrator_custom</span> <span class=kn>import</span> <span class=n>sgs_chalk</span>
<span class=kn>import</span> <span class=nn>numpy</span> <span class=k>as</span> <span class=nn>np</span>
<span class=n>in0</span> <span class=o>=</span> <span class=n>sgs_chalk</span><span class=o>.</span><span class=n>Input</span><span class=p>((</span><span class=mi>1</span><span class=p>,</span><span class=mi>28</span><span class=p>,</span><span class=mi>28</span><span class=p>,</span><span class=mi>512</span><span class=p>),</span><span class=n>name</span><span class=o>=</span><span class=s1>&#39;input0&#39;</span><span class=p>)</span>
<span class=n>out0</span> <span class=o>=</span> <span class=n>sgs_chalk</span><span class=o>.</span><span class=n>Sum</span><span class=p>(</span><span class=n>in0</span><span class=p>,[</span><span class=mi>1</span><span class=p>,</span><span class=mi>2</span><span class=p>],</span><span class=n>name</span><span class=o>=</span><span class=s1>&#39;output&#39;</span><span class=p>)</span>
<span class=n>model</span> <span class=o>=</span> <span class=n>sgs_chalk</span><span class=o>.</span><span class=n>Model</span><span class=p>([</span><span class=n>in0</span><span class=p>],</span><span class=n>out0</span><span class=p>)</span>
<span class=n>model</span><span class=o>.</span><span class=n>save</span><span class=p>(</span><span class=s1>&#39;test.sim&#39;</span><span class=p>)</span>
</code></pre></div> </details> <ul> <li>TFLite_Detection_NMS:</li> </ul> <p>sigmastar postprocess nms</p> <p>Args: <div class=highlight><pre><span></span><code>max_detection: max number pf output dectection bboxes
num_classes:number of classes
is_need_index : outputs include index or not
</code></pre></div></p> <p>Examples:</p> <details> <summary>case0</summary> <div class=highlight><pre><span></span><code><span class=kn>from</span> <span class=nn>calibrator_custom</span> <span class=kn>import</span> <span class=n>sgs_chalk</span>
<span class=kn>import</span> <span class=nn>numpy</span> <span class=k>as</span> <span class=nn>np</span>
<span class=n>bosdecoder_output_list</span> <span class=o>=</span> <span class=n>sgs_chalk</span><span class=o>.</span><span class=n>BoxDecoder</span><span class=p>(</span><span class=n>config</span><span class=p>,</span><span class=n>output_list</span><span class=p>)</span>
<span class=n>confidence_tensor</span> <span class=o>=</span> <span class=n>sgs_chalk</span><span class=o>.</span><span class=n>Logistic</span><span class=p>(</span><span class=n>output_list</span><span class=p>[</span><span class=mi>4</span><span class=p>],</span><span class=n>name</span><span class=o>=</span><span class=s2>&quot;confidence_tensor&quot;</span><span class=p>)</span>
<span class=n>SGS_score0</span> <span class=o>=</span> <span class=n>sgs_chalk</span><span class=o>.</span><span class=n>Logistic</span><span class=p>(</span><span class=n>output_list</span><span class=p>[</span><span class=mi>5</span><span class=p>],</span><span class=n>name</span><span class=o>=</span><span class=s2>&quot;score0_tensor&quot;</span><span class=p>)</span>
<span class=n>SGS_score1</span> <span class=o>=</span> <span class=n>sgs_chalk</span><span class=o>.</span><span class=n>Mul</span><span class=p>(</span><span class=n>confidence_tensor</span><span class=p>,</span><span class=n>SGS_score0</span><span class=p>,</span><span class=n>name</span><span class=o>=</span><span class=s2>&quot;SGS_score1&quot;</span><span class=p>)</span>
<span class=n>out1</span> <span class=o>=</span> <span class=n>sgs_chalk</span><span class=o>.</span><span class=n>TFLite_Detection_NMS</span><span class=p>(</span><span class=n>bosdecoder_output_list</span><span class=p>[</span><span class=mi>0</span><span class=p>],</span><span class=n>bosdecoder_output_list</span><span class=p>[</span><span class=mi>1</span><span class=p>],</span><span class=n>bosdecoder_output_list</span><span class=p>[</span><span class=mi>2</span><span class=p>],</span>
                                    <span class=n>bosdecoder_output_list</span><span class=p>[</span><span class=mi>3</span><span class=p>],</span><span class=n>confidence_tensor</span><span class=p>,</span><span class=n>SGS_score1</span><span class=p>,</span><span class=n>output_list</span><span class=p>[</span><span class=mi>6</span><span class=p>],</span><span class=n>mode</span><span class=o>=</span><span class=s1>&#39;YOLO&#39;</span><span class=p>,</span>
                                    <span class=n>max_detections</span><span class=o>=</span><span class=mi>100</span><span class=p>,</span><span class=n>nms_score_threshold</span><span class=o>=</span><span class=mf>0.4</span><span class=p>,</span>
                                    <span class=n>nms_iou_threshold</span><span class=o>=</span><span class=mf>0.45</span><span class=p>,</span><span class=n>num_classes</span><span class=o>=</span><span class=mi>80</span><span class=p>,</span><span class=n>is_need_index</span><span class=o>=</span><span class=kc>False</span><span class=p>)</span>
</code></pre></div> </details> <details> <summary>case1</summary> <div class=highlight><pre><span></span><code><span class=kn>from</span> <span class=nn>calibrator_custom</span> <span class=kn>import</span> <span class=n>sgs_chalk</span>
<span class=kn>import</span> <span class=nn>numpy</span> <span class=k>as</span> <span class=nn>np</span>
<span class=n>bosdecoder_output_list</span> <span class=o>=</span> <span class=n>sgs_chalk</span><span class=o>.</span><span class=n>BoxDecoder</span><span class=p>(</span><span class=n>config</span><span class=p>,</span><span class=n>output_list</span><span class=p>)</span>
<span class=n>mbox_conf_softmax</span> <span class=o>=</span> <span class=n>sgs_chalk</span><span class=o>.</span><span class=n>Input</span><span class=p>(</span><span class=n>model_config</span><span class=p>[</span><span class=s2>&quot;input_shape&quot;</span><span class=p>][</span><span class=mi>1</span><span class=p>],</span> <span class=n>name</span> <span class=o>=</span> <span class=n>model_config</span><span class=p>[</span><span class=s2>&quot;input&quot;</span><span class=p>][</span><span class=mi>1</span><span class=p>])</span>
<span class=n>postprocess_max_output_list</span> <span class=o>=</span> <span class=n>sgs_chalk</span><span class=o>.</span><span class=n>PostProcess_Max</span><span class=p>(</span><span class=n>mbox_conf_softmax</span><span class=p>,</span><span class=n>scores_lengh</span><span class=o>=</span><span class=mi>21</span><span class=p>,</span><span class=n>skip</span><span class=o>=</span><span class=mi>1</span><span class=p>)</span>
<span class=n>out1</span> <span class=o>=</span> <span class=n>sgs_chalk</span><span class=o>.</span><span class=n>TFLite_Detection_NMS</span><span class=p>(</span><span class=n>bosdecoder_output_list</span><span class=p>[</span><span class=mi>0</span><span class=p>],</span><span class=n>bosdecoder_output_list</span><span class=p>[</span><span class=mi>1</span><span class=p>],</span><span class=n>bosdecoder_output_list</span><span class=p>[</span><span class=mi>2</span><span class=p>],</span>
                                    <span class=n>bosdecoder_output_list</span><span class=p>[</span><span class=mi>3</span><span class=p>],</span><span class=n>postprocess_max_output_list</span><span class=p>[</span><span class=mi>0</span><span class=p>],</span><span class=n>postprocess_max_output_list</span><span class=p>[</span><span class=mi>1</span><span class=p>],</span>
                                    <span class=n>mode</span><span class=o>=</span><span class=s1>&#39;SSD&#39;</span><span class=p>,</span><span class=n>max_detections</span><span class=o>=</span><span class=mi>10</span><span class=p>,</span><span class=n>nms_score_threshold</span><span class=o>=</span><span class=mf>0.01</span><span class=p>,</span>
                                    <span class=n>nms_iou_threshold</span><span class=o>=</span><span class=mf>0.45</span><span class=p>,</span><span class=n>num_classes</span><span class=o>=</span><span class=mi>20</span><span class=p>,</span><span class=n>is_need_index</span><span class=o>=</span><span class=kc>False</span><span class=p>)</span>
</code></pre></div> </details> <ul> <li>Tanh:</li> </ul> <p>Args: <div class=highlight><pre><span></span><code>tensor: A `Tensor`.
name: A name for the output tensor (optional).
</code></pre></div></p> <p>Returns: <div class=highlight><pre><span></span><code>A `Tensor`. Has the same type as `tensor`.
</code></pre></div></p> <p>Examples:</p> <details> <summary>case0</summary> <div class=highlight><pre><span></span><code><span class=kn>from</span> <span class=nn>calibrator_custom</span> <span class=kn>import</span> <span class=n>sgs_chalk</span>
<span class=kn>import</span> <span class=nn>numpy</span> <span class=k>as</span> <span class=nn>np</span>
<span class=n>in0</span> <span class=o>=</span> <span class=n>sgs_chalk</span><span class=o>.</span><span class=n>Input</span><span class=p>((</span><span class=mi>28</span><span class=p>,</span><span class=mi>512</span><span class=p>),</span><span class=n>name</span><span class=o>=</span><span class=s1>&#39;input0&#39;</span><span class=p>)</span>
<span class=n>out0</span> <span class=o>=</span> <span class=n>sgs_chalk</span><span class=o>.</span><span class=n>Tanh</span><span class=p>(</span><span class=n>in0</span><span class=p>,</span><span class=n>name</span><span class=o>=</span><span class=s1>&#39;output&#39;</span><span class=p>)</span>
<span class=n>model</span> <span class=o>=</span> <span class=n>sgs_chalk</span><span class=o>.</span><span class=n>Model</span><span class=p>([</span><span class=n>in0</span><span class=p>],</span><span class=n>out0</span><span class=p>)</span>
<span class=n>model</span><span class=o>.</span><span class=n>save</span><span class=p>(</span><span class=s1>&#39;test.sim&#39;</span><span class=p>)</span>
</code></pre></div> </details> <ul> <li>Tile:</li> </ul> <p>Constructs a tensor by tiling a given tensor.</p> <p>This operation creates a new tensor by replicating <code>input</code> <code>multiples</code> times. The output tensor's i'th dimension has <code>input.dims(i) * multiples[i]</code> elements, and the values of <code>input</code> are replicated <code>multiples[i]</code> times along the 'i'th dimension. For example, tiling <code>[a b c d]</code> by <code>[2]</code> produces <code>[a b c d a b c d]</code>.</p> <p>Args: <div class=highlight><pre><span></span><code>input: A `Tensor`. 1-D or higher.
multiples: A `Tensor`.  Length must be the same as the number of dimensions in `input`
name: A name for the output tensor (optional).
</code></pre></div></p> <p>Returns: <div class=highlight><pre><span></span><code>A `Tensor`. Has the same type as `input`.
</code></pre></div></p> <p>Examples:</p> <details> <summary>case0</summary> <div class=highlight><pre><span></span><code><span class=kn>from</span> <span class=nn>calibrator_custom</span> <span class=kn>import</span> <span class=n>sgs_chalk</span>
<span class=kn>import</span> <span class=nn>numpy</span> <span class=k>as</span> <span class=nn>np</span>
<span class=n>in0</span> <span class=o>=</span> <span class=n>sgs_chalk</span><span class=o>.</span><span class=n>Input</span><span class=p>((</span><span class=mi>28</span><span class=p>,</span><span class=mi>512</span><span class=p>,</span><span class=mi>4</span><span class=p>),</span><span class=n>name</span><span class=o>=</span><span class=s1>&#39;input0&#39;</span><span class=p>)</span>
<span class=n>out0</span> <span class=o>=</span> <span class=n>sgs_chalk</span><span class=o>.</span><span class=n>Tile</span><span class=p>(</span><span class=n>in0</span><span class=p>,(</span><span class=mi>1</span><span class=p>,</span><span class=mi>1</span><span class=p>,</span><span class=mi>2</span><span class=p>),</span><span class=n>name</span><span class=o>=</span><span class=s1>&#39;output&#39;</span><span class=p>)</span>
<span class=n>model</span> <span class=o>=</span> <span class=n>sgs_chalk</span><span class=o>.</span><span class=n>Model</span><span class=p>([</span><span class=n>in0</span><span class=p>],</span><span class=n>out0</span><span class=p>)</span>
<span class=n>model</span><span class=o>.</span><span class=n>save</span><span class=p>(</span><span class=s1>&#39;test.sim&#39;</span><span class=p>)</span>
</code></pre></div> </details> <ul> <li>Transpose:</li> </ul> <p>Transposes x.</p> <p>Permutes the dimensions according to <code>perm</code>.</p> <p>The returned tensor's dimension i will correspond to the input dimension <code>perm[i]</code>. If <code>perm</code> is not given, it is set to (n-1...0), where n is the rank of the input tensor. Hence by default, this operation performs a regular matrix transpose on 2-D input Tensors.</p> <p>Args: <div class=highlight><pre><span></span><code>input: A `Tensor`. 1-D or higher.
perm: A permutation of the dimensions of `a`.
name: A name for the output tensor (optional).
</code></pre></div></p> <p>Returns: <div class=highlight><pre><span></span><code>A transposed `Tensor`.
</code></pre></div></p> <p>Examples:</p> <details> <summary>case0</summary> <div class=highlight><pre><span></span><code><span class=kn>from</span> <span class=nn>calibrator_custom</span> <span class=kn>import</span> <span class=n>sgs_chalk</span>
<span class=kn>import</span> <span class=nn>numpy</span> <span class=k>as</span> <span class=nn>np</span>
<span class=n>in0</span> <span class=o>=</span> <span class=n>sgs_chalk</span><span class=o>.</span><span class=n>Input</span><span class=p>((</span><span class=mi>28</span><span class=p>,</span><span class=mi>512</span><span class=p>,</span><span class=mi>4</span><span class=p>),</span><span class=n>name</span><span class=o>=</span><span class=s1>&#39;input0&#39;</span><span class=p>)</span>
<span class=n>out0</span> <span class=o>=</span> <span class=n>sgs_chalk</span><span class=o>.</span><span class=n>Transpose</span><span class=p>(</span><span class=n>in0</span><span class=p>,(</span><span class=mi>0</span><span class=p>,</span><span class=mi>2</span><span class=p>,</span><span class=mi>1</span><span class=p>),</span><span class=n>name</span><span class=o>=</span><span class=s1>&#39;output&#39;</span><span class=p>)</span>
<span class=n>model</span> <span class=o>=</span> <span class=n>sgs_chalk</span><span class=o>.</span><span class=n>Model</span><span class=p>([</span><span class=n>in0</span><span class=p>],</span><span class=n>out0</span><span class=p>)</span>
<span class=n>model</span><span class=o>.</span><span class=n>save</span><span class=p>(</span><span class=s1>&#39;test.sim&#39;</span><span class=p>)</span>
</code></pre></div> </details> <details> <summary>case1</summary> <div class=highlight><pre><span></span><code><span class=kn>from</span> <span class=nn>calibrator_custom</span> <span class=kn>import</span> <span class=n>sgs_chalk</span>
<span class=kn>import</span> <span class=nn>numpy</span> <span class=k>as</span> <span class=nn>np</span>
<span class=n>in0</span> <span class=o>=</span> <span class=n>sgs_chalk</span><span class=o>.</span><span class=n>Input</span><span class=p>((</span><span class=mi>28</span><span class=p>,</span><span class=mi>512</span><span class=p>,</span><span class=mi>4</span><span class=p>),</span><span class=n>name</span><span class=o>=</span><span class=s1>&#39;input0&#39;</span><span class=p>)</span>
<span class=n>in1</span> <span class=o>=</span> <span class=n>sgs_chalk</span><span class=o>.</span><span class=n>Input</span><span class=p>((</span><span class=mi>28</span><span class=p>,</span><span class=mi>512</span><span class=p>,</span><span class=mi>4</span><span class=p>),</span><span class=n>name</span><span class=o>=</span><span class=s1>&#39;input1&#39;</span><span class=p>)</span>
<span class=n>out0</span> <span class=o>=</span> <span class=n>sgs_chalk</span><span class=o>.</span><span class=n>Add</span><span class=p>(</span><span class=n>in0</span><span class=p>,</span><span class=n>in1</span><span class=p>)</span>
<span class=n>out1</span> <span class=o>=</span> <span class=n>sgs_chalk</span><span class=o>.</span><span class=n>Transpose</span><span class=p>(</span><span class=n>out0</span><span class=p>,(</span><span class=mi>0</span><span class=p>,</span><span class=mi>2</span><span class=p>,</span><span class=mi>1</span><span class=p>),</span><span class=n>name</span><span class=o>=</span><span class=s1>&#39;output&#39;</span><span class=p>)</span>
<span class=n>model</span> <span class=o>=</span> <span class=n>sgs_chalk</span><span class=o>.</span><span class=n>Model</span><span class=p>([</span><span class=n>in0</span><span class=p>,</span><span class=n>in1</span><span class=p>],</span><span class=n>out1</span><span class=p>)</span>
<span class=n>model</span><span class=o>.</span><span class=n>save</span><span class=p>(</span><span class=s1>&#39;test.sim&#39;</span><span class=p>,</span><span class=n>input_config</span><span class=o>=</span><span class=s1>&#39;./input_config.ini&#39;</span><span class=p>,</span><span class=n>convert_fixed</span><span class=o>=</span><span class=kc>True</span><span class=p>)</span>
</code></pre></div> </details> <ul> <li>Unpack:</li> </ul> <p>Unpacks the given dimension of a rank-<code>R</code> tensor into rank-<code>(R-1)</code> tensors.</p> <p>Unpacks <code>num</code> tensors from <code>value</code> by chipping it along the <code>axis</code> dimension. If <code>num</code> is not specified (the default), it is inferred from <code>value</code>'s shape. If <code>value.shape[axis]</code> is not known, <code>ValueError</code> is raised.</p> <p>For example, given a tensor of shape <code>(A, B, C, D)</code>;</p> <p>If <code>axis == 0</code> then the i'th tensor in <code>output</code> is the slice <code>value[i, :, :, :]</code> and each tensor in <code>output</code> will have shape <code>(B, C, D)</code>. (Note that the dimension unpacked along is gone, unlike <code>split</code>).</p> <p>If <code>axis == 1</code> then the i'th tensor in <code>output</code> is the slice <code>value[:, i, :, :]</code> and each tensor in <code>output</code> will have shape <code>(A, C, D)</code>. Etc.</p> <p>Args: <div class=highlight><pre><span></span><code>x: A `Tensor`. Must be Variable Tensor.
axis: An `int`. The axis to unstack along. Defaults to the first dimension.
    Negative values wrap around, so the valid range is `[-R, R)
num:  An `int`. The length of the dimension `axis`. Automatically inferred if
    `None` (the default).
name: A name for the output tensor (optional).
</code></pre></div></p> <p>Returns: <div class=highlight><pre><span></span><code>The list of `Tensor` objects unstacked from `value`.
</code></pre></div></p> <p>Examples:</p> <details> <summary>case0</summary> <div class=highlight><pre><span></span><code><span class=kn>from</span> <span class=nn>calibrator_custom</span> <span class=kn>import</span> <span class=n>sgs_chalk</span>
<span class=kn>import</span> <span class=nn>numpy</span> <span class=k>as</span> <span class=nn>np</span>
<span class=n>in0</span> <span class=o>=</span> <span class=n>sgs_chalk</span><span class=o>.</span><span class=n>Input</span><span class=p>((</span><span class=mi>28</span><span class=p>,</span><span class=mi>4</span><span class=p>,</span><span class=mi>3</span><span class=p>),</span><span class=n>name</span><span class=o>=</span><span class=s1>&#39;input0&#39;</span><span class=p>)</span>
<span class=n>output_list</span> <span class=o>=</span> <span class=n>sgs_chalk</span><span class=o>.</span><span class=n>Unpack</span><span class=p>(</span><span class=n>in0</span><span class=p>,</span><span class=n>axis</span><span class=o>=</span><span class=mi>2</span><span class=p>,</span><span class=n>name</span><span class=o>=</span><span class=p>[</span><span class=s1>&#39;output0&#39;</span><span class=p>,</span><span class=s1>&#39;output1&#39;</span><span class=p>,</span><span class=s1>&#39;output2&#39;</span><span class=p>])</span>
<span class=n>model</span> <span class=o>=</span> <span class=n>sgs_chalk</span><span class=o>.</span><span class=n>Model</span><span class=p>([</span><span class=n>in0</span><span class=p>],</span><span class=n>output_list</span><span class=p>)</span>
<span class=n>model</span><span class=o>.</span><span class=n>save</span><span class=p>(</span><span class=s1>&#39;test.sim&#39;</span><span class=p>,</span><span class=n>input_config</span><span class=o>=</span><span class=s1>&#39;./input_config.ini&#39;</span><span class=p>,</span><span class=n>convert_fixed</span><span class=o>=</span><span class=kc>True</span><span class=p>)</span>
</code></pre></div> </details> <details> <summary>case1</summary> <div class=highlight><pre><span></span><code><span class=kn>from</span> <span class=nn>calibrator_custom</span> <span class=kn>import</span> <span class=n>sgs_chalk</span>
<span class=kn>import</span> <span class=nn>numpy</span> <span class=k>as</span> <span class=nn>np</span>
<span class=n>in0</span> <span class=o>=</span> <span class=n>sgs_chalk</span><span class=o>.</span><span class=n>Input</span><span class=p>((</span><span class=mi>28</span><span class=p>,</span><span class=mi>4</span><span class=p>,</span><span class=mi>3</span><span class=p>),</span><span class=n>name</span><span class=o>=</span><span class=s1>&#39;input0&#39;</span><span class=p>)</span>
<span class=n>output_list</span> <span class=o>=</span> <span class=n>sgs_chalk</span><span class=o>.</span><span class=n>Unpack</span><span class=p>(</span><span class=n>in0</span><span class=p>,</span><span class=n>axis</span><span class=o>=</span><span class=mi>1</span><span class=p>,</span><span class=n>name</span><span class=o>=</span><span class=p>[</span><span class=s1>&#39;output0&#39;</span><span class=p>,</span><span class=s1>&#39;output1&#39;</span><span class=p>,</span><span class=s1>&#39;output2&#39;</span><span class=p>,</span><span class=s1>&#39;output3&#39;</span><span class=p>])</span>
<span class=n>model</span> <span class=o>=</span> <span class=n>sgs_chalk</span><span class=o>.</span><span class=n>Model</span><span class=p>([</span><span class=n>in0</span><span class=p>],</span><span class=n>output_list</span><span class=p>)</span>
<span class=n>model</span><span class=o>.</span><span class=n>save</span><span class=p>(</span><span class=s1>&#39;test.sim&#39;</span><span class=p>,</span><span class=n>input_config</span><span class=o>=</span><span class=s1>&#39;./input_config.ini&#39;</span><span class=p>,</span><span class=n>convert_fixed</span><span class=o>=</span><span class=kc>True</span><span class=p>)</span>
</code></pre></div> </details> </article> </div> </div> </main> <footer class=md-footer> <nav class="md-footer__inner md-grid" aria-label=Footer> <a href=DumpDebug_Tool.html class="md-footer__link md-footer__link--prev" rel=prev> <div class="md-footer__button md-icon"> <svg xmlns=http://www.w3.org/2000/svg viewbox="0 0 24 24"><path d="M20 11v2H8l5.5 5.5-1.42 1.42L4.16 12l7.92-7.92L13.5 5.5 8 11h12z"/></svg> </div> <div class=md-footer__title> <div class=md-ellipsis> <span class=md-footer__direction> Previous </span> 6. DumpDebug Tool </div> </div> </a> <a href=Adding_A_New_Layer.html class="md-footer__link md-footer__link--next" rel=next> <div class=md-footer__title> <div class=md-ellipsis> <span class=md-footer__direction> Next </span> 8. 如何添加新的Layer </div> </div> <div class="md-footer__button md-icon"> <svg xmlns=http://www.w3.org/2000/svg viewbox="0 0 24 24"><path d="M4 11v2h12l-5.5 5.5 1.42 1.42L19.84 12l-7.92-7.92L10.5 5.5 16 11H4z"/></svg> </div> </a> </nav> <div class="md-footer-meta md-typeset"> <div class="md-footer-meta__inner md-grid"> <div class=md-footer-copyright> <div class=md-footer-copyright__highlight> Copyright&copy; 2021 SigmaStar Technology. All rights reserved. Security Level: Confidential A. </div> Made with <a href=https://squidfunk.github.io/mkdocs-material/ target=_blank rel=noopener> Material for MkDocs </a> </div> </div> </div> </footer> </div> <div class=md-dialog data-md-component=dialog> <div class="md-dialog__inner md-typeset"></div> </div> <script id=__config type=application/json>{"base": "../../..", "features": [], "translations": {"clipboard.copy": "Copy to clipboard", "clipboard.copied": "Copied to clipboard", "search.config.lang": "en", "search.config.pipeline": "trimmer, stopWordFilter", "search.config.separator": "[\\s\\-]+", "search.placeholder": "Search", "search.result.placeholder": "Type to start searching", "search.result.none": "No matching documents", "search.result.one": "1 matching document", "search.result.other": "# matching documents", "search.result.more.one": "1 more on this page", "search.result.more.other": "# more on this page", "search.result.term.missing": "Missing"}, "search": "../../../assets/javascripts/workers/search.fb4a9340.min.js", "version": null}</script> <script src=../../../assets/javascripts/bundle.a1c7c35e.min.js></script> <script src=../../../search/search_index.js></script> <script src=../../../javascripts/extra.js></script> <script src="https://cdnjs.cloudflare.com/ajax/libs/mathjax/2.7.0/MathJax.js?config=TeX-MML-AM_CHTML"></script> <script src=https://cdnjs.cloudflare.com/ajax/libs/raphael/2.2.7/raphael.min.js></script> <script src=https://cdnjs.cloudflare.com/ajax/libs/underscore.js/1.8.3/underscore-min.js></script> <script src=https://cdnjs.cloudflare.com/ajax/libs/js-sequence-diagrams/1.0.6/sequence-diagram-min.js></script> <script src=https://cdnjs.cloudflare.com/ajax/libs/flowchart/1.6.5/flowchart.min.js></script> <script src=https://unpkg.com/freezeframe/dist/freezeframe.min.js></script> <script src=https://unpkg.com/mermaid@7.1.0/dist/mermaid.min.js></script> <script src=../../../javascripts/umlconvert.js></script> </body> </html>