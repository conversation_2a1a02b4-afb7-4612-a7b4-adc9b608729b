<!doctype html><html lang=en class=no-js> <head><meta charset=utf-8><meta name=viewport content="width=device-width,initial-scale=1"><meta name=description content="Personal Documentation System Template"><meta name=author content="<PERSON> Cho<PERSON>"><link rel=icon href=../../../images/favicon.ico><meta name=generator content="mkdocs-1.1.2, mkdocs-material-7.0.6"><title>1. 快速开始 - IPU SDK</title><link rel=stylesheet href=../../../assets/stylesheets/main.2c0c5eaf.min.css><link rel=stylesheet href=../../../assets/stylesheets/palette.7fa14f5b.min.css><meta name=theme-color content=#009485><link rel=preconnect href=https://fonts.gstatic.com crossorigin><link rel=stylesheet href="https://fonts.googleapis.com/css?family=Roboto:300,400,400i,700%7CRoboto+Mono&display=fallback"><style>:root{--md-text-font-family:"Roboto";--md-code-font-family:"Roboto Mono"}</style><link rel=stylesheet href=../../../stylesheets/extra.css></head> <body dir=ltr data-md-color-scheme data-md-color-primary=teal data-md-color-accent=teal> <input class=md-toggle data-md-toggle=drawer type=checkbox id=__drawer autocomplete=off> <input class=md-toggle data-md-toggle=search type=checkbox id=__search autocomplete=off> <label class=md-overlay for=__drawer></label> <div data-md-component=skip> <a href=#11 class=md-skip> Skip to content </a> </div> <div data-md-component=announce> </div> <header class=md-header data-md-component=header> <nav class="md-header__inner md-grid" aria-label=Header> <a href=../../.. title="IPU SDK" class="md-header__button md-logo" aria-label="IPU SDK" data-md-component=logo> <svg xmlns=http://www.w3.org/2000/svg viewbox="0 0 24 24"><path d="M10 20v-6h4v6h5v-8h3L12 3 2 12h3v8h5z"/></svg> </a> <label class="md-header__button md-icon" for=__drawer> <svg xmlns=http://www.w3.org/2000/svg viewbox="0 0 24 24"><path d="M3 6h18v2H3V6m0 5h18v2H3v-2m0 5h18v2H3v-2z"/></svg> </label> <div class=md-header__title data-md-component=header-title> <div class=md-header__ellipsis> <div class=md-header__topic> <span class=md-ellipsis> IPU SDK </span> </div> <div class=md-header__topic data-md-component=header-topic> <span class=md-ellipsis> 1. 快速开始 </span> </div> </div> </div> <div class=md-header__options> </div> <label class="md-header__button md-icon" for=__search> <svg xmlns=http://www.w3.org/2000/svg viewbox="0 0 24 24"><path d="M9.5 3A6.5 6.5 0 0 1 16 9.5c0 1.61-.59 3.09-1.56 4.23l.27.27h.79l5 5-1.5 1.5-5-5v-.79l-.27-.27A6.516 6.516 0 0 1 9.5 16 6.5 6.5 0 0 1 3 9.5 6.5 6.5 0 0 1 9.5 3m0 2C7 5 5 7 5 9.5S7 14 9.5 14 14 12 14 9.5 12 5 9.5 5z"/></svg> </label> <div class=md-search data-md-component=search role=dialog> <label class=md-search__overlay for=__search></label> <div class=md-search__inner role=search> <form class=md-search__form name=search> <input type=text class=md-search__input name=query aria-label=Search placeholder=Search autocapitalize=off autocorrect=off autocomplete=off spellcheck=false data-md-component=search-query data-md-state=active required> <label class="md-search__icon md-icon" for=__search> <svg xmlns=http://www.w3.org/2000/svg viewbox="0 0 24 24"><path d="M9.5 3A6.5 6.5 0 0 1 16 9.5c0 1.61-.59 3.09-1.56 4.23l.27.27h.79l5 5-1.5 1.5-5-5v-.79l-.27-.27A6.516 6.516 0 0 1 9.5 16 6.5 6.5 0 0 1 3 9.5 6.5 6.5 0 0 1 9.5 3m0 2C7 5 5 7 5 9.5S7 14 9.5 14 14 12 14 9.5 12 5 9.5 5z"/></svg> <svg xmlns=http://www.w3.org/2000/svg viewbox="0 0 24 24"><path d="M20 11v2H8l5.5 5.5-1.42 1.42L4.16 12l7.92-7.92L13.5 5.5 8 11h12z"/></svg> </label> <button type=reset class="md-search__icon md-icon" aria-label=Clear tabindex=-1> <svg xmlns=http://www.w3.org/2000/svg viewbox="0 0 24 24"><path d="M19 6.41L17.59 5 12 10.59 6.41 5 5 6.41 10.59 12 5 17.59 6.41 19 12 13.41 17.59 19 19 17.59 13.41 12 19 6.41z"/></svg> </button> </form> <div class=md-search__output> <div class=md-search__scrollwrap data-md-scrollfix> <div class=md-search-result data-md-component=search-result> <div class=md-search-result__meta> Initializing search </div> <ol class=md-search-result__list></ol> </div> </div> </div> </div> </div> </nav> </header> <div class=md-container data-md-component=container> <main class=md-main data-md-component=main> <div class="md-main__inner md-grid"> <div class="md-sidebar md-sidebar--primary" data-md-component=sidebar data-md-type=navigation> <div class=md-sidebar__scrollwrap> <div class=md-sidebar__inner> <nav class="md-nav md-nav--primary" aria-label=Navigation data-md-level=0> <label class=md-nav__title for=__drawer> <a href=../../.. title="IPU SDK" class="md-nav__button md-logo" aria-label="IPU SDK" data-md-component=logo> <svg xmlns=http://www.w3.org/2000/svg viewbox="0 0 24 24"><path d="M10 20v-6h4v6h5v-8h3L12 3 2 12h3v8h5z"/></svg> </a> IPU SDK </label> <ul class=md-nav__list data-md-scrollfix> <li class=md-nav__item> <a href=../../../index.html class=md-nav__link> 主页 </a> </li> <li class="md-nav__item md-nav__item--nested"> <input class="md-nav__toggle md-toggle" data-md-toggle=__nav_2 type=checkbox id=__nav_2> <label class=md-nav__link for=__nav_2> SDK介绍 <span class="md-nav__icon md-icon"></span> </label> <nav class=md-nav aria-label=SDK介绍 data-md-level=1> <label class=md-nav__title for=__nav_2> <span class="md-nav__icon md-icon"></span> SDK介绍 </label> <ul class=md-nav__list data-md-scrollfix> <li class=md-nav__item> <a href=../../Introduction/Introduction.html class=md-nav__link> SDK框架介绍 </a> </li> <li class=md-nav__item> <a href=../../Introduction/Docker.html class=md-nav__link> Docker环境 </a> </li> </ul> </nav> </li> <li class="md-nav__item md-nav__item--active md-nav__item--nested"> <input class="md-nav__toggle md-toggle" data-md-toggle=__nav_3 type=checkbox id=__nav_3 checked> <label class=md-nav__link for=__nav_3> 用户手册 <span class="md-nav__icon md-icon"></span> </label> <nav class=md-nav aria-label=用户手册 data-md-level=1> <label class=md-nav__title for=__nav_3> <span class="md-nav__icon md-icon"></span> 用户手册 </label> <ul class=md-nav__list data-md-scrollfix> <li class="md-nav__item md-nav__item--active"> <input class="md-nav__toggle md-toggle" data-md-toggle=toc type=checkbox id=__toc> <label class="md-nav__link md-nav__link--active" for=__toc> 1. 快速开始 <span class="md-nav__icon md-icon"></span> </label> <a href=Environment_Construction.html class="md-nav__link md-nav__link--active"> 1. 快速开始 </a> <nav class="md-nav md-nav--secondary" aria-label="Table of contents"> <label class=md-nav__title for=__toc> <span class="md-nav__icon md-icon"></span> Table of contents </label> <ul class=md-nav__list data-md-component=toc data-md-scrollfix> <li class=md-nav__item> <a href=#11 class=md-nav__link> 1.1. 开发环境准备 </a> <nav class=md-nav aria-label="1.1.    开发环境准备"> <ul class=md-nav__list> <li class=md-nav__item> <a href=#111-docker class=md-nav__link> 1.1.1 安装Docker开发环境 </a> </li> <li class=md-nav__item> <a href=#112-sgs_ipu_sdk class=md-nav__link> 1.1.2 SGS_IPU_SDK工具链环境设置 </a> </li> <li class=md-nav__item> <a href=#113-sgs_models class=md-nav__link> 1.1.3 SGS_Models资源获取 </a> </li> </ul> </nav> </li> <li class=md-nav__item> <a href=#12-ai class=md-nav__link> 1.2 AI模型快速转换 </a> <nav class=md-nav aria-label="1.2 AI模型快速转换"> <ul class=md-nav__list> <li class=md-nav__item> <a href=#121-onnxyolov8s class=md-nav__link> 1.2.1 以ONNX框架yolov8s模型示例分部转换 </a> <nav class=md-nav aria-label="1.2.1 以ONNX框架yolov8s模型示例分部转换"> <ul class=md-nav__list> <li class=md-nav__item> <a href=#1211-floatsim class=md-nav__link> ******* 原始模型转化为float.sim浮点网络模型 </a> </li> <li class=md-nav__item> <a href=#1212-fixedsim class=md-nav__link> ******* 浮点网络模型转化为fixed.sim定点网络模型 </a> </li> <li class=md-nav__item> <a href=#1213-sigmastar class=md-nav__link> ******* 定点网络模型转化为SigmaStar离线网络模型 </a> </li> </ul> </nav> </li> <li class=md-nav__item> <a href=#122-caffemobilenet_v2 class=md-nav__link> 1.2.2 以Caffe框架mobilenet_v2模型示例一键转换 </a> </li> </ul> </nav> </li> <li class=md-nav__item> <a href=#13-ai class=md-nav__link> 1.3 AI模型仿真运行 </a> <nav class=md-nav aria-label="1.3 AI模型仿真运行"> <ul class=md-nav__list> <li class=md-nav__item> <a href=#131-pcyolov8s_floatsim class=md-nav__link> 1.3.1 PC端仿真运行yolov8s_float.sim浮点模型 </a> </li> <li class=md-nav__item> <a href=#132-pcyolov8s_fixedsim class=md-nav__link> 1.3.2 PC端仿真运行yolov8s_fixed.sim定点模型 </a> </li> <li class=md-nav__item> <a href=#133-pcyolov8s_fixedsim_sgsimgimg class=md-nav__link> 1.3.3 PC端仿真运行yolov8s_fixed.sim_sgsimg.img离线模型 </a> </li> <li class=md-nav__item> <a href=#134-pccaffe_mobilenet_v2_shicaiimg class=md-nav__link> 1.3.4 PC端仿真运行caffe_mobilenet_v2_shicai.img离线模型 </a> </li> </ul> </nav> </li> <li class=md-nav__item> <a href=#14-ai class=md-nav__link> 1.4 AI模型开发板运行 </a> <nav class=md-nav aria-label="1.4    AI模型开发板运行"> <ul class=md-nav__list> <li class=md-nav__item> <a href=#141-prog_rpc_server class=md-nav__link> 1.4.1 开发板端启动prog_rpc_server服务 </a> </li> <li class=md-nav__item> <a href=#142-yolov8s_fixedsim_sgsimgimg class=md-nav__link> 1.4.2 运行yolov8s_fixed.sim_sgsimg.img离线模型 </a> </li> <li class=md-nav__item> <a href=#143-caffe_mobilenet_v2_shicaiimg class=md-nav__link> 1.4.3 运行caffe_mobilenet_v2_shicai.img离线模型 </a> </li> </ul> </nav> </li> </ul> </nav> </li> <li class=md-nav__item> <a href=../IPU200M/Convert.html class=md-nav__link> 2. Convert Tool </a> </li> <li class=md-nav__item> <a href=Calibrate.html class=md-nav__link> 3. Calibrator </a> </li> <li class=md-nav__item> <a href=Compile.html class=md-nav__link> 4. Compiler </a> </li> <li class=md-nav__item> <a href=../IPU200M/Simulate.html class=md-nav__link> 5. Simulator </a> </li> <li class=md-nav__item> <a href=DumpDebug_Tool.html class=md-nav__link> 6. DumpDebug Tool </a> </li> <li class=md-nav__item> <a href=SigmaStar_Post_Processing_Module.html class=md-nav__link> 7. SigmaStar后处理模块 </a> </li> <li class=md-nav__item> <a href=Adding_A_New_Layer.html class=md-nav__link> 8. 如何添加新的Layer </a> </li> <li class=md-nav__item> <a href=../IPU200M/Special_Model_Conversion.html class=md-nav__link> 9. 特殊模型转换要点 </a> </li> <li class=md-nav__item> <a href=../IPU200M/DLA_SDK_Support.html class=md-nav__link> 10. DLA SDK 支持 </a> </li> <li class=md-nav__item> <a href=Running_Offline_Network_Model_On_Development_Board.html class=md-nav__link> 11. 在开发板上运行离线网络模型 </a> </li> <li class=md-nav__item> <a href=../IPU200M/Preprocess.py_and_Input_Config.ini_Support.html class=md-nav__link> 附录. 前处理和配置文件注意要点 </a> </li> </ul> </nav> </li> <li class="md-nav__item md-nav__item--nested"> <input class="md-nav__toggle md-toggle" data-md-toggle=__nav_4 type=checkbox id=__nav_4> <label class=md-nav__link for=__nav_4> FAQ <span class="md-nav__icon md-icon"></span> </label> <nav class=md-nav aria-label=FAQ data-md-level=1> <label class=md-nav__title for=__nav_4> <span class="md-nav__icon md-icon"></span> FAQ </label> <ul class=md-nav__list data-md-scrollfix> <li class=md-nav__item> <a href=../../FAQ/Common/Env_Setting.html class=md-nav__link> 环境设置问题 </a> </li> <li class=md-nav__item> <a href=../../FAQ/IPU200M/Model_Conversion.html class=md-nav__link> 模型转换问题 </a> </li> <li class=md-nav__item> <a href=../../FAQ/IPU200M/Development_Board.html class=md-nav__link> 板端使用问题 </a> </li> <li class=md-nav__item> <a href=../../FAQ/IPU200M/Other_Anomalies.html class=md-nav__link> 其他异常问题 </a> </li> </ul> </nav> </li> </ul> </nav> </div> </div> </div> <div class="md-sidebar md-sidebar--secondary" data-md-component=sidebar data-md-type=toc> <div class=md-sidebar__scrollwrap> <div class=md-sidebar__inner> <nav class="md-nav md-nav--secondary" aria-label="Table of contents"> <label class=md-nav__title for=__toc> <span class="md-nav__icon md-icon"></span> Table of contents </label> <ul class=md-nav__list data-md-component=toc data-md-scrollfix> <li class=md-nav__item> <a href=#11 class=md-nav__link> 1.1. 开发环境准备 </a> <nav class=md-nav aria-label="1.1.    开发环境准备"> <ul class=md-nav__list> <li class=md-nav__item> <a href=#111-docker class=md-nav__link> 1.1.1 安装Docker开发环境 </a> </li> <li class=md-nav__item> <a href=#112-sgs_ipu_sdk class=md-nav__link> 1.1.2 SGS_IPU_SDK工具链环境设置 </a> </li> <li class=md-nav__item> <a href=#113-sgs_models class=md-nav__link> 1.1.3 SGS_Models资源获取 </a> </li> </ul> </nav> </li> <li class=md-nav__item> <a href=#12-ai class=md-nav__link> 1.2 AI模型快速转换 </a> <nav class=md-nav aria-label="1.2 AI模型快速转换"> <ul class=md-nav__list> <li class=md-nav__item> <a href=#121-onnxyolov8s class=md-nav__link> 1.2.1 以ONNX框架yolov8s模型示例分部转换 </a> <nav class=md-nav aria-label="1.2.1 以ONNX框架yolov8s模型示例分部转换"> <ul class=md-nav__list> <li class=md-nav__item> <a href=#1211-floatsim class=md-nav__link> ******* 原始模型转化为float.sim浮点网络模型 </a> </li> <li class=md-nav__item> <a href=#1212-fixedsim class=md-nav__link> ******* 浮点网络模型转化为fixed.sim定点网络模型 </a> </li> <li class=md-nav__item> <a href=#1213-sigmastar class=md-nav__link> ******* 定点网络模型转化为SigmaStar离线网络模型 </a> </li> </ul> </nav> </li> <li class=md-nav__item> <a href=#122-caffemobilenet_v2 class=md-nav__link> 1.2.2 以Caffe框架mobilenet_v2模型示例一键转换 </a> </li> </ul> </nav> </li> <li class=md-nav__item> <a href=#13-ai class=md-nav__link> 1.3 AI模型仿真运行 </a> <nav class=md-nav aria-label="1.3 AI模型仿真运行"> <ul class=md-nav__list> <li class=md-nav__item> <a href=#131-pcyolov8s_floatsim class=md-nav__link> 1.3.1 PC端仿真运行yolov8s_float.sim浮点模型 </a> </li> <li class=md-nav__item> <a href=#132-pcyolov8s_fixedsim class=md-nav__link> 1.3.2 PC端仿真运行yolov8s_fixed.sim定点模型 </a> </li> <li class=md-nav__item> <a href=#133-pcyolov8s_fixedsim_sgsimgimg class=md-nav__link> 1.3.3 PC端仿真运行yolov8s_fixed.sim_sgsimg.img离线模型 </a> </li> <li class=md-nav__item> <a href=#134-pccaffe_mobilenet_v2_shicaiimg class=md-nav__link> 1.3.4 PC端仿真运行caffe_mobilenet_v2_shicai.img离线模型 </a> </li> </ul> </nav> </li> <li class=md-nav__item> <a href=#14-ai class=md-nav__link> 1.4 AI模型开发板运行 </a> <nav class=md-nav aria-label="1.4    AI模型开发板运行"> <ul class=md-nav__list> <li class=md-nav__item> <a href=#141-prog_rpc_server class=md-nav__link> 1.4.1 开发板端启动prog_rpc_server服务 </a> </li> <li class=md-nav__item> <a href=#142-yolov8s_fixedsim_sgsimgimg class=md-nav__link> 1.4.2 运行yolov8s_fixed.sim_sgsimg.img离线模型 </a> </li> <li class=md-nav__item> <a href=#143-caffe_mobilenet_v2_shicaiimg class=md-nav__link> 1.4.3 运行caffe_mobilenet_v2_shicai.img离线模型 </a> </li> </ul> </nav> </li> </ul> </nav> </div> </div> </div> <div class=md-content data-md-component=content> <article class="md-content__inner md-typeset"> <h1>1. 快速开始</h1> <h2 id=11>1.1. 开发环境准备<a class=headerlink href=#11 title="Permanent link">&para;</a></h2> <blockquote> <p>&#128221;SigmaStar DLA SDK基于AVX2指令集编写，请使用支援AVX2的Intel®处理器运行。</p> </blockquote> <p><strong>推荐配置</strong></p> <table> <thead> <tr> <th align=left>🖥️💻</th> <th align=right>⚙️</th> </tr> </thead> <tbody> <tr> <td align=left>CPU</td> <td align=right>Intel® CoreTM i7 or Higher</td> </tr> <tr> <td align=left>RAM</td> <td align=right>8G or Higher</td> </tr> </tbody> </table> <p><strong>最低配置</strong></p> <table> <thead> <tr> <th align=left>🖥️💻</th> <th align=right>⚙️</th> </tr> </thead> <tbody> <tr> <td align=left>CPU</td> <td align=right>Intel® CoreTM i5</td> </tr> <tr> <td align=left>RAM</td> <td align=right>6G</td> </tr> </tbody> </table> <hr> <h3 id=111-docker>1.1.1 安装Docker开发环境<a class=headerlink href=#111-docker title="Permanent link">&para;</a></h3> <blockquote> <p>加载 SGS docker 镜像文件.</p> </blockquote> <div class=highlight><pre><span></span><code>tar -Jxvf sgs_docker_v1.7.tar.xz
docker load &lt; sgs_docker_v1.7.tar
</code></pre></div> <blockquote> <p>运行 SGS docker.</p> </blockquote> <div class=highlight><pre><span></span><code>./run_docker.sh
</code></pre></div> <hr> <h3 id=112-sgs_ipu_sdk>1.1.2 SGS_IPU_SDK工具链环境设置<a class=headerlink href=#112-sgs_ipu_sdk title="Permanent link">&para;</a></h3> <blockquote> <p>进入SGS_IPU_SDK工具链目录设置环境。</p> </blockquote> <div class=highlight><pre><span></span><code><span class=nb>cd</span> ~/SGS_IPU_SDK
<span class=nb>source</span> cfg_env.sh
</code></pre></div> <hr> <h3 id=113-sgs_models>1.1.3 SGS_Models资源获取<a class=headerlink href=#113-sgs_models title="Permanent link">&para;</a></h3> <p>SGS_Models是SGS_IPU_SDK验证可以转换的各类模型集合，可以联系Sigmastar工程师获取。</p> <p>SGS_Models包含模型列表如下（持续更新中）： <div class=highlight><pre><span></span><code>SGS_Models
├─caffe
│  ├─caffe_DenseNet_121
│  ├─caffe_DenseNet_201
│  ├─caffe_faster_rcnn
│  ├─caffe_inception_BN_21k
│  ├─caffe_lenet
│  ├─caffe_mobilenet_v1_shicai
│  ├─caffe_mobilenet_v1_yolo_v3_7
│  ├─caffe_mobilenet_v2_shicai
│  ├─caffe_resnet101
│  ├─caffe_resnet152
│  ├─caffe_resnet18
│  ├─caffe_resnet50_conv
│  ├─caffe_rfcn
│  ├─caffe_squeezenet_v1
│  ├─caffe_squeezenet_v1_1
│  ├─caffe_ssd_mobilenet_v1
│  ├─caffe_yolo_v2
│  ├─caffe_yolo_v2_608
│  ├─caffe_yolo_v3_darknet_416
│  └─caffe_yolo_v3_tiny
├─onnx
│  ├─onnx_arcface_resnet100
│  ├─onnx_darknet19
│  ├─onnx_eca_mobilenetv2
│  ├─onnx_efficientnet_lite4
│  ├─onnx_emotion_ferplus
│  ├─onnx_litehrnet_18_coco_384x288
│  ├─onnx_litehrnet_18_mpii_256x256
│  ├─onnx_mnist
│  ├─onnx_mobilenet_v2
│  ├─onnx_pose_higher_hrnet_w32_512
│  ├─onnx_ptcv_mobilenetv3_large_w1
│  ├─onnx_ptcv_mobilenet_w1
│  ├─onnx_qfgaohao_mb2_ssdlite
│  ├─onnx_resnet34_ssd
│  ├─onnx_retinanet
│  ├─onnx_shufflenetv2
│  ├─onnx_shufflenet_V1
│  ├─onnx_shufflenet_v2
│  ├─onnx_squeezenet
│  ├─onnx_squeezenet_v1_1
│  ├─onnx_tiny_yolov2
│  ├─onnx_tiny_yolov3
│  ├─onnx_tv_deeplabv3_resnet101
│  ├─onnx_tv_deeplabv3_resnet50
│  ├─onnx_tv_densenet121
│  ├─onnx_tv_densenet161
│  ├─onnx_tv_densenet169
│  ├─onnx_tv_densenet201
│  ├─onnx_tv_inception_v3
│  ├─onnx_tv_mnasnet1_0
│  ├─onnx_tv_mobilenet_v2
│  ├─onnx_tv_resnet101
│  ├─onnx_tv_resnet152
│  ├─onnx_tv_resnet18
│  ├─onnx_tv_resnet34
│  ├─onnx_tv_resnet50
│  ├─onnx_ultralytics_yolov3
│  ├─onnx_ultralytics_yolov3_tiny
│  ├─onnx_ultralytics_yolov5s
│  ├─onnx_ultralytics_yolov5s_7.0
│  └─onnx_yolov7
├─tensorflow
│  ├─deeplab_v3
│  ├─inception_v3
│  ├─mobilenet_v1
│  ├─mobilenet_v2
│  ├─resnet_18
│  ├─resnet_v2_50
│  ├─shufflenet_v2
│  ├─ssdlite_mobilenet_v2
│  ├─ssd_mobilenet_v1
│  ├─vgg_16
│  ├─yolo_v3_7
│  └─yolo_v3_tiny
└─TransformerModels
    ├─onnx_boxlevelset_512x512
    ├─onnx_CaiT_s24_224
    ├─onnx_conditional_detr_resnet50
    ├─onnx_dab_detr_resnet50
    ├─onnx_deit_base_patch16_224
    ├─onnx_deit_small_patch16_224
    ├─onnx_deit_tiny_patch16_224
    ├─onnx_detr_resnet50
    ├─onnx_dino_vit_small16_224
    ├─onnx_dino_vit_small8_224
    ├─onnx_dn_detr_resnet50
    ├─onnx_mobilevit_xs
    ├─onnx_mobilevit_xxs
    ├─onnx_poolformer_s12
    ├─onnx_poolformer_s24
    ├─onnx_poolformer_s36
    ├─onnx_segformer_512x512
    ├─onnx_segformer_512x512_b0
    ├─onnx_segformer_512x512_b1
    ├─onnx_segformer_512x512_b2
    ├─onnx_segmenter_small_512x512
    ├─onnx_segmenter_tiny_512x512
    ├─onnx_segmentor_512x512
    ├─onnx_swinv2_base_patch4_window8_256
    ├─onnx_swinv2_tiny_patch4_window8_256
    ├─onnx_swin_small_patch4_window7_224
    ├─onnx_swin_tiny_patch4_window7_224
    ├─onnx_topformer_512x512
    ├─onnx_topformer_512x512_B
    ├─onnx_topformer_512x512_S
    ├─onnx_topformer_512x512_T
    ├─onnx_xcit_nano_12_p16_224
    ├─onnx_xcit_small_12_p16_224
    ├─onnx_xcit_tiny_12_p16_224
    ├─onnx_yolos_small
    ├─onnx_yolos_small_dwr
    └─onnx_yolos_tiny
</code></pre></div></p> <hr> <h2 id=12-ai>1.2 AI模型快速转换<a class=headerlink href=#12-ai title="Permanent link">&para;</a></h2> <h3 id=121-onnxyolov8s>1.2.1 以ONNX框架yolov8s模型示例分部转换<a class=headerlink href=#121-onnxyolov8s title="Permanent link">&para;</a></h3> <p>onnx_yolov8s模型可在SDK同级目录的Quick_Start_Demo/onnx_yolov8s获取。</p> <h4 id=1211-floatsim>******* 原始模型转化为float.sim浮点网络模型<a class=headerlink href=#1211-floatsim title="Permanent link">&para;</a></h4> <p>初始化SGS_IPU_SDK <div class=highlight><pre><span></span><code><span class=nb>cd</span> SGS_IPU_SDK
<span class=nb>source</span> cfg_env.sh
</code></pre></div></p> <p>进入onnx_yolov8s目录 <div class=highlight><pre><span></span><code><span class=nb>cd</span> Quick_Start_Demo/onnx_yolov8s
</code></pre></div></p> <p>运行如下命令转换 <div class=highlight><pre><span></span><code>python3 SGS_IPU_SDK/Scripts/ConvertTool/ConvertTool.py onnx <span class=se>\</span>
--model_file onnx_yolov8s.onnx <span class=se>\</span>
--input_shape <span class=m>1</span>,3,640,640 <span class=se>\</span>
--input_config input_config.ini <span class=se>\</span>
--output_file onnx_yolov8s_float.sim
</code></pre></div></p> <blockquote> <p><code>不同框架模型转化为SigmaStar浮点网络模型的使用方式详见用户手册2.2章节。</code></p> </blockquote> <hr> <h4 id=1212-fixedsim>******* 浮点网络模型转化为fixed.sim定点网络模型<a class=headerlink href=#1212-fixedsim title="Permanent link">&para;</a></h4> <div class=highlight><pre><span></span><code>python3 SGS_IPU_SDK/Scripts/calibrator/calibrator.py <span class=se>\</span>
-i coco2017_calibration_set32 <span class=se>\</span>
-m onnx_yolov8s_float.sim <span class=se>\</span>
--input_config input_config.ini <span class=se>\</span>
-n onnx_yolov8s_preprocess.py
</code></pre></div> <blockquote> <p><code>关于SigmaStar浮点网络模型转换为SigmaStar定点网络模型的详细介绍请参考用户手册3.2章节。</code></p> </blockquote> <hr> <h4 id=1213-sigmastar>******* 定点网络模型转化为SigmaStar离线网络模型<a class=headerlink href=#1213-sigmastar title="Permanent link">&para;</a></h4> <div class=highlight><pre><span></span><code>python3 SGS_IPU_SDK/Scripts/calibrator/compiler.py <span class=se>\</span>
-m ./yolov8_fixed.sim
</code></pre></div> <blockquote> <p><code>关于SigmaStar定点网络模型转换为SigmaStar离线网络模型的详细介绍请参考用户手册4.2章节。</code></p> </blockquote> <hr> <h3 id=122-caffemobilenet_v2>1.2.2 以Caffe框架mobilenet_v2模型示例一键转换<a class=headerlink href=#122-caffemobilenet_v2 title="Permanent link">&para;</a></h3> <p>caffe_mobilenet_v2_shicai模型可在SDK同级目录的Quick_Start_Demo/caffe_mobilenet_v2_shicai获取。</p> <p>初始化SGS_IPU_SDK <div class=highlight><pre><span></span><code><span class=nb>cd</span> SGS_IPU_SDK
<span class=nb>source</span> cfg_env.sh
</code></pre></div></p> <p>进入onnx_yolov8s目录 <div class=highlight><pre><span></span><code><span class=nb>cd</span> Quick_Start_Demo/caffe_mobilenet_v2_shicai
</code></pre></div></p> <p>运行如下命令转换 <div class=highlight><pre><span></span><code>python3 SGS_IPU_SDK/Scripts/ConvertTool/SGS_converter.py caffe <span class=se>\</span>
--model_file caffe_mobilenet_v2_shicai.prototxt <span class=se>\</span>
--weight_file caffe_mobilenet_v2_shicai.caffemodel <span class=se>\</span>
--input_config input_config.ini <span class=se>\</span>
-n caffe_mobilenet_v2_shicai_preprocess.py <span class=se>\</span>
-i ilsvrc2012_calibration_set32/ <span class=se>\</span>
--output_file caffe_mobilenet_v2_shicai.img <span class=se>\</span>
--export_models
</code></pre></div></p> <blockquote> <p><code>关于SigmaStar一键生成离线网络模型的详细介绍请参考用户手册第8章。</code></p> </blockquote> <hr> <h2 id=13-ai>1.3 AI模型仿真运行<a class=headerlink href=#13-ai title="Permanent link">&para;</a></h2> <h3 id=131-pcyolov8s_floatsim>1.3.1 PC端仿真运行yolov8s_float.sim浮点模型<a class=headerlink href=#131-pcyolov8s_floatsim title="Permanent link">&para;</a></h3> <p>yolov8_simulator.py脚本可在SDK同级目录的Quick_Start_Demo/onnx_yolov8s获取。</p> <p>进入onnx_yolov8s目录 <div class=highlight><pre><span></span><code><span class=nb>cd</span> Quick_Start_Demo/onnx_yolov8s
</code></pre></div></p> <p>运行如下命令 <div class=highlight><pre><span></span><code>python3 yolov8_simulator.py <span class=se>\</span>
--image <span class=m>000000562557</span>.jpg <span class=se>\</span>
--model onnx_yolov8s_float.sim <span class=se>\</span>
-n onnx_yolov8s_preprocess.py <span class=se>\</span>
--draw_result output
</code></pre></div></p> <blockquote> <p><code>关于calibrator_custom的simulator Python API用法请参考用户手册5.3节</code></p> </blockquote> <p>绘制图片保存在output目录，float模型仿真推理output结果（截取前2个展示）:<br></p> <blockquote> <p><code>{"image_id": 562557, "category_id": 42, "bbox": [43.684708,187.861725,99.633865,204.318481], "score": 0.947503},</code><br> <code>{"image_id": 562557, "category_id": 1, "bbox": [431.325500,144.585709,106.798584,319.920639], "score": 0.890941},</code><br></p> </blockquote> <h3 id=132-pcyolov8s_fixedsim>1.3.2 PC端仿真运行yolov8s_fixed.sim定点模型<a class=headerlink href=#132-pcyolov8s_fixedsim title="Permanent link">&para;</a></h3> <div class=highlight><pre><span></span><code>python3 yolov8_simulator.py <span class=se>\</span>
--image <span class=m>000000562557</span>.jpg <span class=se>\</span>
--model onnx_yolov8s_fixed.sim <span class=se>\</span>
-n onnx_yolov8s_preprocess.py <span class=se>\</span>
--draw_result output
</code></pre></div> <p>绘制图片保存在output目录，fixed模型仿真推理output结果（截取前2个展示）:<br> <code>{"image_id": 562557, "category_id": 42, "bbox": [44.104973,189.841034,96.748329,201.353577], "score": 0.942028},</code><br> <code>{"image_id": 562557, "category_id": 1, "bbox": [431.829865,145.265045,105.226593,319.568085], "score": 0.881899},</code><br></p> <h3 id=133-pcyolov8s_fixedsim_sgsimgimg>1.3.3 PC端仿真运行yolov8s_fixed.sim_sgsimg.img离线模型<a class=headerlink href=#133-pcyolov8s_fixedsim_sgsimgimg title="Permanent link">&para;</a></h3> <div class=highlight><pre><span></span><code>python3 yolov8_simulator.py <span class=se>\</span>
--image <span class=m>000000562557</span>.jpg <span class=se>\</span>
--model onnx_yolov8s_fixed.sim_sgsimg.img <span class=se>\</span>
-n onnx_yolov8s_preprocess.py <span class=se>\</span>
--draw_result output
</code></pre></div> <p>绘制图片保存在output目录，离线模型仿真推理output结果（截取前2个展示）:<br> <code>{"image_id": 562557, "category_id": 42, "bbox": [44.104973,189.841034,96.748329,201.353577], "score": 0.942028},</code><br> <code>{"image_id": 562557, "category_id": 1, "bbox": [431.829865,145.265045,105.226593,319.568085], "score": 0.881899},</code><br></p> <h3 id=134-pccaffe_mobilenet_v2_shicaiimg>1.3.4 PC端仿真运行caffe_mobilenet_v2_shicai.img离线模型<a class=headerlink href=#134-pccaffe_mobilenet_v2_shicaiimg title="Permanent link">&para;</a></h3> <div class=highlight><pre><span></span><code>python3 SGS_IPU_SDK/Scripts/calibrator/simulator.py <span class=se>\</span>
-i ILSVRC2012_test_00000002.bmp <span class=se>\</span>
-m caffe_mobilenet_v2_shicai.img <span class=se>\</span>
-n caffe_mobilenet_v2_shicai_preprocess.py <span class=se>\</span>
-c Classification
</code></pre></div> <p>离线模型仿真推理output结果: <div class=highlight><pre><span></span><code>ILSVRC2012_test_00000002.bmp
Order: 1 index: 18 0.998624
Order: 2 index: 96 0.000488
Order: 3 index: 912 0.000427
Order: 4 index: 91 0.000153
Order: 5 index: 20 0.000092
</code></pre></div></p> <h2 id=14-ai>1.4 AI模型开发板运行<a class=headerlink href=#14-ai title="Permanent link">&para;</a></h2> <h3 id=141-prog_rpc_server>1.4.1 开发板端启动prog_rpc_server服务<a class=headerlink href=#141-prog_rpc_server title="Permanent link">&para;</a></h3> <p>MI_SDK已提供sdk/verify/release_feature/source/dla/ipu_server的app。</p> <p>板端运行ipu_server开启RPC服务（PORT为设定的port号）</p> <div class=highlight><pre><span></span><code>./prog_dla_server -p PORT
</code></pre></div> <h3 id=142-yolov8s_fixedsim_sgsimgimg>1.4.2 运行yolov8s_fixed.sim_sgsimg.img离线模型<a class=headerlink href=#142-yolov8s_fixedsim_sgsimgimg title="Permanent link">&para;</a></h3> <blockquote> <p>板端已运行prog_dla_server app 开启了RPC服务，PC端使用yolov8_simulator.py运行AI离线模型。</p> </blockquote> <div class=highlight><pre><span></span><code>python3 yolov8_simulator.py <span class=se>\</span>
--image <span class=m>000000562557</span>.jpg <span class=se>\</span>
--model onnx_yolov8s_fixed.sim_sgsimg.img <span class=se>\</span>
-n onnx_yolov8s_preprocess.py <span class=se>\</span>
--draw_result output <span class=se>\</span>
--host 板端ip地址 <span class=se>\</span>
--port PORT
</code></pre></div> <p>绘制图片保存在output目录，offline模型板端推理output结果（截取前2个展示）:<br> <code>{"image_id": 562557, "category_id": 42, "bbox": [44.104973,189.841034,96.748329,201.353577], "score": 0.942028},</code><br> <code>{"image_id": 562557, "category_id": 1, "bbox": [431.829865,145.265045,105.226593,319.568085], "score": 0.881899},</code><br></p> <blockquote> <p>参数解释如下：<br> <code>--image</code>: 推理图片 <br> <code>--model</code>: AI离线模型(offline.img) <br> <code>-n</code>: 前处理.py文件 <br> <code>--draw_result</code>: 推理画框结果保存文件夹 <br> <code>--port</code>: 板端ip地址<br> <code>--host</code>: 设定端口号 <br></p> </blockquote> <h3 id=143-caffe_mobilenet_v2_shicaiimg>1.4.3 运行caffe_mobilenet_v2_shicai.img离线模型<a class=headerlink href=#143-caffe_mobilenet_v2_shicaiimg title="Permanent link">&para;</a></h3> <blockquote> <p>板端已运行prog_dla_server app 开启了RPC服务，PC端使用rpc_simulator.py运行AI离线模型。</p> </blockquote> <div class=highlight><pre><span></span><code>python3 SGS_IPU_SDK/Scripts/calibrator/rpc_simulator.py <span class=se>\</span>
-i ILSVRC2012_test_00000002.bmp <span class=se>\</span>
-m caffe_mobilenet_v2_shicai.img <span class=se>\</span>
-n caffe_mobilenet_v2_shicai_preprocess.py <span class=se>\</span>
-c Classification <span class=se>\</span>
--host 板端ip地址 <span class=se>\</span>
--port PORT
</code></pre></div> <p>offline模型板端推理output结果: <div class=highlight><pre><span></span><code>ILSVRC2012_test_00000002.bmp
Order: 1 index: 18 0.998624
Order: 2 index: 96 0.000488
Order: 3 index: 912 0.000427
Order: 4 index: 91 0.000153
Order: 5 index: 20 0.000092
</code></pre></div></p> <blockquote> <p>参数解释如下：<br> <code>-i</code>: 推理图片 <br> <code>-m</code>: AI离线模型(offline.img) <br> <code>-n</code> : 前处理.py文件 <br> <code>-c</code>: 推理结果处理方法 <br> <code>--port</code>: 板端ip地址<br> <code>--host</code>: 设定端口号 <br></p> </blockquote> </article> </div> </div> </main> <footer class=md-footer> <nav class="md-footer__inner md-grid" aria-label=Footer> <a href=../../Introduction/Docker.html class="md-footer__link md-footer__link--prev" rel=prev> <div class="md-footer__button md-icon"> <svg xmlns=http://www.w3.org/2000/svg viewbox="0 0 24 24"><path d="M20 11v2H8l5.5 5.5-1.42 1.42L4.16 12l7.92-7.92L13.5 5.5 8 11h12z"/></svg> </div> <div class=md-footer__title> <div class=md-ellipsis> <span class=md-footer__direction> Previous </span> Docker环境 </div> </div> </a> <a href=../IPU200M/Convert.html class="md-footer__link md-footer__link--next" rel=next> <div class=md-footer__title> <div class=md-ellipsis> <span class=md-footer__direction> Next </span> 2. Convert Tool </div> </div> <div class="md-footer__button md-icon"> <svg xmlns=http://www.w3.org/2000/svg viewbox="0 0 24 24"><path d="M4 11v2h12l-5.5 5.5 1.42 1.42L19.84 12l-7.92-7.92L10.5 5.5 16 11H4z"/></svg> </div> </a> </nav> <div class="md-footer-meta md-typeset"> <div class="md-footer-meta__inner md-grid"> <div class=md-footer-copyright> <div class=md-footer-copyright__highlight> Copyright&copy; 2021 SigmaStar Technology. All rights reserved. Security Level: Confidential A. </div> Made with <a href=https://squidfunk.github.io/mkdocs-material/ target=_blank rel=noopener> Material for MkDocs </a> </div> </div> </div> </footer> </div> <div class=md-dialog data-md-component=dialog> <div class="md-dialog__inner md-typeset"></div> </div> <script id=__config type=application/json>{"base": "../../..", "features": [], "translations": {"clipboard.copy": "Copy to clipboard", "clipboard.copied": "Copied to clipboard", "search.config.lang": "en", "search.config.pipeline": "trimmer, stopWordFilter", "search.config.separator": "[\\s\\-]+", "search.placeholder": "Search", "search.result.placeholder": "Type to start searching", "search.result.none": "No matching documents", "search.result.one": "1 matching document", "search.result.other": "# matching documents", "search.result.more.one": "1 more on this page", "search.result.more.other": "# more on this page", "search.result.term.missing": "Missing"}, "search": "../../../assets/javascripts/workers/search.fb4a9340.min.js", "version": null}</script> <script src=../../../assets/javascripts/bundle.a1c7c35e.min.js></script> <script src=../../../search/search_index.js></script> <script src=../../../javascripts/extra.js></script> <script src="https://cdnjs.cloudflare.com/ajax/libs/mathjax/2.7.0/MathJax.js?config=TeX-MML-AM_CHTML"></script> <script src=https://cdnjs.cloudflare.com/ajax/libs/raphael/2.2.7/raphael.min.js></script> <script src=https://cdnjs.cloudflare.com/ajax/libs/underscore.js/1.8.3/underscore-min.js></script> <script src=https://cdnjs.cloudflare.com/ajax/libs/js-sequence-diagrams/1.0.6/sequence-diagram-min.js></script> <script src=https://cdnjs.cloudflare.com/ajax/libs/flowchart/1.6.5/flowchart.min.js></script> <script src=https://unpkg.com/freezeframe/dist/freezeframe.min.js></script> <script src=https://unpkg.com/mermaid@7.1.0/dist/mermaid.min.js></script> <script src=../../../javascripts/umlconvert.js></script> </body> </html>