<!doctype html><html lang=en class=no-js> <head><meta charset=utf-8><meta name=viewport content="width=device-width,initial-scale=1"><meta name=description content="Personal Documentation System Template"><meta name=author content="<PERSON> Cho<PERSON>"><link rel=icon href=../../../images/favicon.ico><meta name=generator content="mkdocs-1.1.2, mkdocs-material-7.0.6"><title>3. Calibrator - IPU SDK</title><link rel=stylesheet href=../../../assets/stylesheets/main.2c0c5eaf.min.css><link rel=stylesheet href=../../../assets/stylesheets/palette.7fa14f5b.min.css><meta name=theme-color content=#009485><link rel=preconnect href=https://fonts.gstatic.com crossorigin><link rel=stylesheet href="https://fonts.googleapis.com/css?family=Roboto:300,400,400i,700%7CRoboto+Mono&display=fallback"><style>:root{--md-text-font-family:"Roboto";--md-code-font-family:"Roboto Mono"}</style><link rel=stylesheet href=../../../stylesheets/extra.css></head> <body dir=ltr data-md-color-scheme data-md-color-primary=teal data-md-color-accent=teal> <input class=md-toggle data-md-toggle=drawer type=checkbox id=__drawer autocomplete=off> <input class=md-toggle data-md-toggle=search type=checkbox id=__search autocomplete=off> <label class=md-overlay for=__drawer></label> <div data-md-component=skip> <a href=#31 class=md-skip> Skip to content </a> </div> <div data-md-component=announce> </div> <header class=md-header data-md-component=header> <nav class="md-header__inner md-grid" aria-label=Header> <a href=../../.. title="IPU SDK" class="md-header__button md-logo" aria-label="IPU SDK" data-md-component=logo> <svg xmlns=http://www.w3.org/2000/svg viewbox="0 0 24 24"><path d="M10 20v-6h4v6h5v-8h3L12 3 2 12h3v8h5z"/></svg> </a> <label class="md-header__button md-icon" for=__drawer> <svg xmlns=http://www.w3.org/2000/svg viewbox="0 0 24 24"><path d="M3 6h18v2H3V6m0 5h18v2H3v-2m0 5h18v2H3v-2z"/></svg> </label> <div class=md-header__title data-md-component=header-title> <div class=md-header__ellipsis> <div class=md-header__topic> <span class=md-ellipsis> IPU SDK </span> </div> <div class=md-header__topic data-md-component=header-topic> <span class=md-ellipsis> 3. Calibrator </span> </div> </div> </div> <div class=md-header__options> </div> <label class="md-header__button md-icon" for=__search> <svg xmlns=http://www.w3.org/2000/svg viewbox="0 0 24 24"><path d="M9.5 3A6.5 6.5 0 0 1 16 9.5c0 1.61-.59 3.09-1.56 4.23l.27.27h.79l5 5-1.5 1.5-5-5v-.79l-.27-.27A6.516 6.516 0 0 1 9.5 16 6.5 6.5 0 0 1 3 9.5 6.5 6.5 0 0 1 9.5 3m0 2C7 5 5 7 5 9.5S7 14 9.5 14 14 12 14 9.5 12 5 9.5 5z"/></svg> </label> <div class=md-search data-md-component=search role=dialog> <label class=md-search__overlay for=__search></label> <div class=md-search__inner role=search> <form class=md-search__form name=search> <input type=text class=md-search__input name=query aria-label=Search placeholder=Search autocapitalize=off autocorrect=off autocomplete=off spellcheck=false data-md-component=search-query data-md-state=active required> <label class="md-search__icon md-icon" for=__search> <svg xmlns=http://www.w3.org/2000/svg viewbox="0 0 24 24"><path d="M9.5 3A6.5 6.5 0 0 1 16 9.5c0 1.61-.59 3.09-1.56 4.23l.27.27h.79l5 5-1.5 1.5-5-5v-.79l-.27-.27A6.516 6.516 0 0 1 9.5 16 6.5 6.5 0 0 1 3 9.5 6.5 6.5 0 0 1 9.5 3m0 2C7 5 5 7 5 9.5S7 14 9.5 14 14 12 14 9.5 12 5 9.5 5z"/></svg> <svg xmlns=http://www.w3.org/2000/svg viewbox="0 0 24 24"><path d="M20 11v2H8l5.5 5.5-1.42 1.42L4.16 12l7.92-7.92L13.5 5.5 8 11h12z"/></svg> </label> <button type=reset class="md-search__icon md-icon" aria-label=Clear tabindex=-1> <svg xmlns=http://www.w3.org/2000/svg viewbox="0 0 24 24"><path d="M19 6.41L17.59 5 12 10.59 6.41 5 5 6.41 10.59 12 5 17.59 6.41 19 12 13.41 17.59 19 19 17.59 13.41 12 19 6.41z"/></svg> </button> </form> <div class=md-search__output> <div class=md-search__scrollwrap data-md-scrollfix> <div class=md-search-result data-md-component=search-result> <div class=md-search-result__meta> Initializing search </div> <ol class=md-search-result__list></ol> </div> </div> </div> </div> </div> </nav> </header> <div class=md-container data-md-component=container> <main class=md-main data-md-component=main> <div class="md-main__inner md-grid"> <div class="md-sidebar md-sidebar--primary" data-md-component=sidebar data-md-type=navigation> <div class=md-sidebar__scrollwrap> <div class=md-sidebar__inner> <nav class="md-nav md-nav--primary" aria-label=Navigation data-md-level=0> <label class=md-nav__title for=__drawer> <a href=../../.. title="IPU SDK" class="md-nav__button md-logo" aria-label="IPU SDK" data-md-component=logo> <svg xmlns=http://www.w3.org/2000/svg viewbox="0 0 24 24"><path d="M10 20v-6h4v6h5v-8h3L12 3 2 12h3v8h5z"/></svg> </a> IPU SDK </label> <ul class=md-nav__list data-md-scrollfix> <li class=md-nav__item> <a href=../../../index.html class=md-nav__link> 主页 </a> </li> <li class="md-nav__item md-nav__item--nested"> <input class="md-nav__toggle md-toggle" data-md-toggle=__nav_2 type=checkbox id=__nav_2> <label class=md-nav__link for=__nav_2> SDK介绍 <span class="md-nav__icon md-icon"></span> </label> <nav class=md-nav aria-label=SDK介绍 data-md-level=1> <label class=md-nav__title for=__nav_2> <span class="md-nav__icon md-icon"></span> SDK介绍 </label> <ul class=md-nav__list data-md-scrollfix> <li class=md-nav__item> <a href=../../Introduction/Introduction.html class=md-nav__link> SDK框架介绍 </a> </li> <li class=md-nav__item> <a href=../../Introduction/Docker.html class=md-nav__link> Docker环境 </a> </li> </ul> </nav> </li> <li class="md-nav__item md-nav__item--active md-nav__item--nested"> <input class="md-nav__toggle md-toggle" data-md-toggle=__nav_3 type=checkbox id=__nav_3 checked> <label class=md-nav__link for=__nav_3> 用户手册 <span class="md-nav__icon md-icon"></span> </label> <nav class=md-nav aria-label=用户手册 data-md-level=1> <label class=md-nav__title for=__nav_3> <span class="md-nav__icon md-icon"></span> 用户手册 </label> <ul class=md-nav__list data-md-scrollfix> <li class=md-nav__item> <a href=Environment_Construction.html class=md-nav__link> 1. 快速开始 </a> </li> <li class=md-nav__item> <a href=../IPU200M/Convert.html class=md-nav__link> 2. Convert Tool </a> </li> <li class="md-nav__item md-nav__item--active"> <input class="md-nav__toggle md-toggle" data-md-toggle=toc type=checkbox id=__toc> <label class="md-nav__link md-nav__link--active" for=__toc> 3. Calibrator <span class="md-nav__icon md-icon"></span> </label> <a href=Calibrate.html class="md-nav__link md-nav__link--active"> 3. Calibrator </a> <nav class="md-nav md-nav--secondary" aria-label="Table of contents"> <label class=md-nav__title for=__toc> <span class="md-nav__icon md-icon"></span> Table of contents </label> <ul class=md-nav__list data-md-component=toc data-md-scrollfix> <li class=md-nav__item> <a href=#31 class=md-nav__link> 3.1. 使用方法 </a> <nav class=md-nav aria-label="3.1. 使用方法"> <ul class=md-nav__list> <li class=md-nav__item> <a href=#311 class=md-nav__link> 3.1.1 必选参数 </a> </li> <li class=md-nav__item> <a href=#312 class=md-nav__link> 3.1.2 可选参数 </a> </li> <li class=md-nav__item> <a href=#313 class=md-nav__link> 3.1.3 注意事项 </a> </li> </ul> </nav> </li> <li class=md-nav__item> <a href=#32 class=md-nav__link> 3.2. 图片前处理方法 </a> </li> <li class=md-nav__item> <a href=#33 class=md-nav__link> 3.3. 卷积量化选项 </a> </li> <li class=md-nav__item> <a href=#34-calibrator_customcalibrator class=md-nav__link> 3.4. calibrator_custom.calibrator </a> <nav class=md-nav aria-label="3.4. calibrator_custom.calibrator"> <ul class=md-nav__list> <li class=md-nav__item> <a href=#341-calibrator_customcalibrator class=md-nav__link> 3.4.1 calibrator_custom.calibrator方法 </a> </li> <li class=md-nav__item> <a href=#342-calibrator_customsim_calibrator class=md-nav__link> 3.4.2 calibrator_custom.SIM_Calibrator </a> </li> <li class=md-nav__item> <a href=#343 class=md-nav__link> 3.4.3 导入量化参数规则 </a> <nav class=md-nav aria-label="3.4.3 导入量化参数规则"> <ul class=md-nav__list> <li class=md-nav__item> <a href=#3431 class=md-nav__link> 3.4.3.1. 量化策略和方法 </a> </li> <li class=md-nav__item> <a href=#3432 class=md-nav__link> *******. 量化参数的内容 </a> </li> <li class=md-nav__item> <a href=#34321 class=md-nav__link> *******.1 量化模型量化参数导出工具 </a> </li> <li class=md-nav__item> <a href=#3433 class=md-nav__link> *******. 导入量化数据流程 </a> </li> </ul> </nav> </li> </ul> </nav> </li> <li class=md-nav__item> <a href=#35-torch_calibrator class=md-nav__link> 3.5. torch_calibrator </a> <nav class=md-nav aria-label="3.5. torch_calibrator"> <ul class=md-nav__list> <li class=md-nav__item> <a href=#351 class=md-nav__link> 3.5.1. 参数说明 </a> </li> <li class=md-nav__item> <a href=#352 class=md-nav__link> 3.5.2. 量化选项 </a> </li> <li class=md-nav__item> <a href=#353-yaml class=md-nav__link> 3.5.3. 量化参数(yaml格式)文件 </a> </li> </ul> </nav> </li> </ul> </nav> </li> <li class=md-nav__item> <a href=Compile.html class=md-nav__link> 4. Compiler </a> </li> <li class=md-nav__item> <a href=../IPU200M/Simulate.html class=md-nav__link> 5. Simulator </a> </li> <li class=md-nav__item> <a href=DumpDebug_Tool.html class=md-nav__link> 6. DumpDebug Tool </a> </li> <li class=md-nav__item> <a href=SigmaStar_Post_Processing_Module.html class=md-nav__link> 7. SigmaStar后处理模块 </a> </li> <li class=md-nav__item> <a href=Adding_A_New_Layer.html class=md-nav__link> 8. 如何添加新的Layer </a> </li> <li class=md-nav__item> <a href=../IPU200M/Special_Model_Conversion.html class=md-nav__link> 9. 特殊模型转换要点 </a> </li> <li class=md-nav__item> <a href=../IPU200M/DLA_SDK_Support.html class=md-nav__link> 10. DLA SDK 支持 </a> </li> <li class=md-nav__item> <a href=Running_Offline_Network_Model_On_Development_Board.html class=md-nav__link> 11. 在开发板上运行离线网络模型 </a> </li> <li class=md-nav__item> <a href=../IPU200M/Preprocess.py_and_Input_Config.ini_Support.html class=md-nav__link> 附录. 前处理和配置文件注意要点 </a> </li> </ul> </nav> </li> <li class="md-nav__item md-nav__item--nested"> <input class="md-nav__toggle md-toggle" data-md-toggle=__nav_4 type=checkbox id=__nav_4> <label class=md-nav__link for=__nav_4> FAQ <span class="md-nav__icon md-icon"></span> </label> <nav class=md-nav aria-label=FAQ data-md-level=1> <label class=md-nav__title for=__nav_4> <span class="md-nav__icon md-icon"></span> FAQ </label> <ul class=md-nav__list data-md-scrollfix> <li class=md-nav__item> <a href=../../FAQ/Common/Env_Setting.html class=md-nav__link> 环境设置问题 </a> </li> <li class=md-nav__item> <a href=../../FAQ/IPU200M/Model_Conversion.html class=md-nav__link> 模型转换问题 </a> </li> <li class=md-nav__item> <a href=../../FAQ/IPU200M/Development_Board.html class=md-nav__link> 板端使用问题 </a> </li> <li class=md-nav__item> <a href=../../FAQ/IPU200M/Other_Anomalies.html class=md-nav__link> 其他异常问题 </a> </li> </ul> </nav> </li> </ul> </nav> </div> </div> </div> <div class="md-sidebar md-sidebar--secondary" data-md-component=sidebar data-md-type=toc> <div class=md-sidebar__scrollwrap> <div class=md-sidebar__inner> <nav class="md-nav md-nav--secondary" aria-label="Table of contents"> <label class=md-nav__title for=__toc> <span class="md-nav__icon md-icon"></span> Table of contents </label> <ul class=md-nav__list data-md-component=toc data-md-scrollfix> <li class=md-nav__item> <a href=#31 class=md-nav__link> 3.1. 使用方法 </a> <nav class=md-nav aria-label="3.1. 使用方法"> <ul class=md-nav__list> <li class=md-nav__item> <a href=#311 class=md-nav__link> 3.1.1 必选参数 </a> </li> <li class=md-nav__item> <a href=#312 class=md-nav__link> 3.1.2 可选参数 </a> </li> <li class=md-nav__item> <a href=#313 class=md-nav__link> 3.1.3 注意事项 </a> </li> </ul> </nav> </li> <li class=md-nav__item> <a href=#32 class=md-nav__link> 3.2. 图片前处理方法 </a> </li> <li class=md-nav__item> <a href=#33 class=md-nav__link> 3.3. 卷积量化选项 </a> </li> <li class=md-nav__item> <a href=#34-calibrator_customcalibrator class=md-nav__link> 3.4. calibrator_custom.calibrator </a> <nav class=md-nav aria-label="3.4. calibrator_custom.calibrator"> <ul class=md-nav__list> <li class=md-nav__item> <a href=#341-calibrator_customcalibrator class=md-nav__link> 3.4.1 calibrator_custom.calibrator方法 </a> </li> <li class=md-nav__item> <a href=#342-calibrator_customsim_calibrator class=md-nav__link> 3.4.2 calibrator_custom.SIM_Calibrator </a> </li> <li class=md-nav__item> <a href=#343 class=md-nav__link> 3.4.3 导入量化参数规则 </a> <nav class=md-nav aria-label="3.4.3 导入量化参数规则"> <ul class=md-nav__list> <li class=md-nav__item> <a href=#3431 class=md-nav__link> 3.4.3.1. 量化策略和方法 </a> </li> <li class=md-nav__item> <a href=#3432 class=md-nav__link> *******. 量化参数的内容 </a> </li> <li class=md-nav__item> <a href=#34321 class=md-nav__link> *******.1 量化模型量化参数导出工具 </a> </li> <li class=md-nav__item> <a href=#3433 class=md-nav__link> *******. 导入量化数据流程 </a> </li> </ul> </nav> </li> </ul> </nav> </li> <li class=md-nav__item> <a href=#35-torch_calibrator class=md-nav__link> 3.5. torch_calibrator </a> <nav class=md-nav aria-label="3.5. torch_calibrator"> <ul class=md-nav__list> <li class=md-nav__item> <a href=#351 class=md-nav__link> 3.5.1. 参数说明 </a> </li> <li class=md-nav__item> <a href=#352 class=md-nav__link> 3.5.2. 量化选项 </a> </li> <li class=md-nav__item> <a href=#353-yaml class=md-nav__link> 3.5.3. 量化参数(yaml格式)文件 </a> </li> </ul> </nav> </li> </ul> </nav> </div> </div> </div> <div class=md-content data-md-component=content> <article class="md-content__inner md-typeset"> <h1>3. Calibrator</h1> <h2 id=31>3.1. 使用方法<a class=headerlink href=#31 title="Permanent link">&para;</a></h2> <p>Calibrator工具的位置在 <strong>SGS_IPU_SDK/Scripts/calibrator/calibrator.py</strong>。 该工具是将SigmaStar浮点网络模型转换为SigmaStar定点网络模型。 在SGS_IPU_SDK ⽬录下运⾏以下脚本，输出Library的路径（已经做过该步骤可忽略）： <div class=highlight><pre><span></span><code>cd ~/SGS_IPU_SDK
source cfg_env.sh
</code></pre></div> 进入到该工具目录，工具使用示例： <div class=highlight><pre><span></span><code>python3 calibrator.py \
-i ~/SGS_Models/resource/detection/coco2017_calibration_set32 \
-m ~/SGS_Models/tensorflow/ssd_mobilenet_v1/ssd_mobilenet_v1_float.sim \
-n ~/SGS_Models/tensorflow/ssd_mobilenet_v1/ssd_mobilenet_v1.py \
--input_config ~/SGS_Models/tensorflow/ssd_mobilenet_v1/input_config.ini \
--num_process 20
</code></pre></div></p> <p>或者可以使用传入 <strong>指定图片路径列表文件</strong> 的形式：</p> <div class=highlight><pre><span></span><code>python3 calibrator.py \
-i ~/SGS_Models/resource/detection/file.list \
-m ~/SGS_Models/tensorflow/ssd_mobilenet_v1/ssd_mobilenet_v1_float.sim \
-n ~/SGS_Models/tensorflow/ssd_mobilenet_v1/ssd_mobilenet_v1.py \
--input_config ~/SGS_Models/tensorflow/ssd_mobilenet_v1/input_config.ini \
--num_process 20
</code></pre></div> <p>对该工具参数的具体说明如下：</p> <hr> <h3 id=311>3.1.1 必选参数<a class=headerlink href=#311 title="Permanent link">&para;</a></h3> <p><code>-i</code>,<code>--image</code> : 图片文件或图片文件夹路径或 <strong>指定图片路径列表文件</strong> 。</p> <p><code>-m</code>, <code>--model</code> : 浮点网络模型文件路径。</p> <p><code>--input_config</code> : input_config.ini文件路径，该文件为input tensor的配置信息。 具体设置方法见 [input config配置信息设置]2.2. input config配置信息设置).</p> <p><code>-n</code>, <code>--preprocess</code> : 前处理方法，与图片前处理方法相关，详见[3.2. 图片前处理方法]。 也可以完成前处理文件配置后，给定前处理文件路径。</p> <p><strong>Please Note:</strong></p> <ul> <li> <p>若模型为 <strong>多输入</strong> 时，<code>-n,--preprocess</code> 参数用法需要多个前处理方法，例如 <strong>-n preprocess1.py,preprocess2.py</strong> 或者 <strong>--preprocess preprocess1.py,preprocess2.py</strong></p> </li> <li> <p><code>-i/--image</code> 参数传入 <strong>指定图片路径列表文件</strong> 的形式：</p> </li> </ul> <div class=highlight><pre><span></span><code>网络模型为单输入时：
~/SGS_IPU_SDK/image_test/2007000364.jpg
~/SGS_IPU_SDK/image_test/ILSVRC2012_test_00000002.bmp
...


网络模型为多输入时：
~/SGS_IPU_SDK/image_test/2007000364.jpg,~/SGS_IPU_SDK/image_test/ILSVRC2012_test_00000002.bmp
~/SGS_IPU_SDK/image_test/2007000365.jpg,~/SGS_IPU_SDK/image_test/ILSVRC2012_test_00000003.bmp
...
</code></pre></div> <hr> <h3 id=312>3.1.2 可选参数<a class=headerlink href=#312 title="Permanent link">&para;</a></h3> <p><code>-o</code>, <code>--output</code> : 模型输出路径。指定定点网络模型输出数据位置：指定到文件夹，将自动以浮点网络模型文件前缀命名，后接fixed.sim；指定到具体路径和文件名，将以指定路径和文件名命名定点网络模型；不指定该参数，将以浮点网络模型文件路径储存定点网络模型。</p> <p><code>--num_process</code> : 进程数，运行同时运行的进程数。（可选参数，不加该参数默认为10个进程）。</p> <p><code>--quant_level</code> : 选择量化等级：[L1, L2, L3, L4, L5]。默认L5量化等级，等级越高，量化精度越高，量化速度会相应变慢。各量化等级说明：</p> <ul> <li><code>L1</code>: 采用最大最小值快速对比数据量化，速度较快。</li> <li><code>L2</code>: 采用快速对比数据量化权重数据。</li> <li><code>L3</code>: 对统计信息做进一步分析，近似拟合原有数据分布。</li> <li><code>L4</code>: 近似拟合权重数据分布，并建议升级某些卷积为16bit量化。</li> <li><code>L5</code>: 采用高精度数据分析方法，极大限度拟合原有数据分布，并建议升级某些卷积为16bit量化。</li> </ul> <hr> <h3 id=313>3.1.3 注意事项<a class=headerlink href=#313 title="Permanent link">&para;</a></h3> <ul> <li>浮点网络模型转换成定点网络模型需要30张左右训练图片用来分析和量化定点网络模型的数据，因此在转换中， <code>-i/--image</code>参数后应接图片文件夹的路径。当然，如果<code>-i/--image</code>参数给定单张图片文件的路径，依然可以转换为定点网络模型，但是其模型精度可能会收到影响。另外，由于对精度要求不同，在转换网络中在input_config.ini文件中可以针对卷积配置不同的量化信息，从而获得精度和速度的平衡。</li> <li>calibrator工具会寻找系统变量从而获得对应阶段任务所需工具路径，因此默认情况下参数<code>-t/--tool</code>不用指定相关工具的位置。</li> <li>使用calibrator将浮点网络模型转换为定点网络模型时，会在运行目录下产生log目录，log目录下的tensor_min_max.txt文件记录了网络的每层输入和输出的最大与最小值，将在之后分析数据时有用。log目录下的内容会在下一次使用calibrator时删除，请注意保存。</li> </ul> <hr> <h2 id=32>3.2. 图片前处理方法<a class=headerlink href=#32 title="Permanent link">&para;</a></h2> <p>由于不同网络模型的前处理方式不尽相同，为了能够在转换网络时尽可能减小精度的丢失，应该使用与训练相同的图片前处理方式，每种前处理方式需独立编写python文件。 以下两种方式保存前处理Python文件均可被调用：</p> <ol> <li>请将文件保存到 <strong>SGS_IPU_SDK/Scripts/calibrator/preprocess</strong> 文件夹内，并在preprocess_method/<strong>init</strong>.py 文件中增加文件名称。在使用calibrator或simulator时，<code>-n/--preprocess</code>参数为编写的文件名称，不需要指定编写文件的路径。</li> <li><code>-n/--preprocess</code>参数为前处理Python的文件路径。</li> </ol> <p>下面以caffe_resnet18网络为例，编写图片前处理文件。 编写图片处理函数（函数名称不限），返回np.array 格式的图片数据，函数必须包含2个参数：</p> <ul> <li> <p>图片路径</p> </li> <li> <p>归一化标记（norm=True）</p> </li> </ul> <p>归一化标记是为了区分网络模型是否是浮点模型。因为在浮点网络模型阶段，图片的归一化需要在送进网络前处理好。但是定点网络模型和离线网络模型已经包含了input_config.ini文件的设置信息，能够将图片数据自行做归一化处理，因此送进网络模型的数据不需要做归一化，这与在SigmaStar硬件上处理方式相同。</p> <p><div class=highlight><pre><span></span><code><span class=kn>import</span> <span class=nn>cv2</span>
<span class=kn>import</span> <span class=nn>numpy</span> <span class=k>as</span> <span class=nn>np</span>

<span class=k>def</span> <span class=nf>get_image</span><span class=p>(</span><span class=n>img_path</span><span class=p>,</span> <span class=n>resizeH</span><span class=o>=</span><span class=mi>224</span><span class=p>,</span> <span class=n>resizeW</span><span class=o>=</span><span class=mi>224</span><span class=p>,</span> <span class=n>resizeC</span><span class=o>=</span><span class=mi>3</span><span class=p>,</span> <span class=n>norm</span><span class=o>=</span><span class=kc>True</span><span class=p>,</span> <span class=n>meanB</span><span class=o>=</span><span class=mf>104.0</span><span class=p>,</span> <span class=n>meanG</span><span class=o>=</span><span class=mf>117.0</span><span class=p>,</span> <span class=n>meanR</span><span class=o>=</span><span class=mf>123.0</span><span class=p>,</span> <span class=n>std</span><span class=o>=</span><span class=mf>1.0</span><span class=p>,</span> <span class=n>rgb</span><span class=o>=</span><span class=kc>False</span><span class=p>,</span> <span class=n>nchw</span><span class=o>=</span><span class=kc>False</span><span class=p>):</span>
    <span class=n>img</span> <span class=o>=</span> <span class=n>cv2</span><span class=o>.</span><span class=n>imread</span><span class=p>(</span><span class=n>img_path</span><span class=p>,</span> <span class=n>flags</span><span class=o>=-</span><span class=mi>1</span><span class=p>)</span>
    <span class=k>if</span> <span class=n>img</span> <span class=ow>is</span> <span class=kc>None</span><span class=p>:</span>
        <span class=k>raise</span> <span class=ne>FileNotFoundError</span><span class=p>(</span><span class=s1>&#39;No such image: </span><span class=si>{}</span><span class=s1>&#39;</span><span class=o>.</span><span class=n>format</span><span class=p>(</span><span class=n>img_path</span><span class=p>))</span>

    <span class=k>try</span><span class=p>:</span>
        <span class=n>img_dim</span> <span class=o>=</span> <span class=n>img</span><span class=o>.</span><span class=n>shape</span><span class=p>[</span><span class=mi>2</span><span class=p>]</span>
    <span class=k>except</span> <span class=ne>IndexError</span><span class=p>:</span>
        <span class=n>img_dim</span> <span class=o>=</span> <span class=mi>1</span>
    <span class=k>if</span> <span class=n>img_dim</span> <span class=o>==</span> <span class=mi>4</span><span class=p>:</span>
        <span class=n>img</span> <span class=o>=</span> <span class=n>cv2</span><span class=o>.</span><span class=n>cvtColor</span><span class=p>(</span><span class=n>img</span><span class=p>,</span> <span class=n>cv2</span><span class=o>.</span><span class=n>COLOR_BGRA2BGR</span><span class=p>)</span>
    <span class=k>elif</span> <span class=n>img_dim</span> <span class=o>==</span> <span class=mi>1</span><span class=p>:</span>
        <span class=n>img</span> <span class=o>=</span> <span class=n>cv2</span><span class=o>.</span><span class=n>cvtColor</span><span class=p>(</span><span class=n>img</span><span class=p>,</span> <span class=n>cv2</span><span class=o>.</span><span class=n>COLOR_GRAY2BGR</span><span class=p>)</span>
    <span class=n>img_float</span> <span class=o>=</span> <span class=n>img</span><span class=o>.</span><span class=n>astype</span><span class=p>(</span><span class=s1>&#39;float32&#39;</span><span class=p>)</span>
    <span class=n>img_norm</span> <span class=o>=</span> <span class=n>cv2</span><span class=o>.</span><span class=n>resize</span><span class=p>(</span><span class=n>img_float</span><span class=p>,</span> <span class=p>(</span><span class=n>resizeW</span><span class=p>,</span> <span class=n>resizeH</span><span class=p>),</span> <span class=n>interpolation</span><span class=o>=</span><span class=n>cv2</span><span class=o>.</span><span class=n>INTER_LINEAR</span><span class=p>)</span>

    <span class=k>if</span> <span class=n>norm</span> <span class=ow>and</span> <span class=p>(</span><span class=n>resizeC</span> <span class=o>==</span> <span class=mi>3</span><span class=p>):</span>
        <span class=n>img_norm</span> <span class=o>=</span> <span class=p>(</span><span class=n>img_norm</span> <span class=o>-</span> <span class=p>[</span><span class=n>meanB</span><span class=p>,</span> <span class=n>meanG</span><span class=p>,</span> <span class=n>meanR</span><span class=p>])</span> <span class=o>/</span> <span class=n>std</span>
        <span class=n>img_norm</span> <span class=o>=</span> <span class=n>img_norm</span><span class=o>.</span><span class=n>astype</span><span class=p>(</span><span class=s1>&#39;float32&#39;</span><span class=p>)</span>
    <span class=k>elif</span> <span class=n>norm</span> <span class=ow>and</span> <span class=p>(</span><span class=n>resizeC</span> <span class=o>==</span> <span class=mi>1</span><span class=p>):</span>
        <span class=n>img_norm</span> <span class=o>=</span> <span class=p>(</span><span class=n>img_norm</span> <span class=o>-</span> <span class=n>meanB</span><span class=p>)</span> <span class=o>/</span> <span class=n>std</span>
        <span class=n>img_norm</span> <span class=o>=</span> <span class=n>img_norm</span><span class=o>.</span><span class=n>astype</span><span class=p>(</span><span class=s1>&#39;float32&#39;</span><span class=p>)</span>
    <span class=k>else</span><span class=p>:</span>
        <span class=n>img_norm</span> <span class=o>=</span> <span class=n>np</span><span class=o>.</span><span class=n>round</span><span class=p>(</span><span class=n>img_norm</span><span class=p>)</span><span class=o>.</span><span class=n>astype</span><span class=p>(</span><span class=s1>&#39;uint8&#39;</span><span class=p>)</span>

    <span class=k>if</span> <span class=n>rgb</span><span class=p>:</span>
        <span class=n>img_norm</span> <span class=o>=</span> <span class=n>cv2</span><span class=o>.</span><span class=n>cvtColor</span><span class=p>(</span><span class=n>img_norm</span><span class=p>,</span> <span class=n>cv2</span><span class=o>.</span><span class=n>COLOR_BGR2RGB</span><span class=p>)</span>

    <span class=k>if</span> <span class=n>nchw</span><span class=p>:</span>
        <span class=c1># NCHW</span>
        <span class=n>img_norm</span> <span class=o>=</span> <span class=n>np</span><span class=o>.</span><span class=n>transpose</span><span class=p>(</span><span class=n>img_norm</span><span class=p>,</span> <span class=n>axes</span><span class=o>=</span><span class=p>(</span><span class=mi>2</span><span class=p>,</span> <span class=mi>0</span><span class=p>,</span> <span class=mi>1</span><span class=p>))</span>

    <span class=k>return</span> <span class=n>np</span><span class=o>.</span><span class=n>expand_dims</span><span class=p>(</span><span class=n>img_norm</span><span class=p>,</span> <span class=mi>0</span><span class=p>)</span>

<span class=k>def</span> <span class=nf>image_preprocess</span><span class=p>(</span><span class=n>img_path</span><span class=p>,</span> <span class=n>norm</span><span class=o>=</span><span class=kc>True</span><span class=p>):</span>
    <span class=k>return</span> <span class=n>get_image</span><span class=p>(</span><span class=n>img_path</span><span class=p>,</span> <span class=n>norm</span><span class=o>=</span><span class=n>norm</span><span class=p>)</span>
</code></pre></div> 使用image_preprocess函数调用，此处务必按照该方法书写。 <div class=highlight><pre><span></span><code><span class=k>def</span> <span class=nf>image_preprocess</span><span class=p>(</span><span class=n>img_path</span><span class=p>,</span> <span class=n>norm</span><span class=o>=</span><span class=kc>True</span><span class=p>):</span>
    <span class=k>return</span> <span class=n>get_image</span><span class=p>(</span><span class=n>img_path</span><span class=p>,</span> <span class=n>norm</span><span class=o>=</span><span class=n>norm</span><span class=p>)</span>
</code></pre></div> 保存为 caffe_resnet18.py. 在SGS_IPU_SDK/Scripts/calibrator/preprocess_method/<strong>init</strong>.py 文件中添加刚刚编写的python文件 <div class=highlight><pre><span></span><code><span class=n>_all_</span> <span class=o>=</span> <span class=p>[</span><span class=s1>&#39;caffe_mobilenet_v2&#39;</span><span class=p>,</span> <span class=s1>&#39;caffe_resnet18&#39;</span><span class=p>,</span> <span class=s1>&#39;caffe_resnet50_conv&#39;</span><span class=p>,</span> <span class=s1>&#39;mobilenet_v1&#39;</span><span class=p>]</span>
</code></pre></div> 使用calibrator或simulator时 <code>-n/--preprocess</code> 参数为caffe_resnet18 即可调用刚刚编写的图片前处理文件。 如果不在SGS_IPU_SDK/Scripts/calibrator/preprocess_method/<strong>init</strong>.py 文件中增加，<code>-n/--preprocess</code> 参数为caffe_resnet18.py文件路径，也可以为图片做前处理。</p> <hr> <h2 id=33>3.3. 卷积量化选项<a class=headerlink href=#33 title="Permanent link">&para;</a></h2> <p>浮点网络模型转换到定点网络模型时，卷积量化支持 <strong>UINT8</strong> 和 <strong>INT16</strong> 两种量化方式。使用calibrator时， <code>--quant_level</code> 选择L2、L3或L4时会根据统计信息自动配置卷积的量化方式，如果需要强制指定，指定方式在 [2.2.3 CONV_CONFIG]中有说明。可对某些卷积层单独设置INT16量化，或者对全部卷积INT16量化；不设置时默认使用calibrator的推荐量化方式。</p> <hr> <h2 id=34-calibrator_customcalibrator>3.4. calibrator_custom.calibrator<a class=headerlink href=#34-calibrator_customcalibrator title="Permanent link">&para;</a></h2> <p>calibrator_custom.calibrator是基于Python的快速量化和转换模型的模块。使用calibrator_custom.calibrator可以更方便灵活的对多输入、多段网络进行量化和转换。目前基于的docker环境，提供Python3.7的预编译的Python模块，使用方法和相关API接口如下：</p> <p><div class=highlight><pre><span></span><code><span class=kn>import</span> <span class=nn>calibrator_custom</span>
<span class=n>model_path</span> <span class=o>=</span> <span class=s1>&#39;./mobilenet_v2_float.sim&#39;</span>
<span class=n>input_config_path</span> <span class=o>=</span> <span class=s1>&#39;./input_config.ini&#39;</span>
<span class=n>model</span> <span class=o>=</span> <span class=n>calibrator_custom</span><span class=o>.</span><span class=n>calibrator</span><span class=p>(</span><span class=n>model_path</span><span class=p>,</span> <span class=n>input_config_path</span><span class=p>)</span>
</code></pre></div> 使用 <code>calibrator_custom.calibrator</code> 时，需要给定float.sim模型的路径和对应的input_config.ini的路径，用于创建calibrator的实例。参数给定错误，将无法成功创建calibrator实例，并返回ValueError。</p> <hr> <h3 id=341-calibrator_customcalibrator>3.4.1 calibrator_custom.calibrator方法<a class=headerlink href=#341-calibrator_customcalibrator title="Permanent link">&para;</a></h3> <p><code>get_input_details</code></p> <p>返回网络模型输入信息（list）</p> <div class=highlight><pre><span></span><code><span class=n>input_details</span> <span class=o>=</span> <span class=n>model</span><span class=o>.</span><span class=n>get_input_details</span><span class=p>()</span>
<span class=o>&gt;&gt;&gt;</span> <span class=nb>print</span><span class=p>(</span><span class=n>input_details</span><span class=p>)</span>
<span class=p>[{</span><span class=s1>&#39;index&#39;</span><span class=p>:</span> <span class=mi>0</span><span class=p>,</span> <span class=s1>&#39;shape&#39;</span><span class=p>:</span> <span class=n>array</span><span class=p>([</span> <span class=mi>1</span><span class=p>,</span> <span class=mi>513</span><span class=p>,</span> <span class=mi>513</span><span class=p>,</span> <span class=mi>3</span><span class=p>],</span> <span class=n>dtype</span><span class=o>=</span><span class=n>int32</span><span class=p>),</span> <span class=s1>&#39;name&#39;</span><span class=p>:</span> <span class=s1>&#39;sub_7&#39;</span><span class=p>,</span> <span class=s1>&#39;dtype&#39;</span><span class=p>:</span> <span class=o>&lt;</span><span class=k>class</span> <span class=err>&#39;</span><span class=nc>numpy</span><span class=o>.</span><span class=n>float32</span><span class=s1>&#39;&gt;}]</span>
</code></pre></div> <p>返回的list中根据模型输入个数包含以下dict信息：</p> <p><code>index</code>: 输入Tensor序号</p> <p><code>name</code>: 输入Tensor名称</p> <p><code>shape</code>: 输入Tensor的形状</p> <p><code>dtype</code>: 输入Tensor的数据类型</p> <p><code>get_output_detail</code></p> <p>返回网络模型输出信息（list）</p> <div class=highlight><pre><span></span><code><span class=n>output_details</span> <span class=o>=</span> <span class=n>model</span><span class=o>.</span><span class=n>get_output_details</span><span class=p>()</span>
<span class=o>&gt;&gt;&gt;</span> <span class=nb>print</span><span class=p>(</span><span class=n>output_details</span><span class=p>)</span>
<span class=p>[{</span><span class=s1>&#39;index&#39;</span><span class=p>:</span> <span class=mi>0</span><span class=p>,</span> <span class=s1>&#39;shape&#39;</span><span class=p>:</span> <span class=n>array</span><span class=p>([</span> <span class=mi>1</span><span class=p>,</span> <span class=mi>257</span><span class=p>,</span> <span class=mi>257</span><span class=p>,</span> <span class=mi>30</span><span class=p>],</span> <span class=n>dtype</span><span class=o>=</span><span class=n>int32</span><span class=p>),</span> <span class=s1>&#39;name&#39;</span><span class=p>:</span> <span class=s1>&#39;MobilenetV2/Conv/Conv2D&#39;</span><span class=p>,</span> <span class=s1>&#39;dtype&#39;</span><span class=p>:</span> <span class=o>&lt;</span><span class=k>class</span> <span class=err>&#39;</span><span class=nc>numpy</span><span class=o>.</span><span class=n>float32</span><span class=s1>&#39;&gt;}]</span>
</code></pre></div> <p>返回的list中根据模型输出个数包含以下dict信息：</p> <p><code>index</code>: 输出Tensor序号</p> <p><code>name</code>: 输出Tensor名称</p> <p><code>shape</code>: 输出Tensor的形状</p> <p><code>dtype</code>: 输出Tensor的数据类型</p> <p><code>set_input</code></p> <p>设置网络模型输入数据</p> <div class=highlight><pre><span></span><code><span class=n>model</span><span class=o>.</span><span class=n>set_input</span><span class=p>(</span><span class=mi>0</span><span class=p>,</span> <span class=n>img_data</span><span class=p>)</span>
</code></pre></div> <p>输入数据，0为输入Tensor的index，可以在get_input_details()的返回值里拿到。img_data是与model输入shape和dtype相同的numpy.ndarray格式数据，错误的shape或dtype，将导致set_input返回ValueError。如果模型有多个输入，可以多次调用set_input，根据get_input_details()的返回值里拿到index设置对应Tensor的输入数据。</p> <p><code>invoke</code></p> <p>模型运行一次</p> <div class=highlight><pre><span></span><code><span class=n>model</span><span class=o>.</span><span class=n>invoke</span><span class=p>()</span>
</code></pre></div> <p>调用invoke前请先使用set_input设置输入数据，未调用set_input直接调用invoke会返回ValueError。</p> <p><code>get_output</code></p> <p>获取网络模型输出数据</p> <div class=highlight><pre><span></span><code><span class=n>result</span> <span class=o>=</span> <span class=n>model</span><span class=o>.</span><span class=n>get_output</span><span class=p>(</span><span class=mi>0</span><span class=p>)</span>
</code></pre></div> <p>获取输出数据，0为输出Tensor的index，可以在get_output_details()的返回值里拿到，返回numpy.ndarray格式输出数据。如果模型有多个输出，可以多次调用get_output，根据get_output_details()的返回值里拿到index获取对应Tensor的输出数据。</p> <p><code>get_tensor_details</code></p> <p>返回网络模型每个Tensor的信息（list）</p> <div class=highlight><pre><span></span><code><span class=n>tensor_details</span> <span class=o>=</span> <span class=n>model</span><span class=o>.</span><span class=n>get_tensor_details</span><span class=p>()</span>
<span class=o>&gt;&gt;&gt;</span><span class=nb>print</span><span class=p>(</span><span class=n>tensor_details</span><span class=p>)</span>
<span class=p>[{</span><span class=s1>&#39;dtype&#39;</span><span class=p>:</span> <span class=s1>&#39;FLOAT32&#39;</span><span class=p>,</span> <span class=s1>&#39;name&#39;</span><span class=p>:</span> <span class=s1>&#39;MobilenetV2/Conv/Conv2D&#39;</span><span class=p>,</span> <span class=s1>&#39;qtype&#39;</span><span class=p>:</span> <span class=s1>&#39;INT16&#39;</span><span class=p>,</span> <span class=s1>&#39;shape&#39;</span><span class=p>:</span> <span class=n>array</span><span class=p>([</span> <span class=mi>1</span><span class=p>,</span> <span class=mi>257</span><span class=p>,</span> <span class=mi>257</span><span class=p>,</span> <span class=mi>30</span><span class=p>],</span> <span class=n>dtype</span><span class=o>=</span><span class=n>int32</span><span class=p>)},</span> <span class=p>{</span><span class=s1>&#39;dtype&#39;</span><span class=p>:</span> <span class=s1>&#39;FLOAT32&#39;</span><span class=p>,</span> <span class=s1>&#39;name&#39;</span><span class=p>:</span> <span class=s1>&#39;MobilenetV2/Conv/Conv2D_bias&#39;</span><span class=p>,</span> <span class=s1>&#39;qtype&#39;</span><span class=p>:</span> <span class=s1>&#39;INT16&#39;</span><span class=p>,</span> <span class=s1>&#39;shape&#39;</span><span class=p>:</span> <span class=n>array</span><span class=p>([</span> <span class=mi>2</span><span class=p>,</span> <span class=mi>30</span><span class=p>],</span> <span class=n>dtype</span><span class=o>=</span><span class=n>int32</span><span class=p>)},</span> <span class=p>{</span><span class=s1>&#39;dtype&#39;</span><span class=p>:</span> <span class=s1>&#39;FLOAT32&#39;</span><span class=p>,</span> <span class=s1>&#39;name&#39;</span><span class=p>:</span> <span class=s1>&#39;MobilenetV2/Conv/weights/read&#39;</span><span class=p>,</span> <span class=s1>&#39;qtype&#39;</span><span class=p>:</span> <span class=s1>&#39;INT8&#39;</span><span class=p>,</span> <span class=s1>&#39;shape&#39;</span><span class=p>:</span> <span class=n>array</span><span class=p>([</span><span class=mi>30</span><span class=p>,</span> <span class=mi>3</span><span class=p>,</span> <span class=mi>3</span><span class=p>,</span> <span class=mi>3</span><span class=p>],</span> <span class=n>dtype</span><span class=o>=</span><span class=n>int32</span><span class=p>)},</span> <span class=p>{</span><span class=s1>&#39;dtype&#39;</span><span class=p>:</span> <span class=s1>&#39;FLOAT32&#39;</span><span class=p>,</span> <span class=s1>&#39;name&#39;</span><span class=p>:</span> <span class=s1>&#39;sub_7&#39;</span><span class=p>,</span> <span class=s1>&#39;qtype&#39;</span><span class=p>:</span> <span class=s1>&#39;UINT8&#39;</span><span class=p>,</span> <span class=s1>&#39;shape&#39;</span><span class=p>:</span> <span class=n>array</span><span class=p>([</span> <span class=mi>1</span><span class=p>,</span> <span class=mi>513</span><span class=p>,</span> <span class=mi>513</span><span class=p>,</span> <span class=mi>3</span><span class=p>],</span> <span class=n>dtype</span><span class=o>=</span><span class=n>int32</span><span class=p>)}]</span>
</code></pre></div> <p>返回的list中根据模型Tensor个数包含以下dict信息：</p> <p><code>name</code>: Tensor名称</p> <p><code>shape</code>: Tensor的形状</p> <p><code>dtype</code>: Tensor的数据类型</p> <p><code>qtype</code>: 定点模型该Tensor可能的数据类型（quantization type）</p> <hr> <h3 id=342-calibrator_customsim_calibrator>3.4.2 calibrator_custom.SIM_Calibrator<a class=headerlink href=#342-calibrator_customsim_calibrator title="Permanent link">&para;</a></h3> <p>对于多输入、多段网络同时转换时，提供calibrator_custom.SIM_Calibrator，方便进行简单定义后，统一转换。</p> <p>calibrator_custom.SIM_Calibrator是已经实现好的class，当中只有forward方法未实现，使用时仅需实现该方法，即可转换完成。</p> <p>下面以<code>SGS_IPU_SDK/Scripts/examples/sim_calibrator.py</code>为例，说明calibrator_custom.SIM_Calibrator的使用方法：</p> <div class=highlight><pre><span></span><code><span class=kn>import</span> <span class=nn>calibrator_custom</span>
<span class=k>class</span> <span class=nc>Net</span><span class=p>(</span><span class=n>calibrator_custom</span><span class=o>.</span><span class=n>SIM_Calibrator</span><span class=p>):</span>
    <span class=k>def</span> <span class=fm>__init__</span><span class=p>(</span><span class=bp>self</span><span class=p>):</span>
        <span class=nb>super</span><span class=p>()</span><span class=o>.</span><span class=fm>__init__</span><span class=p>()</span>
        <span class=bp>self</span><span class=o>.</span><span class=n>model</span> <span class=o>=</span> <span class=n>calibrator_custom</span><span class=o>.</span><span class=n>calibrator</span><span class=p>(</span><span class=n>model_path</span><span class=p>,</span> <span class=n>input_config</span><span class=p>)</span>
    <span class=k>def</span> <span class=nf>forward</span><span class=p>(</span><span class=bp>self</span><span class=p>,</span> <span class=n>x</span><span class=p>):</span>
        <span class=n>out_details</span> <span class=o>=</span> <span class=bp>self</span><span class=o>.</span><span class=n>model</span><span class=o>.</span><span class=n>get_output_details</span><span class=p>()</span>
        <span class=bp>self</span><span class=o>.</span><span class=n>model</span><span class=o>.</span><span class=n>set_input</span><span class=p>(</span><span class=mi>0</span><span class=p>,</span> <span class=n>x</span><span class=p>)</span>
        <span class=bp>self</span><span class=o>.</span><span class=n>model</span><span class=o>.</span><span class=n>invoke</span><span class=p>()</span>
        <span class=n>result_list</span> <span class=o>=</span> <span class=p>[]</span>
        <span class=k>for</span> <span class=n>idx</span> <span class=ow>in</span> <span class=nb>range</span><span class=p>(</span><span class=nb>len</span><span class=p>(</span><span class=n>out_details</span><span class=p>)):</span>
            <span class=n>result</span> <span class=o>=</span> <span class=bp>self</span><span class=o>.</span><span class=n>model</span><span class=o>.</span><span class=n>get_output</span><span class=p>(</span><span class=n>idx</span><span class=p>)</span>
            <span class=n>result_list</span><span class=o>.</span><span class=n>append</span><span class=p>(</span><span class=n>result</span><span class=p>)</span>
        <span class=k>return</span> <span class=n>result_list</span>
</code></pre></div> <p>定义forward方法，forward的参数为模型输入，如有多个输入，可增加forward的参数。</p> <p>创建calibrator_custom.SIM_Calibrator的实例</p> <div class=highlight><pre><span></span><code><span class=n>net</span> <span class=o>=</span> <span class=n>Net</span><span class=p>()</span>
</code></pre></div> <p>调用calibrator_custom.SIM_Calibrator的convert方法</p> <div class=highlight><pre><span></span><code><span class=n>net</span><span class=o>.</span><span class=n>convert</span><span class=p>(</span><span class=n>img_gen</span><span class=p>,</span> <span class=n>fix_model</span><span class=o>=</span><span class=p>[</span><span class=n>out_model_path</span><span class=p>])</span>
</code></pre></div> <p>convert方法必须输入两个参数：图片生成器、fixed.sim模型的保存路径list。</p> <ul> <li>图片生成器（img_gen）</li> </ul> <p>为方便多输入、多段网络转换模型，通过生成器方便组织输入图片的序列。如模型有多个输入，生成器应该按照定义forward时的输入顺序，返回有多个numpy.ndarray的list。</p> <p>calibrator_custom.utils.image_preprocess_func使用预先定义好的前处理方法。</p> <div class=highlight><pre><span></span><code><span class=n>preprocess_func</span> <span class=o>=</span> <span class=n>calibrator_custom</span><span class=o>.</span><span class=n>utils</span><span class=o>.</span><span class=n>image_preprocess_func</span><span class=p>(</span><span class=n>model_name</span><span class=p>)</span>

<span class=k>def</span> <span class=nf>image_generator</span><span class=p>(</span><span class=n>folder_path</span><span class=p>,</span> <span class=n>preprocess_func</span><span class=p>):</span>
    <span class=n>images</span> <span class=o>=</span> <span class=p>[</span><span class=n>os</span><span class=o>.</span><span class=n>path</span><span class=o>.</span><span class=n>join</span><span class=p>(</span><span class=n>folder_path</span><span class=p>,</span> <span class=n>img</span><span class=p>)</span> <span class=k>for</span> <span class=n>img</span> <span class=ow>in</span> <span class=n>os</span><span class=o>.</span><span class=n>listdir</span><span class=p>(</span><span class=n>folder_path</span><span class=p>)]</span>
    <span class=k>for</span> <span class=n>image</span> <span class=ow>in</span> <span class=n>images</span><span class=p>:</span>
        <span class=n>img</span> <span class=o>=</span> <span class=n>preprocess_func</span><span class=p>(</span><span class=n>image</span><span class=p>)</span>
        <span class=k>yield</span> <span class=p>[</span><span class=n>img</span><span class=p>]</span>
<span class=n>img_gen</span> <span class=o>=</span> <span class=n>image_generator</span><span class=p>(</span><span class=s1>&#39;./images&#39;</span><span class=p>,</span> <span class=n>preprocess_func</span><span class=p>)</span>
</code></pre></div> <ul> <li>fixed.sim模型的保存路径list</li> </ul> <p>如果在__init__中定义多个模型，fixed模型的保存路径list应该按照__init__中定义模型的顺序，依次命名。</p> <ul> <li>其他可选参数：</li> </ul> <div class=highlight><pre><span></span><code>num_process: 进程数，运行同时运行的CPU数量

quant_level: 选择量化等级：[L1, L2, L3, L4, L5]，默认L5量化等级，具体含义详见3.1.2节Calibrator的可选参数使用方法。

quant_param: 量化导入参数。如果已有对应模型的量化参数，可以将量化的参数在模型转换中导入。
</code></pre></div> <hr> <h3 id=343>3.4.3 导入量化参数规则<a class=headerlink href=#343 title="Permanent link">&para;</a></h3> <h4 id=3431>3.4.3.1. 量化策略和方法<a class=headerlink href=#3431 title="Permanent link">&para;</a></h4> <ul> <li> <p>Conv2D的量化方法</p> <p>Conv2D的量化分为输入、Weights和输出，目前支持8bit，16bit量化。</p> <p>根据统计得到的输入、Weights和输出的min和max值，首先提前将Weights量化到定点数据，保存在fixed的网络内部，再在网络运行时动态量化输入和输出数据。Weights的min和max值的个数由kernel的个数决定，输入和输出的min和max值的个数由输入输出的C维度决定。如果使用8bit量化，Conv2D的输入为UINT8（相当于INT9的表达能力），Weights为INT8；如果使用16bit量化，Conv2D的输入和Weights均为INT16。</p> </li> <li> <p>DepthwiseConv2D的量化方法</p> <p>DepthwiseConv2D的量化分为输入、Weights和输出，目前支持8bit，16bit量化。</p> <p>根据统计得到的输入、Weights和输出的min和max值，首先提前将Weights量化到定点数据，保存在fixed的网络内部，再在网络运行时动态量化输入和输出数据。Weights、输入和输出的min和max值的个数均由对应Tensor的C维度决定。如果使用8bit量化，DepthwiseConv2D的输入为UINT8（相当于INT9的表达能力），Weights为INT8；如果使用16bit量化，DepthwiseConv2D的输入和Weights均为INT16。</p> </li> <li> <p>其他Op量化方法</p> <p>网络中其他算子的量化min和max均基于C维度数量，仅支持16bit量化。调用calibrator_custom.calibrator的get_tensor_details方法可以从qtype中得知该Tensor在定点模型时的data type。</p> </li> </ul> <hr> <h4 id=3432>*******. 量化参数的内容<a class=headerlink href=#3432 title="Permanent link">&para;</a></h4> <p>基于上述量化的策略和方法，需要为Tensor提供如下信息：</p> <ol> <li> <p>Tensor的名字（name）[str]</p> </li> <li> <p>算子类型提供对应数量的min，max。[list]</p> </li> <li> <p>量化bit位。[int]</p> </li> <li> <p>常量Tensor数据（data）（可选）[numpy.ndarray]</p> </li> </ol> <p>下表展示了对应的参数方式，整个参数为list，其中每个item是一个dict，包含上述信息。</p> <div class=highlight><pre><span></span><code><span class=p>[</span>
    <span class=p>{</span>
        <span class=s2>&quot;name&quot;</span><span class=p>:</span> <span class=s2>&quot;FeatureExtractor/MobilenetV2/Conv2d_0/weights&quot;</span><span class=p>,</span>
        <span class=s2>&quot;min&quot;</span><span class=p>:</span> <span class=p>[</span><span class=o>-</span><span class=mf>4.555312</span><span class=p>,</span> <span class=o>-</span><span class=mf>2.876907</span><span class=p>,</span> <span class=o>-</span><span class=mf>1.234419</span><span class=p>],</span>
        <span class=s2>&quot;max&quot;</span><span class=p>:</span> <span class=p>[</span><span class=mf>7.364561</span><span class=p>,</span> <span class=mf>3.960804</span><span class=p>,</span> <span class=mf>6.0</span><span class=p>],</span>
        <span class=s2>&quot;bit&quot;</span><span class=p>:</span> <span class=mi>8</span>
    <span class=p>},</span>
    <span class=p>{</span><span class=o>...</span><span class=p>},</span>
    <span class=o>...</span>
<span class=p>]</span>
</code></pre></div> <p>示例SGS_IPU_SDK/Scripts/examples/sim_calibrator.py中已完成了参数导入，可以提供json和pkl文件的读取和导入。</p> <hr> <h4 id=34321>*******.1 量化模型量化参数导出工具<a class=headerlink href=#34321 title="Permanent link">&para;</a></h4> <p>量化模型量化参数导出工具位置 SGS_IPU_SDK/Scripts/examples/save_quant_param.py 使用示例：<code>--output_mode</code>参数支持JSON或Pickle格式量化参数数据的导出。</p> <div class=highlight><pre><span></span><code>python3 save_quant_param.py \
-m fixed.sim \
--output_mode {JSON,Pickle}
</code></pre></div> <hr> <h4 id=3433>*******. 导入量化数据流程<a class=headerlink href=#3433 title="Permanent link">&para;</a></h4> <p>为了兼容原有量化流程，原有量化所采用的策略依然进行。</p> <p>原始模型在转成SGS_Float模型以后，一些算子会被合并和优化，计算图与原框架的模型有较大差异。所提供的量化文件内容需基于已转成的SGS_Float模型针对性调整，如Tensor名称等信息。</p> <p>为方便修改被合并和优化的层，可以使用calibrator_custom.calibrator的get_tensor_details方法获取Tensor的基本信息。可以获得每个Tensor的name，shape，dtype，qtype信息。</p> <p>转换好的fixed模型可以使用calibrator_custom.fixed_simulator的get_tensor_details方法获取Tensor的基本信息，可以获得每个Tensor的name，shape，min，max，quantization，dtype信息。</p> <p>根据模型Tensor信息，更新原始模型的量化文件后，即可导入量化数据文件。</p> <p>解析完量化文件后，会匹配已有量化信息与导入的量化信息。优先使用导入的量化信息，但不排除在合并时由于导入信息的不合理而放弃使用。</p> <p><img alt src=mymedia/quantization_data_import_flow.png></p> <hr> <h2 id=35-torch_calibrator>3.5. torch_calibrator<a class=headerlink href=#35-torch_calibrator title="Permanent link">&para;</a></h2> <p>torch_calibrator工具的位置在 <strong>SGS_IPU_SDK/Scripts/calibrator/torch_calibrator.py</strong>。 torch_calibrator是基于Pytorch开发的新一代量化工具，与旧工具链相比，有以下特性：</p> <ul> <li> <p>支持使用GPU，同等级的算法在GPU环境下量化速度更快（需配合使用Nvidia® GPU Docker）；</p> </li> <li> <p>支持4/8bit混合量化算法，对于有条件的模型在8bit量化的基础上继续向下压缩，进一步提高运行效率；</p> </li> <li> <p>支持更复杂的量化标定算法，量化精度进一步得到提升，对小模型（mobilenet系列、shufflenet系列、nanodet系列等）提升效果更为明显。</p> </li> </ul> <p>torch_calibrator是对3.1 calibrator工具的补充。当模型参数量较小，使用3.1 calibrator工具无法达到满意的精度时；或者模型参数量较大，期待使用4bit量化减小模型大小，可以使用torch_calibrator工具。</p> <p>工具使用示例： <div class=highlight><pre><span></span><code>python3 torch_calibrator.py \
-i ilsvrc2012_calibration_set100 \
-m caffe_mobilenet_v2_float.sim \
--input_config input_config.ini \
--quant_config quant_config.yaml \
-n caffe_mobilenet_v2.py \
-q Q2
</code></pre></div></p> <hr> <h3 id=351>3.5.1. 参数说明<a class=headerlink href=#351 title="Permanent link">&para;</a></h3> <p><code>-i</code>,<code>--image</code> : 标定数据集图像路径，100张训练图片。(必选)</p> <p><code>-m</code>, <code>--model</code> : 浮点网络模型文件路径。(必选)</p> <p><code>--input_config</code> : input_config.ini文件路径。(必选)</p> <p><code>--quant_config</code> : 量化参数(yaml格式)文件路径，后面会详细介绍该文件的配置方法和用途。(必选)</p> <p><code>-n</code>, <code>--preprocess</code> : 模型预处理文件（py文件）路径。(必选)</p> <p><code>-q</code>, <code>--q_mode</code> : 量化选项，后面会详细介绍。(必选)</p> <p><code>-o</code>, <code>--ouptut</code> : 模型输出路径。指定定点网络模型输出位置：指定到文件夹，将自动以浮点网络模型文件前缀命名，后接fixed.sim；指定到具体路径和文件名，将以指定路径和文件名命名定点网络模型；不指定该参数，将以浮点网络模型文件路径储存定点网络模型。</p> <p><code>--cal_batchsize</code> : 标定数据集的batchsize，一般情况下等于数据集个数；如果标定数据集十分庞大或模型结构特殊无法在batchsize != 1的情况下推理，则可以设置这个参数。默认为100。</p> <hr> <h3 id=352>3.5.2. 量化选项<a class=headerlink href=#352 title="Permanent link">&para;</a></h3> <table> <tr> <th style="width: 30px; text-align: center;">量化等级</th> <th style="width: 180px; text-align: center;">量化特色说明</th> <th>量化子选项</th> <th>详细说明</th> </tr> <tr> <td rowspan=4>Q1</td> <td rowspan=4> 仅做min和max的标定，不会优化卷积参数，速度整体较快，适用于没有GPU的环境，如果有GPU则可以加快标定速度；精度方面整体持平3.1 calibrator量化的精度； </td> <td>Q10</td> <td>全16bit量化，量化精度与float32模型基本一致</td> </tr> <tr> <td>Q1/Q11</td> <td>全8bit快速量化，量化精度与L2模式相当</td> </tr> <tr> <td>Q12</td> <td>自适应8bit和16bit量化，量化精度与L5模式相当</td> </tr> <tr> <td>Q13</td> <td>4/8bit(8/16bit 可根据配置文件更改，默认4/8bit)混合量化，对于有进一步压缩需求的模型可以选用此方式。</td> </tr> <tr> <td rowspan=4>Q2</td> <td rowspan=4> 量化等级会对模型进行逐层优化，低bit情况下精度提升明显，在轻量级模型效果更为显著，适用于有单块GPU的环境；精度上整体优于Q1 </td> <td>Q20</td> <td>全16bit量化，量化精度与float32模型基本一致</td> </tr> <tr> <td>Q21</td> <td>全8bit量化，在一些小模型上能够获得更好的效果</td> </tr> <tr> <td>Q2/Q22</td> <td>自适应选择量化方式，是目前效果最好的量化方式，生成的定点模型为全8bit量化或接近全8的8/16混合量化。</td> </tr> <tr> <td>Q23</td> <td>4/8bit（8/16bit 可根据配置文件更改，默认4/8bit）混合量化，可以进一步压缩模型，压缩程度和精度均优于Q13</td> </tr> </table> <hr> <h3 id=353-yaml>3.5.3. 量化参数(yaml格式)文件<a class=headerlink href=#353-yaml title="Permanent link">&para;</a></h3> <div class=highlight><pre><span></span><code><span class=nt>QUANT_CONFIG</span><span class=p>:</span>
  <span class=nt>retrain_iter_num</span><span class=p>:</span> <span class="l l-Scalar l-Scalar-Plain">40</span>
  <span class=nt>device</span><span class=p>:</span> <span class=s>&#39;cuda:0&#39;</span>
  <span class=nt>mixed_precisions</span><span class=p>:</span> <span class="p p-Indicator">[</span><span class=nv>4</span><span class="p p-Indicator">,</span> <span class=nv>8</span><span class="p p-Indicator">]</span>
  <span class=nt>use_preset_min_max_bit</span><span class=p>:</span> <span class="l l-Scalar l-Scalar-Plain">0</span>
  <span class=nt>mp_rate</span><span class=p>:</span> <span class="l l-Scalar l-Scalar-Plain">0.6</span>

<span class=nt>PRESET_MIN_MAX_BIT</span><span class=p>:</span> <span class=c1># preset tensor min/max and bit</span>
  <span class="p p-Indicator">[</span>
    <span class="p p-Indicator">{</span><span class=nt>name</span><span class=p>:</span> <span class=s>&quot;126&quot;</span><span class="p p-Indicator">,</span><span class=nt> min</span><span class=p>:</span> <span class=nv>null</span><span class="p p-Indicator">,</span><span class=nt> max</span><span class=p>:</span> <span class=nv>null</span><span class="p p-Indicator">,</span><span class=nt> bit</span><span class=p>:</span> <span class=nv>8</span><span class="p p-Indicator">},</span>
    <span class="p p-Indicator">{</span><span class=nt>name</span><span class=p>:</span> <span class=s>&quot;layer1.0.conv1.weight&quot;</span><span class="p p-Indicator">,</span><span class=nt> min</span><span class=p>:</span> <span class=nv>null</span><span class="p p-Indicator">,</span><span class=nt> max</span><span class=p>:</span> <span class=nv>null</span><span class="p p-Indicator">,</span><span class=nt> bit</span><span class=p>:</span> <span class=nv>8</span><span class="p p-Indicator">}</span>
  <span class="p p-Indicator">]</span>
</code></pre></div> <p>QUANT_CONFIG</p> <ul> <li> <p>retrain_iter_num：每层重训练迭代次数，如果该项不写则默认为40，部分轻量级模型可能需要更多的迭代次数。</p> </li> <li> <p>device：指定GPU/CPU，如果不给则会自动寻找空闲的GPU。若无GPU则会自动调用CPU。指定'cpu'会使用CPU运算。</p> </li> <li> <p>use_preset_min_max_bit：是否使用手工预设的min/max/bit信息；如果该项为1，则会使用下方手动设置的信息；如果该项为0或不写，则不会使用下方的信息。</p> </li> <li> <p>mixed_precisions: 混合量化模式，可选[4, 8]，[8, 16]。如果不填，默认是[4, 8]。仅在Q13、Q23的模式生效。</p> </li> <li> <p>mp_rate：混合量化指定的压缩率，如未指定或不在[0.5, 1]的范围内，则会自动推荐一个压缩率。该参数会配合<code>mixed_precisions</code>，生成4/8bit或8/16bit的模型。仅在Q13、Q23的模式下生效。</p> </li> </ul> <p>如果自动推荐的压缩率满足精度要求，可通过以0.05步长下调压缩率，满足精度要求的同时提升模型性能。</p> <p>如果自动推荐的压缩率不满足精度要求，可通过以0.05步长上调压缩率，提升模型量化精度。</p> <p>PRESET_MIN_MAX_BIT：可以预设某个Tensor的min/max/bit信息，详见3.4.3节<a href=#3431>量化策略和方法</a>，Tensor名字以float.sim模型中为准。需要将<code>use_preset_min_max_bit</code>设为1。</p> </article> </div> </div> </main> <footer class=md-footer> <nav class="md-footer__inner md-grid" aria-label=Footer> <a href=../IPU200M/Convert.html class="md-footer__link md-footer__link--prev" rel=prev> <div class="md-footer__button md-icon"> <svg xmlns=http://www.w3.org/2000/svg viewbox="0 0 24 24"><path d="M20 11v2H8l5.5 5.5-1.42 1.42L4.16 12l7.92-7.92L13.5 5.5 8 11h12z"/></svg> </div> <div class=md-footer__title> <div class=md-ellipsis> <span class=md-footer__direction> Previous </span> 2. Convert Tool </div> </div> </a> <a href=Compile.html class="md-footer__link md-footer__link--next" rel=next> <div class=md-footer__title> <div class=md-ellipsis> <span class=md-footer__direction> Next </span> 4. Compiler </div> </div> <div class="md-footer__button md-icon"> <svg xmlns=http://www.w3.org/2000/svg viewbox="0 0 24 24"><path d="M4 11v2h12l-5.5 5.5 1.42 1.42L19.84 12l-7.92-7.92L10.5 5.5 16 11H4z"/></svg> </div> </a> </nav> <div class="md-footer-meta md-typeset"> <div class="md-footer-meta__inner md-grid"> <div class=md-footer-copyright> <div class=md-footer-copyright__highlight> Copyright&copy; 2021 SigmaStar Technology. All rights reserved. Security Level: Confidential A. </div> Made with <a href=https://squidfunk.github.io/mkdocs-material/ target=_blank rel=noopener> Material for MkDocs </a> </div> </div> </div> </footer> </div> <div class=md-dialog data-md-component=dialog> <div class="md-dialog__inner md-typeset"></div> </div> <script id=__config type=application/json>{"base": "../../..", "features": [], "translations": {"clipboard.copy": "Copy to clipboard", "clipboard.copied": "Copied to clipboard", "search.config.lang": "en", "search.config.pipeline": "trimmer, stopWordFilter", "search.config.separator": "[\\s\\-]+", "search.placeholder": "Search", "search.result.placeholder": "Type to start searching", "search.result.none": "No matching documents", "search.result.one": "1 matching document", "search.result.other": "# matching documents", "search.result.more.one": "1 more on this page", "search.result.more.other": "# more on this page", "search.result.term.missing": "Missing"}, "search": "../../../assets/javascripts/workers/search.fb4a9340.min.js", "version": null}</script> <script src=../../../assets/javascripts/bundle.a1c7c35e.min.js></script> <script src=../../../search/search_index.js></script> <script src=../../../javascripts/extra.js></script> <script src="https://cdnjs.cloudflare.com/ajax/libs/mathjax/2.7.0/MathJax.js?config=TeX-MML-AM_CHTML"></script> <script src=https://cdnjs.cloudflare.com/ajax/libs/raphael/2.2.7/raphael.min.js></script> <script src=https://cdnjs.cloudflare.com/ajax/libs/underscore.js/1.8.3/underscore-min.js></script> <script src=https://cdnjs.cloudflare.com/ajax/libs/js-sequence-diagrams/1.0.6/sequence-diagram-min.js></script> <script src=https://cdnjs.cloudflare.com/ajax/libs/flowchart/1.6.5/flowchart.min.js></script> <script src=https://unpkg.com/freezeframe/dist/freezeframe.min.js></script> <script src=https://unpkg.com/mermaid@7.1.0/dist/mermaid.min.js></script> <script src=../../../javascripts/umlconvert.js></script> </body> </html>