<!doctype html><html lang=en class=no-js> <head><meta charset=utf-8><meta name=viewport content="width=device-width,initial-scale=1"><meta name=description content="Personal Documentation System Template"><meta name=author content="<PERSON> Cho<PERSON>"><link rel=icon href=../../../images/favicon.ico><meta name=generator content="mkdocs-1.1.2, mkdocs-material-7.0.6"><title>8. 如何添加新的Layer - IPU SDK</title><link rel=stylesheet href=../../../assets/stylesheets/main.2c0c5eaf.min.css><link rel=stylesheet href=../../../assets/stylesheets/palette.7fa14f5b.min.css><meta name=theme-color content=#009485><link rel=preconnect href=https://fonts.gstatic.com crossorigin><link rel=stylesheet href="https://fonts.googleapis.com/css?family=Roboto:300,400,400i,700%7CRoboto+Mono&display=fallback"><style>:root{--md-text-font-family:"Roboto";--md-code-font-family:"Roboto Mono"}</style><link rel=stylesheet href=../../../stylesheets/extra.css></head> <body dir=ltr data-md-color-scheme data-md-color-primary=teal data-md-color-accent=teal> <input class=md-toggle data-md-toggle=drawer type=checkbox id=__drawer autocomplete=off> <input class=md-toggle data-md-toggle=search type=checkbox id=__search autocomplete=off> <label class=md-overlay for=__drawer></label> <div data-md-component=skip> <a href=#81 class=md-skip> Skip to content </a> </div> <div data-md-component=announce> </div> <header class=md-header data-md-component=header> <nav class="md-header__inner md-grid" aria-label=Header> <a href=../../.. title="IPU SDK" class="md-header__button md-logo" aria-label="IPU SDK" data-md-component=logo> <svg xmlns=http://www.w3.org/2000/svg viewbox="0 0 24 24"><path d="M10 20v-6h4v6h5v-8h3L12 3 2 12h3v8h5z"/></svg> </a> <label class="md-header__button md-icon" for=__drawer> <svg xmlns=http://www.w3.org/2000/svg viewbox="0 0 24 24"><path d="M3 6h18v2H3V6m0 5h18v2H3v-2m0 5h18v2H3v-2z"/></svg> </label> <div class=md-header__title data-md-component=header-title> <div class=md-header__ellipsis> <div class=md-header__topic> <span class=md-ellipsis> IPU SDK </span> </div> <div class=md-header__topic data-md-component=header-topic> <span class=md-ellipsis> 8. 如何添加新的Layer </span> </div> </div> </div> <div class=md-header__options> </div> <label class="md-header__button md-icon" for=__search> <svg xmlns=http://www.w3.org/2000/svg viewbox="0 0 24 24"><path d="M9.5 3A6.5 6.5 0 0 1 16 9.5c0 1.61-.59 3.09-1.56 4.23l.27.27h.79l5 5-1.5 1.5-5-5v-.79l-.27-.27A6.516 6.516 0 0 1 9.5 16 6.5 6.5 0 0 1 3 9.5 6.5 6.5 0 0 1 9.5 3m0 2C7 5 5 7 5 9.5S7 14 9.5 14 14 12 14 9.5 12 5 9.5 5z"/></svg> </label> <div class=md-search data-md-component=search role=dialog> <label class=md-search__overlay for=__search></label> <div class=md-search__inner role=search> <form class=md-search__form name=search> <input type=text class=md-search__input name=query aria-label=Search placeholder=Search autocapitalize=off autocorrect=off autocomplete=off spellcheck=false data-md-component=search-query data-md-state=active required> <label class="md-search__icon md-icon" for=__search> <svg xmlns=http://www.w3.org/2000/svg viewbox="0 0 24 24"><path d="M9.5 3A6.5 6.5 0 0 1 16 9.5c0 1.61-.59 3.09-1.56 4.23l.27.27h.79l5 5-1.5 1.5-5-5v-.79l-.27-.27A6.516 6.516 0 0 1 9.5 16 6.5 6.5 0 0 1 3 9.5 6.5 6.5 0 0 1 9.5 3m0 2C7 5 5 7 5 9.5S7 14 9.5 14 14 12 14 9.5 12 5 9.5 5z"/></svg> <svg xmlns=http://www.w3.org/2000/svg viewbox="0 0 24 24"><path d="M20 11v2H8l5.5 5.5-1.42 1.42L4.16 12l7.92-7.92L13.5 5.5 8 11h12z"/></svg> </label> <button type=reset class="md-search__icon md-icon" aria-label=Clear tabindex=-1> <svg xmlns=http://www.w3.org/2000/svg viewbox="0 0 24 24"><path d="M19 6.41L17.59 5 12 10.59 6.41 5 5 6.41 10.59 12 5 17.59 6.41 19 12 13.41 17.59 19 19 17.59 13.41 12 19 6.41z"/></svg> </button> </form> <div class=md-search__output> <div class=md-search__scrollwrap data-md-scrollfix> <div class=md-search-result data-md-component=search-result> <div class=md-search-result__meta> Initializing search </div> <ol class=md-search-result__list></ol> </div> </div> </div> </div> </div> </nav> </header> <div class=md-container data-md-component=container> <main class=md-main data-md-component=main> <div class="md-main__inner md-grid"> <div class="md-sidebar md-sidebar--primary" data-md-component=sidebar data-md-type=navigation> <div class=md-sidebar__scrollwrap> <div class=md-sidebar__inner> <nav class="md-nav md-nav--primary" aria-label=Navigation data-md-level=0> <label class=md-nav__title for=__drawer> <a href=../../.. title="IPU SDK" class="md-nav__button md-logo" aria-label="IPU SDK" data-md-component=logo> <svg xmlns=http://www.w3.org/2000/svg viewbox="0 0 24 24"><path d="M10 20v-6h4v6h5v-8h3L12 3 2 12h3v8h5z"/></svg> </a> IPU SDK </label> <ul class=md-nav__list data-md-scrollfix> <li class=md-nav__item> <a href=../../../index.html class=md-nav__link> 主页 </a> </li> <li class="md-nav__item md-nav__item--nested"> <input class="md-nav__toggle md-toggle" data-md-toggle=__nav_2 type=checkbox id=__nav_2> <label class=md-nav__link for=__nav_2> SDK介绍 <span class="md-nav__icon md-icon"></span> </label> <nav class=md-nav aria-label=SDK介绍 data-md-level=1> <label class=md-nav__title for=__nav_2> <span class="md-nav__icon md-icon"></span> SDK介绍 </label> <ul class=md-nav__list data-md-scrollfix> <li class=md-nav__item> <a href=../../Introduction/Introduction.html class=md-nav__link> SDK框架介绍 </a> </li> <li class=md-nav__item> <a href=../../Introduction/Docker.html class=md-nav__link> Docker环境 </a> </li> </ul> </nav> </li> <li class="md-nav__item md-nav__item--active md-nav__item--nested"> <input class="md-nav__toggle md-toggle" data-md-toggle=__nav_3 type=checkbox id=__nav_3 checked> <label class=md-nav__link for=__nav_3> 用户手册 <span class="md-nav__icon md-icon"></span> </label> <nav class=md-nav aria-label=用户手册 data-md-level=1> <label class=md-nav__title for=__nav_3> <span class="md-nav__icon md-icon"></span> 用户手册 </label> <ul class=md-nav__list data-md-scrollfix> <li class=md-nav__item> <a href=Environment_Construction.html class=md-nav__link> 1. 快速开始 </a> </li> <li class=md-nav__item> <a href=../IPU200M/Convert.html class=md-nav__link> 2. Convert Tool </a> </li> <li class=md-nav__item> <a href=Calibrate.html class=md-nav__link> 3. Calibrator </a> </li> <li class=md-nav__item> <a href=Compile.html class=md-nav__link> 4. Compiler </a> </li> <li class=md-nav__item> <a href=../IPU200M/Simulate.html class=md-nav__link> 5. Simulator </a> </li> <li class=md-nav__item> <a href=DumpDebug_Tool.html class=md-nav__link> 6. DumpDebug Tool </a> </li> <li class=md-nav__item> <a href=SigmaStar_Post_Processing_Module.html class=md-nav__link> 7. SigmaStar后处理模块 </a> </li> <li class="md-nav__item md-nav__item--active"> <input class="md-nav__toggle md-toggle" data-md-toggle=toc type=checkbox id=__toc> <label class="md-nav__link md-nav__link--active" for=__toc> 8. 如何添加新的Layer <span class="md-nav__icon md-icon"></span> </label> <a href=Adding_A_New_Layer.html class="md-nav__link md-nav__link--active"> 8. 如何添加新的Layer </a> <nav class="md-nav md-nav--secondary" aria-label="Table of contents"> <label class=md-nav__title for=__toc> <span class="md-nav__icon md-icon"></span> Table of contents </label> <ul class=md-nav__list data-md-component=toc data-md-scrollfix> <li class=md-nav__item> <a href=#81 class=md-nav__link> 8.1. 概述 </a> </li> <li class=md-nav__item> <a href=#82-caffe-proto class=md-nav__link> 8.2. Caffe proto 文件修改 </a> <nav class=md-nav aria-label="8.2. Caffe proto 文件修改"> <ul class=md-nav__list> <li class=md-nav__item> <a href=#821-caffe-proto class=md-nav__link> 8.2.1 修改caffe proto </a> </li> <li class=md-nav__item> <a href=#822-proto class=md-nav__link> 8.2.2 编译proto 文件 </a> </li> </ul> </nav> </li> <li class=md-nav__item> <a href=#83-caffe_converterpy class=md-nav__link> 8.3 修改caffe_converter.py </a> </li> <li class=md-nav__item> <a href=#84-shape_inferencepy class=md-nav__link> 8.4. 修改shape_inference.py </a> </li> <li class=md-nav__item> <a href=#85-sgsmodel_transformpy class=md-nav__link> 8.5. 修改SGSModel_transform.py </a> </li> <li class=md-nav__item> <a href=#86-layer class=md-nav__link> 8.6. Layer拆解注意事项与技巧 </a> <nav class=md-nav aria-label="8.6. Layer拆解注意事项与技巧"> <ul class=md-nav__list> <li class=md-nav__item> <a href=#861-layer class=md-nav__link> 8.6.1. Layer拆解注意事项与技巧 数据维度的问题 </a> </li> <li class=md-nav__item> <a href=#862 class=md-nav__link> 8.6.2. 建议拆解流程 </a> </li> <li class=md-nav__item> <a href=#863 class=md-nav__link> 8.6.3. 数据对比及验证 </a> </li> </ul> </nav> </li> </ul> </nav> </li> <li class=md-nav__item> <a href=../IPU200M/Special_Model_Conversion.html class=md-nav__link> 9. 特殊模型转换要点 </a> </li> <li class=md-nav__item> <a href=../IPU200M/DLA_SDK_Support.html class=md-nav__link> 10. DLA SDK 支持 </a> </li> <li class=md-nav__item> <a href=Running_Offline_Network_Model_On_Development_Board.html class=md-nav__link> 11. 在开发板上运行离线网络模型 </a> </li> <li class=md-nav__item> <a href=../IPU200M/Preprocess.py_and_Input_Config.ini_Support.html class=md-nav__link> 附录. 前处理和配置文件注意要点 </a> </li> </ul> </nav> </li> <li class="md-nav__item md-nav__item--nested"> <input class="md-nav__toggle md-toggle" data-md-toggle=__nav_4 type=checkbox id=__nav_4> <label class=md-nav__link for=__nav_4> FAQ <span class="md-nav__icon md-icon"></span> </label> <nav class=md-nav aria-label=FAQ data-md-level=1> <label class=md-nav__title for=__nav_4> <span class="md-nav__icon md-icon"></span> FAQ </label> <ul class=md-nav__list data-md-scrollfix> <li class=md-nav__item> <a href=../../FAQ/Common/Env_Setting.html class=md-nav__link> 环境设置问题 </a> </li> <li class=md-nav__item> <a href=../../FAQ/IPU200M/Model_Conversion.html class=md-nav__link> 模型转换问题 </a> </li> <li class=md-nav__item> <a href=../../FAQ/IPU200M/Development_Board.html class=md-nav__link> 板端使用问题 </a> </li> <li class=md-nav__item> <a href=../../FAQ/IPU200M/Other_Anomalies.html class=md-nav__link> 其他异常问题 </a> </li> </ul> </nav> </li> </ul> </nav> </div> </div> </div> <div class="md-sidebar md-sidebar--secondary" data-md-component=sidebar data-md-type=toc> <div class=md-sidebar__scrollwrap> <div class=md-sidebar__inner> <nav class="md-nav md-nav--secondary" aria-label="Table of contents"> <label class=md-nav__title for=__toc> <span class="md-nav__icon md-icon"></span> Table of contents </label> <ul class=md-nav__list data-md-component=toc data-md-scrollfix> <li class=md-nav__item> <a href=#81 class=md-nav__link> 8.1. 概述 </a> </li> <li class=md-nav__item> <a href=#82-caffe-proto class=md-nav__link> 8.2. Caffe proto 文件修改 </a> <nav class=md-nav aria-label="8.2. Caffe proto 文件修改"> <ul class=md-nav__list> <li class=md-nav__item> <a href=#821-caffe-proto class=md-nav__link> 8.2.1 修改caffe proto </a> </li> <li class=md-nav__item> <a href=#822-proto class=md-nav__link> 8.2.2 编译proto 文件 </a> </li> </ul> </nav> </li> <li class=md-nav__item> <a href=#83-caffe_converterpy class=md-nav__link> 8.3 修改caffe_converter.py </a> </li> <li class=md-nav__item> <a href=#84-shape_inferencepy class=md-nav__link> 8.4. 修改shape_inference.py </a> </li> <li class=md-nav__item> <a href=#85-sgsmodel_transformpy class=md-nav__link> 8.5. 修改SGSModel_transform.py </a> </li> <li class=md-nav__item> <a href=#86-layer class=md-nav__link> 8.6. Layer拆解注意事项与技巧 </a> <nav class=md-nav aria-label="8.6. Layer拆解注意事项与技巧"> <ul class=md-nav__list> <li class=md-nav__item> <a href=#861-layer class=md-nav__link> 8.6.1. Layer拆解注意事项与技巧 数据维度的问题 </a> </li> <li class=md-nav__item> <a href=#862 class=md-nav__link> 8.6.2. 建议拆解流程 </a> </li> <li class=md-nav__item> <a href=#863 class=md-nav__link> 8.6.3. 数据对比及验证 </a> </li> </ul> </nav> </li> </ul> </nav> </div> </div> </div> <div class=md-content data-md-component=content> <article class="md-content__inner md-typeset"> <h1>8. 如何添加新的Layer</h1> <h2 id=81>8.1. 概述<a class=headerlink href=#81 title="Permanent link">&para;</a></h2> <p>本节仅适用于caffe 模型。对于sigmastar 还未实现的layer，用sigmastar 提供底层算子来组合实现。底层算子与 tensorflow 的算子类似，支持的算子参考10.2 节[TensofFlow 支持算子] 支持算子转换流程如下图</p> <p><img alt=box_process src=mymedia/caffe001_en.png></p> <p>CaffeConvert Tool (代码位置：<code>SGS_IPU_SDK/Scripts/ConvertTool/ConvertTool.py</code>) caffe 模型转换工具借鉴了小米开源框架mace 的设计，要添加新的layer，请先到<a href=https://github.com/XiaoMi/mace>Xiaomi MACE</a>下载原始代码，按照mace 官方文档配置 好编译环境，或者直接使用我们提供的docker 环境。 下面以<code>reorg layer</code>为例子，讲解如何添加新的layer，主要修改以下几个文件</p> <p><img alt=box_process src=mymedia/caffe002_en.png></p> <hr> <h2 id=82-caffe-proto>8.2. Caffe proto 文件修改<a class=headerlink href=#82-caffe-proto title="Permanent link">&para;</a></h2> <h3 id=821-caffe-proto>8.2.1 修改caffe proto<a class=headerlink href=#821-caffe-proto title="Permanent link">&para;</a></h3> <p>Mace 原始代码下载后，用<code>SGS_IPU_SDK/Scripts/CaffeConvertTool/third_party/caffe/caffe.proto</code> 替换掉mace 工程 路径下mace/third_party/caffe 下caffe.proto 文件。因为sigmastar 在mace 基础上做了二次开发，所以sigmstar 支持 的caffe layer 已经远远多于原生的mace 支持数量。要添加新的layer，请以sigmastar 的caffe proto 文件作为基础 添加。</p> <div class=highlight><pre><span></span><code>optional ReorgParameter <span class=nv>reorg_param</span> <span class=o>=</span> <span class=m>157</span><span class=p>;</span>
<span class=o>}</span>
message ReorgParameter <span class=o>{</span>
optional uint32 <span class=nv>stride</span> <span class=o>=</span> <span class=m>1</span><span class=p>;</span>
optional bool <span class=nv>reverse</span> <span class=o>=</span> <span class=m>2</span> <span class=o>[</span><span class=nv>default</span> <span class=o>=</span> false<span class=o>]</span><span class=p>;</span>
<span class=o>}</span>
</code></pre></div> <hr> <h3 id=822-proto>8.2.2 编译proto 文件<a class=headerlink href=#822-proto title="Permanent link">&para;</a></h3> <p>在mace 开发环境中</p> <div class=highlight><pre><span></span><code><span class=nb>cd</span> pathToMace/
bazel build third_party/caffe/caffe_py
</code></pre></div> <p>在bazel-genfiles 下生成caffe_pb2.py 将上面修改后的caffe.proto 和生成的caffe_pb2.py 替换<code>SGS_IPU_SDK/Scripts/CaffeConvertTool/third_party/caffe</code>同 名文件，然后按照如下顺序修改代码</p> <hr> <h2 id=83-caffe_converterpy>8.3 修改caffe_converter.py<a class=headerlink href=#83-caffe_converterpy title="Permanent link">&para;</a></h2> <p>文件路径<code>SGS_IPU_SDK/Scripts/CaffeConvertTool/mace/python/tools/converter_tool/caffe_converter.py</code> 该文件的作用是把caffe模型转换为mace模型，并保存该layer的参数 <div class=highlight><pre><span></span><code><span class=n>Reorg</span><span class=s1>&#39;:self.convert_Reorg,</span>
<span class=o>...</span> 
<span class=o>...</span> 
<span class=k>def</span> <span class=nf>convert_Reorg</span><span class=p>(</span><span class=bp>self</span><span class=p>,</span> <span class=n>caffe_op</span><span class=p>):</span> 
    <span class=n>op</span> <span class=o>=</span> <span class=bp>self</span><span class=o>.</span><span class=n>convert_general_op</span><span class=p>(</span><span class=n>caffe_op</span><span class=p>)</span> 
    <span class=n>op</span><span class=o>.</span><span class=n>type</span> <span class=o>=</span> <span class=s2>&quot;Reorg&quot;</span> 
    <span class=n>param</span> <span class=o>=</span> <span class=n>caffe_op</span><span class=o>.</span><span class=n>layer</span><span class=o>.</span><span class=n>reorg_param</span> 
    <span class=n>stride_arg</span> <span class=o>=</span> <span class=n>op</span><span class=o>.</span><span class=n>arg</span><span class=o>.</span><span class=n>add</span><span class=p>()</span> 
    <span class=n>stride_arg</span><span class=o>.</span><span class=n>name</span> <span class=o>=</span> <span class=s2>&quot;stride&quot;</span> 
    <span class=n>stride_arg</span><span class=o>.</span><span class=n>i</span> <span class=o>=</span> <span class=mi>1</span> <span class=c1>#default is true </span>
    <span class=k>if</span> <span class=n>param</span><span class=o>.</span><span class=n>HasField</span><span class=p>(</span><span class=s1>&#39;stride&#39;</span><span class=p>):</span> 
        <span class=n>stride_arg</span><span class=o>.</span><span class=n>i</span> <span class=o>=</span> <span class=nb>int</span><span class=p>(</span><span class=n>param</span><span class=o>.</span><span class=n>stride</span><span class=p>)</span>
</code></pre></div></p> <hr> <h2 id=84-shape_inferencepy>8.4. 修改shape_inference.py<a class=headerlink href=#84-shape_inferencepy title="Permanent link">&para;</a></h2> <p>文件路径<code>SGS_IPU_SDK/Scripts/CaffeConvertTool/mace/python/tools/converter_tool/shape_inference.py</code> 该文件的作用是计算layer的output shape</p> <div class=highlight><pre><span></span><code><span class=n>Reorg</span><span class=s1>&#39;: self.infer_shape_Reorg, </span>
<span class=o>...</span> 
<span class=o>...</span> 
<span class=k>def</span> <span class=nf>infer_shape_Reorg</span><span class=p>(</span><span class=bp>self</span><span class=p>,</span> <span class=n>op</span><span class=p>):</span> 
    <span class=c1>#only support stride is 2 </span>
    <span class=n>output_shape</span> <span class=o>=</span> <span class=bp>self</span><span class=o>.</span><span class=n>_output_shape_cache</span><span class=p>[</span><span class=n>op</span><span class=o>.</span><span class=n>input</span><span class=p>[</span><span class=mi>0</span><span class=p>]]</span> 
    <span class=n>input_shape</span> <span class=o>=</span> <span class=nb>list</span><span class=p>(</span><span class=bp>self</span><span class=o>.</span><span class=n>_output_shape_cache</span><span class=p>[</span><span class=n>op</span><span class=o>.</span><span class=n>input</span><span class=p>[</span><span class=mi>0</span><span class=p>]])</span> 
    <span class=n>input_n</span> <span class=o>=</span> <span class=n>input_shape</span><span class=p>[</span><span class=mi>0</span><span class=p>]</span> 
    <span class=n>input_c</span> <span class=o>=</span> <span class=n>input_shape</span><span class=p>[</span><span class=mi>1</span><span class=p>]</span> 
    <span class=n>input_h</span> <span class=o>=</span> <span class=n>input_shape</span><span class=p>[</span><span class=mi>2</span><span class=p>]</span> 
    <span class=n>input_w</span> <span class=o>=</span> <span class=n>input_shape</span><span class=p>[</span><span class=mi>3</span><span class=p>]</span> 
    <span class=n>output_shape</span> <span class=o>=</span> <span class=p>[</span><span class=n>input_n</span><span class=p>,</span><span class=nb>int</span><span class=p>(</span><span class=n>input_c</span><span class=o>*</span><span class=mi>4</span><span class=p>),</span><span class=nb>int</span><span class=p>(</span><span class=n>input_h</span><span class=o>/</span><span class=mi>2</span><span class=p>),</span><span class=nb>int</span><span class=p>(</span><span class=n>input_w</span><span class=o>/</span><span class=mi>2</span><span class=p>)]</span> 
    <span class=bp>self</span><span class=o>.</span><span class=n>add_output_shape</span><span class=p>(</span><span class=n>op</span><span class=p>,</span> <span class=p>[</span><span class=n>output_shape</span><span class=p>])</span>
</code></pre></div> <hr> <h2 id=85-sgsmodel_transformpy>8.5. 修改SGSModel_transform.py<a class=headerlink href=#85-sgsmodel_transformpy title="Permanent link">&para;</a></h2> <p>如下图所示，我们发现reorg在NCHW的排列顺序下，它实际上是对数据做了一个重排。</p> <p><img alt=box_process src=mymedia/********************************.png></p> <p>因此，我们最后拆解出来的算子组合是</p> <p><img alt=box_process src=mymedia/********************************.png></p> <p>按照算子组合顺序，编写代码 文件路径<code>SGS_IPU_SDK/Scripts/CaffeConvertTool/mace/python/tools/SGSModel_transform.py</code> 该文件作用是增加该layer 的拆解函数。即用Sigmastar 提供的基本算子实现该layer。</p> <p><div class=highlight><pre><span></span><code><span class=n>Reorg</span><span class=s1>&#39;:self.split_Reorg,</span>
<span class=o>...</span>
<span class=o>...</span>
<span class=k>def</span> <span class=nf>split_Reorg</span><span class=p>(</span><span class=bp>self</span><span class=p>,</span> <span class=n>op</span><span class=p>):</span>
<span class=c1>#only support strid is 2</span>
<span class=p>[</span><span class=n>n</span><span class=p>,</span><span class=n>c</span><span class=p>,</span><span class=n>h</span><span class=p>,</span><span class=n>w</span><span class=p>]</span> <span class=o>=</span> <span class=n>op</span><span class=o>.</span><span class=n>output_shape</span><span class=p>[</span><span class=mi>0</span><span class=p>]</span><span class=o>.</span><span class=n>dims</span><span class=p>[:]</span>
<span class=n>c</span> <span class=o>=</span> <span class=nb>int</span><span class=p>(</span><span class=n>c</span><span class=o>/</span><span class=mi>4</span><span class=p>)</span>
<span class=n>op_name</span> <span class=o>=</span> <span class=n>op</span><span class=o>.</span><span class=n>name</span>
<span class=n>xi</span> <span class=o>=</span> <span class=n>op</span><span class=o>.</span><span class=n>input</span><span class=p>[</span><span class=mi>0</span><span class=p>]</span>
<span class=o>...</span>
<span class=o>...</span>
</code></pre></div> 至此，新的layer 支持添加完成</p> <hr> <h2 id=86-layer>8.6. Layer拆解注意事项与技巧<a class=headerlink href=#86-layer title="Permanent link">&para;</a></h2> <h3 id=861-layer>8.6.1. Layer拆解注意事项与技巧 数据维度的问题<a class=headerlink href=#861-layer title="Permanent link">&para;</a></h3> <p>在caffe网络中，使用NCHW数据排列。而sigmastar使用NHWC的顺序进行数据计算（与tensorflow类似）。所以在模型转换成sim模型的时候，会对所有4维度的shape和数据做一次转换。即NCHW转到NHWC 参考代码：<code>SGS_IPU_SDK/Scripts/CaffeConvertTool/mace/python/tools/SGSModel_converter.py</code> 对于tensor data的转换</p> <p><div class=highlight><pre><span></span><code><span class=k>def</span> <span class=nf>_creatBuffer</span><span class=p>(</span><span class=bp>self</span><span class=p>):</span>
<span class=o>...</span> 
<span class=o>...</span> 
<span class=k>if</span> <span class=nb>len</span><span class=p>(</span><span class=n>ori_shape</span><span class=p>)</span> <span class=o>==</span> <span class=mi>4</span><span class=p>:</span> 
<span class=c1>#transpose data to NHWC </span>
    <span class=n>six</span><span class=o>.</span><span class=n>print_</span><span class=p>(</span><span class=s2>&quot;Reshape &quot;</span><span class=p>,</span><span class=n>tensor</span><span class=o>.</span><span class=n>name</span><span class=p>,</span><span class=s2>&quot;to NHWC&quot;</span><span class=p>)</span> 
    <span class=n>data</span> <span class=o>=</span> <span class=n>np</span><span class=o>.</span><span class=n>array</span><span class=p>(</span><span class=n>ori_data</span><span class=p>)</span> 
    <span class=n>data</span> <span class=o>=</span> <span class=n>data</span><span class=o>.</span><span class=n>reshape</span><span class=p>(</span><span class=n>ori_shape</span><span class=p>)</span> 
    <span class=n>data</span> <span class=o>=</span> <span class=n>data</span><span class=o>.</span><span class=n>transpose</span><span class=p>(</span><span class=mi>0</span><span class=p>,</span><span class=mi>2</span><span class=p>,</span><span class=mi>3</span><span class=p>,</span><span class=mi>1</span><span class=p>)</span>
</code></pre></div> 对于shape的转换</p> <p><div class=highlight><pre><span></span><code><span class=k>def</span> <span class=nf>_creatTensor</span><span class=p>(</span><span class=bp>self</span><span class=p>):</span> 
<span class=o>...</span> 
<span class=o>...</span> 
<span class=k>if</span> <span class=nb>len</span><span class=p>(</span><span class=n>shape</span><span class=p>)</span> <span class=o>==</span> <span class=mi>4</span> <span class=ow>and</span> <span class=n>data_format</span> <span class=o>==</span> <span class=n>mace_pb2</span><span class=o>.</span><span class=n>DT_NCHW</span><span class=p>:</span> 
    <span class=n>Transformer</span><span class=o>.</span><span class=n>transpose_shape</span><span class=p>(</span><span class=n>shape</span><span class=p>,</span> <span class=p>[</span><span class=mi>0</span><span class=p>,</span> <span class=mi>2</span><span class=p>,</span> <span class=mi>3</span><span class=p>,</span> <span class=mi>1</span><span class=p>])</span> 
<span class=n>tflite</span><span class=o>.</span><span class=n>Tensor</span><span class=o>.</span><span class=n>TensorStartShapeVector</span><span class=p>(</span><span class=bp>self</span><span class=o>.</span><span class=n>_builder</span><span class=p>,</span><span class=nb>len</span><span class=p>(</span><span class=n>shape</span><span class=p>))</span>
</code></pre></div> 因为shape在创建sim模型的时候会被强行改变，所以拆解的时候要注意</p> <p>（1）有axis参数的，要注意顺序交换</p> <p>比如`concat layer，caffe model里面，它要对C纬度做concat，其axis为1。网络转换后，C为换到了最低维，所 以要修改axis为3，代码如下。相似的还有split、PRelu等</p> <p><div class=highlight><pre><span></span><code><span class=k>def</span> <span class=nf>split_Concat</span><span class=p>(</span><span class=bp>self</span><span class=p>,</span> <span class=n>op</span><span class=p>):</span> 
<span class=o>...</span> 
<span class=o>...</span> 
<span class=k>if</span> <span class=nb>len</span><span class=p>(</span><span class=n>output_shape</span><span class=p>[</span><span class=mi>0</span><span class=p>]</span><span class=o>.</span><span class=n>dims</span><span class=p>)</span> <span class=o>==</span> <span class=mi>4</span><span class=p>:</span><span class=c1>#nchw -&gt; nhwc </span>
    <span class=k>if</span> <span class=n>arg</span><span class=p>[</span><span class=n>i</span><span class=p>]</span><span class=o>.</span><span class=n>i</span> <span class=o>==</span> <span class=mi>1</span><span class=p>:</span> 
        <span class=n>arg</span><span class=p>[</span><span class=n>i</span><span class=p>]</span><span class=o>.</span><span class=n>i</span> <span class=o>=</span> <span class=mi>3</span> 
    <span class=k>else</span> <span class=k>if</span> <span class=n>arg</span><span class=p>[</span><span class=n>i</span><span class=p>]</span><span class=o>.</span><span class=n>i</span> <span class=o>==</span> <span class=mi>2</span><span class=p>:</span> 
        <span class=n>arg</span><span class=p>[</span><span class=n>i</span><span class=p>]</span><span class=o>.</span><span class=n>i</span> <span class=o>=</span> <span class=mi>1</span> 
    <span class=k>else</span> <span class=k>if</span> <span class=n>arg</span><span class=p>[</span><span class=n>i</span><span class=p>]</span><span class=o>.</span><span class=n>i</span> <span class=o>==</span> <span class=mi>3</span><span class=p>:</span> 
        <span class=n>arg</span><span class=p>[</span><span class=n>i</span><span class=p>]</span><span class=o>.</span><span class=n>i</span> <span class=o>=</span> <span class=mi>2</span>
</code></pre></div> （2）指定算子output_shape的时候，特别是4维的输出，要注意顺序变化带来的影响。比如希望tensor的输出是[A,B,C,D]，那么代码中我们写的数值为[A,D,B,C]。因为[A,D,B,C]经过上面代码的转换，就得到了[A,B,C,D]这个顺序</p> <p>（3）在NCHW 数据排列下有很强规律的layer，可以先转到NCHW 下操作。比如上面提到的reorg layer。</p> <p>（4）用sdk 里面提供的Netron 工具打开转换后的sim 模型，方便查看各种属性和shape。</p> <hr> <h3 id=862>8.6.2. 建议拆解流程<a class=headerlink href=#862 title="Permanent link">&para;</a></h3> <p>综上所述，在拆解的过程中，我们建议按照如下流程进行</p> <p><img alt=box_process src=mymedia/caffe003_en.png></p> <hr> <h3 id=863>8.6.3. 数据对比及验证<a class=headerlink href=#863 title="Permanent link">&para;</a></h3> <p>前期可以用python 创建tensorflow 算子的方法，进行验证。 当验证完成，再修改SGS_IPU_SDK 的相关代码，参考<a href=DumpDebug_Tool.html>Dump_Debug TOOL</a>章节，dump 出最后结果与golden 比对。</p> </article> </div> </div> </main> <footer class=md-footer> <nav class="md-footer__inner md-grid" aria-label=Footer> <a href=SigmaStar_Post_Processing_Module.html class="md-footer__link md-footer__link--prev" rel=prev> <div class="md-footer__button md-icon"> <svg xmlns=http://www.w3.org/2000/svg viewbox="0 0 24 24"><path d="M20 11v2H8l5.5 5.5-1.42 1.42L4.16 12l7.92-7.92L13.5 5.5 8 11h12z"/></svg> </div> <div class=md-footer__title> <div class=md-ellipsis> <span class=md-footer__direction> Previous </span> 7. SigmaStar后处理模块 </div> </div> </a> <a href=../IPU200M/Special_Model_Conversion.html class="md-footer__link md-footer__link--next" rel=next> <div class=md-footer__title> <div class=md-ellipsis> <span class=md-footer__direction> Next </span> 9. 特殊模型转换要点 </div> </div> <div class="md-footer__button md-icon"> <svg xmlns=http://www.w3.org/2000/svg viewbox="0 0 24 24"><path d="M4 11v2h12l-5.5 5.5 1.42 1.42L19.84 12l-7.92-7.92L10.5 5.5 16 11H4z"/></svg> </div> </a> </nav> <div class="md-footer-meta md-typeset"> <div class="md-footer-meta__inner md-grid"> <div class=md-footer-copyright> <div class=md-footer-copyright__highlight> Copyright&copy; 2021 SigmaStar Technology. All rights reserved. Security Level: Confidential A. </div> Made with <a href=https://squidfunk.github.io/mkdocs-material/ target=_blank rel=noopener> Material for MkDocs </a> </div> </div> </div> </footer> </div> <div class=md-dialog data-md-component=dialog> <div class="md-dialog__inner md-typeset"></div> </div> <script id=__config type=application/json>{"base": "../../..", "features": [], "translations": {"clipboard.copy": "Copy to clipboard", "clipboard.copied": "Copied to clipboard", "search.config.lang": "en", "search.config.pipeline": "trimmer, stopWordFilter", "search.config.separator": "[\\s\\-]+", "search.placeholder": "Search", "search.result.placeholder": "Type to start searching", "search.result.none": "No matching documents", "search.result.one": "1 matching document", "search.result.other": "# matching documents", "search.result.more.one": "1 more on this page", "search.result.more.other": "# more on this page", "search.result.term.missing": "Missing"}, "search": "../../../assets/javascripts/workers/search.fb4a9340.min.js", "version": null}</script> <script src=../../../assets/javascripts/bundle.a1c7c35e.min.js></script> <script src=../../../search/search_index.js></script> <script src=../../../javascripts/extra.js></script> <script src="https://cdnjs.cloudflare.com/ajax/libs/mathjax/2.7.0/MathJax.js?config=TeX-MML-AM_CHTML"></script> <script src=https://cdnjs.cloudflare.com/ajax/libs/raphael/2.2.7/raphael.min.js></script> <script src=https://cdnjs.cloudflare.com/ajax/libs/underscore.js/1.8.3/underscore-min.js></script> <script src=https://cdnjs.cloudflare.com/ajax/libs/js-sequence-diagrams/1.0.6/sequence-diagram-min.js></script> <script src=https://cdnjs.cloudflare.com/ajax/libs/flowchart/1.6.5/flowchart.min.js></script> <script src=https://unpkg.com/freezeframe/dist/freezeframe.min.js></script> <script src=https://unpkg.com/mermaid@7.1.0/dist/mermaid.min.js></script> <script src=../../../javascripts/umlconvert.js></script> </body> </html>