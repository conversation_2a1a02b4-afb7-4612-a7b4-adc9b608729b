<!doctype html><html lang=en class=no-js> <head><meta charset=utf-8><meta name=viewport content="width=device-width,initial-scale=1"><meta name=description content="Personal Documentation System Template"><meta name=author content="<PERSON> Cho<PERSON>"><link rel=icon href=../../../images/favicon.ico><meta name=generator content="mkdocs-1.1.2, mkdocs-material-7.0.6"><title>Model Conversion - IPU SDK</title><link rel=stylesheet href=../../../assets/stylesheets/main.2c0c5eaf.min.css><link rel=stylesheet href=../../../assets/stylesheets/palette.7fa14f5b.min.css><meta name=theme-color content=#009485><link rel=preconnect href=https://fonts.gstatic.com crossorigin><link rel=stylesheet href="https://fonts.googleapis.com/css?family=Roboto:300,400,400i,700%7CRoboto+Mono&display=fallback"><style>:root{--md-text-font-family:"Roboto";--md-code-font-family:"Roboto Mono"}</style><link rel=stylesheet href=../../../stylesheets/extra.css></head> <body dir=ltr data-md-color-scheme data-md-color-primary=teal data-md-color-accent=teal> <input class=md-toggle data-md-toggle=drawer type=checkbox id=__drawer autocomplete=off> <input class=md-toggle data-md-toggle=search type=checkbox id=__search autocomplete=off> <label class=md-overlay for=__drawer></label> <div data-md-component=skip> </div> <div data-md-component=announce> </div> <header class=md-header data-md-component=header> <nav class="md-header__inner md-grid" aria-label=Header> <a href=../../.. title="IPU SDK" class="md-header__button md-logo" aria-label="IPU SDK" data-md-component=logo> <svg xmlns=http://www.w3.org/2000/svg viewbox="0 0 24 24"><path d="M10 20v-6h4v6h5v-8h3L12 3 2 12h3v8h5z"/></svg> </a> <label class="md-header__button md-icon" for=__drawer> <svg xmlns=http://www.w3.org/2000/svg viewbox="0 0 24 24"><path d="M3 6h18v2H3V6m0 5h18v2H3v-2m0 5h18v2H3v-2z"/></svg> </label> <div class=md-header__title data-md-component=header-title> <div class=md-header__ellipsis> <div class=md-header__topic> <span class=md-ellipsis> IPU SDK </span> </div> <div class=md-header__topic data-md-component=header-topic> <span class=md-ellipsis> Model Conversion </span> </div> </div> </div> <div class=md-header__options> </div> <label class="md-header__button md-icon" for=__search> <svg xmlns=http://www.w3.org/2000/svg viewbox="0 0 24 24"><path d="M9.5 3A6.5 6.5 0 0 1 16 9.5c0 1.61-.59 3.09-1.56 4.23l.27.27h.79l5 5-1.5 1.5-5-5v-.79l-.27-.27A6.516 6.516 0 0 1 9.5 16 6.5 6.5 0 0 1 3 9.5 6.5 6.5 0 0 1 9.5 3m0 2C7 5 5 7 5 9.5S7 14 9.5 14 14 12 14 9.5 12 5 9.5 5z"/></svg> </label> <div class=md-search data-md-component=search role=dialog> <label class=md-search__overlay for=__search></label> <div class=md-search__inner role=search> <form class=md-search__form name=search> <input type=text class=md-search__input name=query aria-label=Search placeholder=Search autocapitalize=off autocorrect=off autocomplete=off spellcheck=false data-md-component=search-query data-md-state=active required> <label class="md-search__icon md-icon" for=__search> <svg xmlns=http://www.w3.org/2000/svg viewbox="0 0 24 24"><path d="M9.5 3A6.5 6.5 0 0 1 16 9.5c0 1.61-.59 3.09-1.56 4.23l.27.27h.79l5 5-1.5 1.5-5-5v-.79l-.27-.27A6.516 6.516 0 0 1 9.5 16 6.5 6.5 0 0 1 3 9.5 6.5 6.5 0 0 1 9.5 3m0 2C7 5 5 7 5 9.5S7 14 9.5 14 14 12 14 9.5 12 5 9.5 5z"/></svg> <svg xmlns=http://www.w3.org/2000/svg viewbox="0 0 24 24"><path d="M20 11v2H8l5.5 5.5-1.42 1.42L4.16 12l7.92-7.92L13.5 5.5 8 11h12z"/></svg> </label> <button type=reset class="md-search__icon md-icon" aria-label=Clear tabindex=-1> <svg xmlns=http://www.w3.org/2000/svg viewbox="0 0 24 24"><path d="M19 6.41L17.59 5 12 10.59 6.41 5 5 6.41 10.59 12 5 17.59 6.41 19 12 13.41 17.59 19 19 17.59 13.41 12 19 6.41z"/></svg> </button> </form> <div class=md-search__output> <div class=md-search__scrollwrap data-md-scrollfix> <div class=md-search-result data-md-component=search-result> <div class=md-search-result__meta> Initializing search </div> <ol class=md-search-result__list></ol> </div> </div> </div> </div> </div> </nav> </header> <div class=md-container data-md-component=container> <main class=md-main data-md-component=main> <div class="md-main__inner md-grid"> <div class="md-sidebar md-sidebar--primary" data-md-component=sidebar data-md-type=navigation> <div class=md-sidebar__scrollwrap> <div class=md-sidebar__inner> <nav class="md-nav md-nav--primary" aria-label=Navigation data-md-level=0> <label class=md-nav__title for=__drawer> <a href=../../.. title="IPU SDK" class="md-nav__button md-logo" aria-label="IPU SDK" data-md-component=logo> <svg xmlns=http://www.w3.org/2000/svg viewbox="0 0 24 24"><path d="M10 20v-6h4v6h5v-8h3L12 3 2 12h3v8h5z"/></svg> </a> IPU SDK </label> <ul class=md-nav__list data-md-scrollfix> <li class=md-nav__item> <a href=../../../index.html class=md-nav__link> 主页 </a> </li> <li class="md-nav__item md-nav__item--nested"> <input class="md-nav__toggle md-toggle" data-md-toggle=__nav_2 type=checkbox id=__nav_2> <label class=md-nav__link for=__nav_2> SDK介绍 <span class="md-nav__icon md-icon"></span> </label> <nav class=md-nav aria-label=SDK介绍 data-md-level=1> <label class=md-nav__title for=__nav_2> <span class="md-nav__icon md-icon"></span> SDK介绍 </label> <ul class=md-nav__list data-md-scrollfix> <li class=md-nav__item> <a href=../../Introduction/Introduction.html class=md-nav__link> SDK框架介绍 </a> </li> <li class=md-nav__item> <a href=../../Introduction/Docker.html class=md-nav__link> Docker环境 </a> </li> </ul> </nav> </li> <li class="md-nav__item md-nav__item--nested"> <input class="md-nav__toggle md-toggle" data-md-toggle=__nav_3 type=checkbox id=__nav_3> <label class=md-nav__link for=__nav_3> 用户手册 <span class="md-nav__icon md-icon"></span> </label> <nav class=md-nav aria-label=用户手册 data-md-level=1> <label class=md-nav__title for=__nav_3> <span class="md-nav__icon md-icon"></span> 用户手册 </label> <ul class=md-nav__list data-md-scrollfix> <li class=md-nav__item> <a href=../../User_Guide/Common/Environment_Construction.html class=md-nav__link> 1. 快速开始 </a> </li> <li class=md-nav__item> <a href=../../User_Guide/IPU200M/Convert.html class=md-nav__link> 2. Convert Tool </a> </li> <li class=md-nav__item> <a href=../../User_Guide/Common/Calibrate.html class=md-nav__link> 3. Calibrator </a> </li> <li class=md-nav__item> <a href=../../User_Guide/Common/Compile.html class=md-nav__link> 4. Compiler </a> </li> <li class=md-nav__item> <a href=../../User_Guide/IPU200M/Simulate.html class=md-nav__link> 5. Simulator </a> </li> <li class=md-nav__item> <a href=../../User_Guide/Common/DumpDebug_Tool.html class=md-nav__link> 6. DumpDebug Tool </a> </li> <li class=md-nav__item> <a href=../../User_Guide/Common/SigmaStar_Post_Processing_Module.html class=md-nav__link> 7. SigmaStar后处理模块 </a> </li> <li class=md-nav__item> <a href=../../User_Guide/Common/Adding_A_New_Layer.html class=md-nav__link> 8. 如何添加新的Layer </a> </li> <li class=md-nav__item> <a href=../../User_Guide/IPU200M/Special_Model_Conversion.html class=md-nav__link> 9. 特殊模型转换要点 </a> </li> <li class=md-nav__item> <a href=../../User_Guide/IPU200M/DLA_SDK_Support.html class=md-nav__link> 10. DLA SDK 支持 </a> </li> <li class=md-nav__item> <a href=../../User_Guide/Common/Running_Offline_Network_Model_On_Development_Board.html class=md-nav__link> 11. 在开发板上运行离线网络模型 </a> </li> <li class=md-nav__item> <a href=../../User_Guide/IPU200M/Preprocess.py_and_Input_Config.ini_Support.html class=md-nav__link> 附录. 前处理和配置文件注意要点 </a> </li> </ul> </nav> </li> <li class="md-nav__item md-nav__item--nested"> <input class="md-nav__toggle md-toggle" data-md-toggle=__nav_4 type=checkbox id=__nav_4> <label class=md-nav__link for=__nav_4> FAQ <span class="md-nav__icon md-icon"></span> </label> <nav class=md-nav aria-label=FAQ data-md-level=1> <label class=md-nav__title for=__nav_4> <span class="md-nav__icon md-icon"></span> FAQ </label> <ul class=md-nav__list data-md-scrollfix> <li class=md-nav__item> <a href=../Common/Env_Setting.html class=md-nav__link> 环境设置问题 </a> </li> <li class=md-nav__item> <a href=../IPU200M/Model_Conversion.html class=md-nav__link> 模型转换问题 </a> </li> <li class=md-nav__item> <a href=../IPU200M/Development_Board.html class=md-nav__link> 板端使用问题 </a> </li> <li class=md-nav__item> <a href=../IPU200M/Other_Anomalies.html class=md-nav__link> 其他异常问题 </a> </li> </ul> </nav> </li> </ul> </nav> </div> </div> </div> <div class=md-content data-md-component=content> <article class="md-content__inner md-typeset"> <h1>Model Conversion</h1> <p><strong>可以使用IPU_SDK转换工具直接转换INT8的模型吗？</strong></p> <p>当前不支援直接输入IPU SDK的量化定点网络模型，模型必须从浮点开始转换。可以使用量化导入工具将已有量化信息导入到模型中。详见3.4.3节。</p> <p><strong>为什么使用ConvertTool.py转换keras模型时Tensorflow报错？</strong></p> <p>由于默认使用了Tensorflow1.14.0，ConvertTool.py中也调用了Tensorflow的接口将keras模型转换为tflite。由于Tensorflow1.x不能转换Tensorflow2.0以上版本的keras模型，因此会发生错误。请先使用训练的Tensorflow版本将keras模型转换为tflite，然后再使用ConvertTool.py进行转换。</p> <p><strong>为什么使用simulator.py对YOLO网络测试mAP，精度会那么差？</strong></p> <p>由于COCO数据集虽然有80个分类，但是label的index不是连续的，加上中间插空的index，一共有90类。所提供的官方YOLO网络预训练模型去除了label的空白项，80类是连续的。如果直接调用COCO的接口计算mAP，结果是不正确的。</p> <p><strong>为什么在转换定点模型时有这样的报错：SGS_DMA_CoefMartixGen: Assertion <code>s32coef_bgr &lt; HW_COEF_MAX</code> failed.？</strong></p> <p>这个错误信息说明配置的python前处理和input_config.ini中不一致，主要体现在配置的mean和std不一致。具体详见3.2节图片前处理方法和2.2节input_config配置信息设置。检查统计输入的范围，与设置的input_config.ini中的数据比较。当使用量化导入时，也应该注意第一层卷积输入的设置范围应和input_config.ini中保持一致。</p> <p><strong>为什么input_config.ini中设置了dequantizations为FALSE，在PC仿真时仍然输出浮点数？</strong></p> <p>input_config.ini中的dequantizations配置仅在板上运行时生效。模型的输出结果都是int16，是simulator.py完成了将int16输出结果乘scale的过程。还可以使用calibrator_custom.fixed_simulator创建定点模型的实例，通过get_output_details的方法查看网络输出的数据类型。</p> <p><strong>为什么从浮点模型转到定点模型的时候，一直卡在Start to analysis model...？</strong></p> <p>遇到这个情况，请检查提供量化使用的数据集中文件名是否包含有中文字符。</p> <p><strong>为什么用DumpDebug工具比较有精度损失的定点模型和浮点模型时,输出的log中不能显示出所有层的比较结果?</strong></p> <p>这是由于DebugConfig.txt中的disableDomainFuseOps没有开启,导致有些op融合。请先将DebugConfig.txt拷贝到执行目录中,并将disableDomainFuseOps开启,并重新转换模型后再进行simulator生成<code>sigma_outtensor_dump.bin</code>文件。</p> <p><strong>为什么用DumpDebug工具比较时,会出现 <code>write file fail...</code>?</strong></p> <p>这是由于DebugConfig.txt中<code>path=</code>配置的路径不存在,请检查配置的路径并配置存在的绝地路径。默认路径为用户根目录。</p> </article> </div> </div> </main> <footer class=md-footer> <div class="md-footer-meta md-typeset"> <div class="md-footer-meta__inner md-grid"> <div class=md-footer-copyright> <div class=md-footer-copyright__highlight> Copyright&copy; 2021 SigmaStar Technology. All rights reserved. Security Level: Confidential A. </div> Made with <a href=https://squidfunk.github.io/mkdocs-material/ target=_blank rel=noopener> Material for MkDocs </a> </div> </div> </div> </footer> </div> <div class=md-dialog data-md-component=dialog> <div class="md-dialog__inner md-typeset"></div> </div> <script id=__config type=application/json>{"base": "../../..", "features": [], "translations": {"clipboard.copy": "Copy to clipboard", "clipboard.copied": "Copied to clipboard", "search.config.lang": "en", "search.config.pipeline": "trimmer, stopWordFilter", "search.config.separator": "[\\s\\-]+", "search.placeholder": "Search", "search.result.placeholder": "Type to start searching", "search.result.none": "No matching documents", "search.result.one": "1 matching document", "search.result.other": "# matching documents", "search.result.more.one": "1 more on this page", "search.result.more.other": "# more on this page", "search.result.term.missing": "Missing"}, "search": "../../../assets/javascripts/workers/search.fb4a9340.min.js", "version": null}</script> <script src=../../../assets/javascripts/bundle.a1c7c35e.min.js></script> <script src=../../../search/search_index.js></script> <script src=../../../javascripts/extra.js></script> <script src="https://cdnjs.cloudflare.com/ajax/libs/mathjax/2.7.0/MathJax.js?config=TeX-MML-AM_CHTML"></script> <script src=https://cdnjs.cloudflare.com/ajax/libs/raphael/2.2.7/raphael.min.js></script> <script src=https://cdnjs.cloudflare.com/ajax/libs/underscore.js/1.8.3/underscore-min.js></script> <script src=https://cdnjs.cloudflare.com/ajax/libs/js-sequence-diagrams/1.0.6/sequence-diagram-min.js></script> <script src=https://cdnjs.cloudflare.com/ajax/libs/flowchart/1.6.5/flowchart.min.js></script> <script src=https://unpkg.com/freezeframe/dist/freezeframe.min.js></script> <script src=https://unpkg.com/mermaid@7.1.0/dist/mermaid.min.js></script> <script src=../../../javascripts/umlconvert.js></script> </body> </html>