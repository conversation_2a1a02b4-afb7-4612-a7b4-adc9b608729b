window.MathJax = {
    tex2jax: {
        inlineMath: [
            ["\\(", "\\)"]
        ],
        displayMath: [
            ["\\[", "\\]"]
        ]
    },
    TeX: {
        TagSide: "right",
        TagIndent: ".8em",
        MultLineWidth: "85%",
        equationNumbers: {
            autoNumber: "AMS",
        },
        unicode: {
            fonts: "STIXGeneral,'Arial Unicode MS'"
        }
    },
    displayAlign: "left",
    showProcessingMessages: false,
    messageStyle: "none"
};